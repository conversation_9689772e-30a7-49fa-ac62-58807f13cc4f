Index: IntegrationCore/BusinessLogic/Receiver/Tasks/Composite/Components/RemoveReceiverFromBlacklist.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/IntegrationCore/BusinessLogic/Receiver/Tasks/Composite/Components/RemoveReceiverFromBlacklist.php b/IntegrationCore/BusinessLogic/Receiver/Tasks/Composite/Components/RemoveReceiverFromBlacklist.php
--- a/IntegrationCore/BusinessLogic/Receiver/Tasks/Composite/Components/RemoveReceiverFromBlacklist.php	
+++ b/IntegrationCore/BusinessLogic/Receiver/Tasks/Composite/Components/RemoveReceiverFromBlacklist.php	(date 1732106657007)
@@ -29,11 +29,11 @@
                 $suffix = $this->getGroupService()->getBlacklistedEmailsSuffix();
                 $this->getReceiverProxy()->whitelist($receiver->getEmail() . $suffix);
             } catch (\Exception $e) {
-                Logger::logWarning(
+                /*Logger::logWarning(
                     "Failed to remove receiver from a blacklist because: {$e->getMessage()}.",
                     'Core',
                     array(new LogContextData('trace', $e->getTraceAsString()))
-                );
+                );*/
             }
         }
 
