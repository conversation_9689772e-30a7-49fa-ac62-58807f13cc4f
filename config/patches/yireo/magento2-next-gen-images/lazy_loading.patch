Index: Image/HtmlReplacer.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Image/HtmlReplacer.php b/Image/HtmlReplacer.php
--- a/Image/HtmlReplacer.php	
+++ b/Image/HtmlReplacer.php	(date 1639214354000)
@@ -144,7 +144,7 @@
             ->setClass($this->getAttributeText($htmlTag, 'class'))
             ->setWidth($this->getAttributeText($htmlTag, 'width'))
             ->setHeight($this->getAttributeText($htmlTag, 'height'))
-            ->setLazyLoading($this->config->addLazyLoading())
+            ->setLazyLoading($this->getAttributeText($htmlTag, 'loading') == "lazy" ?: $this->config->addLazyLoading())
             ->setIsDataSrc($isDataSrc)
             ->toHtml();
     }
