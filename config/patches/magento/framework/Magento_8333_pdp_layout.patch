Index: View/Model/Layout/Merge.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
--- View/Model/Layout/Merge.php	(revision )
+++ View/Model/Layout/Merge.php	(revision )
@@ -48,6 +48,11 @@
      */
     private $theme;

+    /**
+     * @var \Magento\Framework\View\DesignInterface
+     */
+    private $design;
+
     /**
      * @var \Magento\Framework\Url\ScopeInterface
      */
@@ -183,6 +188,7 @@
         \Magento\Framework\View\Design\ThemeInterface $theme = null,
         $cacheSuffix = ''
     ) {
+        $this->design = $design;
         $this->theme = $theme ?: $design->getDesignTheme();
         $this->scope = $scopeResolver->getScope();
         $this->fileSource = $fileSource;
@@ -424,6 +430,8 @@
         }

         $this->addHandle($handles);
+        $this->theme = $this->design->getDesignTheme();
+

         $cacheId = $this->getCacheId();
         $cacheIdPageLayout = $cacheId . '_' . self::PAGE_LAYOUT_CACHE_SUFFIX;