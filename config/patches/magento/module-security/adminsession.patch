Index: Model/AdminSessionInfo.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Model/AdminSessionInfo.php b/Model/AdminSessionInfo.php
--- a/Model/AdminSessionInfo.php
+++ b/Model/AdminSessionInfo.php	(date 1631547278000)
@@ -132,6 +132,8 @@
         $lifetime = $this->securityConfig->getAdminSessionLifetime();
         $currentTime = $this->dateTime->gmtTimestamp();
         $lastUpdatedTime = $this->getUpdatedAt();
+        if(!$lastUpdatedTime)
+            return true;
         if (!is_numeric($lastUpdatedTime)) {
             $lastUpdatedTime = strtotime($lastUpdatedTime);
         }
