From 847c7997143d3c90b26f94480a9fe202f499faf7 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 22 Sep 2022 14:07:47 +0700
Subject: [PATCH 1/2] Revert changes for fixing JS errors in customer data

Revert code changes for fixing errors in customer data when component is not initialized - accident removed in a previous commit
---
 .../view/frontend/web/js/customer-data.js     | 29 +++++++++++++------
 1 file changed, 20 insertions(+), 9 deletions(-)

diff --git a/view/frontend/web/js/customer-data.js b/view/frontend/web/js/customer-data.js
index 213aa105ba25b..1f580cfccd0b3 100644
--- a/view/frontend/web/js/customer-data.js
+++ b/view/frontend/web/js/customer-data.js
@@ -17,7 +17,9 @@ define([
 ], function ($, _, ko, sectionConfig, url) {
     'use strict';
 
-    var options = {},
+    var options = {
+            cookieLifeTime: 86400 //1 day by default
+        },
         storage,
         storageInvalidation,
         invalidateCacheBySessionTimeOut,
@@ -30,6 +32,18 @@ define([
     url.setBaseUrl(window.BASE_URL);
     options.sectionLoadUrl = url.build('customer/section/load');
 
+    function initStorage() {
+        $.cookieStorage.setConf({
+            path: '/',
+            expires: new Date(Date.now() + parseInt(options.cookieLifeTime, 10) * 1000)
+        });
+        storage = $.initNamespaceStorage('mage-cache-storage').localStorage;
+        storageInvalidation = $.initNamespaceStorage('mage-cache-storage-section-invalidation').localStorage;
+    }
+
+    // Initialize storage with default parameters to prevent JS errors while component still not initialized
+    initStorage();
+
     /**
      * @param {Object} invalidateOptions
      */
@@ -217,14 +231,7 @@ define([
         /**
          * Storage init
          */
-        initStorage: function () {
-            $.cookieStorage.setConf({
-                path: '/',
-                expires: new Date(Date.now() + parseInt(options.cookieLifeTime, 10) * 1000)
-            });
-            storage = $.initNamespaceStorage('mage-cache-storage').localStorage;
-            storageInvalidation = $.initNamespaceStorage('mage-cache-storage-section-invalidation').localStorage;
-        },
+        initStorage: initStorage,
 
         /**
          * Retrieve the list of sections that has expired since last page reload.
@@ -389,6 +396,10 @@ define([
          */
         'Magento_Customer/js/customer-data': function (settings) {
             options = settings;
+
+            // re-init storage with a new settings
+            customerData.initStorage();
+
             customerData.initStorage();
             invalidateCacheBySessionTimeOut(settings);
             invalidateCacheByCloseCookieSession();

From d08f710be69b94ca8e60d28327feafd9b0e18630 Mon Sep 17 00:00:00 2001
From: Tu Van <<EMAIL>>
Date: Mon, 21 Nov 2022 00:51:55 +0700
Subject: [PATCH 2/2] Fix mage-cache-storage does not reflect to the change
 from system config value.

---
 .../view/frontend/web/js/customer-data.js      | 18 +++++++++---------
 1 file changed, 9 insertions(+), 9 deletions(-)

diff --git a/view/frontend/web/js/customer-data.js b/view/frontend/web/js/customer-data.js
index 1f580cfccd0b3..3c356d5ba7ee6 100644
--- a/view/frontend/web/js/customer-data.js
+++ b/view/frontend/web/js/customer-data.js
@@ -17,9 +17,7 @@ define([
 ], function ($, _, ko, sectionConfig, url) {
     'use strict';
 
-    var options = {
-            cookieLifeTime: 86400 //1 day by default
-        },
+    var options = {},
         storage,
         storageInvalidation,
         invalidateCacheBySessionTimeOut,
@@ -33,12 +31,14 @@ define([
     options.sectionLoadUrl = url.build('customer/section/load');
 
     function initStorage() {
-        $.cookieStorage.setConf({
-            path: '/',
-            expires: new Date(Date.now() + parseInt(options.cookieLifeTime, 10) * 1000)
-        });
-        storage = $.initNamespaceStorage('mage-cache-storage').localStorage;
-        storageInvalidation = $.initNamespaceStorage('mage-cache-storage-section-invalidation').localStorage;
+        if (options.cookieLifeTime !== 'undefined') {
+            $.cookieStorage.setConf({
+                path: '/',
+                expires: new Date(Date.now() + parseInt(options.cookieLifeTime, 10) * 1000)
+            });
+            storage = $.initNamespaceStorage('mage-cache-storage').localStorage;
+            storageInvalidation = $.initNamespaceStorage('mage-cache-storage-section-invalidation').localStorage;
+        }
     }
 
     // Initialize storage with default parameters to prevent JS errors while component still not initialized