diff --git a/vendor/magento/module-customer/view/frontend/web/js/customer-data.js b/vendor/magento/module-customer/view/frontend/web/js/customer-data.js
index 3ee2rd..8349152 111644
--- a/vendor/magento/module-customer/view/frontend/web/js/customer-data.js
+++ b/vendor/magento/module-customer/view/frontend/web/js/customer-data.js
@@ -250,7 +250,9 @@

             // process sections that can expire due to lifetime constraints
             _.each(options.expirableSectionNames, function (sectionName) {
-                sectionData = storage.get(sectionName);
+                if (storage !== undefined) {
+                    sectionData = storage.get(sectionName);
+                }

                 if (typeof sectionData === 'object' && sectionData['data_id'] + sectionLifetime <= currentTimestamp) {
                     expiredSectionNames.push(sectionName);
