Index: Model/Wysiwyg/Validator.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Model/Wysiwyg/Validator.php b/Model/Wysiwyg/Validator.php
--- a/Model/Wysiwyg/Validator.php	
+++ b/Model/Wysiwyg/Validator.php	(date 1627906400057)
@@ -81,7 +81,7 @@
             if ($throwException) {
                 throw $exception;
             } else {
-                $this->messages->addUniqueMessages(
+                /* $this->messages->addUniqueMessages(
                     [
                         $this->messageFactory->create(
                             MessageInterface::TYPE_WARNING,
@@ -91,7 +91,7 @@
                             )
                         )
                     ]
-                );
+                ); */
             }
         } catch (\Throwable $exception) {
             if ($throwException) {
