Index: Model/Writer.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Model/Writer.php b/Model/Writer.php
--- a/Model/Writer.php	
+++ b/Model/Writer.php	(date 1709851771453)
@@ -20,10 +20,10 @@
 use Magento\Framework\Filesystem\Directory\WriteInterface;
 use Magento\Store\Model\StoreManagerInterface;
 use \Magento\Framework\Filesystem\Io\File;
-use \Zend\Validator\Sitemap\Changefreq as ChangefreqValidator;
-use \Zend\Validator\Sitemap\Lastmod as LastmodValidator;
-use \Zend\Validator\Sitemap\Loc as LocationValidator;
-use \Zend\Validator\Sitemap\Priority as PriorityValidator;
+use \Laminas\Validator\Sitemap\Changefreq as ChangefreqValidator;
+use \Laminas\Validator\Sitemap\Lastmod as LastmodValidator;
+use \Laminas\Validator\Sitemap\Loc as LocationValidator;
+use \Laminas\Validator\Sitemap\Priority as PriorityValidator;
 use \Magento\Store\Model\Store as StoreModel;
 use \Magento\Framework\DataObject;
 use \MageWorx\XmlSitemap\Model\LinkChecker;
