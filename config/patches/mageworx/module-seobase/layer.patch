Index: Model/Hreflangs/Category.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Model/Hreflangs/Category.php b/Model/Hreflangs/Category.php
--- a/Model/Hreflangs/Category.php	
+++ b/Model/Hreflangs/Category.php	(date 1658390796478)
@@ -49,6 +49,10 @@
      * @var \Magento\Catalog\Model\Layer\Category\
      */
     protected $catalogLayer;
+    /**
+     * @var LayerResolver
+     */
+    private $layerResolver;
 
     /**
      * Category constructor.
@@ -77,10 +81,10 @@
         $this->registry        = $registry;
         $this->helperStore     = $helperStore;
         $this->url             = $url;
-        $this->catalogLayer    = $layerResolver->get();
         $this->helperHreflangs = $helperHreflangs;
         $this->hreflangFactory = $hreflangFactory;
         parent::__construct($hreflangsConfigReader, $helperUrl, $fullActionName);
+        $this->layerResolver = $layerResolver;
     }
 
     /**
@@ -141,6 +145,7 @@
      */
     protected function getLayeredNavigationFiltersCode(): array
     {
+        $this->catalogLayer = $this->layerResolver->get();
         $filterCodes    = [];
         $appliedFilters = $this->catalogLayer->getState()->getFilters();
 
