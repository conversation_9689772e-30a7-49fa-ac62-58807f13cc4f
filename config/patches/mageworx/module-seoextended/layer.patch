Index: Model/FiltersConvertor/Category.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Model/FiltersConvertor/Category.php b/Model/FiltersConvertor/Category.php
--- a/Model/FiltersConvertor/Category.php	
+++ b/Model/FiltersConvertor/Category.php	(date 1658391210013)
@@ -17,6 +17,10 @@
      * @var \Magento\Catalog\Model\Layer\Category
      */
     protected $catalogLayer;
+    /**
+     * @var LayerResolver|\MageWorx\SeoExtended\Model\FiltersConvertor\LayerResolver
+     */
+    private $layerResolver;
 
     /**
      *
@@ -32,7 +36,7 @@
         $fullActionName = null
     ) {
         parent::__construct($helperData, $request, $fullActionName);
-        $this->catalogLayer = $layerResolver->get();
+        $this->layerResolver = $layerResolver;
     }
 
     /**
@@ -40,6 +44,7 @@
      */
     public function getStringByFilters()
     {
+        $this->catalogLayer = $this->layerResolver->get();
         $appliedFilters = $this->catalogLayer->getState()->getFilters();
 
         if (empty($appliedFilters)) {
