Index: etc/csp_whitelist.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/etc/csp_whitelist.xml b/etc/csp_whitelist.xml
--- a/etc/csp_whitelist.xml	
+++ b/etc/csp_whitelist.xml	(date 1714066516000)
@@ -1,23 +1,6 @@
 <?xml version="1.0" encoding="UTF-8"?>
 <csp_whitelist xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Csp:etc/csp_whitelist.xsd">
     <policies>
-        <policy id="frame-src">
-            <values>
-                <value id="xtento-frame" type="host">www.xtento.com</value>
-            </values>
-        </policy>
-        <policy id="img-src">
-            <values>
-                <value id="xtento" type="host">www.xtento.com</value>
-                <value id="xtento-cdn" type="host">cdn.xtento.com</value>
-            </values>
-        </policy>
-        <policy id="script-src">
-            <values>
-                <value id="xtento-scripts" type="host">www.xtento.com</value>
-                <value id="xtento-scripts-cdn" type="host">cdn.xtento.com</value>
-            </values>
-        </policy>
     </policies>
 </csp_whitelist>
 <!--
