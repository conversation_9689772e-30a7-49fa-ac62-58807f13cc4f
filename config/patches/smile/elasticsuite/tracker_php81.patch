Index: src/module-elasticsuite-tracker/Block/Variables/Page/Catalog.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/module-elasticsuite-tracker/Block/Variables/Page/Catalog.php b/src/module-elasticsuite-tracker/Block/Variables/Page/Catalog.php
--- a/src/module-elasticsuite-tracker/Block/Variables/Page/Catalog.php	
+++ b/src/module-elasticsuite-tracker/Block/Variables/Page/Catalog.php	(date 1701704809598)
@@ -206,7 +206,7 @@
     private function getCategoryBreadcrumb(\Magento\Catalog\Model\Category $category)
     {
         $path    = $category->getPath();
-        $rawPath = explode('/', $path);
+        $rawPath = explode('/', (string)$path);
 
         // First occurence is root category (1), second is root category of store.
         $rawPath = array_slice($rawPath, 2);
