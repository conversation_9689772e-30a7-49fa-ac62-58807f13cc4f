Index: src/module-elasticsuite-tracker/etc/csp_whitelist.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/module-elasticsuite-tracker/etc/csp_whitelist.xml b/src/module-elasticsuite-tracker/etc/csp_whitelist.xml
--- a/src/module-elasticsuite-tracker/etc/csp_whitelist.xml	
+++ b/src/module-elasticsuite-tracker/etc/csp_whitelist.xml	(date 1733862604000)
@@ -16,28 +16,5 @@
 <csp_whitelist xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Csp:etc/csp_whitelist.xsd">
         <policies>
-            <policy id="connect-src">
-                <values>
-                    <value id="elasticsuite" type="host">t.elasticsuite.io</value>
-                </values>
-            </policy>
-            <policy id="img-src">
-                <values>
-                    <value id="hsnet" type="host">*.hsforms.net</value>
-                    <value id="hscom" type="host">*.hsforms.com</value>
-                </values>
-            </policy>
-            <policy id="connect-src">
-                <values>
-                    <value id="hsnet" type="host">*.hsforms.net</value>
-                    <value id="hscom" type="host">*.hsforms.com</value>
-                </values>
-            </policy>
-            <policy id="script-src">
-                <values>
-                    <value id="hsnet" type="host">*.hsforms.net</value>
-                    <value id="hscom" type="host">*.hsforms.com</value>
-                </values>
-            </policy>
         </policies>
 </csp_whitelist>
