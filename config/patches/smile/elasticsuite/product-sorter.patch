Index: src/module-elasticsuite-catalog/view/adminhtml/web/js/form/element/product-sorter.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/module-elasticsuite-catalog/view/adminhtml/web/js/form/element/product-sorter.js b/src/module-elasticsuite-catalog/view/adminhtml/web/js/form/element/product-sorter.js
--- a/src/module-elasticsuite-catalog/view/adminhtml/web/js/form/element/product-sorter.js
+++ b/src/module-elasticsuite-catalog/view/adminhtml/web/js/form/element/product-sorter.js	(date 1688551692042)
@@ -181,7 +181,7 @@

         refreshProductList: function () {
             if (this.refreshRateLimiter !== undefined) {
-                clearTimeout();
+                clearTimeout(this.refreshRateLimiter);
             }

             this.loading(true);
@@ -270,14 +271,8 @@
         },

         getProductById : function (productId) {
-            var product = null;
-            productId   = parseInt(productId, 10);
-            this.products().forEach(function (currentProduct) {
-                if (currentProduct.getId() === productId) {
-                    product = currentProduct;
-                }
-            });
-            return product;
+            productId = parseInt(productId, 10);
+            return this.products().find( product => product.getId() === productId);
         },

         enableSortableList: function (element, component) {
