Index: etc/crontab.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/etc/crontab.xml b/etc/crontab.xml
--- a/etc/crontab.xml
+++ b/etc/crontab.xml	(date 1669367957690)
@@ -6,9 +6,4 @@
  */
 -->
 <config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Cron:etc/crontab.xsd">
-    <group id="default">
-        <job name="mf_community_module" instance="Magefan\Community\Cron\Sections" method="execute">
-            <schedule>1 1 1,10 * *</schedule>
-        </job>
-    </group>
 </config>
Index: etc/adminhtml/events.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/etc/adminhtml/events.xml b/etc/adminhtml/events.xml
--- a/etc/adminhtml/events.xml
+++ b/etc/adminhtml/events.xml	(date 1669368031765)
@@ -6,10 +6,4 @@
  */
 -->
 <config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
-    <event name="controller_action_predispatch">
-        <observer name="magefan_community_controller_action_predispatch" instance="Magefan\Community\Observer\PredispathAdminActionControllerObserver" />
-    </event>
-    <event name="controller_action_predispatch_adminhtml_system_config_save">
-        <observer name="magefan_community_controller_action_predispatch_adminhtml_system_config_save" instance="Magefan\Community\Observer\ConfigObserver"/>
-    </event>
 </config>
\ No newline at end of file
