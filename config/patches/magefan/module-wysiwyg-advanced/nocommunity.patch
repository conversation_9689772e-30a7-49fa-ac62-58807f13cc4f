Index: etc/adminhtml/system.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/etc/adminhtml/system.xml b/etc/adminhtml/system.xml
--- a/etc/adminhtml/system.xml	
+++ b/etc/adminhtml/system.xml	(date 1732738515981)
@@ -10,14 +10,10 @@
         <section id="mfwysiwygadvanced" translate="label" type="text" sortOrder="12033682130" showInDefault="1" showInWebsite="0" showInStore="0">
             <class>separator-top</class>
             <label>WYSIWYG Advanced</label>
-            <tab>magefan</tab>
             <resource>Magefan_WysiwygAdvanced::config</resource>
             <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                 <label>General</label>
                 <attribute type="expanded">1</attribute>
-                <field id="version" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
-                    <frontend_model>Magefan\WysiwygAdvanced\Block\Adminhtml\System\Config\Form\Info</frontend_model>
-                </field>
                 <field id="cms_page_enabled" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="0" showInStore="0" canRestore="1" >
                     <label>WYSIWYG Editor on CMS Page</label>
                     <source_model>Magefan\WysiwygAdvanced\Model\Config\Source\Wysiwyg\Enabled</source_model>

Index: Block/Adminhtml/System/Config/Form/Info.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Block/Adminhtml/System/Config/Form/Info.php b/Block/Adminhtml/System/Config/Form/Info.php
--- a/Block/Adminhtml/System/Config/Form/Info.php
+++ b/Block/Adminhtml/System/Config/Form/Info.php	(date 1732738374045)
@@ -11,7 +11,7 @@
 /**
  * Admin blog configurations information block
  */
-class Info extends \Magefan\Community\Block\Adminhtml\System\Config\Form\Info
+class Info
 {
     /**
      * Return extension url
