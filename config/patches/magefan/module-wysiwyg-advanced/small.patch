Index: Plugin/Magento/Ui/Component/Wysiwyg/ConfigPlugin.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Plugin/Magento/Ui/Component/Wysiwyg/ConfigPlugin.php b/Plugin/Magento/Ui/Component/Wysiwyg/ConfigPlugin.php
--- a/Plugin/Magento/Ui/Component/Wysiwyg/ConfigPlugin.php
+++ b/Plugin/Magento/Ui/Component/Wysiwyg/ConfigPlugin.php	(date 1749716021523)
@@ -109,12 +109,12 @@
             }

             // configure tinymce settings
-            $settings['menubar'] = true;
-            $settings['image_advtab'] = true;
+            $settings['menubar'] = false;
+            $settings['image_advtab'] = false;

             if (strpos($editor, 'tinymceAdapter')) {
-                $settings['plugins'] = 'advlist autolink code directionality link media table visualchars anchor charmap codesample help image insertdatetime lists nonbreaking pagebreak preview searchreplace visualblocks wordcount magentovariable magentowidget emoticons';
-                $settings['toolbar1'] = 'magentovariable magentowidget | blocks | styles | fontfamily | fontsizeinput | lineheight | forecolor backcolor | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent';
+                $settings['plugins'] = 'advlist autolink code directionality hr imagetools link media noneditable paste table toc visualchars anchor codesample  image  visualblocks  magentovariable magentowidget';
+                $settings['toolbar1'] = 'magentovariable magentowidget | code | blocks | styles | fontfamily | fontsize | lineheight | forecolor backcolor | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent';
             } else {
                 $settings['plugins'] = 'advlist autolink code colorpicker directionality hr imagetools link media noneditable paste print table textcolor toc visualchars anchor charmap codesample contextmenu help image insertdatetime lists nonbreaking pagebreak preview searchreplace template textpattern visualblocks wordcount magentovariable magentowidget emoticons';
                 $settings['toolbar1'] = 'magentovariable magentowidget | formatselect | styleselect | fontselect | fontsizeselect | lineheight | forecolor backcolor | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent';
