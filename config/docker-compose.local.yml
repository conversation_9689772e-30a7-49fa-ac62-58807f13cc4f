volumes:
    composer-cache:
        external: true

networks:
    backend:
        external: true

services:
#    varnish:
#        image: varnish:7.0
#        restart: unless-stopped
#        container_name: "${PROJECT}-varnish"
#        networks:
#            - web
#        depends_on:
#            - app
#        volumes:
#            - ./varnish/varnish.vcl:/etc/varnish/default.vcl
#        ports:
#            - "80:80"
#            - "443:443"
#        environment:
#            VARNISH_STORAGE: "malloc,400m"
#            CACHE_SIZE: "256M"
    app:
        image: copex/nginx-php-fpm:dev
        ports:
            - "80:80"
            - "443:443"
        extra_hosts:
            - "host.docker.internal:host-gateway"
        environment:
            PHP_IDE_CONFIG: "serverName=${DEV_DOMAIN}"
            XDEBUG_MODE: debug
            XDEBUG_CONFIG: "client_host=host.docker.internal log_level=0 start_with_request=yes remote_enable=1 remote_mode=req remote_port=9000 remote_host=host.docker.internal remote_connect_back=0 remote_autostart=0 idekey=PHPSTORM"
            DOMAIN: ${DEV_DOMAIN}
            MAGENTO_ROOT: ${DEV_MAGENTO_ROOT}
            SSH_AUTH_SOCK: /ssh-agent
        volumes:
            - ../:${DEV_MAGENTO_ROOT}
            - composer-cache:/var/www/.composer/cache
            - ${SSH_AUTH_SOCK}:/ssh-agent
            - ~/.n98-magerun2:/var/www/.n98-magerun2
    mysql:
        environment:
            MYSQL_ROOT_PASSWORD: "r00t"
            MYSQL_USER: "magento"
            MYSQL_PASSWORD: "magento"
            MYSQL_DATABASE: "magento"
        ports:
            - "3306:3306"
    search:
        ports:
            - "9200:9200"
            - "9300:9300"
    elastichq:
        image: elastichq/elasticsearch-hq
        networks:
            - backend
        ports:
            - 5000:5000
        environment:
            HQ_DEFAULT_URL: 'http://search:9200'
    redis:
        command: redis-server --maxmemory 4G --save 3600 1
    mailcatcher:
        image: sj26/mailcatcher
        ports:
            - "1080:1080"
        networks:
            - backend
