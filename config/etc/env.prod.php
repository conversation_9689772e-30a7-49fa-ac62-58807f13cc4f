<?php
return [
    'modules'         =>
    [
        'ADM_QuickDevBar'                    => 0,
        'Yireo_Whoops'                       => 0,
        'MagePal_PreviewCheckoutSuccessPage' => 0,
        'Smile_DebugToolbar'                 => 0,
        'MSP_Common'                         => 0,
        'MSP_DevTools'                       => 0,
        'Ho_Templatehints'                   => 0,
        'Salecto_MediaStorageSync'           => 0,
        'Awin_AdvertiserTracking'            => 1
    ],
    'backend' => [
        'frontName' => 'backendgfp'
    ],
    'install' => [
        'date' => 'Mon, 24 Jul 2017 15:50:05 +0000'
    ],
    'crypt' => [
        'key' => 'gfp-international
DZuS87vcjmycAcgqe34k25ebDAnoDCx6
2fbdb775687a715262e7e09c89464d18'
    ],
    'cache' => [
        'frontend' => [
            'default' => [
                'backend' => 'Cm_Cache_Backend_Redis',
                'backend_options' => [
                    'server' => '/var/run/redis/redis.sock',
                    'database' => '3',
                    'port' => '0'
                ]
            ]
        ]
    ],
    'session' => [
        'save' => 'redis',
        'redis' => [
            'host' => '/var/run/redis/redis.sock',
            'database' => '4',
            'log_level' => '1',
            'port' => '6379',
            'password' => '',
            'timeout' => '2.5',
            'persistent_identifier' => '',
            'compression_threshold' => '2048',
            'compression_library' => 'gzip',
            'max_concurrency' => '6',
            'break_after_frontend' => '5',
            'break_after_adminhtml' => '30',
            'first_lifetime' => '600',
            'bot_first_lifetime' => '60',
            'bot_lifetime' => '7200',
            'disable_locking' => '0',
            'min_lifetime' => '60',
            'max_lifetime' => '2592000'
        ]
    ],
    'db' => [
        'connection' => [
            'default' => [
                'host' => 'localhost',
                'dbname' => 'usrdb_updatgbu_update',
                'username' => 'updatgbu',
                'password' => 'wUwbdH6xR!7nQhrjbfPY',
                'model' => 'mysql4',
                'engine' => 'innodb',
                'initStatements' => 'SET NAMES utf8;',
                'active' => '1'
            ]
        ],
        'table_prefix' => ''
    ],
    'resource' => [
        'default_setup' => [
            'connection' => 'default'
        ]
    ],
    'x-frame-options' => 'SAMEORIGIN',
    'MAGE_MODE' => 'production',
    'cache_types' => [
        'config' => 1,
        'layout' => 1,
        'block_html' => 1,
        'collections' => 1,
        'reflection' => 1,
        'db_ddl' => 1,
        'eav' => 1,
        'config_integration' => 1,
        'config_integration_api' => 1,
        'full_page' => 1,
        'translate' => 1,
        'config_webservice' => 1,
        'compiled_config' => 1,
        'customer_notification' => 1
    ],
    'directories'     =>
        [
            'document_root_is_pub' => true,
        ],
    'system' => [
        'default' => [
            'catalog' => [
                'search' => [
                    'engine' => "elasticsuite",
                    "elasticsearch7_server_hostname" => "127.0.0.1"
                ]
            ],
            'smile_elasticsuite_core_base_settings' => [
                'es_client' => [
                    'servers' => '127.0.0.1:9200'
                ],
                'indices_settings' => [
                    'alias' => 'production'
                ]
            ],
            'smile_elasticsuite_telemetry' => [
                'telemetry' => [
                    'enabled' => 0
                ]
            ],
            'googletagmanager' => [
                'general' => [
                    'account' =>  'GTM-TLDKVH',
                    'active' => 1
                ]
            ],
            'system' => [
                'backup' => [
                    'functionality_enabled' => 0
                ]
            ],
            'csp' => [
                'general' => [
                    'enabled' => 1,
                    'merge' => 1
                ]
            ],
            'dev' => [
                'debug' => [
                    'debug_logging' => '0'
                ],
                'js' => [
                    'enable_js_bundling' => '0',
                    'merge_files' => '0',
                    'minify_files' => '1',
                    'enable_magepack_js_bundling' => 0,
                    'move_script_to_bottom' => 1
                ],
                'css' => [
                    'minify_files' => '1',
                    'merge_css_files' => '0',
                    'use_css_critical_path' => '0'
                ],
                'static' => [
                    'sign' => 1
                ]
            ],
            'weltpixel_backend_developer' =>[
                'notifications' => [
                    'enable_admin_notification' => 0
                ]
            ],
            'orderexport' => [
                'general' => [
                    'serial' => 'fab09b866f1251b9baa4dd2684b0efb9eb1deef6'
                ]
            ]
        ]
    ],
    'http_cache_hosts' => [
        [
            'host' => '127.0.0.1',
            'port' => '8080'
        ]
    ]
];
