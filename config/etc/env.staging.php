<?php
return [
    'modules'         =>
    [
        'ADM_QuickDevBar'                    => 0,
        'Yireo_Whoops'                       => 0,
        'MagePal_PreviewCheckoutSuccessPage' => 0,
        'Smile_DebugToolbar'                 => 0,
        'MSP_Common'                         => 0,
        'MSP_DevTools'                       => 0,
        'Ho_Templatehints'                   => 0,
        'Salecto_MediaStorageSync'           => 0,
    ],
    'backend' => [
        'frontName' => 'backendgfp'
    ],
    'install' => [
        'date' => 'Mon, 24 Jul 2017 15:50:05 +0000'
    ],
    'crypt' => [
        'key' => 'gfp-international
DZuS87vcjmycAcgqe34k25ebDAnoDCx6
e5096490bcd3863599ba5f33d88bccf9'
    ],
    'cache' => [
        'frontend' => [
            'default' => [
                'backend' => 'Cm_Cache_Backend_Redis',
                'backend_options' => [
                    'server' => '/var/run/redis/redis.sock',
                    'database' => '6',
                    'port' => '0'
                ]
            ]
        ]
    ],
    'session' => [
        'save' => 'redis',
        'redis' => [
            'host' => 'redis',
            'database' => '7',
            'log_level' => '1',
            'port' => '6379',
            'password' => '',
            'timeout' => '2.5',
            'persistent_identifier' => '',
            'compression_threshold' => '2048',
            'compression_library' => 'gzip',
            'max_concurrency' => '6',
            'break_after_frontend' => '5',
            'break_after_adminhtml' => '30',
            'first_lifetime' => '600',
            'bot_first_lifetime' => '60',
            'bot_lifetime' => '7200',
            'disable_locking' => '0',
            'min_lifetime' => '60',
            'max_lifetime' => '2592000'
        ]
    ],
    'db' => [
        'connection' => [
            'default' => [
                'host' => 'mysql',
                'dbname' => 'gfp-staging',
                'username' => 'gfp',
                'password' => 'h#DlHpdfgdg+MWRaw',
                'model' => 'mysql4',
                'engine' => 'innodb',
                'initStatements' => 'SET NAMES utf8;',
                'active' => '1'
            ]
        ],
        'table_prefix' => ''
    ],
    'resource' => [
        'default_setup' => [
            'connection' => 'default'
        ]
    ],
    'x-frame-options' => 'SAMEORIGIN',
    'MAGE_MODE' => 'production',
    'cache_types' => [
        'config' => 1,
        'layout' => 1,
        'block_html' => 1,
        'collections' => 1,
        'reflection' => 1,
        'db_ddl' => 1,
        'eav' => 1,
        'config_integration' => 1,
        'config_integration_api' => 1,
        'full_page' => 1,
        'translate' => 1,
        'config_webservice' => 1,
        'compiled_config' => 1,
        'customer_notification' => 1
    ],
    'directories'     =>
        [
            'document_root_is_pub' => true,
        ],
    'system' => [
        'default' => [
            'web'                                   => [
                'secure'   => [
                    'base_url'      => 'https://gfp-staging.copex.io/',
                    'base_link_url' => 'https://gfp-staging.copex.io/',
                ],
                'unsecure' => [
                    'base_url'      => 'https://gfp-staging.copex.io/',
                    'base_link_url' => 'https://gfp-staging.copex.io/',
                ],
            ],
            'catalog' => [
                'search' => [
                    'engine' => 'elasticsuite',
                    'elasticsearch7_server_hostname' => 'search',
                    'elasticsearch7_index_prefix' => 'staging'
                ]
            ],
            'smile_elasticsuite_core_base_settings' => [
                'es_client' => [
                    'servers' => 'search:9200'
                ],
                'indices_settings' => [
                    'alias' => 'staging'
                ]
            ],
            'smile_elasticsuite_telemetry' => [
                'telemetry' => [
                    'enabled' => 0
                ]
            ],
            'googletagmanager' => [
                'general' => [
                    'account' =>  'GTM-TLDKVH',
                    'active' => 0
                ]
            ],
            'system' => [
                'backup' => [
                    'functionality_enabled' => 0
                ]
            ],
            'basecom_csp_split_header' => [
                'settings' => [
                    'header_splitting_enable' => 1,
                    'max_header_size' => 6000
                ]
            ],
            'dev' => [
                'debug' => [
                    'debug_logging' => '0'
                ],
                'js' => [
                    'enable_js_bundling' => '0',
                    'merge_files' => '0',
                    'minify_files' => '1',
                    'enable_magepack_js_bundling' => 1,
                    'move_script_to_bottom' => 1
                ],
                'css' => [
                    'minify_files' => '1',
                    'merge_css_files' => '0',
                    'use_css_critical_path' => '0'
                ],
                'static' => [
                    'sign' => 1
                ]
            ],
            'weltpixel_backend_developer' =>[
                'notifications' => [
                    'enable_admin_notification' => 0
                ]
            ],
            'orderexport' => [
                'general' => [
                    'serial' => 'aabdf8c795aa6568bcf7707ddd53d6b543255c5c'
                ]
            ],
            'magenest_cachewarmer' => [
                'general' => [
                    'enabled' => 0,
                    'auto_flush' => 0,
                    'auto_update' => 0
                ],
                'performance_settings' => [
                    'enable_schedule' => 0
                ]
            ],
            'payment' => [
                'amazon_payment' => [
                    'client_id' => 'amzn1.application-oa2-client.09e8a5bebf9c4b93806e8170dc8dd2aa',
                    'client_secret' => '9a396b2e127f12c062eabf2216189d55f58b8795542ceba63cb073caba979571',
                    'sandbox' => 1
                ],
                'mollie_general' => [
                    'type' => 'test'
                ]
            ]
        ],
        'websites' => [
            'gfp_fr' => [
                'general' => [
                    "locale" => [
                        "code" => "fr_FR"
                    ]
                ]
            ]
        ]
    ],
    'http_cache_hosts' => [
        [
            'host' => 'varnish',
            'port' => '80'
        ]
    ]
];
