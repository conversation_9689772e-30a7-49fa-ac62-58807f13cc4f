<?php
return [
    'modules' => [
        'MSP_Common' => 1,
        'MSP_DevTools' => 1,
        'Awin_AdvertiserTracking' => 0,
        'ADM_QuickDevBar' => 0,
        'Hyva_MollieThemeBundle' => 0
    ],
    'backend' => [
        'frontName' => 'backoffice'
    ],
    'install' => [
        'date' => 'Mon, 24 Jul 2017 15:50:05 +0000'
    ],
    'crypt' => [
        'key' => 'gfp-international
        DZuS87vcjmycAcgqe34k25ebDAnoDCx6'
    ],
    'session' => [
        'save' => 'redis',
        'redis' => [
            'host' => 'redis',
            'port' => '6379',
            'password' => '',
            'timeout' => '2.5',
            'persistent_identifier' => '',
            'database' => '2',
            'compression_threshold' => '2048',
            'compression_library' => 'gzip',
            'log_level' => '1',
            'max_concurrency' => '20',
            'break_after_frontend' => '5',
            'break_after_adminhtml' => '30',
            'first_lifetime' => '600',
            'bot_first_lifetime' => '60',
            'bot_lifetime' => '7200',
            'disable_locking' => '0',
            'min_lifetime' => '60',
            'max_lifetime' => '2592000'
        ]
    ],
    'cache' => [
        'frontend' => [
            'default' => [
                'backend' => 'Cm_Cache_Backend_Redis',
                'backend_options' => [
                    'server' => 'redis',
                    'port' => '6379'
                ],
                'id_prefix' => '26b_'
            ],
            'page_cache' => [
                'backend' => 'Cm_Cache_Backend_Redis',
                'backend_options' => [
                    'server' => 'redis',
                    'port' => '6379',
                    'database' => '1',
                    'compress_data' => '0'
                ],
                'id_prefix' => '26b_'
            ]
        ],
        'graphql' => [
            'id_salt' => 'xQZ5gBKvOmqZTYUcT2LY9h1wAtFLKxMk'
        ]
    ],
    'db' => [
        'table_prefix' => '',
        'connection' => [
            'default' => [
                'host' => 'mysql',
                'dbname' => 'magento',
                'username' => 'magento',
                'password' => 'magento',
                'active' => '1',
                'model' => 'mysql4',
                'engine' => 'innodb',
                'initStatements' => 'SET NAMES utf8;',
                'driver_options' => [
                    1014 => false
                ]
            ]
        ]
    ],
    'resource' => [
        'default_setup' => [
            'connection' => 'default'
        ]
    ],
    'x-frame-options' => 'SAMEORIGIN',
    'MAGE_MODE' => 'developer',
    'cache_types' => [
        'config' => 1,
        'layout' => 1,
        'block_html' => 1,
        'collections' => 1,
        'reflection' => 1,
        'db_ddl' => 1,
        'eav' => 1,
        'config_integration' => 1,
        'config_integration_api' => 1,
        'full_page' => 0,
        'translate' => 1,
        'config_webservice' => 1,
        'compiled_config' => 1,
        'customer_notification' => 1,
        'elasticsuite' => 1
    ],
    'directories' => [
        'document_root_is_pub' => true
    ],
    'system' => [
        'default' => [
            'catalog' => [
                'search' => [
                    'engine' => 'elasticsuite',
                    'elasticsearch7_server_hostname' => 'search'
                ]
            ],
            'googletagmanager' => [
                'general' => [
                    'account' => 'GTM-TLDKVH',
                    'active' => 0
                ]
            ],
            'admin' => [
                'security' => [
                    'session_lifetime' => ********
                ]
            ],
            'system' => [
                'gmailsmtpapp' => [
                    'active' => 0
                ],
                'backup' => [
                    'functionality_enabled' => 0
                ],
                'smtp' => [
                    'transport' => 'sendmail'
                ]
            ],
            'klarna' => [
                'api' => [
                    'test_mode' => 1,
                    'debug' => 1,
                    'api_version' => 'kp_eu',
                    'merchant_id' => 'PK42367_9704917755da',
                    'shared_secret' => '1:3:57P/UnCvE2rJ/7bK5GSdaaTpi8DvRZBOOIuZcxWG6yYf/vaqJnylNMNsqXs='
                ]
            ],
            'smile_elasticsuite_core_base_settings' => [
                'es_client' => [
                    'servers' => 'search:9200'
                ]
            ],
            'smile_elasticsuite_telemetry' => [
                'telemetry' => [
                    'enabled' => 0
                ]
            ],
            'xtcore' => [
                'adminnotification' => [
                    'enabled' => 0
                ]
            ],
            'msp_securitysuite_recaptcha' => [
                'backend' => [
                    'enabled' => 0
                ],
                'frontend' => [
                    'enabled' => 0
                ]
            ],
            'recaptcha_frontend' => [
                'type_recaptcha_v3' => [
                    'public_key' => ''
                ]
            ],
            'media_storage_sync' => [
                'general' => [
                    'enabled' => 1,
                    'url' => 'https://www.gfp-international.com/'
                ]
            ],
            'csp' => [
                'general' => [
                    'enabled' => 1,
                    'merge' => 1
                ]
            ],
            'dev' => [
                'debug' => [
                    'debug_logging' => '1'
                ],
                'js' => [
                    'enable_js_bundling' => '0',
                    'merge_files' => '0',
                    'minify_files' => '0',
                    'enable_magepack_js_bundling' => 0,
                    'move_script_to_bottom' => 0
                ],
                'css' => [
                    'minify_files' => '0',
                    'merge_css_files' => '0',
                    'use_css_critical_path' => '0'
                ],
                'template' => [
                    'minify_html' => '0',
                    'allow_symlink' => '1'
                ],
                'quickdevbar' => [
                    'enable' => 1
                ],
                'image' => [
                    'default_adapter' => 'IMAGEMAGICK'
                ]
            ],
            'smile_debugtoolbar' => [
                'configuration' => [
                    'enabled' => 1
                ]
            ],
            'msp_devtools' => [
                'general' => [
                    'enabled' => 1
                ]
            ],
            'wirecard_checkoutpage' => [
                'basicdata' => [
                    'configuration' => 'demo'
                ]
            ],
            'paypal' => [
                'wpp' => [
                    'sandbox_flag' => 1
                ]
            ],
            'payone_payment' => [
                'payone_creditcard' => [
                    'mode' => 'test'
                ]
            ],
            'payment' => [
                'amazon_payment' => [
                    'client_id' => 'amzn1.application-oa2-client.09e8a5bebf9c4b93806e8170dc8dd2aa',
                    'client_secret' => '9a396b2e127f12c062eabf2216189d55f58b8795542ceba63cb073caba979571',
                    'sandbox' => 1
                ],
                'mollie_general' => [
                    'type' => 'test'
                ]
            ],
            'subscribe_cleverreach' => [
                'general' => [
                    'enabled' => 0
                ]
            ],
            'weltpixel_backend_developer' => [
                'notifications' => [
                    'enable_admin_notification' => 0
                ]
            ],
            'aminvisiblecaptcha' => [
                'setup' => [
                    'captchaKey' => '6LcIqhQdAAAAAGWYF3MptU96fKKhftfkwI8zSYrz',
                    'captchaSecret' => '6LcIqhQdAAAAAMWkE-FcUyzPt6H56jA27X5TB98Y',
                    'captchaKeyV3' => '6LfTrxQdAAAAANvn_n6RrLfCQMeFsjbmck1BH1n0',
                    'captchaSecretV3' => '6LfTrxQdAAAAAEiUZFEnZucDjuanaB7SIxQeiyAd'
                ]
            ],
            'orderexport' => [
                'general' => [
                    'serial' => '9a9f33da56c2a751a997d2f0d40520ccf428c57c',
                    'debug' => 1
                ]
            ],
            'customer' => [
                'captcha' => [
                    'enable' => 0
                ]
            ]
        ]
    ],
    'downloadable_domains' => [
        'www.gfp.test',
        'www.gfp-international.com'
    ],
    'queue' => [
        'consumers_wait_for_messages' => 1
    ],
    'http_cache_hosts' => [
        [
            'host' => 'varnish',
            'port' => '8080'
        ]
    ]
];
