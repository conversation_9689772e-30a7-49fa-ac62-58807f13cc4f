volumes:
    dbdata:
    magento-redis:
    magento-search:
    composer-cache:
        external: true

networks:
    web:
        external: true
    backend:
        external: false

services:
    app:
        image: copex/nginx-php-fpm
        restart: unless-stopped
        container_name: "${PROJECT}-app"
        networks:
            - web
            - backend
        depends_on:
            - redis
            - mysql
            - search
        environment:
            DOMAIN: ${DOMAIN}
            MAGENTO_VERSION: ${MAGENTO_VERSION}
            MAGENTO_ROOT: ${MAGENTO_ROOT}
            PHP_VERSION: ${PHP_VERSION}
            MAGENTO_DEVELOPERMODE: 0
        volumes:
            - composer-cache:/var/www/.composer/cache
        labels:
            - "traefik.enable=true"
            - "traefik.docker.network=web"
            - "traefik.basic.frontend.rule=Host:${TRAEFIK_DOMAIN}"
            - "traefik.basic.port=80"
            - "traefik.basic.protocol=http"
    mysql:
        image: mariadb:10.4
        restart: unless-stopped
        container_name: "${PROJECT}-mysql"
        networks:
            - web
            - backend
        environment:
            MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
            MYSQL_USER: ${MYSQL_USER}
            MYSQL_PASSWORD: ${MYSQL_PASSWORD}
            MYSQL_DATABASE: ${MYSQL_DATABASE}
        volumes:
            - dbdata:/var/lib/mysql
    redis:
        image: redis:latest
        restart: unless-stopped
        container_name: "${PROJECT}-redis"
        volumes:
            - magento-redis:/data
        networks:
            - backend
#    cron:
#        image: copex/cron:ui
#        restart: unless-stopped
#        container_name: "${PROJECT}-cron"
#        networks:
#          - backend
#          - web
#        environment:
#          MAGENTO_VERSION: ${MAGENTO_VERSION}
#          MAGENTO_ROOT: ${MAGENTO_ROOT}
#          PHP_VERSION: ${PHP_VERSION}
#        depends_on:
#            - redis
#            - search
    search:
        build: docker/search
        restart: unless-stopped
        container_name: "${PROJECT}-search"
        environment:
            'discovery.type': 'single-node'
            'ES_JAVA_OPTS': '-Xms512m -Xmx512m'
        networks:
          - backend
        volumes:
            - magento-search:/usr/share/elasticsearch/data
