<?php

namespace Deployer;

/**
 * Hosts
 */
host('production')
    ->setHostname(getenv('CI') === 'true' ? 'serv49838915.secure-node.at' : 'gfp')
    ->setPort('12488')
    ->setConfigFile('~/.ssh/config')
    ->setRemoteUser('updatgbu')
    ->setDeployPath('/home/<USER>/www.update.gfp-international.com/html')
    ->set('docker',false)
    ->setForwardAgent(true)
    ->setLabels([
        'env'  => ENV_PRODUCTION,
    ])
    ->set('branch', 'master')
    ->set('static_content_locales', 'de_DE en_US');
