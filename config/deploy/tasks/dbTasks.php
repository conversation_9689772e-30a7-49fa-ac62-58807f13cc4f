<?php

namespace Deployer;



task('db:download', function () {
    $strippedTables = '@stripped @trade amasty_feed_google_taxonomy amasty_file_report amasty_fpc_flushes_log amasty_fpc_reports amasty_geip_* amasty_orderachrive_sales_order_grid_archive catalogsearch_fulltext_* catgento_activity_* cleverreach_* cobby_* elasticsuite_tracker_log_event experius_* xemail_sentemail xtento_orderexport_log xtento_productexport_log';
    $docker = get('docker');
    if($docker){
        run('docker exec -u $(id -u ${USER}):www-data {{application}}-app {{release_or_current_path}}/bin/n98-magerun2 --root-dir={{release_or_current_path}} --skip-root-check db:dump --strip="' . $strippedTables . '" --no-tablespaces	-c gzip /backups/database/database_bkp.sql.gz');
        download("/var/backups/database/database_bkp.sql.gz", "./db/import/database_bkp_" . date('Y.m.d-H:i:s') . ".sql.gz");
        run("rm /var/backups/database/database_bkp.sql.gz");
    }
    else {
        run ('cd {{release_or_current_path}} && ./bin/n98-magerun2 db:dump --strip="' . $strippedTables . '" --compression="gzip" /home/<USER>/database_bkp.sql.gz');
        download("/home/<USER>/database_bkp.sql.gz", "./db/import/database_bkp_" . date('Y.m.d-H:i:s') . ".sql.gz");
        run("rm /home/<USER>/database_bkp.sql.gz");
    }
})->desc('Connects to remote host, take a dump from the database via n98-magerun and download to local db/import folder');