<?php

namespace Deployer;

use Deployer\Task\Context;

require 'recipe/magento2.php';
//require __DIR__ . '/vendor/deployer/deployer/contrib/rsync.php';
const ENV_PRODUCTION = 'production';
const ENV_STAGING = 'staging';
/**
 * Settings
 */
set('docker','false');
set('application', 'gfp');
set('repository', '*******************:copex/gfp.git');
set('deployment_root_path', '/var/www');
set('keep_releases', 2);
set('php_version', "8.1");
set('hyva_theme',['Gfp/int']);
set('shared_files', [
    'var/.maintenance.ip',
    'var/.setup_cronjob_status',
    'var/.update_cronjob_status',
    'pub/googlea6199a39444aa78f.html',
    'pub/pinterest-f04c9.html'
]);
set('shared_dirs', [
    'var/composer_home',
    'var/log',
    'var/export',
    'var/report',
    'var/import',
    'var/amasty',
    'var/import_history',
    'var/session',
    'var/geoip',
    'var/importexport',
    'var/backups',
    'var/tmp',
    'pub/sitemap',
    'pub/errors',
    'pub/media'
]);
set('bin/php', function(){
    $docker = get('docker');
    if ($docker) {
        return 'docker exec -u $(id -u ${USER}):www-data {{application}}-app php -d memory_limit=-1';
    }
    if (currentHost()->hasOwn('php_version')) {
        return '/usr/bin/php{{php_version}}';
    }
    return which('php');
});

//deploy targets
include __DIR__ . '/deploy/production.php';
include __DIR__ . '/deploy/staging.php';
//custom tasks
include __DIR__ . '/deploy/tasks/dbTasks.php';
include __DIR__ . '/deploy/tasks/miscTasks.php';

// in case of rsync deployment this have to be enabled
//task('deploy:update_code')->disable();
//after('deploy:update_code', 'rsync');

//set php executable to docker container

//composer command
set('bin/composer', function () {
    $docker = get('docker');
    if ($docker) {
        return 'docker run --rm  -w="{{release_or_current_path}}" -v {{deployment_root_path}}:{{deployment_root_path}} -v /var/.composer:/.composer -u $(id -u ${USER}):www-data -e COMPOSER_MEMORY_LIMIT=-1 copex/php:{{php_version}} composer';
    }
    return '/home/<USER>/composer.phar';
});

//define n98-magerun command
set('bin/n98-magerun2', function () {
    $docker = get('docker');
    if ($docker) {
        return 'docker run --rm  -w="{{release_or_current_path}}" -v {{deployment_root_path}}:{{deployment_root_path}} -u $(id -u ${USER}):www-data -e COMPOSER_MEMORY_LIMIT=-1 copex/php:{{php_version}} ./bin/n98-magerun2';
    }
    return 'bin/n98-magerun2';
});

// which languages should get deployed
// set('static_content_locales', 'de_DE en_US fr_FR');
set('static_content_locales', 'de_DE en_US');
set('static_content_jobs', '4');

// themes to deploy
set('magento_themes', array_merge(get('hyva_theme') ?? [],[ 'Gfp/international', 'Magento/backend']));

//copy env.php file
task('copy_config', function () {
    $env = get('labels')['env'];
    if ($env == ENV_PRODUCTION) {
        upload('config/etc/env.prod.php', '{{release_or_current_path}}/app/etc/env.php');
    }
    if ($env == ENV_STAGING) {
        upload('config/etc/env.staging.php', '{{release_or_current_path}}/app/etc/env.php');
    }
});
//change to copex:www-data
task('change_owner', function () {
    $env = get('labels')['env'];
    if ($env == ENV_STAGING) {
        run("cd {{release_or_current_path}} && chown -R copex:www-data *");
    }
});

task('build_theme', function(){
    $hyvaThemes = get('labels')['hyvaTheme'] ?? get('hyva_theme') ?? false;
    if($hyvaThemes){
        $env = get('labels')['env'];
        if(get('docker')){
            foreach ($hyvaThemes as $theme) {
                run('docker run --rm -u $(id -u ${USER}):www-data -w="{{release_or_current_path}}/app/design/frontend/' . $theme .
                    '/web/tailwind" -v {{deployment_root_path}}/{{application}}/' . $env . ':{{deployment_root_path}}/{{application}}/' . $env .
                    ' node:14-alpine npm ci');

                run('docker run --rm -u $(id -u ${USER}):www-data -w="{{release_or_current_path}}/app/design/frontend/' . $theme .
                    '/web/tailwind" -v {{deployment_root_path}}/{{application}}/' . $env . ':{{deployment_root_path}}/{{application}}/' . $env .
                    ' node:14-alpine npm run build-prod');
            }
        }else {
            foreach ($hyvaThemes as $theme) {
                run('npm --prefix {{release_or_current_path}}/app/design/frontend/' . $theme . '/web/tailwind ci');
                run('npm --prefix {{release_or_current_path}}/app/design/frontend/' . $theme . '/web/tailwind run build-prod');
            }
        }
    }
});

task('restart_webserver', function () {
    $docker = get('docker');
    if ($docker) {
        run("cd {{deploy_path}}/docker && docker-compose restart app");
    }
    else {
        run("sudo fpm/reload");
        run("sudo apache/reload");
    }
});


task('symlink:htaccess', function () {
    run("ln -sf {{deploy_path}}/shared/pub/.htaccess {{release_or_current_path}}/pub/.htaccess");
    run("ln -sf {{deploy_path}}/shared/.htaccess {{release_or_current_path}}/.htaccess");
});

task('symlink:trustedshops', function () {
    run("mkdir -p {{release_or_current_path}}/vendor/trustedshops/trustedshops/Cache/");
    run("ln -sf {{deploy_path}}/shared/vendor/trustedshops/trustedshops/Cache/trustedshops_shops.cache {{release_or_current_path}}/vendor/trustedshops/trustedshops/Cache/trustedshops_shops.cache");
});

task('magepack:bundle', function (){
    run("cd {{release_or_current_path}} && magepack bundle");
});

after('deploy:update_code', 'change_owner');
after('deploy:failed', 'deploy:unlock');
before('magento:deploy:assets', 'build_theme');
before('magento:compile', 'copy_config');
after('deploy:symlink', 'restart_webserver');
// after("magento:build", "magepack:bundle");
before("deploy:publish", "symlink:htaccess");
after("symlink:htaccess", "symlink:trustedshops");
