<?php

return array(

	'Select' => '選択',
	'Erase' => '消去',
	'Open' => '開く',
	'Confirm_del' => 'このファイルを削除しますか?',
	'All' => '全て',
	'Files' => 'ファイル',
	'Images' => 'イメージ',
	'Archives' => 'アーカイブ',
	'Error_Upload' => 'アップロード可能な最大サイズを超えています。',
	'Error_extension' => '拡張子が許可されていません。',
	'Upload_file' => 'アップロード',
	'Filters' => 'フィルタ',
	'Videos' => 'ビデオ',
	'Music' => '音楽',
	'New_Folder' => '新規フォルダ',
	'Folder_Created' => 'フォルダを作成しました',
	'Existing_Folder' => '存在するフォルダ',
	'Confirm_Folder_del' => 'フォルダとフォルダの中身を削除しますか?',
	'Return_Files_List' => 'ファイルの一覧に戻る',
	'Preview' => 'プレビュー',
	'Download' => 'ダウンロード',
	'Insert_Folder_Name' => 'フォルダ名の追加',
	'Root' => 'ルート',
	'Rename' => '名称変更',
	'Back' => '戻る',
	'View' => 'ビュー',
	'View_list' => '一覧表示',
	'View_columns_list' => 'カラム表示',
	'View_boxes' => 'ボックス表示',
	'Toolbar' => 'ツールバー',
	'Actions' => 'アクション',
	'Rename_existing_file' => 'このファイルはすでに存在しています。',
	'Rename_existing_folder' => 'このフォルダはすでに存在しています。',
	'Empty_name' => '名前が空です',
	'Text_filter' => 'テキストフィルタ',
	'Swipe_help' => 'ファイル・フォルダ名をスワイプしてオプションを表示する',
	'Upload_base' => '標準アップロード',
	'Upload_url' => 'URL',
	'Upload_java' => 'Java アップロード (大きいファイルサイズ)',
	'Upload_java_help' => "Java　Applet がロード出来ない場合１．<a href='http://java.com/en/download/'>[download link]</a> インストールしてください。　2．ファイアウォールでブロックされていないことを確認してください。",
	'Upload_base_help' => "ファイルをドラッグ＆ドロップ（モダンブラウザのみ）または上の領域をクリックしてファイルを選択してください。アップロードが完了したら「ファイルの一覧に戻る」ボタンをクリックしてください。",
	'Type_dir' => 'ディレクトリ',
	'Type' => '種類',
	'Dimension' => '画像サイズ',
	'Size' => 'サイズ',
	'Date' => '日付',
	'Filename' => 'ファイル名',
	'Operations' => '操作',
	'Date_type' => 'Y/m/d',
	'OK' => 'OK',
	'Cancel' => 'キャンセル',
	'Sorting' => 'ソート',
	'Show_url' => 'URL表示',
	'Extract' => 'ここに解凍',
	'File_info' => 'ファイル情報',
	'Edit_image' => '画像編集',
	'Duplicate' => '複製',
	'Folders' => 'フォルダ',
	'Copy' => 'コピー',
	'Cut' => 'カット',
	'Paste' => 'ペースト',
	'CB' => 'クリップボード', // clipboard
	'Paste_Here' => 'このディレクトリにペーストする',
	'Paste_Confirm' => 'このディレクトリにペーストしますか？既存のファイル/フォルダは上書きされます。',
	'Paste_Failed' => 'ペーストできませんでした。',
	'Clear_Clipboard' => 'クリップボードの消去',
	'Clear_Clipboard_Confirm' => 'クリップボード内のデータを消去しますか?',
	'Files_ON_Clipboard' => 'クリップボードにファイルがあります。',
	'Copy_Cut_Size_Limit' => '選択したファイルが/フォルダを％sするには大きすぎます。 リミット: %d MB/操作', // %s = cut or copy
	'Copy_Cut_Count_Limit' => '選択したファイルが/フォルダを％sするには大きすぎます。 リミット: %d ファイル/操作', // %s = cut or copy
	'Copy_Cut_Not_Allowed' => 'ファイルを %s する許可がありません。', // %s(1) = cut or copy, %s(2) = files or folders
	'Aviary_No_Save' => 'イメージを保存できませんでした',
	'Zip_No_Extract' => '解凍できませんでした。ファイルが破損している可能性があります。',
	'Zip_Invalid' => '拡張子がサポートされてません。有効：　zip, gz, tar.',
	'Dir_No_Write' => '選択したディレクトリに書き込み権限がありません',
	'Function_Disabled' => '%s はサーバによって無効にされています。', // %s = cut or copy
	'File_Permission' => 'ファイルの権限',
	'File_Permission_Not_Allowed' => '%s の権限変更は許可されていません。', // %s = files or folders
	'File_Permission_Recursive' => '内包するファイルに適用しますか?',
	'File_Permission_Wrong_Mode' => "供給された権限が正しくありません。",
	'User' => 'ユーザー',
	'Group' => 'グループ',
	'Yes' => 'はい',
	'No' => 'いいえ',
	'Lang_Not_Found' => '言語がみつかりませんでした。',
	'Lang_Change' => '言語の変更',
	'File_Not_Found' => '言語ファイルがみつかりませんでした。',
	'File_Open_Edit_Not_Allowed' => 'このファイルを%sことができませんでした。', // %s = open or edit
	'Edit' => '編集する',
	'Edit_File' => "ファイルを編集",
	'File_Save_OK' => "ファイルの保存が完了しました。",
	'File_Save_Error' => "ファイルの保存時にエラーが発生しました。",
	'New_File' => '新規ファイル',
	'No_Extension' => 'ファイルの拡張子を指定してください。',
	'Valid_Extensions' => '有効な拡張子: %s', // %s = txt,log etc.
	'Upload_message' => "Drop file here to upload",

	'SERVER ERROR' => "SERVER ERROR",
	'forbiden' => "Forbiden",
	'wrong path' => "Wrong path",
	'wrong name' => "Wrong name",
	'wrong extension' => "Wrong extension",
	'wrong option' => "Wrong option",
	'wrong data' => "Wrong data",
	'wrong action' => "Wrong action",
	'wrong sub-action' => "Wrong sub-actio",
	'no action passed' => "No action passed",
	'no path' => "No path",
	'no file' => "No file",
	'view type number missing' => "View type number missing",
	'Not enought Memory' => "Not enought Memory",
	'max_size_reached' => "Your image folder has reach its maximale size of %d MB.", //%d = max overall size
	'B' => "B",
	'KB' => "KB",
	'MB' => "MB",
	'GB' => "GB",
	'TB' => "TB",
	'total size' => "Total size",
);
