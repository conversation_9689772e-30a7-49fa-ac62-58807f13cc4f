<?php

declare(strict_types=1);

use CopeX\Swiper\ViewModel\Swiper;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Catalog\Block\Product\View\Gallery;
use Magento\Catalog\Helper\Image;

/** @var Gallery $block */

$images = $block->getGalleryImages()->getItems();

$mainImage = current(array_filter($images, function ($img) use ($block) {
    return $block->isMainImage($img);
}));

if (!empty($images) && empty($mainImage)) {
    $mainImage = current($images);
}

/** @var Image $helper */
$helper = $block->getData('imageHelper');
$mainImageData = $mainImage ?
    $mainImage->getData('medium_image_url') :
    $helper->getDefaultPlaceholderUrl('image');

$productName = $block->getProduct()->getName();

/** @var ViewModelRegistry $viewModels */
/** @var Swiper $swiper */

$swiper = $viewModels->require(Swiper::class);

$swiperBlock = $swiper->getSwiper();

$swiperBlock->addData([
    'pagination'=> false,
    'thumbs'=> true,
    'lightbox'=> true,
    'main_arrow_color'=> '#fff',
    'width' => "w-full",
    'load_main_lazy' => false
]);
$photoGallery = $block->getLayout()->getBlock("photogallery");
$uniqueArray = [$mainImage];
foreach($images as $image){
    if(!in_array($image, $uniqueArray)){
        $uniqueArray [] = $image;
    }
}
$images = array_filter($uniqueArray);
$galleryImages = array_map(function($item) use ($mainImage) {
    return [
        "url" => $item->getData('medium_image_url'),
        "label" => ($item->getLabel() ?: $this->getProduct()->getName()),
        "thumb" => $item->getData('small_image_url'),
        "lightbox" =>  $item->getData('large_image_url'),
        "main" => $item->getFile() == $mainImage->getFile()
    ];
}, $images);

$swiperBlock->setGalleryImages($galleryImages)->setInitScript('gallerySwiperInit()'); ?>
<meta itemprop="image" content="<?= $mainImageData ?>">
<script>
    function gallerySwiperInit() {
        "use strict";
        return {
            appendOnReceiveImages: <?= $block->getVar(
                'gallery_switch_strategy',
                'Magento_ConfigurableProduct'
            ) === 'append' ? 'true' : 'false' ?>,
            receivedImages: [],
            lightboxInit: false,
            openLightbox(){
                if (this.lightbox) {
                    this.lightbox.init();
                }
                let self = this;
                if (!this.lightboxInit) { //Fix for first view
                    this.lightbox.on('resize',function(){
                        if (self.getCurrentSlideCount(self.swiper) !== self.getCurrentSlideCount(self.lightbox)) {
                            self.updateReceivedImages(self.lightbox, "full");
                            if (self.lightbox) {
                                self.lightbox.slideToLoop(self.swiper.realIndex,0,false);
                                self.lightbox.lazy.load();
                            }
                        }
                    });
                    this.lightboxInit = true;
                }
                this.updateReceivedImages(this.lightbox, "full");
                this.lightboxOpen = true;
                if (this.lightbox) {
                    this.lightbox.slideToLoop(this.swiper.realIndex,0,false);
                }
            },
            receiveImages (images) {
                if(images.length === 0) return;
                this.receivedImages = images;
                this.updateReceivedImages(this.swiper, "img");
                this.updateReceivedImages(this.thumb, "thumb");
                this.swiper.slideToLoop(this.prependCount,0,false);
                this.thumbnail && this.thumbnail.slideToLoop(this.prependCount,0,false);
            },
            updateReceivedImages(slides, type){
                let template = this.getSliderTemplate(slides);
                if(template ){
                    if (!this.appendOnReceiveImages) {
                        slides.removeAllSlides();
                    }
                    for(let count = 0; count < slides.receivedImageCount; count++){ //Remove previous images
                        slides.removeSlide(this.prependCount);
                    }
                    for(let image of this.receivedImages){
                        let slide = this.createSlide(template,image,type);
                        slides.addSlide(this.prependCount,slide);
                    }
                    slides.receivedImageCount = this.receivedImages.length;
                    slides.update();
                }
            },
            eventListeners: {
                ['@keydown.window.escape']() {
                    this.closeLightbox();
                },
                ['@update-gallery.window'](event) {
                    this.receiveImages(event.detail);
                }
            }
        };
    }
</script>
<style>
    @media screen and (max-width: 767px){
        #gallery .swiper-thumbnail .swiper-wrapper { max-height: 110px;}
        #gallery .swiper-thumbnail .swiper-wrapper .swiper-slide {width: auto;}
    }</style>
<div id="gallery" class="order-1 relative">
    <?= $swiperBlock->toHtml(); ?>
    <?php
    if($photoGallery && count($photoGallery->getGalleryForCatorPro())) : ?>
        <div class="relative font-medium text-gfpyellow hover:underline focus:underline pl-1" onclick="scrollToCustomerGallery()"><?= __("Show customer gallery")?></div>
        <script>
            function scrollToCustomerGallery(){
                if(document.getElementById('customer_gallery')){
                    let customerGalleryTab = document.querySelector('.product-info-details-tab-details');
                    if(customerGalleryTab && customerGalleryTab.scrollHeight === 0){
                        window.dispatchEvent(new CustomEvent("openproduct-info-details-tab-details"));
                    }
                    setTimeout(() => {
                        document.getElementById('customer_gallery').scrollIntoView({behavior: 'smooth'});
                    }, 100);
                }
            }
        </script>

    <?php endif; ?>
</div>