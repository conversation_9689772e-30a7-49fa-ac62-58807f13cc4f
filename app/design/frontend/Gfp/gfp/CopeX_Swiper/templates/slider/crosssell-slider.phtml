<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductCompare;
use Hyva\Theme\ViewModel\ProductList;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\Slider;
use Hyva\Theme\ViewModel\Store;
use Hyva\Theme\ViewModel\Wishlist;
use Magento\Catalog\Model\Product\Visibility as ProductVisibility;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Catalog\Block\Product\ReviewRendererInterface as ProductReviewRenderer;


/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var Slider $sliderViewModel */
/** @var ProductList $productListViewModel */
/** @var Wishlist $wishlistViewModel */
/** @var ProductCompare $compareViewModel */
/** @var Store $storeViewModel */
/** @var ProductListItem $productListItemViewModel */
$productListItemViewModel = $viewModels->require(ProductListItem::class);

$sliderViewModel      = $viewModels->require(Slider::class);
$productListViewModel = $viewModels->require(ProductList::class);
$productViewModel     = $viewModels->require(ProductPage::class);
$wishlistViewModel    = $viewModels->require(Wishlist::class);
$compareViewModel     = $viewModels->require(ProductCompare::class);
$storeViewModel       = $viewModels->require(Store::class);

$categoryIds       = $block->getData('category_ids') ?: '';
$pageSize          = $block->getData('page_size') ?: 16;
$priceFrom         = $block->getData('price_from');
$priceTo           = $block->getData('price_to');
$sortAttribute     = $block->getData('sort_attribute') ?: '';
$sortDirection     = $block->getData('sort_direction') ?: 'ASC';
$title             = $block->getData('title') ?: '';
$hideDetails       = $block->getData('hide_details') ?? false;
$hideRatingSummary = $block->getData('hide_rating_summary') ?? false;
$type              = $block->getData('type');
$skusFilter        = $block->getData('product_skus') ? explode(',', $block->getData('product_skus')) : [];
$additionalFilters = (array) $block->getData('additional_filters');
$containerTemplate = $block->getData('container_template')
    ?? 'Magento_Catalog::product/slider/product-slider-container.phtml';


if ($categoryIds) {
    $productListViewModel->addFilter('category_id', $categoryIds, 'in');
}

if ($priceFrom) {
    $productListViewModel->addFilter('price', $priceFrom, 'gteq');
}
if ($priceTo) {
    $productListViewModel->addFilter('price', $priceTo, 'lteq');
}

if ($hideRatingSummary) {
    $productListViewModel->excludeReviewSummary();
}

if ($skusFilter) {
    $productListViewModel->addFilter('sku', array_map('trim', $skusFilter), 'in');
}

foreach ($additionalFilters as $filter) {
    $productListViewModel->addFilter($filter['field'], $filter['value'], $filter['conditionType'] ?? 'eq');
}

$productListViewModel->setPageSize($pageSize);
$productListViewModel->addFilter('website_id', $storeViewModel->getWebsiteId());
$productListViewModel->addFilter('visibility', [
    ProductVisibility::VISIBILITY_IN_CATALOG,
    ProductVisibility::VISIBILITY_IN_SEARCH,
    ProductVisibility::VISIBILITY_BOTH,
], 'in');
if ($sortAttribute) {
    $sortDirection === 'ASC'
        ? $productListViewModel->addAscendingSortOrder($sortAttribute)
        : $productListViewModel->addDescendingSortOrder($sortAttribute);
}

if (in_array($type, ['related', 'upsell', 'crosssell'], true)) {
    $currentProduct = $block->getProduct();
    if(!$currentProduct){
        $currentProduct = $productViewModel->getProduct();
    }
    $items = $currentProduct->getCrossSellProductCollection();
    $items->addAttributeToSelect('name');
    $items->addAttributeToSelect('price');
    $items->addAttributeToSelect('special_price');
    $items->addAttributeToFilter('status', 1);
    $items->addAttributeToSelect('small_image');

} else {
    $items = $productListViewModel->getItems();
}
if (is_object($items) && $items instanceof Iterator) {
    $items = iterator_to_array($items);
}
if (!$itemCount = count($items)) {
    return '';
}

$pagination = $block->getPagination() ?? false;
$headline = $block->getHeadline() ?? $block->getTitle();
$lightbox = $block->getLightbox() ?? true;
$width = $block->getWidth() ?? "w-full";
$swiperWrapperClass = $block->getSwiperWrapperClass() ?? "swiper-wrapper items-stretch";
$swiperSlideClass = $block->getSwiperSlideClass() ?? "swiper-slide px-2 pb-2 flex flex-col w-full md:w-1/2 lg:w-1/3";
$swiperActiveClass = $block->getSwiperActiveClass() ?? "swiper-slide-visible swiper-slide-active";
$sliderId = time() . uniqid();
$mainArrowColor = $block->getMainArrowColor() ?? 'currentColor';
$slidesPerView = $block->getSlidesPerView() ?: 1;
$itemTemplate      = $block->getData('item_template') ?? 'Magento_Catalog::product/list/item.phtml';


$block->setLightbox(false);

$breakpointConfig = '{ 768: { slidesPerView: 2.6}, 1200: {slidesPerView: 3.6} }, slidesPerView: 1.6';

switch ($itemCount) {
    case 1:
        $breakpointConfig = '{ 768: { slidesPerView: 2.1}, 1200: {slidesPerView: 3} }, slidesPerView: 1.1';
        break;
    case 2:
        $breakpointConfig = '{ 768: { slidesPerView: 2.1}, 1200: {slidesPerView: 3} }, slidesPerView: 1.6';
        break;
    case 3:
        $breakpointConfig = '{ 768: { slidesPerView: 2.6}, 1200: {slidesPerView: 3.2} }, slidesPerView: 1.6';
        break;
}

$block->setExtraConfig("{
    autoHeight: false,
    breakpoints: " . $breakpointConfig . ",
    loop: false,
    loopedSlides: 0,
    rewind: true,
    speed: 100,
    watchSlidesProgress: true,
    watchOverflow: true
}");


$viewMode = 'grid';
$imageDisplayArea = 'category_page_grid';
$showDescription = false;
$sliderItemRenderer = $block->getChildBlock('slider.item.template') ?:
    $block->getLayout()->getBlock('product_list_item') ?:
        $block->getLayout()->createBlock(Template::class);

$sliderItemRenderer->setTemplate($itemTemplate);
$hideRatingSummary = (bool) $block->getData('hide_rating_summary');
$hideDetails       = (bool) $block->getData('hide_details');

$sliderItemRenderer->setData('hide_details', $hideDetails);
$sliderItemRenderer->setData('hide_rating_summary', $hideRatingSummary);

?>

<div class="min-w-0 -mr-2 md:-mr-4">

    <?php if ($headline): ?>
    <h3 class="mx-0 mb-5 mt-12 text-xl font-semibold text-left text-gfpgreen uppercase"><?= $headline ?></h3>
    <?php endif; ?>

    <div x-data="initSwiper<?= $sliderId ?>()"
         x-init="init()"
         x-defer="intersect"
         x-bind="eventListeners"
         class="bg-white">
        <div class="swiper-main relative <?= $width ?> mx-auto swiper">
            <div class="mx-6 my-2 overflow-hidden pb-10 mr-0">
                <?php if($itemCount > $slidesPerView): ?>
                    <div class="absolute inset-y-0 -left-4 z-10 flex items-center">
                        <button aria-label="prev"
                                class="crosssell-slider-prev flex justify-center items-center ml-7 w-10 h-10 focus:outline-none bg-white shadow rounded-md">
                            <svg viewBox="0 0 20 20" fill="<?= $mainArrowColor ?>" class="chevron-left w-20 h-20">
                                <use href="#chevron-left"></use>
                            </svg>
                        </button>
                    </div>
                <?php endif; ?>
                <div class="swiper-main-container swiper-container" x-ref="container_<?= $sliderId ?>">
                    <div class="<?= $swiperWrapperClass ?>">
                        <?php $itemCounter = 0; ?>
                        <?php foreach ($items as $id => $product) : ?>
                            <?php if ($product->getIsSalable()) : ?>
                                <div class="<?= $swiperSlideClass ?> <?= $itemCounter++ < $slidesPerView ? $swiperActiveClass : ""?>" @click="openLightbox" style="height: auto;">
                                    <?= /** @noEscape */
                                    $productListItemViewModel->getItemHtmlWithRenderer(
                                        $sliderItemRenderer,
                                        $product,
                                        $block,
                                        $viewMode,
                                        ProductReviewRenderer::SHORT_VIEW,
                                        $imageDisplayArea,
                                        $showDescription
                                    ) ?>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                    <?php if($pagination) : ?>
                        <div class="swiper-pagination md:mb-0"></div>
                    <?php endif; ?>
                    <?php if($block->getAfterMain()) : ?>
                        <?= $block->getAfterMain(); ?>
                    <?php endif; ?>
                </div>
                <?php if($itemCount > $slidesPerView): ?>
                    <div class="absolute inset-y-0 -right-8 mr-4 z-10 flex items-center">
                        <button aria-label="next"
                                class="crosssell-slider-next flex justify-center items-center mr-7 w-10 h-10 focus:outline-none bg-white shadow rounded-md">
                            <svg viewBox="0 0 20 20" fill="<?= $mainArrowColor ?>" class="chevron-right w-20 h-20">
                                <use href="#chevron-right"></use>
                            </svg>
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <script>
        function initSwiper<?= $sliderId ?>() {
            return Object.assign( {
                swiper: null,
                thumb: null,
                baseConfig: {
                    loop: <?= $block->getLoop() ?: "true" ?>,
                    spaceBetween:  5,
                    freeMode: false,
                    lazy: {loadPrevNext: true, checkInView: true, loadOnTransitionStart: true}
                },
                init() {
                    swiperInit.init( () => {
                        this.swiper = new Swiper(this.$refs.container_<?= $sliderId ?>, Object.assign({},this.baseConfig,
                            {
                                slidesPerView:  <?= $slidesPerView ?: "1" ?>,
                                pagination: { el:  '.swiper-main .swiper-pagination', dynamicBullets: true, dynamicMainBullets: 10},
                                enabled: <?= $itemCount > $slidesPerView ? "true" : "false";?>,
                                navigation: {
                                    nextEl: '.crosssell-slider-next',
                                    prevEl: '.crosssell-slider-prev',
                                },
                            },
                            <?= $block->getExtraConfig() ?: "{}"?>
                        ));
                        <?php if($lightbox): ?>
                        this.lightbox = new Swiper(this.$refs.container_<?= $sliderId ?>_lightbox, Object.assign({},this.baseConfig,
                            {
                                slidesPerView: <?= $slidesPerView ?: "1" ?>,
                                init: false,
                                pagination: { el:  '.swiper-lightbox .swiper-pagination', dynamicBullets: true, dynamicMainBullets: 10},
                                enabled: <?= $itemCount > $slidesPerView ? "true" : "false";?>,
                            },
                            <?= $block->getLightboxExtraConfig() ?: "{}"?>
                        ));
                        <?php endif; ?>
                    });
                },
                mainNext(){
                    this.swiper.slideNext();
                },
                mainPrev(){
                    this.swiper.slidePrev();
                },
                lightboxNext(){
                    this.lightbox.slideNext();
                },
                lightboxPrev(){
                    this.lightbox.slidePrev();
                },
                openLightbox(){
                    this.lightboxOpen = true;
                    if (this.lightbox) {
                        this.lightbox.init();
                        this.lightbox.slideTo(this.swiper.activeIndex);
                    }
                },
                closeLightbox(){
                    this.lightboxOpen = false;
                },
                eventListeners: {
                    ['@keydown.window.escape']() {
                        this.closeLightbox();
                    }
                }
            }, <?= $block->getInitScript() ?: "{}"?>);
        }
    </script>
</div>

