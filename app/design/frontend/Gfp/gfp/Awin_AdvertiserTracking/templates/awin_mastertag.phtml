<?php
/**
 * This file is part of the Awin AdvertiserTracking module
 */

/** @var Awin\AdvertiserTracking\Block\AllPageTemplate $block */
if ($block->isConfigured()) :?>
<img src="<?= $block->getImgUrl() ?>" style="display: none;width:0px;height:0px;" fetchpriority="low">
<script defer>
    window.addEventListener('init-external-scripts', () => {
        window.setTimeout(() => {
            const script = document.createElement('script');
            script.type = 'text/javascript'; script.defer = true;
            script.src = "https://www.dwin1.com/<?= $block->getAdvertiserId() ?>.js";
            const scriptNode = document.getElementsByTagName('script')[0];
            scriptNode.parentNode.insertBefore(script, scriptNode);
        }, 20);
    });
</script>
<?php endif; ?>
    