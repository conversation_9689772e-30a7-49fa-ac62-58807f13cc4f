<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */


declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Store as StoreViewModel;
use Magento\Cookie\Block\Html\Notices;
use Magento\Framework\Escaper;
use Magento\Cookie\Helper\Cookie;

/** @var Notices $block */
/** @var Escaper $escaper */
/** @var Cookie $cookieHelper */
/** @var ViewModelRegistry $viewModels */
/** @var StoreViewModel $storeViewModel */

$storeViewModel = $viewModels->require(StoreViewModel::class);

?>
<?php

$cookieHelper = $block->getData('cookieHelper');
if ($cookieHelper->isCookieRestrictionModeEnabled()): ?>

<script>
    function initCookieBanner() {
        const isUserAllowedSaveCookieName = '<?= /* @noEscape */  Cookie::IS_USER_ALLOWED_SAVE_COOKIE ?>';
        const currentWebsiteId = <?= (int) $storeViewModel->getStore()->getWebsiteId() ?>;

        const isAllowedSaveCookie = () => {
            const allowedCookies = hyva.getCookie(isUserAllowedSaveCookieName);
            const allowedCookieWebsites = allowedCookies
                ? JSON.parse(unescape(allowedCookies))
                : [];

            return allowedCookieWebsites[currentWebsiteId] !== undefined
                ? !! allowedCookieWebsites[currentWebsiteId]
                : false;
        };

        return {
            showCookieBanner: false,
            cookieName: isUserAllowedSaveCookieName,
            cookieValue: '<?= /* @noEscape */ $cookieHelper->getAcceptedSaveCookiesWebsiteIds() ?>',
            cookieLifetime: '<?= /* @noEscape */ $cookieHelper->getCookieRestrictionLifetime() ?>',
            noCookiesUrl: '<?= $escaper->escapeJs($block->getUrl('cookie/index/noCookies')) ?>',

            checkAcceptCookies() {
                window.loadOnMove(
                    ()=> {
                        this.showCookieBanner = ! isAllowedSaveCookie();
                    }
                );
            },
            setAcceptCookies() {
                const cookieExpires = this.cookieLifetime /  60 / 60 / 24;
                hyva.setCookie(this.cookieName, this.cookieValue, cookieExpires);
                if (!hyva.getCookie(this.cookieName)) {
                    window.location.href = this.noCookiesUrl;
                } else {
                    window.dispatchEvent(new CustomEvent('user-allowed-save-cookie'));
                }
            },
            setDeclineCookies(){
                const cookieExpires = this.cookieLifetime /  60 / 60 / 24;
                hyva.setCookie(this.cookieName, "{}", cookieExpires);
            }
        }
    }
</script>

<section id="notice-cookie-block"
         x-data="initCookieBanner();"
         @private-content-loaded.window="checkAcceptCookies()"
>
    <template x-if="showCookieBanner">
        <div role="dialog"
             aria-modal="true"
        >
            <div class="p-6 fixed flex flex-col h-screen w-screen justify-center items-center inset-0 bg-black bg-opacity-60 z-50 transition">
                <div class="p-6 flex max-w-screen-lg bg-container-darker border-t-2 border-container-darker">
                    <button @click="setAcceptCookies(); showCookieBanner = false" aria-label="Close panel"
                            class="absolute right-0 top-0 p-4">
                        <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                  stroke-width="2" d="M6 18L18 6M6 6l12 12">
                            </path>
                        </svg>
                    </button>

                    <div class="p-2 pr-4">
                        <p class="mb-1">
                            <span class="font-semibold">
                                <?= $escaper->escapeHtml(__('We use cookies to make your experience better.')) ?>
                            </span>
                        </p>
                        <p class="mb-1">
                            <span>
                                <?= $escaper->escapeHtml(__( // phpcs:disable Generic.Files.LineLength.TooLong
                                    'To comply with the new e-Privacy directive, we need to ask for your consent to set the cookies.'
                                )) // phpcs:enable ?>
                            </span>
                        </p>
                        <p class="mb-4">
                            <a href="<?= $escaper->escapeUrl($block->getPrivacyPolicyLink()) ?>"
                               class="underline">
                                <?= $escaper->escapeHtml(__('Read our policy')) ?>
                            </a>
                        </p>
                        <div class="my-2">
                            <button @click="setAcceptCookies(); showCookieBanner = false"
                                    id="btn-cookie-allow"
                                    class="btn btn-primary"
                            >
                                <?= $escaper->escapeHtml(__('Allow Cookies')) ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>
</section>
<?php endif; ?>
