<?xml version="1.0"?>
<page layout="1column" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <referenceBlock name="product.info">
        <block class="Magento\Framework\View\Element\Template"
               name="product.info.shippinginfo"
               template="Magento_Catalog::product/view/shipping_info.phtml">
            <block class="Magento\Cms\Block\Block" name="product.info.shippinginfo.block">
                <arguments>
                    <argument name="block_id" xsi:type="string">product-price-shipping-info</argument>
                </arguments>
            </block>
        </block>
    </referenceBlock>
</page>
