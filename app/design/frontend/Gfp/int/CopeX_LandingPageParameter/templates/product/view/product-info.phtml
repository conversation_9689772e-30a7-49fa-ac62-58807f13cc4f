<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductAttributes;
use Hyva\Theme\ViewModel\ProductPage;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\ViewModel\HeroiconsOutline;


/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var ProductAttributes $attributesViewModel */
$attributesViewModel = $viewModels->require(ProductAttributes::class);
/** @var Product $product */
$product = $productViewModel->getProduct();
$productShortName = $product->getPlpProduktNameShort() ?: $product->getName();
/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);


?>
<div class="product-info order-2 md:pl-5 lg:pl-2 w-1/2 md:mb-0">
    <h1 class="pb-2 m-0 text-base sm:text-2xl leading-tight lg:leading-relaxed md:text-4xl font-bold md:font-medium text-left text-primary uppercase md:mt-0 title-font" itemprop="name">
        <span class="hidden sm:block"><?= $escaper->escapeHtml($product->getName()) ?></span>
        <span class="sm:hidden"><?= $escaper->escapeHtml($productShortName) ?></span>
    </h1>
    <div itemprop="brand" itemscope itemtype="http://schema.org/Brand">
        <meta itemprop="name" content="GFP" />
    </div>
    <div class="mb-4 text-base font-medium hidden lg:block">
        <?= $escaper->escapeHtml($product->getSubname()) ?>
    </div>

    <div class="my-2 cursor-pointer" onclick="scrollToReview()">
        <?= $block->getChildHtml('product.info.review') ?>
    </div>

    <?= $block->getChildHtml("product.info.topusp.container"); ?>
    <?php $shortDescription = $productViewModel->getProduct()->getShortDescription(); ?>

    <?php
    $bulletpointsShort = array_filter([
        $product->getPlpBulletpoint1Short(),
        $product->getPlpBulletpoint2Short(),
        $product->getPlpBulletpoint3Short(),
        $product->getPlpBulletpoint4Short()
    ]);

    if ($bulletpointsShort) { ?>
        <div class="mb-4 leading-relaxed product-description sm:hidden">
            <ul class="px-0.5 list-none">
                <?php
                    foreach ($bulletpointsShort as $bulletpoint) { ?>
                        <li class='text-sm md:text-lg my-2'>
                            <span><?= $heroicons->checkCircleHtml('text-gfpgreen-lighter w-7 h-7 inline pb-1', 32, 32) ?></span>
                            <?= $escaper->escapeHtml($bulletpoint) ?>
                        </li>
                <?php
                    }
                ?>
            </ul>
        </div>

    <?php }
    if ($shortDescription) : ?>
        <div class="mb-4 px-0.5 leading-relaxed product-description prose <?= $bulletpointsShort ? 'hidden sm:block': '' ?>"><?= /* @noEscape */ $shortDescription ?></div>
    <?php endif; ?>

    <div class="pt-8 product-info-form"><?= $block->getChildHtml('product.info.form') ?></div>

    <div class="addtocart-container w-screen  bg-white fixed left-0 pb-2 pt-0 bottom-0 px-2 sm:px-4 w-auto z-20 md:bg-transparent md:relative md:px-0 md:pb-0 md:w-auto" >
        <div class="flex flex-row flex-nowrap items-end mb-2 sm:mb-4 mt-0 justify-between" >
            <?= $block->getChildHtml("product.info.shippinginfo") ?>
            <div class="flex-grow text-right">
                <?= $block->getChildHtml("product.info.price") ?>
            </div>
        </div>
        <div class="ml-auto justify-between md:justify-end hidden md:flex">
            <?php if ($product->isSaleable()): ?>
                <a href="<?= $product->getUrlInStore() ?>"
                        title="<?= $escaper->escapeHtmlAttr(__('Visit product page')) ?>"
                        class="btn btn-primary w-full py-2 px-2 lg:px-20"
                >
                    <span class="block text-2xl md:text-xl font-bold lg:font-extrabold "><?= $escaper->escapeHtml(__('Visit product page')) ?></span>
                </a>
            <?php else: ?>
                <div class="stock unavailable">
                    <span><?= $escaper->escapeHtml(__('Out of stock')) ?></span>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <?php if ($product->isSaleable()): ?>
        <div class="flex mt-4 justify-end">
            <?= $block->getChildHtml('addtocart.shortcut.buttons') ?>
        </div>
    <?php endif; ?>

    <?php if ($tierPriceBlock = $block->getChildHtml("product.price.tier")): ?>
        <div class="py-4 my-2 tier-price-container">
            <?= /** @noEscape */ $tierPriceBlock ?>
        </div>
    <?php endif; ?>
    <?= $block->getChildHtml("product.info.additional") ?>
</div>
<div class="product-info w-full px-2 order-3 hidden">
    <div class="pt-8 product-info-form"><?= $block->getChildHtml('product.info.form') ?></div>

    <div class="addtocart-container w-screen  bg-white fixed left-0 py-0 bottom-0 px-2 sm:px-4 w-auto z-20 md:bg-transparent md:relative md:px-0 md:pb-0 md:w-auto" >
        <div class="flex flex-row flex-nowrap items-end mb-2 sm:mb-4 mt-0 justify-between" >
            <div class="flex-grow text-right">
                <?= $block->getChildHtml("product.info.price") ?>
            </div>
            <?= $block->getChildHtml("product.info.shippinginfo") ?>
        </div>
    </div>

    <?php if ($product->isSaleable()): ?>
        <div class="flex mt-4 justify-end">
            <?= $block->getChildHtml('addtocart.shortcut.buttons') ?>
        </div>
    <?php endif; ?>

    <?php if ($tierPriceBlock = $block->getChildHtml("product.price.tier")): ?>
        <div class="py-4 my-2 tier-price-container">
            <?= /** @noEscape */ $tierPriceBlock ?>
        </div>
    <?php endif; ?>
</div>
<div class="md:hidden order-3 flex w-full justify-between md:justify-end">
    <?php if ($product->isSaleable()): ?>
        <a href="<?= $product->getUrlInStore() ?>"
           title="<?= $escaper->escapeHtmlAttr(__('Visit product page')) ?>"
           class="btn btn-primary w-full px-2 lg:px-20"
        >
            <span class="block text-2xl md:text-xl font-bold lg:font-extrabold "><?= $escaper->escapeHtml(__('Visit product page')) ?></span>
        </a>
    <?php else: ?>
        <div class="stock unavailable">
            <span><?= $escaper->escapeHtml(__('Out of stock')) ?></span>
        </div>
    <?php endif; ?>
</div>

<script>
    function scrollToReview(){
        if(document.getElementById('customer-review-list')){
            let reviewTab = document.querySelector('.product-info-details-tab-review');
            if( reviewTab && reviewTab.scrollHeight === 0){
                window.dispatchEvent(new CustomEvent("openproduct-info-details-tab-review"));
            }
            setTimeout(() => {
                document.getElementById('customer-review-list').scrollIntoView({behavior: 'smooth'});
            }, 100);
        }
    }
</script>
