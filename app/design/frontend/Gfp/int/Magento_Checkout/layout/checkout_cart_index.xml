<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <referenceContainer name="footer" remove="true"/>
    <referenceContainer name="footer.bottom" remove="true"/>
    <referenceContainer name="footer-bottom-container" remove="true"/>
    <referenceBlock name="elasticsuite_footer" remove="true"/>
    <referenceBlock name="breadcrumbs" remove="true"/>
    <referenceBlock name="payolution_translate" remove="true"/>
    <referenceBlock name="xcheckout.cart.delivery" remove="true"/>
    <referenceBlock name="amazon_pay_config" remove="true"/>
    <referenceBlock name="trustedshops_trustedshops.trustbadge" remove="true"/>
    <referenceBlock name="checkout.cart.wrapper">
        <container name="cart.summary.bottom" label="Cart Summary Bottom Container" htmlTag="div" htmlClass="cart-summary-bottom" after="-">
        </container>
    </referenceBlock>
    <referenceBlock name="footer_contact" remove="true"/>
<!--    <referenceContainer name="page.bottom">-->
<!--        <block class="Magento\Cms\Block\Block" name="home.contactrow" before="-">-->
<!--            <arguments>-->
<!--                <argument name="block_id" xsi:type="string">hometeaserbreak</argument>-->
<!--            </arguments>-->
<!--        </block>-->
<!--    </referenceContainer>-->
    <referenceBlock name="checkout.cart.methods.bottom">
        <block class="Magento\Cms\Block\Block" name="cart.guestinfo">
            <arguments>
                <argument name="block_id" xsi:type="string">cartguestinfo</argument>
            </arguments>
        </block>
    </referenceBlock>
    <referenceContainer name="cart.summary">
        <container name="cart.summary.actions.before" after="checkout.cart.totals.container" before="checkout.cart.methods.bottom">
        </container>
    </referenceContainer>
    <move element="checkout.cart.methods.bottom" destination="cart.summary" after="checkout.cart.totals" />
    <move element="cart.discount" destination="cart.summary.actions.before" before="cart.xcheckout"/>
    <referenceBlock name="checkout.cart.shipping" remove="true"/>
    <referenceBlock name="header-content">
        <action method="setTemplate">
            <argument name="template" xsi:type="string">Magento_Checkout::html/cart/header.phtml</argument>
        </action>
    </referenceBlock>
    <referenceBlock name="crosssell" remove="true"/>
</page>
