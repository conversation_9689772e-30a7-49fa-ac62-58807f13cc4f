@charset "UTF-8"; .mgz-flex, .mgz-flex-position-top-left, .mgz-flex-position-top-center, .mgz-flex-position-top-right, .mgz-flex-position-middle-left, .mgz-flex-position-center-left, .mgz-flex-position-middle-center, .mgz-flex-position-center-center, .mgz-flex-position-middle-right, .mgz-flex-position-center-right, .mgz-flex-position-bottom-left, .mgz-flex-position-bottom-center, .mgz-flex-position-bottom-right, .mgz-element-row.mgz-row-wrap-reverse > .mgz-element-inner, .mgz-element-row.mgz-row-equal-height > .mgz-element-inner, .mgz-element-row.mgz-row-wrap-reverse > .mgz-element-inner > .inner-content, .mgz-element-row.mgz-row-equal-height > .mgz-element-inner > .inner-content, .mgz-element-row.mgz-row-wrap-reverse > .mgz-element-inner > .inner-content > .mgz-element, .mgz-element-row.mgz-row-equal-height > .mgz-element-inner > .inner-content > .mgz-element, .mgz-element-row.mgz-row-wrap-reverse > .mgz-element-inner > .inner-content > .mgz-element > .mgz-element-inner, .mgz-element-row.mgz-row-equal-height > .mgz-element-inner > .inner-content > .mgz-element > .mgz-element-inner, .mgz-socialicons li, .mgz-countdown {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.mgz-invisible {
  border: 0;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

a.mgz-btn {
  color: #333;
}

.mgz-btn:not(.primary) {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

button.mgz-btn {
  border: 0;
}

.mgz-btn {
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  margin: 0;
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  background-image: none;
  word-wrap: break-word;
  text-decoration: none;
  position: relative;
  line-height: normal;
  padding: 10px 20px;
  color: #333;
  background-color: #e3e3e3;
  font-size: 1.4rem;
  max-width: 100%;
  height: auto;
}

.mgz-btn:hover {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  color: #5e5e5e;
  background-color: #dcdcdc;
  text-decoration: none;
}

.mgz-btn:focus {
  outline: none;
}

.mgz-btn.mgz-btn-save, .mgz-btn.mgz-btn-cancel, .mgz-btn.mgz-btn-replace {
  padding: 15px 20px;
  font-size: 1.6rem;
  font-weight: 500;
  min-width: 140px;
}

.mgz-btn.mgz-btn-save {
  background: #007dbd;
  color: #FFF;
}

.mgz-btn.mgz-btn-save:hover {
  background: #0073ae;
}

.mgz-btn.mgz-btn-cancel {
  color: #fff;
  background-color: #afafaf;
}

.mgz-btn.mgz-btn-cancel:hover {
  background-color: #8c8c8c;
}

.mgz-btn.mgz-btn-replace {
  float: left;
  color: #fff;
  background-color: #afafaf;
}

.mgz-btn.mgz-btn-replace:hover {
  background-color: #8c8c8c;
}

.mgz-btn.mgz-btn-delete {
  color: #FFF;
  background-color: #e22626;
}

.mgz-btn.mgz-btn-delete:hover {
  background-color: #ca1c1c;
}

.mgz-icon, .tabs-opener:before {
  font-family: 'Magezon-Icons' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.mgz-fa {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

.mgz-fa-s {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

.mgz-fa-r {
  font-family: 'Font Awesome 5 Free';
  font-weight: 400;
}

.mgz-liststyle {
  margin: 0;
  padding: 0;
  list-style: none;
}

.mgz-hover-main {
  overflow: hidden;
}

[class^='mgz-hover-'] > img, [class*=' mgz-hover-'] > img {
  vertical-align: top;
  max-width: 100%;
}

[class^='mgz-hover-'], [class*=' mgz-hover-'], [class^='mgz-hover-']:before, [class^='mgz-hover-']:after, [class*=' mgz-hover-']:before, [class*=' mgz-hover-']:after, [class^='mgz-hover-'] *, [class*=' mgz-hover-'] *, [class^='mgz-hover-'] *:before, [class^='mgz-hover-'] *:after, [class*=' mgz-hover-'] *:before, [class*=' mgz-hover-'] *:after {
  box-sizing: border-box;
  transition: all 0.35s ease;
}

.mgz-image-hovers .hover-type-liftup {
  position: relative;
}

.mgz-image-hovers .hover-type-liftup img {
  -webkit-transform: perspective(1000px) scale(1);
  transform: perspective(1000px) scale(1);
  -webkit-transition: -webkit-transform 0.35s !important;
  transition: -webkit-transform 0.35s !important;
  -o-transition: transform 0.35s !important;
  transition: transform 0.35s !important;
  transition: transform 0.35s, -webkit-transform 0.35s !important;
  position: relative;
  z-index: 1;
}

.mgz-image-hovers .hover-type-liftup:before {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  content: '';
  -webkit-transition: opacity 0.35s !important;
  -o-transition: opacity 0.35s !important;
  transition: opacity 0.35s !important;
  -webkit-box-shadow: 0 3px 15px rgba(0, 0, 0, 0.4);
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.4);
  opacity: 0;
}

.mgz-image-hovers .hover-type-liftup:hover img {
  -webkit-transform: perspective(1000px) scale(1.03);
  transform: perspective(1000px) scale(1.03);
}

.mgz-image-hovers .hover-type-liftup:hover:before {
  opacity: 1;
}
.mgz-flex-position-bottom-center {
  -webkit-align-items: flex-end;
  -ms-align-items: flex-end;
  align-items: flex-end;
  -webkit-justify-content: center;
  -ms-justify-content: center;
  justify-content: center;
}
.mgz-element-inner {
  height: 100%;
  position: relative;
}

.mgz-element-inner:before, .mgz-element-inner:after {
  content: " ";
  display: table;
  clear: both;
}

.inner-content {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

.inner-content:before, .inner-content:after {
  content: " ";
  display: table;
  clear: both;
}

.magezon-builder-preload .mgz-element {
  z-index: 0;
}

.mgz-child:not(:last-child) > .mgz-element-inner {
  margin-bottom: 15px;
}

.mgz-element {
  position: relative;
  text-align: left;
}

.mgz-element:before, .mgz-element:after {
  content: " ";
  display: table;
  clear: both;
}

.mgz-element p:last-child {
  margin-bottom: 0;
}

.mgz-parallax, .mgz-parallax-inner, .mgz-parallax-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.mgz-parallax {
  -webkit-border-radius: inherit;
  -moz-border-radius: inherit;
  border-radius: inherit;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  overflow: hidden;
  pointer-events: none;
}

.mgz-parallax .mgz-parallax-inner {
  background-repeat: no-repeat;
  background-position: 50% 50%;
}

.mgz-element-row.mgz-row-wrap-reverse > .mgz-element-inner > .inner-content, .mgz-element-row.mgz-row-equal-height > .mgz-element-inner > .inner-content {
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.mgz-element-row.mgz-row-wrap-reverse > .mgz-element-inner > .inner-content:before, .mgz-element-row.mgz-row-equal-height > .mgz-element-inner > .inner-content:before, .mgz-element-row.mgz-row-wrap-reverse > .mgz-element-inner > .inner-content:after, .mgz-element-row.mgz-row-equal-height > .mgz-element-inner > .inner-content:after {
  display: none;
}

.mgz-element-row.mgz-row-wrap-reverse > .mgz-element-inner > .inner-content > .mgz-element, .mgz-element-row.mgz-row-equal-height > .mgz-element-inner > .inner-content > .mgz-element {
  -webkit-align-items: stretch;
  -ms-align-items: stretch;
  align-items: stretch;
}

.mgz-element-row.mgz-row-wrap-reverse > .mgz-element-inner > .inner-content > .mgz-element > .mgz-element-inner, .mgz-element-row.mgz-row-equal-height > .mgz-element-inner > .inner-content > .mgz-element > .mgz-element-inner {
  -webkit-flex-grow: 1;
  -ms-flex-grow: 1;
  flex-grow: 1;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
}

.mgz-element-row.content-top > .mgz-element-inner > .inner-content > .mgz-element > .mgz-element-inner {
  -webkit-align-self: flex-start;
  -ms-align-self: flex-start;
  align-self: flex-start;
  -webkit-justify-content: flex-start;
  -ms-justify-content: flex-start;
  justify-content: flex-start;
}

.mgz-element-row.content-middle > .mgz-element-inner > .inner-content > .mgz-element > .mgz-element-inner {
  -webkit-align-self: center;
  -ms-align-self: center;
  align-self: center;
  -webkit-justify-content: center;
  -ms-justify-content: center;
  justify-content: center;
}

.mgz-element-column > .mgz-element-inner {
  padding: 10px;
}

.mgz-element-column.mgz-element-column-empty > .mgz-element-inner {
  padding: 0;
  margin: 0;
}

.mgz-row-gap-margin > .mgz-element-inner {
  margin: 10px;
  padding: 0;
}

.mgz-element-heading-text {
  margin: 0;
  color: inherit;
}

.mgz-element-text p:first-child {
  margin-top: 0;
}

.mgz-element-text p:last-child {
  margin-bottom: 0;
}


.mgz-btn-size-sm .mgz-btn {
  font-size: 12px;
  padding: 11px 16px;
}

.mgz-element-single_image .mgz-element-inner {
  font-size: 0;
}

.mgz-single-image-wrapper {
  display: inline-block;
  line-height: 0;
  max-width: 100%;
  font-size: 1.4rem;
  position: relative;
}

.mgz-single-image-wrapper a {
  line-height: 0;
  font-size: 0;
  overflow: hidden;
  display: block;
}

.mgz-single-image-wrapper img {
  border-color: #ebebeb;
}
.mgz-single-image-wrapper .image-content {
  padding: 10px 20px;
  text-align: left;
  line-height: initial;
  clear: both;
  z-index: 1;
  position: absolute;
}
.mgz-single-image-wrapper .mgz-image-link {
  cursor: pointer;
}

.page-layout-1column, .page-layout-2columns-left, .page-layout-2columns-right, .page-layout-1column-fullwidth {
  overflow-x: hidden;
}

.magezon-builder {
  position: relative;
  z-index: 2;
}

.magezon-builder:before, .magezon-builder:after {
  content: " ";
  display: table;
  clear: both;
}

.magezon-builder * {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.magezon-builder iframe, .magezon-builder video, .magezon-builder img {
  max-width: 100%;
}

.magezon-builder p {
  margin-top: 0;
}

/** MGZ Pagebuilder Icon Box */

.mgz-icon-box-wrapper {
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  display: inline-block;
  line-height: 0;
  position: relative;
}

.mgz-icon-box-wrapper a {
  color: inherit;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.mgz-icon-box-wrapper a:before,
.mgz-icon-box-wrapper a:after {
  content: " ";
  display: table;
  clear: both;
}

.mgz-icon-box-wrapper .mgz-icon-box-element {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
}

.mgz-icon-box-size-xs {
  width: 2.5em;
  height: 2.5em;
}
.mgz-icon-box-size-xs .mgz-icon-box-element {
  font-size: 1.2em;
}
.mgz-icon-box-size-sm {
  width: 3.15em;
  height: 3.15em;
}
.mgz-icon-box-size-sm .mgz-icon-box-element {
  font-size: 1.6em;
}
.mgz-icon-box-size-md {
  width: 4em;
  height: 4em;
}
.mgz-icon-box-size-md .mgz-icon-box-element {
  font-size: 2.15em;
}
.mgz-icon-box-size-lg {
  width: 5em;
  height: 5em;
}
.mgz-icon-box-size-lg .mgz-icon-box-element {
  font-size: 2.85em;
}
.mgz-icon-box-size-xl {
  width: 7.15em;
  height: 7.15em;
}
.mgz-icon-box-size-xl .mgz-icon-box-element {
  font-size: 5em;
}

.mgz-heading-text {
  margin: 0;
  color: inherit;
  margin-bottom: 10px;
}
.mgz-heading-text > a {
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  opacity: 1;
  font-weight: inherit;
}
.mgz-heading-text > a,
.mgz-heading-text > a:focus,
.mgz-heading-text > a:hover,
.mgz-heading-text > a:visited {
  border: none;
  text-decoration: inherit;
  color: inherit;
  font-weight: inherit;
}
.mgz-heading-text > a:hover {
  opacity: 0.85;
}

a.mgz-icon-box-btn {
  color: #333;
}
.mgz-icon-box-btn:not(.primary) {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
button.mgz-icon-box-btn {
  border: 0;
}
.mgz-element-icon_box .mgz-icon-box-btn {
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  margin: 0;
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  background-image: none;
  word-wrap: break-word;
  text-decoration: none;
  position: relative;
  line-height: normal;
  padding: 10px 20px;
  color: #333;
  background-color: #e3e3e3;
  font-size: 1.4rem;
  max-width: 100%;
  height: auto;
}
.mgz-element-icon_box .mgz-icon-box-btn:hover {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  color: #5e5e5e;
  background-color: #dcdcdc;
  text-decoration: none;
}
.mgz-element-icon_box .mgz-icon-box-btn:focus {
  outline: none;
}
.mgz-element-icon_box .mgz-btn-style-modern .mgz-icon-box-btn {
  background-image: -webkit-linear-gradient(top, rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.01) 100%);
  background-image: -o-linear-gradient(top, rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.01) 100%);
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.01) 100%);
  background-repeat: repeat-x;
}
.mgz-element-icon_box .mgz-btn-style-gradient .mgz-icon-box-btn {
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  background-size: 200% 100%;
}
.mgz-element-icon_box .mgz-btn-style-gradient .mgz-icon-box-btn:hover {
  background-position: 100% 0;
}
.mgz-element-icon_box .mgz-btn-style-3d .mgz-icon-box-btn:hover {
  top: 2px;
}
.mgz-element-icon_box .mgz-btn-size-xs .mgz-icon-box-btn {
  font-size: 11px;
  padding: 8px 12px;
}
.mgz-element-icon_box .mgz-btn-size-sm .mgz-icon-box-btn {
  font-size: 12px;
  padding: 11px 16px;
}
.mgz-element-icon_box .mgz-btn-size-md .mgz-icon-box-btn {
  font-size: 14px;
  padding: 14px 20px;
}
.mgz-element-icon_box .mgz-btn-size-lg .mgz-icon-box-btn {
  font-size: 18px;
  padding: 18px 30px;
}
.mgz-element-icon_box .mgz-btn-size-xl .mgz-icon-box-btn {
  font-size: 22px;
  padding: 22px 35px;
}

.mgz-element-icon_box .icon-box-btn-right {

}

.btn-position {
  display: inline-block;
  width: 100%;
}

.mgz-icon-box-container {
  display: flex;
}

.mgz-icon-box-left,
.mgz-icon-box-right {
  width: 30%;
  margin: auto;
}

.mgz-description {
  margin-bottom: 25px;

}

.mgz-tabs:before,
.mgz-tabs-nav:before,
.mgz-tabs-content:before,
.mgz-tabs:after,
.mgz-tabs-nav:after,
.mgz-tabs-content:after {
  content: " ";
  display: table;
  clear: both;
}
.mgz-tabs-nav > .mgz-tabs-tab-title > a,
.mgz-tabs-content > .mgz-tabs-tab-title > a {
  color: #666666;
}
.mgz-tabs-nav > .mgz-tabs-tab-title:not(.mgz-active) > a,
.mgz-tabs-content > .mgz-tabs-tab-title:not(.mgz-active) > a {
  background: #ebebeb;
}
.mgz-tabs-nav > .mgz-tabs-tab-title:not(.mgz-active) > a:hover,
.mgz-tabs-content > .mgz-tabs-tab-title:not(.mgz-active) > a:hover {
  background: #dcdcdc;
}
.mgz-tabs-nav > .mgz-tabs-tab-title:first-child > a,
.mgz-tabs-content > .mgz-tabs-tab-title:first-child > a {
  margin-top: 0 !important;
}
.mgz-tabs-nav > .mgz-tabs-tab-title.mgz-active > a,
.mgz-tabs-content > .mgz-tabs-tab-title.mgz-active > a {
  background: #f8f8f8;
}
.mgz-tabs {
  color: #333;
}
.mgz-tabs .mgz-tabs-tab-title > a {
  -webkit-transition: background 0.2s ease-in-out, color 0.2s ease-in-out, border 0.2s ease-in-out;
  -moz-transition: background 0.2s ease-in-out, color 0.2s ease-in-out, border 0.2s ease-in-out;
  -ms-transition: background 0.2s ease-in-out, color 0.2s ease-in-out, border 0.2s ease-in-out;
  -o-transition: background 0.2s ease-in-out, color 0.2s ease-in-out, border 0.2s ease-in-out;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  padding: 14px 20px;
  display: block;
  position: relative;
  z-index: 1;
  border: 1px solid #e3e3e3;
}
.mgz-tabs .mgz-tabs-tab-title > a:hover {
  text-decoration: none;
}
.mgz-tabs .mgz-tabs-tab-content {
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  display: none;
}
.mgz-tabs .mgz-tabs-tab-content.mgz-active {
  display: block;
}
.mgz-tabs .mgz-tabs-tab-content > .mgz-element > .mgz-element-inner {
  margin-bottom: 0;
}
.mgz-tabs:not(.mgz-tabs-no-fill-content) .mgz-tabs-tab-content {
  border: 1px solid #e3e3e3;
  background: #f8f8f8;
  margin-top: -1px;
}
.mgz-element-tab-position-top.mgz-tabs,
.mgz-element-tab-position-bottom.mgz-tabs {
  flex-direction: column;
}
.mgz-element-tab-position-top.mgz-tabs > .mgz-tabs-nav > .mgz-tabs-tab-title > a,
.mgz-element-tab-position-bottom.mgz-tabs > .mgz-tabs-nav > .mgz-tabs-tab-title > a {
  margin-right: 5px;
}
.mgz-element-tab-position-top.mgz-tabs > .mgz-tabs-nav > .mgz-tabs-tab-title:last-child,
.mgz-element-tab-position-bottom.mgz-tabs > .mgz-tabs-nav > .mgz-tabs-tab-title:last-child {
  margin-right: 0;
}
@media (min-width: 768px) {
  .mgz-tabs {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
  }
  .mgz-tabs .mgz-tabs-tab-title {
    text-align: left;
    display: inline-block;
  }
  .mgz-tabs .mgz-tabs-content > .mgz-tabs-tab-title {
    display: none;
  }
  .mgz-element-tab-align-right.mgz-tabs > .mgz-tabs-nav {
    text-align: right;
  }
  .mgz-element-tab-align-center.mgz-tabs > .mgz-tabs-nav {
    text-align: center;
  }
  .mgz-element-tab-position-top.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-nav > .mgz-tabs-tab-title > a,
  .mgz-element-tab-position-top.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-content > .mgz-tabs-tab-title > a {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }
  .mgz-element-tab-position-top.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-nav > .mgz-tabs-tab-title:last-child > a,
  .mgz-element-tab-position-top.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-content > .mgz-tabs-tab-title:last-child > a {
    margin-right: 0;
  }
  .mgz-element-tab-position-top.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-nav > .mgz-tabs-tab-title.mgz-active > a,
  .mgz-element-tab-position-top.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-content > .mgz-tabs-tab-title.mgz-active > a {
    border-bottom-color: transparent;
  }
  .mgz-element-tab-position-top.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-content > .mgz-tabs-tab-content {
    border-top-left-radius: 0;
  }
  .mgz-element-tab-position-top.mgz-tabs > .mgz-tabs-nav > .mgz-tabs-tab-title > a {
    margin-top: 0 !important;
  }
  .mgz-element-tab-position-top.mgz-element-tab-align-right.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-content > .mgz-tabs-tab-content {
    border-top-left-radius: 5px;
    border-top-right-radius: 0;
  }
  .mgz-element-tab-position-bottom.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-nav > .mgz-tabs-tab-title > a,
  .mgz-element-tab-position-bottom.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-content > .mgz-tabs-tab-title > a {
    border-top-right-radius: 0;
    border-top-left-radius: 0;
  }
  .mgz-element-tab-position-bottom.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-nav > .mgz-tabs-tab-title:last-child > a,
  .mgz-element-tab-position-bottom.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-content > .mgz-tabs-tab-title:last-child > a {
    margin-right: 0;
  }
  .mgz-element-tab-position-bottom.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-nav > .mgz-tabs-tab-title.mgz-active > a,
  .mgz-element-tab-position-bottom.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-content > .mgz-tabs-tab-title.mgz-active > a {
    border-top-color: transparent;
  }
  .mgz-element-tab-position-bottom.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-content > .mgz-tabs-tab-content {
    border-bottom-left-radius: 0;
    margin-bottom: -1px;
  }
  .mgz-element-tab-position-bottom.mgz-tabs > .mgz-tabs-nav {
    order: 1;
  }
  .mgz-element-tab-position-bottom.mgz-tabs > .mgz-tabs-nav > .mgz-tabs-tab-title > a {
    margin-top: 0 !important;
  }
  .mgz-element-tab-position-bottom.mgz-tabs > .mgz-tabs-content > .mgz-tabs-tab-content {
    margin-top: 0 !important;
  }
  .mgz-element-tab-position-left.mgz-tabs > .mgz-tabs-nav,
  .mgz-element-tab-position-right.mgz-tabs > .mgz-tabs-nav {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }
  .mgz-element-tab-position-left.mgz-tabs > .mgz-tabs-nav > .mgz-tabs-tab-title,
  .mgz-element-tab-position-right.mgz-tabs > .mgz-tabs-nav > .mgz-tabs-tab-title {
    margin-right: 0;
    display: block;
    width: 100%;
    margin-bottom: 5px;
  }
  .mgz-element-tab-position-left.mgz-tabs > .mgz-tabs-nav > .mgz-tabs-tab-title > a,
  .mgz-element-tab-position-right.mgz-tabs > .mgz-tabs-nav > .mgz-tabs-tab-title > a {
    margin-right: 0;
  }
  .mgz-element-tab-position-left.mgz-tabs > .mgz-tabs-nav > .mgz-tabs-tab-title:first-child > a,
  .mgz-element-tab-position-right.mgz-tabs > .mgz-tabs-nav > .mgz-tabs-tab-title:first-child > a {
    margin-top: 0;
  }
  .mgz-element-tab-position-left.mgz-tabs > .mgz-tabs-content,
  .mgz-element-tab-position-right.mgz-tabs > .mgz-tabs-content {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    min-width: 0;
  }
  .mgz-element-tab-position-left.mgz-tabs > .mgz-tabs-content > .mgz-tabs-tab-content,
  .mgz-element-tab-position-right.mgz-tabs > .mgz-tabs-content > .mgz-tabs-tab-content {
    margin-top: 0;
    height: 100%;
  }
  .mgz-element-tab-position-left .mgz-tabs-tab-content,
  .mgz-element-tab-position-right .mgz-tabs-tab-content {
    margin-top: 0 !important;
  }
  .mgz-element-tab-position-left.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-nav > .mgz-tabs-tab-title > a {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .mgz-element-tab-position-left.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-nav > .mgz-tabs-tab-title.mgz-active > a {
    border-right-width: 0;
  }
  .mgz-element-tab-position-left.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-content > .mgz-tabs-tab-content {
    border-top-left-radius: 0;
    margin-left: -1px;
  }
  .mgz-element-tab-position-right.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-nav > .mgz-tabs-tab-title > a {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .mgz-element-tab-position-right.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-nav > .mgz-tabs-tab-title.mgz-active > a {
    border-left-width: 0;
  }
  .mgz-element-tab-position-right.mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-content > .mgz-tabs-tab-content {
    border-top-right-radius: 0;
    margin-right: -1px;
  }
  .mgz-element-tab-position-right.mgz-tabs > .mgz-tabs-nav {
    order: 1;
  }
}
.tabs-opener {
  height: 50px;
  width: 50px;
  position: absolute;
  right: 0;
  top: 0;
  cursor: pointer;
  text-align: center;
  font-size: 20px;
  font-weight: 100;
  display: none;
}
.tabs-opener:before {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
  content: '\e624';
}
.tabs-opener:hover {
  background-color: rgba(0, 0, 0, 0.05);
}
@media (max-width: 767px) {
  .mgz-row-wrap-reverse > .mgz-element-inner > .inner-content {
    -webkit-flex-direction: column-reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse;
  }
  .mgz-tabs-content .mgz-tabs-tab-title {
    display: none;
  }
  .mgz-tabs-nav .mgz-tabs-tab-title {
    display: inline-block;
  }
  .mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-content > .mgz-tabs-tab-content.mgz-active {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
  .mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-content > .mgz-tabs-tab-title {
    margin-top: 5px;
  }
  .mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-content > .mgz-tabs-tab-title:first-child {
    margin-top: 0;
  }
  .mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-content > .mgz-tabs-tab-title.mgz-active > a {
    border-bottom-color: transparent;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }
  .mgz-tabs:not(.mgz-tabs-no-fill-content)[data-gap="0"] > .mgz-tabs-content > .mgz-tabs-tab-content.mgz-active {
    border-top-color: transparent;
  }
  .mgz-tabs.tabs-mobile-accordion .mgz-tabs-nav {
    display: none;
  }
  .mgz-tabs.tabs-mobile-accordion .mgz-tabs-content .mgz-tabs-tab-title {
    display: block;
    position: relative;
  }
  .mgz-tabs.tabs-mobile-accordion .mgz-tabs-content .mgz-tabs-tab-title .tabs-opener {
    display: block;
  }
  .mgz-tabs.tabs-mobile-accordion .mgz-tabs-content .mgz-tabs-tab-title.mgz-active .tabs-opener:before {
    content: '\e623';
  }
  .mgz-tabs .mgz-tabs-content > .mgz-tabs-tab-title:first-child {
    margin-top: 0 !important;
  }
  .mgz-tabs .mgz-tabs-content > .mgz-tabs-tab-title > a {
    margin-right: 0 !important;
  }
  .mgz-tabs .mgz-tabs-content > .mgz-tabs-tab-content {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}