<?php

/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\View\Helper\SecureHtmlRenderer;
use Magento\Framework\Escaper;
use Magento\Review\Block\Product\View as ProductReview;
use Magento\Theme\Block\Html\Pager;

/** @var ProductReview $block */
/** @var SecureHtmlRenderer $secureRenderer */
/** @var Escaper $escaper */
/** @var Pager $toolbar */
/** @var ViewModelRegistry $viewModels */

$reviewCollection = $block->getReviewsCollection()->setPageSize(20)->setCurPage(1);
$toolbar = "";
$reviewCollection->load()->addRateVotes();

$items = $reviewCollection->getItems();
$format = $block->getDateFormat() ?: \IntlDateFormatter::SHORT;

/** @var \CopeX\AmastyReviews\ViewModel\Review $amReviewHelper */
$amReviewHelper = $viewModels->require(\CopeX\AmastyReviews\ViewModel\Review::class);
?>
<?php
/** @var \Amasty\AdvancedReview\Block\Images $imageBlock */
$imageBlock = $block->getChildBlock('review_list.images');
/** @var \Amasty\AdvancedReview\Block\Customer\Review\View  $answerBlock */
$answerBlock = $block->getChildBlock('review_list.answer');

$rating = $block->getProduct()->getRatingSummary();
$ratingSteps = 5;
$setRatingValue = $rating / 100 * $ratingSteps;
$starsFilled = is_numeric($rating) ? floor($setRatingValue) : 0;
$starFragment = $setRatingValue - $starsFilled;
$starsEmpty = floor($ratingSteps - $starsFilled - $starFragment);
$yellowHex = '#e8b723';
$greyHex = '#cbd5e0';

$sliderViewModel = $viewModels->require(\Hyva\Theme\ViewModel\Slider::class);
$itemsTemplate   = 'Magento_Review::product/view/list/item.phtml';
$reviewCount = $block->getProduct()->getReviewsCount();
?>

<?php if (count($items)): ?>
    <div class="mx-auto py-6 items-center" id="customer-review-list">
        <div class="flex flex-row">
            <span class="float-left mt-0 mr-5 mb-2 ml-0 text-6xl"><?= round($setRatingValue,1) ?></span>
            <div class="flex flex-col">
                <div class="rating-summary flex">
            <?php if ($rating): ?>
            <?php $i = 0; ?>
            <?php while ($i < $starsFilled): ?>
                <svg xmlns="http://www.w3.org/2000/svg" class="fill-current w-6 h-6" viewBox="3 0 20 20"
                     style="color: <?= /** @noEscape */ $yellowHex ?>"
                     fill="currentColor">
                    <use href="#star"></use>
                </svg>
                <?php $i++; ?>
            <?php endwhile; ?>
            <?php if ($starFragment): ?>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="3 0 20 20" fill="currentColor" class="w-6 h-6">
                    <defs>
                        <linearGradient id="partialFill<?= /* @noEscape */ $block->getProduct()->getId() ?>">
                            <stop offset="0%" stop-color="<?= /** @noEscape */ $yellowHex ?>"/>
                            <stop offset="<?= $starFragment * 100 ?>%" stop-color="<?= /** @noEscape */ $yellowHex ?>"/>
                            <stop offset="<?= $starFragment * 100 ?>%" stop-color="<?= /** @noEscape */ $greyHex ?>"/>
                            <stop offset="100%" stop-color="<?= /** @noEscape */ $greyHex ?>"/>
                        </linearGradient>
                    </defs>
                    <g fill="url(#partialFill<?= (int)$block->getProduct()->getId() ?>)">
                        <use href="#star"></use>
                    </g>
                </svg>
            <?php endif; ?>
            <?php $i = 0; ?>
            <?php while ($i < $starsEmpty): ?>
                <svg xmlns="http://www.w3.org/2000/svg"
                     class="fill-current w-6 h-6"
                     style="color: <?= /** @noEscape */ $greyHex ?>"
                     viewBox="3 0 20 20"
                     fill="currentColor">
                    <use href="#star"></use>
                </svg>
                <?php $i++; ?>
            <?php endwhile; ?>
        <?php else: ?>
            <?php $i = 0; ?>
            <?php while ($i < $ratingSteps): ?>
                <svg xmlns="http://www.w3.org/2000/svg"
                     class="fill-current text-gray-200 w-6 h-6"
                     viewBox="3 0 20 20"
                     fill="currentColor">
                    <use href="#star"></use>
                </svg>
                <?php $i++; ?>
            <?php endwhile; ?>
        <?php endif; ?>
        </div>
                <?php if($reviewCount > 50): ?>
                    <div>
                        <span><?= $escaper->escapeHtml($reviewCount) ?></span>
                        <span><?=  $escaper->escapeHtml(__('Reviews')) ?></span>
                    </div>
                <?php endif; ?>
            </div>
            <div x-data="{loaded: false}" x-intersect.once.margin.-200px="loaded=true">
                <template x-if="loaded">
                    <div class="text-xs" x-data="initPopper($el, document.querySelector('#review-tooltip'))" x-init="init()">
                        <div class="rounded-full border w-4 h-4 flex justify-center items-center ml-1">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="w-2 h-2 text-primary-lighter" viewBox="0 0 192 512"><path d="M160 448h-32V224c0-17.69-14.33-32-32-32L32 192c-17.67 0-32 14.31-32 32s14.33 31.1 32 31.1h32v192H32c-17.67 0-32 14.31-32 32s14.33 32 32 32h128c17.67 0 32-14.31 32-32S177.7 448 160 448zM96 128c26.51 0 48-21.49 48-48S122.5 32.01 96 32.01s-48 21.49-48 48S69.49 128 96 128z"/></svg>
                        </div>
                    </div>
                </template>
            </div>
        </div>
        <div class="mx-0 mt-0 mb-2 block">
            <?php if (!$block->getHideTitle()): ?>
                <div class="relative flex pt-5 items-center w-full">
                    <span class="font-normal pr-6 whitespace-no-wrap text-2xl"><?= $escaper->escapeHtml(__('Top customer reviews')) ?></span>
                    <div class="flex-grow border-t border-gray-300"></div>
                </div>
            <?php endif ?>
        </div>
        <div>
            <?= $sliderViewModel->getSliderForItems($itemsTemplate, $items, 'CopeX_Swiper::slider.phtml')
                    ->addData([
                            'product' => $block->getProduct(),
                            'width' => 'w-full',
                            'lightbox' => false,
                            'loop' => "false",
                            'slides_per_view' => 1,
                            'extra_config' => '{
                                breakpoints: {
                                    768: { 
                                        slidesPerView: 2,
                                        slidesPerGroup: 2,
                                    }
                                },
                                speed: 100,
                                watchSlidesProgress: true,
                            }',
                            'swiper_wrapper_class' => 'swiper-wrapper',
                            'swiper_slide_class' => 'swiper-slide px-2 pb-2 flex flex-col w-full md:w-1/2 customer_review_slide',
                    ])
                    ->toHtml(); ?>
        </div>
    </div>
<?php endif; ?>
