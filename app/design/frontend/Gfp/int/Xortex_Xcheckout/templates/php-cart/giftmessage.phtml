<?php
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

use Hyva\Theme\ViewModel\Cart\CheckoutConfig;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\StoreConfig;
use Magento\Framework\Escaper;
use Hyva\Theme\ViewModel\Store;

$storeConfig = $viewModels->require(StoreConfig::class);
$viewModelStore = $viewModels->require(Store::class);
?>

<div class="pb-2"
     x-data="initGiftMessage()"
     x-bind="eventListeners"
     id="gift-card-block"
>
    <div class="mx-auto md:mx-0">
        <div class="text-left text-sm">
            <span @click="show = !show" class="cursor-pointer text-primary-lighter select-none flex items-center gap-2">
                <span x-show="!show">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </span>
                <span x-show="show">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M15 12H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </span>
                <span class="text-sm text-black"><?= $escaper->escapeHtml(__('Leave a message?')) ?></span>
            </span>
        </div>
        <div x-show="show" x-cloak>
            <div class="flex flex-col gap-2 justify-center md:justify-start px-0 pt-4">
                <div class="control">
                    <textarea
                           class="form-input w-full disabled:opacity-75 disabled:bg-gray-100 disabled:pointer-events-none text-lg"
                           name="gift-message"
                           x-model="message"
                           @change="updateMessage"
                           @mouseout="updateMessage"
                           @keyup="updateMessage"
                           placeholder="<?= $escaper->escapeHtmlAttr(__('Your message here...')) ?>"
                    ></textarea>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    function initGiftMessage() {
        return {
            show: false,
            cartId: null,
            loggedIn: false,
            message: '',
            timeoutFunction: null,
            lastSentMessage: "",

            updateMessage: function(){
                if (this.timeoutFunction !== null) {
                    clearTimeout(this.timeoutFunction);
                }
                if(this.lastSentMessage === this.message || !this.cartId) return;
                this.timeoutFunction = setTimeout(() => {
                    this.lastSentMessage = this.message;

                    const request = new XMLHttpRequest();
                    request.open('POST', '<?= $escaper->escapeUrl($block->getBaseUrl()) ?>xcheckout/cart/post');
                    request.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                    request.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                    request.send("delivery_comment="+this.message);
                    }, 300
                );
            },
            setCartIdByCustomerData(customerData) {
                if (customerData) {
                    if (customerData.cart && customerData.cart.cartId) {
                        this.cartId = customerData.cart.cartId;
                    }
                    if (customerData.customer && customerData.customer.fullname) {
                        this.loggedIn = true;
                    }
                }
                this.lastSentMessage = this.message;
            },
            eventListeners: {
                ['@private-content-loaded.window'](event) {
                    this.setCartIdByCustomerData(event.detail.data)
                },
            }
        }
    }
</script>
