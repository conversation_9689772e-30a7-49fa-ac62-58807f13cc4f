<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\LayeredNavigation\Block\Navigation\State;

/** @var State $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>
<?php $filters = $block->getActiveFilters() ?>
<?php if (!empty($filters)): ?>
<div class="filter-current bg-container-lighter border border-container p-4 md:px-8 mb-6" x-data="{ open: true }">
    <div class="filter-options-title flex justify-between items-center cursor-pointer"
         @click="open = !open">
        <strong class="text-lg"><?= $escaper->escapeHtml(__('Active filtering')) ?></strong>
        <span class="py-1 px-1 rounded border border-container">
            <?= $heroicons->chevronDownHtml(
                'transition-transform transform duration-300 ease-in-out',
                24,
                24,
                [":class" => "{ 'rotate-180': open }"]
            ); ?>
        </span>
    </div>
    <div class="items pt-1 pb-3" x-show="open">
        <?php foreach ($filters as $filter): ?>
            <div class="item flex justify-between items-center mb-2">
                <span>
                    <span class="filter-label block"><?= $escaper->escapeHtml(__($filter->getName())) ?></span>
                    <span class="filter-value text-sm block">
                        <?php
                        if($filter->getFilter() instanceof \Magento\CatalogSearch\Model\Layer\Filter\Price):
                            $label = (string) $filter->getLabel();
                            echo $escaper->escapeHtml($block->stripTags(str_replace(",00","",$label)));
                        else: ?>
                            <?= $escaper->escapeHtml($block->stripTags($filter->getLabel())) ?>
                        <?php endif; ?>
                    </span>
                </span>
                <span>
                    <a class="py-2 px-2 text-center block remove text-gfpgreen hover:text-primary-darker"
                       href="<?= $escaper->escapeUrl($filter->getRemoveUrl()) ?>"
                       title="<?= /* @noEscape */ $escaper->escapeHtmlAttr(__('Remove This Item')) ?>"
                       >
                        <?= $heroicons->trashHtml('', 20, 20); ?>
                    </a>
                </span>
            </div>
        <?php endforeach; ?>
    </div>
    <?php if ($block->getLayer()->getState()->getFilters()): ?>
        <div class="block-actions filter-actions">
            <a href="<?= $escaper->escapeUrl($block->getClearUrl()) ?>"
               class="text-xs text-primary hover:text-primary-darker"><?= $escaper->escapeHtml(__('Clear All')) ?></a>
        </div>
    <?php endif; ?>
</div>
<?php endif; ?>
