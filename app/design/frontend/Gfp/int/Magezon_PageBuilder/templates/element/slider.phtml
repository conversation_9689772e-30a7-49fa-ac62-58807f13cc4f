<?php

use Cope<PERSON>\HyvaTheme\ViewModel\Image;
use CopeX\Swiper\ViewModel\Swiper;
use Hyva\Theme\Model\ViewModelRegistry;


/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Framework\View\Element\Template $block */

/** @var ViewModelRegistry $viewModels */

/** @var Swiper $swiper */



$swiper = $viewModels->require(Swiper::class);
$image = $viewModels->require(Image::class);

?>

<?php
$coreHelper                  = $this->helper('\Magezon\Core\Helper\Data');
$builderHelper               = $this->helper('\Magezon\Builder\Helper\Data');
$element                     = $this->getElement();
$items                       = $this->toObjectArray($element->getItems());
$sliderHeight                = $element->getData('slider_height') ?? null;
$lightbox                    = $element->getData('lightbox');
$loop                        = $element->getData('owl_loop') ? "true": "false";
$dots                        = $element->getData('owl_dots');
$maxResolutionLightbox       = 1200;
$slidesPerViewDesktop        = $element->getData('slidesPerViewDesktop') ?? 3.15;
$slidesPerViewTablet         = $element->getData('slidesPerViewTablet') ?? 2.15;
$slidesPerViewMobile         = $element->getData('slidesPerViewMobile') ?? 1;

$extraConfig = 'breakpoints: { 768: { slidesPerView: ' . $slidesPerViewTablet . '}, 1200: {slidesPerView: ' . $slidesPerViewDesktop . '} }, slidesPerView: ' . $slidesPerViewMobile.', loop: '.$loop;

$images = array_map(function ($item) use($builderHelper, $sliderHeight, $image, $maxResolutionLightbox) {
    if ($sliderHeight) {
        return [
            'url' => $image->resize($builderHelper->getImageUrl($item['image']),null, $sliderHeight),
            'lightbox' => $image->resize($builderHelper->getImageUrl($item['image']), $maxResolutionLightbox, $maxResolutionLightbox),
            'label' => $item['caption1'] ?: $item['caption2'] ?: $item['heading'] ?: pathinfo($item['image'],PATHINFO_FILENAME),
            'height' => $sliderHeight,
            'width' => null,
            "class" => 'md:pb-9',
            "style" => 'max-height: ' . $sliderHeight . 'px'
        ];
    }
    return [
        'url' => $builderHelper->getImageUrl($item['image']),
        'label' => $item['caption1'] ?: $item['caption2'] ?: $item['heading'] ?: pathinfo($item['image'],PATHINFO_FILENAME)
    ];
}, $items);

$swiperBlock = $swiper->getSwiper();

$swiperBlock->setPagination($dots)->setThumbs(false)->setLightbox($lightbox);
$swiperBlock->setExtraConfig("{ " . $extraConfig . " }");

$swiperBlock->setGalleryImages($images);

?>
<div x-data="{ loaded: false }" x-intersect.once.margin.-200px="loaded=true">
    <template x-if="loaded">
        <div x-data="{}" x-init="window.evaluateScriptsByElement($el)">
            <?= $swiperBlock->toHtml(); ?>
        </div>
    </template>
</div>
<?php
$swiperBlock->setExtraConfig("{ }");
?>
