<?php

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductCompare;
use Hyva\Theme\ViewModel\ProductList;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\Slider;
use Hyva\Theme\ViewModel\Store;
use Hyva\Theme\ViewModel\Wishlist;
use Magento\Catalog\Model\Product\Visibility as ProductVisibility;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Catalog\Block\Product\ReviewRendererInterface as ProductReviewRenderer;


/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var ProductListItem $productListItemViewModel */
$productListItemViewModel = $viewModels->require(ProductListItem::class);
$sliderViewModel      = $viewModels->require(Slider::class);
$productListViewModel = $viewModels->require(ProductList::class);
$productViewModel     = $viewModels->require(ProductPage::class);
$wishlistViewModel    = $viewModels->require(Wishlist::class);
$compareViewModel     = $viewModels->require(ProductCompare::class);
$storeViewModel       = $viewModels->require(Store::class);

$element              = $this->getElement();
$coreHelper           = $this->helper('\Magezon\Core\Helper\Data');
$headline             = $coreHelper->filter($element->getData('title'));
$sliderId             = time() . uniqid();
$width                = "w-full";
$items                = array_map(function($item){return $item[0]; },$this->getItems());
$itemCount            = count($items);
$titleTag             = $element->getData('title_tag') ? $element->getData('title_tag') : 'h3';
$titleAlign           = $element->getData('title_align') ?? 'left';
$description          = $coreHelper->filter($element->getData('description'));

$options      = $this->getOwlCarouselOptions();

$config = [
    'autoHeight' => false,
    'breakpoints' => [
        576 => [
            'slidesPerView' => $options['item_sm'],
            'centeredSlides' => false
        ],
        768 => [
            'slidesPerView' => $options['item_md'],
            'centeredSlides' => false
        ],
        992 => [
            'slidesPerView' => $options['item_lg'],
            'centeredSlides' => false,
        ],
        1200 => [
            'slidesPerView' => $options['item_xl'],
            'centeredSlides' => false,
        ]
    ],
    'slidesPerView' => $options['item_xs'],
    'loop' => $options['loop'],
    'centeredSlides' => $options['center'] ?? true,
    'watchSlidesProgress' => true,
    'watchOverflow' => true
];

// Add autoplay configuration if enabled
if ($options['autoplay']) {
    $config['autoplay'] = [
        'delay' => $options['autoplaySpeed'] ?? 3000,
        'pauseOnMouseEnter' => $options['autoplayHoverPause']
    ];
}

$extraConfig = json_encode($config);

$slidesPerView = $options['slideBy'] ?? 1;
$mainArrowColor = 'currentColor';
$swiperWrapperClass =  "swiper-wrapper items-stretch";
$swiperSlideClass = "swiper-slide px-2 pb-2 flex flex-col w-full md:w-1/2 lg:w-1/3";
$swiperActiveClass = "swiper-slide-visible swiper-slide-active";

$sliderItemRenderer = $block->getChildBlock('slider.item.template') ?:
    $block->getLayout()->getBlock('product_list_item') ?:
        $block->getLayout()->createBlock(Template::class);

$itemTemplate      = 'CopeX_Swiper::slider/crosssell-product-item.phtml';

$sliderItemRenderer->setTemplate($itemTemplate);
$hideDetails       = (bool) $block->getData('hide_details');
$showDescription = $element->getData('product_shortdescription') ?? false;
$sliderItemRenderer->setData('hide_details', !$showDescription);
$block->setData('hide_rating_summary', !$element->getData('product_review'));
$viewMode = 'grid';
$imageDisplayArea              = 'category_page_grid';
$pagination = $options['dots'];
$sliderItemRenderer->setData('lazyload', $element->getData('owl_lazyload'));



//
//$compareHelper        = $this->helper('Magento\Catalog\Helper\Product\Compare');

//$lazyLoad             = $element->getData('owl_lazyload');
//$templateType         = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
//$showImage            = $element->getData('product_image');
//$showName             = $element->getData('product_name');
//$showPrice            = $element->getData('product_price');
//$showWishlist         = $element->getData('product_wishlist');
//$showCompare          = $element->getData('product_compare');
//$showCart             = $element->getData('product_addtocart');
//$swatches             = $element->getData('product_swatches');
//$htmlId               = $element->getHtmlId();
//
//$classes              = $this->getOwlCarouselClasses();
?>
<style>
    #s<?=$sliderId?> .ob-notice {
        justify-content: flex-end;
    }
</style>


<div class="min-w-0 -mr-2 md:-mr-4">

    <?php if ($headline): ?>
        <<?=$titleTag ?> class="mx-0 mb-5 mt-12 text-xl font-semibold text-<?= $titleAlign ?> text-gfpgreen uppercase"><?= $headline ?></<?=$titleTag ?>>
    <?php endif; ?>
    <?php if ($description) { ?>
        <div class="info"><?= $description ?></div>
    <?php } ?>
    <div x-data="initSwiper<?= $sliderId ?>()" id="s<?=$sliderId?>"
         class="bg-white">
        <div class="swiper-main relative <?= $width ?> mx-auto swiper">
            <div class="mx-0 md:mx-6 my-2 overflow-hidden pb-10 mr-0">
                <?php if($itemCount > $slidesPerView): ?>
                    <div class="absolute inset-y-0 -left-4 z-10 flex items-center">
                        <button aria-label="prev"
                                class="crosssell-slider-prev flex justify-center items-center ml-7 w-10 h-10 focus:outline-none bg-white shadow rounded-md">
                            <svg viewBox="0 0 20 20" fill="<?= $mainArrowColor ?>" class="chevron-left w-20 h-20">
                                <use href="#chevron-left"></use>
                            </svg>
                        </button>
                    </div>
                <?php endif; ?>
                <div class="swiper-main-container swiper-container" x-ref="container_<?= $sliderId ?>">
                    <div class="<?= $swiperWrapperClass ?>">
                        <?php $itemCounter = 0; ?>
                        <?php foreach ($items as $id => $product) : ?>
                            <?php if ($product->getIsSalable()) : ?>
                                <div class="<?= $swiperSlideClass ?> <?= $itemCounter++ < $slidesPerView ? $swiperActiveClass : ""?>" style="height: auto;">
                                    <?= /** @noEscape */
                                    $productListItemViewModel->getItemHtmlWithRenderer(
                                        $sliderItemRenderer,
                                        $product,
                                        $block,
                                        $viewMode,
                                        "mgz_slider",
                                        $imageDisplayArea,
                                        $showDescription
                                    ) ?>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                    <?php if($pagination) : ?>
                        <div class="swiper-pagination md:mb-0"></div>
                    <?php endif; ?>
                    <?php if($block->getAfterMain()) : ?>
                        <?= $block->getAfterMain(); ?>
                    <?php endif; ?>
                </div>
                <?php if($itemCount > $slidesPerView): ?>
                    <div class="absolute inset-y-0 -right-8 mr-4 z-10 flex items-center">
                        <button aria-label="next"
                                class="crosssell-slider-next flex justify-center items-center mr-7 w-10 h-10 focus:outline-none bg-white shadow rounded-md">
                            <svg viewBox="0 0 20 20" fill="<?= $mainArrowColor ?>" class="chevron-right w-20 h-20">
                                <use href="#chevron-right"></use>
                            </svg>
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <script>
        function initSwiper<?= $sliderId ?>() {
            return Object.assign( {
                swiper: null,
                thumb: null,
                baseConfig: {
                    loop: <?= $block->getLoop() ?: "true" ?>,
                    spaceBetween:  5,
                    freeMode: false,
                    lazy: {loadPrevNext: true, checkInView: true, loadOnTransitionStart: true}
                },
                init() {
                    swiperInit.init( () => {
                        const swiper =  new Swiper(this.$refs.container_<?= $sliderId ?>, Object.assign({},this.baseConfig,
                            {
                                slidesPerView:  <?= $slidesPerView ?: "1" ?>,
                                <?php if($pagination): ?>
                                pagination: { el:  '.swiper-main .swiper-pagination', dynamicBullets: true, dynamicMainBullets: 10},
                                <?php endif; ?>
                                enabled: <?= $itemCount > $slidesPerView ? "true" : "false";?>,
                                navigation: {
                                    nextEl: '.crosssell-slider-next',
                                    prevEl: '.crosssell-slider-prev',
                                },
                            },
                            <?= $extraConfig ?: "{}"?>
                        ));
                        if(window.innerWidth < 786){
                            swiper.slideNext(10);
                            setTimeout(() => {
                                swiper.slidePrev(10);
                            },3000);
                        }
                        this.swiper = swiper;
                    });
                }
            },  "{}" );
        }
    </script>
</div>