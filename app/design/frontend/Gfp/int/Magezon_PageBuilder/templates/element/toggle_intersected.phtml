<?php
$coreHelper    = $this->helper('\Magezon\Core\Helper\Data');
$element       = $this->getElement();
$toggleTitle   = $element->getData('toggle_title');
$toggleContent = $element->getData('toggle_content');
$icon          = $element->getData('icon');
$activeIcon    = $element->getData('active_icon');
$iconStyle     = $element->getData('icon_style');
$iconSize      = $element->getData('icon_size');
$open          = $element->getData('open');
$id            = $element->getId();
if ($iconStyle =='text_only') $icon = $activeIcon = '';
?>
<div x-data="{ loaded: false }" x-intersect.once.margin.-200px="loaded=true">
    <template x-if="loaded">
        <div class="mgz-toggle <?= $icon ? 'mgz-toggle-icon' : '' ?> mgz-toggle-icon-<?= $iconStyle ?> mgz-toggle-icon-size-<?= $iconSize ?>"
             x-data="{active: <?= $open ? 'true' : 'false' ?>}"
             :class="{'mgz-active': active === true }"
        >
            <?php if ($toggleTitle) { ?>
                <div class="mgz-toggle-title" @click="active = !active">
                    <span x-show="active" class="<?= $activeIcon ?>" data-role="icons"></span>
                    <span x-cloak x-show="!active" class="<?= $icon ?>" data-role="icons"></span>
                    <h4>
                        <span><?= $toggleTitle ?></span>
                    </h4>
                </div>
            <?php } ?>
            <?php if ($toggleContent) { ?>
                <div class="mgz-toggle-content overflow-hidden duration-700 transition-all max-h-0" x-ref="<?= $id ?>"
                     x-bind:style="active == true ? 'max-height: ' + $refs.<?= $id ?>.scrollHeight + 'px;' : ''"
                >
                    <?= $coreHelper->filter($toggleContent) ?>
                </div>
            <?php } ?>
        </div>
    </template>
</div>