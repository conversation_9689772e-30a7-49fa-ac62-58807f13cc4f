<?php
/** @var \Magento\Framework\Escaper $escaper */
/** @var $block \Magento\Framework\View\Element\Template */
?>
<script defer>
    var lazyLoadInstance = null;

    const initCustomScripts = function(){
        if (document.querySelectorAll('.lazy').length || document.querySelectorAll('.lzy_img').length) {
            lazyLoadJs('<?= $escaper->escapeUrl($block->getViewFileUrl("js/lazy.js")) ?>').then(
                () => {
                    lazyLoadInstance = new LazyLoad({
                        elements_selector: ".lazy, .lzy_img"
                    });
                }
            );
        }
        if (document.querySelectorAll('.js-navigation a').length || window.location.hash) {
            lazyLoadJs('<?= $escaper->escapeUrl($block->getViewFileUrl("js/jsNavigation.js")) ?>').then(
                () => {
                    jsNavigation.init('.js-navigation a');
                }
            );
        }
    }

    if(document.readyState !== 'loading'){
        initCustomScripts();
    }
    else {
        window.addEventListener("load", () => {initCustomScripts()});
    }
    window.addEventListener("content-load-after", () => {initCustomScripts()});

</script>