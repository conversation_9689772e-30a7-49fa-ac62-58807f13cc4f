<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */
?>
<script>
    function initCartDrawer() {
        return {
            open: false,
            cart: {},
            getData(data) {
                if (data.cart) {
                    this.cart = data.cart
                    this.setCartItems();
                }
            },
            cartItems: {},
            setCartItems() {
                if (this.cart.items) {
                    this.cartItems = this.cart && this.cart.items.sort(function(a,b) { return a.item_id - b.item_id }) || {}
                }
            },
            deleteItemFromCart(itemId) {
                fetch(BASE_URL+"checkout/sidebar/removeItem/", {
                    "headers": {
                        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                    },
                    "body": "form_key="+ hyva.getFormKey() + "&item_id="+itemId,
                    "method": "POST",
                    "mode": "cors",
                    "credentials": "include"
                }).then(function (response) {
                    if (response.redirected) {
                        window.location.href = response.url;
                    } else if (response.ok) {
                        return response.json();
                    } else {
                        typeof window.dispatchMessages !== "undefined" && window.dispatchMessages(
                            [{
                                type: "warning",
                                text: "<?= $escaper->escapeHtml(__('Could not remove item from quote.')) ?>"
                            }], 5000
                        );
                    }
                }).then(function (response) {
                    typeof window.dispatchMessages !== "undefined" && window.dispatchMessages(
                        [{
                            type: response.success ? "success" : "error",
                            text: response.success
                                ? "<?= $escaper->escapeHtml(__('You removed the item.')) ?>"
                                : response.error_message
                        }], 5000
                    );
                    const reloadCustomerDataEvent = new CustomEvent("reload-customer-section-data");
                    window.dispatchEvent(reloadCustomerDataEvent);
                });
            }
        }
    }
</script>
<section id="cart-drawer" class=""
         x-data="initCartDrawer()"
         @private-content-loaded.window="getData(event.detail.data)"
         @toggle-cart.window="open=true;"
         @keydown.window.escape="open=false"
>
    <template x-if="cart && cart.summary_count">
        <div role="dialog"
             aria-labelledby="cart-drawer-title"
             aria-modal="true"
             @click.away="open = false"
             class="fixed inset-y-0 right-0 z-40 flex max-w-full">
            <div class="backdrop"
                 x-show="open"
                 x-transition:enter="ease-in-out duration-500"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="ease-in-out duration-500"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 @click="open = false"
                 aria-label="Close panel"></div>
            <div class="relative w-screen max-w-xl shadow-2xl"
                 x-show="open"
                 x-transition:enter="transform transition ease-in-out duration-500 sm:duration-700"
                 x-transition:enter-start="translate-x-full"
                 x-transition:enter-end="translate-x-0"
                 x-transition:leave="transform transition ease-in-out duration-500 sm:duration-700"
                 x-transition:leave-start="translate-x-0"
                 x-transition:leave-end="translate-x-full"
            >
                <div
                    x-show="open"
                    x-transition:enter="ease-in-out duration-500"
                    x-transition:enter-start="opacity-0"
                    x-transition:enter-end="opacity-100"
                    x-transition:leave="ease-in-out duration-500"
                    x-transition:leave-start="opacity-100"
                    x-transition:leave-end="opacity-0" class="absolute top-0 right-0 flex p-2 mt-2">
                    <button @click="open = false;" aria-label="Close panel"
                            class="p-2 text-gray-300 transition duration-150 ease-in-out hover:text-black">
                        <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                  stroke-width="2" d="M6 18L18 6M6 6l12 12">
                            </path>
                        </svg>
                    </button>
                </div>
                <div class="flex flex-col h-full py-6 space-y-2 md:space-y-6 bg-white shadow-xl">
                    <header class="px-4 sm:px-6">
                        <span id="cart-drawer-title" class="text-lg font-medium leading-7 text-gray-900 my-2 md:my-6">
                            <?= $escaper->escapeHtml(__('My Cart')) ?>
                        </span>
                    </header>
                    <div class="relative grid gap-6 px-4 py-6 overflow-y-auto bg-white border-b
                        sm:gap-8 sm:px-6 border-container">
                        <template x-for="item in cartItems">
                            <div class="flex items-start p-3 -m-3 space-x-4 transition duration-150
                                ease-in-out rounded-lg hover:bg-gray-100">
                                <a :href="item.product_url" class="w-1/3">
                                    <img
                                        :src="item.product_image.src"
                                        loading="lazy"
                                    />
                                </a>
                                <div class="w-2/3 space-y-2">
                                    <div>
                                        <p class="text-md">
                                           <span x-html="item.product_name"></span>
                                        </p>
                                    </div>
                                    <div x-show="item.options && item.options[0].label">
                                        <div @click="item.show = !item.show" class="cursor-default">
                                            <span><?= __("See Details") ?></span>
                                            <span x-show="!item.show">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
</svg>
                                            </span>
                                            <span x-show="item.show" style="display: none">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
</svg>
                                            </span>
                                        </div>
                                        <div x-show="item.show" x-transition>
                                            <template x-for="option in item.options">
                                                <div class="pt-0">
                                                    <p class="font-semibold mb-0" x-text="option.label + ':'"></p>
                                                    <p class="text-secondary mb-0" x-html="option.value"></p>
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                    <div class="flex justify-end items-start">
                                        <span>
                                            <span class="flex justify-end">
                                                <span class="font-normal mr-2" >
                                                    <span x-html="item.qty"></span> x
                                                </span>
                                                <span class="font-bold" x-html="item.product_price"></span>
                                            </span>
                                            <div x-show="item.product_price_value < item.product_original_price_value" class="flex justify-end items-center text-xs">
                                                <span><?= $escaper->escapeHtml(__('Regular Price')) ?></span>
                                                <span class="line-through ml-1" x-html="hyva.formatPrice(item.product_original_price_value)"></span>
                                            </div>
                                        </span>
                                        <button class="inline-flex px-2" @click="deleteItemFromCart(item.item_id)">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                        </button></div>

                                </div>
                            </div>
                        </template>
                    </div>
                    <div class="relative grid gap-2 px-4 py-1 bg-white">
                        <div class="w-full pl-3 pr-12 m-0 space-x-4 text-right">
                            <div class="flex justify-between items-center font-normal text-xl"><?= $escaper->escapeHtml(__('Total Summary')) ?> <span class="font-semibold" x-html="cart.subtotal"></span></div>
                            <div class="flex justify-end items-center"><?= $escaper->escapeHtml(__('Regular Price')) ?> <span class="line-through ml-1" x-html="cart.total_original_price"></span></div>
                            <div class="flex justify-end items-center"><span class="save text-gfpgreen-lighter" x-html="cart.total_reduction"></span></div>
                        </div>
                        <div class="w-full p-3 -m-3 space-x-4">
                            <a href="<?= $escaper->escapeUrl($block->getUrl('checkout/cart')) ?>"
                               class="inline-flex btn btn-primary w-full">
                                <?= $escaper->escapeHtml(__('Go to Checkout')) ?><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </a>
                        </div>
                        <?= $block->getChildHtml('extra_actions')  ?>
                    </div>
                </div>
            </div>
        </div>
    </template>
</section>
