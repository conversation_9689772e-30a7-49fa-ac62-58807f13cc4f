<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Theme\Block\Html\Title;

/** @var Escaper $escaper */
/** @var Title $block */

$cssClass = $block->getCssClass() ? ' ' . $block->getCssClass() : 'text-3xl';
$titleHtml = '';
if (trim((string)$block->getPageHeading())) {
    $titleHtml = '<span class="base" data-ui-id="page-title-wrapper" '
        . $block->getAddBaseAttribute()
        . '>'
        . $escaper->escapeHtml($block->getPageHeading())
        . '</span>';
}
?>
<?php if ($titleHtml): ?>
<div class="container flex flex-col md:flex-row flex-wrap md:mb-6 md:my-0 font-medium <?= /** @noEscape */ $cssClass ?>">
    <h1 class="page-title title-font m-0"
        <?php if ($block->getId()): ?> id="<?= $escaper->escapeHtmlAttr($block->getId()) ?>" <?php endif; ?>>
        <?= /* @noEscape */ $titleHtml ?>
    </h1>
    <?= $block->getChildHtml() ?>
</div>
<?php endif; ?>
