<script defer>
    function scrollToTop(){
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
</script>
<button onclick="scrollToTop()" class="transition z-20" style="opacity: 0;" id="backtotop">
    <span class="gt-text"><?= __('Back to the top of the page') ?></span>
    <span class="gt-arrow">^</span>
</button>
<script defer>
    const backtotop = document.querySelector('#backtotop');
    let lastScrollTop = 0;
    document.addEventListener('scroll', function(e) {
         if(window.scrollY > 300){
             const st = document.documentElement.scrollTop;
             if (st <= lastScrollTop) {
                 backtotop.style.opacity = "0.9";

                 if (window.screen.width < 768) {
                     setTimeout(function () {
                         backtotop.style.opacity = "0";
                     }, 3000);
                 }
             }
             lastScrollTop = st <= 0 ? 0 : st;
         }else {
             backtotop.style.opacity = "0";
         }
    });
</script>
