.page-main {
}

.table-row-items > div.table-row-item {

    @apply bg-container-darker;

    &:nth-child(2n + 1) {
        @apply bg-container-lighter;
    }
}

.page-wrapper {
    border-top: none;
    background: #F5F5F5;
}

.background-gradient{
    background-image: linear-gradient(rgb(255, 209, 25) 0%, rgb(220, 170, 40) 100%);
}

label {
    display: inline-block;
    max-width: 100%;
    margin-bottom: 5px;
    font-weight: 400;
}

img { @apply inline;}

.action {
    @apply background-gradient text-white;
    transition: color 0.4s ease 0s;
    border-radius: 5px;
    padding: 17px 15px;
    font-size: 16px;
    white-space: nowrap;
    content: normal;
    text-decoration: none;
    cursor: pointer;
    width: 100%;
    box-sizing: border-box;
}

.action.ghost{
    @apply py-0 px-4 w-auto text-center text-gfpyellow no-underline bg-none rounded border-2 border-gfpyellow cursor-pointer hover:no-underline focus:no-underline;
}

.action.small {
    @apply py-1 w-max;
}

strong { font-weight: 500;}

.swiper-pagination-bullet.swiper-pagination-bullet-active {
    @apply bg-gfpgreen;
}

.lightbox .swiper-pagination-bullet{
    background: #fff;
}

p {
    margin: 0 0 10px;
}

.shipping-countries{
    @apply text-gfpgreen;
}

.radiobutton {
    border-radius: 50%;
    height: 16px;
    width: 16px;
    display: inline-block;
    border: 1px solid #636d70;
    position: relative;
    bottom: 0;
}

*[role=tooltip], .tooltip{
    position: absolute;
    top: 2em;
    left: -6em;
    width: 20em;
    padding: 0.5em;
    z-index: 151;
    display: none;
    background-color: #fff;
    border-radius: 0.2em;
    box-shadow: 0 0 15px 0 rgba(0,0,0,0.5);
    @apply text-secondary-darker;
}

input[type="radio"]:checked + i.radiobutton:after{
    content: "";
    position: absolute;
    top: 3px;
    left: 3px;
    height: 8px;
    width: 8px;
    background: #636d70;
    border-radius: 50%;
}
ul.columns2{
    @apply md:table;
    > li {
        @apply md:w-1/2 md:inline-block;
    }
}

.checklist.checklist-black ul, ul.checklist.checklist-black {
    li {
        &:before {
            @apply text-black;
        }
    }
}

.checklist.checklist-yellow ul, ul.checklist.checklist-yellow {
    li {
        &:before {
            @apply text-gfpyellow;
        }
    }
}

.checklist ul, ul.checklist {
    li {
        list-style: none;
        line-height: normal;
        font-size: 17px;
        margin-bottom: 5px;
        position: relative;
        padding-left: 22px;
        &:before {
             @apply text-gfpgreen;
             content: "\f00c";
             font-family: 'Font Awesome 5 Free';
             font-weight: 900;
             padding: 0 2px 0 0;
             position: absolute;
             left:0;
         }
    }
}

span.dot {
    @apply mr-2 inline-block w-3 h-3 bg-center bg-red-500 rounded-full;
    &.available{
        @apply bg-gfpgreen-lighter;
     }
    &.soon{
         @apply bg-gfpyellow;
    }
    &.presale{
        background-color: rgba(255, 105, 0, var(--tw-bg-opacity));
    }
}

@layer utilities{
    @variants responsive {
        .flex-1\/1 {
            flex-basis: calc(100% - 15px);
        }
        .flex-1\/2 {
            flex-basis: calc(50% - 15px);
        }
        .flex-1\/3 {
            flex-basis: calc(33% - 15px);
        }
        .flex-1\/4 {
            flex-basis: calc(25% - 15px);
        }
    }

}

div[id^="trustbadge-container"] {
    z-index: 20 !important;
}

.btn-invert {
    background: #d3e4be;
    border-radius: 3px;
    display: table;
    margin: auto;
    font-weight: 500;
    font-size: 18px;
    width: 100%;
    margin-top: 5px;
    padding: 8px;
    span {
        color: #667751;
    }
}
