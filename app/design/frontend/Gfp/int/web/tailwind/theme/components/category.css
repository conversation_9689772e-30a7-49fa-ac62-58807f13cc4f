.category-view {
    .sidebar .block.filter {}
}

.products-grid {
    > div.flex {
        justify-content: center;
    }
    .product-item .price-box {
        display: flex;
        flex-direction: column;
        text-align: center;
        .price-container {
            .price {
                white-space: nowrap;
            }
        }

        .old-price {
            .price {
                text-decoration: line-through;
            }
        }
    }
}

.page-wrapper .top-container {
    @apply bg-white;

    .breadcrumbs {
        @apply py-2.5;
    }
}

.filtered-item-els {
    @apply 2xl:max-w-sm;
}

@media (max-width: 1023px) {
    .catalog-category-view .breadcrumbs-block {
        display: none;
    }
    .catalog-category-view .columns {
        display: block;
    }
    .catalog-category-view span.dot {
        display: none;
    }
    .catalog-category-view .list-price .final-price .price-wrapper {
        background-image: linear-gradient(rgb(51, 51, 51) 0%, rgb(51, 51, 51) 100%);
        -webkit-text-fill-color: inherit;
    }
    .list-view-block .normal-price .price-container {
        display: inline-block;
    }
    .catalog-category-view .list-price .price-box {
        &>span:not(.old-price):not(.save-info-block) span.price,
        .price-container .price {
            color: #333333;
        }
    }
    .catalog-category-view .grid-price-view .price-box {
        &>span:not(.old-price):not(.save-info-block) span.price,
        .price-container .price {
            color: #e8b723;
            font-size: 24px;
        }
    }
    .catalog-category-view .grid-price-view .old-price .price-container span.price {
        font-size: 16px;
        line-height: 24px;
        font-weight: 400;
        text-decoration: line-through;
        color: #333333;
    }
}

@media (min-width: 1024px) {

    .catalog-category-view .price-box .price-container .final-price span.price,
    .catalog-category-view .normal-price .price-container span.price {
        font-size: 24px;
        color: #e8b723;
    }
}

.product-item .old-price .price-container span.price {
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    text-decoration: line-through;
}


.catalog-category-view .list-price .price-box .price-container .final-price span.price,
.catalog-category-view .product-item .list-view .list-price .normal-price .price-container span.price {
    @media (max-width: 1023px) {
        font-size: 2rem;
    }
    @media (max-width: 767px) {
        font-size: 1.8rem;
    }
    @media (max-width: 639px) {
        font-size: 1.5rem;
    }
    @media (max-width: 499px) {
        font-size: 1.3rem;
    }
}

@media (max-width: 460px) {
    .catalog-category-view .product-item .list-view .rating-summary svg{
        width: 0.75rem;
        height: 0.75rem;
    }
}

@media (min-width: 768px) {
    .page-layout-2columns-left .products .product-item {
        flex-basis: calc(33% - 15px) !important;
    }
}
