form,
fieldset {
    .field {
        @apply mt-1
    }

    /* Reserve space for single line form validation messages */
    .field.field-reserved {
        & input, select, textarea {
            @apply mb-7
        }
    }

    .field.field-reserved ul {
        @apply -mt-6;
        @apply pb-1 text-sm leading-5 /* The padding + leading have to match the negative margin on the line above */
    }

    .field.field-reserved textarea {
        @apply align-top
    }

    label {
        @apply mb-2 block text-secondary-darker
    }

    .field.choice {
        @apply flex items-center
    }

    .field.choice input {
        @apply mr-4
    }

    .field.choice label {
        @apply mb-0
    }

    .field.field-error .messages {
        @apply text-red-500
    }

    legend {
        @apply text-primary text-xl mb-3
    }

    legend + br {
        @apply hidden
    }
}

fieldset ~ fieldset {
    @apply mt-8
}
