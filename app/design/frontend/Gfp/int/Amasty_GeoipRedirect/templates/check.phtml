<?php /** @var Magento\Framework\View\Element\Template $block */ ?>
<?php /** @var $escaper Magento\Framework\Escaper */ ?>

<?php $uniqueSciptId = md5($block->getNameInLayout()) ?>
<div x-cloak id="amredirect-popup" x-data="{...hyva.modal({duration: 100, dialog:'geoippopup'}), ...initGeoIpPopup_<?= $uniqueSciptId ?>()}" x-init="$nextTick(() => {check($data)})">
    <div x-cloak x-bind="overlay()" class="fixed inset-0 flex items-center justify-center text-left bg-black bg-opacity-50 z-50" >
        <div x-ref="geoippopup" role="dialog" aria-labelledby="the-label"
             class="inline-block overflow-auto bg-white shadow-xl rounded py-4 mx-2 px-2 md:px-4 max-w-screen-xl max-h-screen w-full">
        </div>
    </div>
</div>
<script defer>
    function initGeoIpPopup_<?= $uniqueSciptId ?>() {
        let $data = false;
        return {
            check: async (self) => {
                this.$data = self;
                this.childElementCount && this.children[0].classList.remove('hidden');
                if (!sessionStorage.getItem('popupShowed')) {
                    await fetch("<?= $block->getUrl('geoipcheck/index/check') ?>?isAjax=true", {
                        dataType: 'json', method: "GET", cache: 'no-cache', async: true, headers: { 'X-Requested-With': 'geoip' }
                    }).then(response => response.json()).then((data) => {
                        data.show ? self.getPopup() : sessionStorage.setItem('popupShowed', '1');
                    });
		        }
            },
            getPopup: () => {
                fetch("<?= $block->getUrl('geoipcheck/index/popup') ?>?isAjax=true", {
                    method: "GET", async: true, cache: 'no-cache', headers: { 'X-Requested-With': 'XMLHttpRequest' }
                }).then(async response => await response.json()).then((data) => {
                    if (!data.content) return false;
                    let placeholder = this.$data.$refs['geoippopup']; placeholder.innerHTML = data.content;
                    placeholder.querySelectorAll('.popup_button').forEach(element => {
                        element.addEventListener('click', (e) => {
                            e.preventDefault();
                            const targetStore = e.currentTarget.getAttribute('href');
                            if(targetStore){
                                const elem = document.getElementById('language-switcher-'+targetStore.substring(1,targetStore.length-1));
                                if(elem) elem.click();
                            }
                        })
                    });
                    window.dispatchEvent(new CustomEvent("content-load-after"));
                    window.dispatchEvent(
                        new CustomEvent('hyva-modal-show', {detail: {dialog: 'geoippopup'}})
                    )
                });
            },
            accept: function () {
                fetch("<?= $block->getUrl('geoipredirect/redirect/accept') ?>", { method: "POST", async: false }).then( () => {
                    sessionStorage.setItem('popupShowed', '1'); location.reload();
                });
            },
            decline: function () {
                this.$data.hide('geoippopup'); sessionStorage.setItem('popupShowed', '1');
                fetch("<?= $block->getUrl('geoipredirect/redirect/decline') ?>", { method: "POST", async: true });
            }
        }
    }
    document.addEventListener('DOMContentLoaded', () => {
        if (hyva.getCookie('store')) return;
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('___store')) {
            hyva.setCookie('store', urlParams.get('___store'), {'lifetime': (86400 * 30), 'samesite': 'Lax'});
        }
    });
</script>
