
function checkStoreCookie () {
    if (hyva.getCookie('store')) {
        return;
    }
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('___store')) {
        const options = {'lifetime': (86400 * 30), 'samesite': 'Lax'},
            store = urlParams.get('___store');
        hyva.setCookie('store', store, options);
    }
}
document.addEventListener('DOMContentLoaded', function(e) {
    checkStoreCookie();
});

