<?php

declare(strict_types=1);

?>

<template x-if="searchResult.type == 'product'">
    <a class="w-full flex hover:bg-secondary lg:my-2" :href="searchResult.url" :title="searchResult.title">
        <div class="search-ite pr-3 flex w-full flex-col items-center md:flex-row">
            <div class="text-left">
                <img :src="searchResult.image" class="inline-block search-img" />
            </div>
            <div class="flex flex-col w-full my-2">
                <div class="pl-4 text-center md:text-left">
                    <span class="font-semibold product-title text-lg uppercase leading-5 " x-text="searchResult.title"></span>
                </div>
                <div class="block md:flex pl-4 flex-grow">
                    <div class="listdescription-block flex-grow text-xs mt-2 checklist p-0" x-html="searchResult.listdescription || ''"></div>
                    <template x-if="searchResult.price">
                        <div class="pr-container text-sm font-bold flex justify-end	items-end w-full md:w-1/3 text-right" x-html="searchResult.price"></div>
                    </template>
                    <template x-if="!searchResult.price">
                        <div class="pr-container out-descr text-sm font-bold flex justify-end items-end">
                            <div class="stock unavailable mt-2">
                                <span><?= __('Out of stock') ?></span>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </a>
</template>
