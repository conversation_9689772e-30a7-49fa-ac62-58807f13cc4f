<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Advanced Review Hyva
 */

use Amasty\AdvancedReview\Model\Toolbar\Applier;
use Amasty\AdvancedReviewHyva\ViewModel\AdditionalData;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Framework\Escaper;
use Magento\Review\Block\Product\View;

/**
 * @var View $block;
 * @var Escaper $escaper;
 * @var ViewModelRegistry $viewModels
 */

$heroicons = $viewModels->require(HeroiconsOutline::class);
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);
$additionalViewModel = $viewModels->require(AdditionalData::class);
$advancedHelper = $block->getData('advanced-review-helper');
$reviewCollection = $block->getReviewsCollection();
$reviewCollection->load()->addRateVotes();
$items = $reviewCollection->getItems();

/* configs */
$headingTag = $block->getData('heading_tag') ?: 'h3';
$isFilterApplied = $reviewCollection->getFlag(Applier::COLLECTION_FLAG);
$filterStars = $reviewCollection->getFlag('filter_by_stars');
?>

<script>
    'use strict';
    function amReviews() {
        return Object.assign({
                reviewSelectors: {
                    customerReviewList: '#reviews'
                },
                eventListeners: {
                    ['@amreviews-filter.window'](event) {
                        this.filterReviews(event.detail.url);
                    }
                },

                /**
                 * @param {string} url
                 * @returns {void}
                 */
                filterReviews: function (url) {
                    fetch(url, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    }).then(response => {
                        return response.ok ? response.text() : Promise.reject(`${response.status} ${response.statusText}`);
                    }).then(result => {
                        !!result && hyva.replaceDomElement(this.reviewSelectors.customerReviewList, result);
                        this.$nextTick(() => {
                            this.scrollToReviewsBlock();
                        })
                    }).catch(error => {
                        typeof window.dispatchMessages !== "undefined" && window.dispatchMessages([
                            {
                                type: "error",
                                text: error
                            }
                        ]);
                    })
                },

                /**
                 * @returns {void}
                 */
                scrollToReviewsBlock: function () {
                    document.querySelector(this.reviewSelectors.customerReviewList).scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            },
            typeof amReviewCommentsHandler === 'function' ? amReviewCommentsHandler() : {}
        );
    }
</script>

<div class="mx-auto py-6 items-center review-list customer-reviews"
     id="reviews"
     aria-label="<?= $escaper->escapeHtmlAttr(__('Customer Reviews')) ?>"
     x-data="amReviews()"
     x-bind="eventListeners">
    <div class="container mx-auto flex pt-6 pb-3 mb-6 md:flex-row border-b-2 border-gray-300">
        <?php if (!$block->getHideTitle()): ?>
        <<?= /* @noEscape */ $headingTag ?> class="text-gray-900 text-2xl title-font font-base text-center md:text-left w-full">
        <?= $escaper->escapeHtml(__('Customer Reviews')) ?>
    </<?= /* @noEscape */ $headingTag ?>>
    <?php endif ?>
</div>
<div class="card p-9"
    <?php if ($advancedHelper->isCommentsAllowed()): ?>
        x-intersect.once="loadComments()"
    <?php endif; ?>
>
    <div class="amreview-info-wrapper flex justify-between flex-wrap min-h-[9rem]">
        <?= /* @noEscape */ $advancedHelper->getReviewsSummaryHtml($block->getProduct(), $reviewCollection) ?>
        <?= $block->getChildHtml('amasty.reviews.write_review_button') ?>
    </div>
    <div class="amreview-title-block flex my-8 align-middle items-center">
        <p class="text-xl font-normal text-2xl"><?= $escaper->escapeHtml(__('Top customer reviews')) ?>
            <?php if ($isFilterApplied && $filterStars): ?>
                with <?= $escaper->escapeHtml($filterStars) ?> stars |
                <button title="<?= $escaper->escapeHtmlAttr(__('Show All')) ?>"
                        @click="window.dispatchEvent(new CustomEvent('am-stars-filter-clear', {
                                detail: {
                                    productUrl: '<?= $escaper->escapeUrl($advancedHelper->getProductReviewUrl(['id' => $block->getProductId()])) ?>'
                                }
                            }))"
                        class="reset-star-filter text-blue-600 hover:underline hover:cursor-pointer">
                    <?= $escaper->escapeHtmlAttr(__('Show All')) ?>
                </button>
            <?php endif ?>
        </p>
    </div>

    <?= /* @noEscape */ $advancedHelper->getReviewToolbarHtml($reviewCollection) ?>

    <div class="amrev-items-block w-full px-6 py-3">
        <?php if (count($items)): ?>
            <ol class="items amreview-review-items">
                <?php foreach ($items as $review): ?>
                    <li class="review-item py-8 justify-between flex-wrap border-solid border-b border-gray-400 block md:flex"
                        data-amreview-js="review-entity"
                        data-amreview-id="<?= /* @noEscape */ (int)$review->getId() ?>"
                        itemscope itemprop="review"
                        itemtype="http://schema.org/Review"
                        x-data="amReviewItem()">
                        <div class="amreview-author-details md:w-1/4 relative mb-4">
                            <p class="amreview-author break-all text-xl" itemprop="author">
                                <?= $escaper->escapeHtml($review->getNickname()) ?>
                            </p>
                            <?= $block->getChildBlock('amasty.reviews.verified_buyer')->setData('review', $review)->toHtml() ?>
                            <p class="amreview-date text-gray-500 text-xs mb-5 mt-1 md:static absolute top-0 right-0">
                                <time class="review-details-value"
                                      itemprop="datePublished"
                                      datetime="<?= $escaper->escapeHtmlAttr($additionalViewModel->formatDate($block, $review->getCreatedAt())) ?>">
                                    <?= $escaper->escapeHtml($additionalViewModel->formatDate($block, $review->getCreatedAt())) ?>
                                </time>
                            </p>
                            <?= $block->getChildBlock('amasty.reviews.recommended')->setData('review', $review)->toHtml() ?>
                        </div>
                        <div class="amreview-description-wrap md:w-3/4 max-w-[100%]">
                            <div class="amrev-desc-title items-center flex justify-between">
                                <span class="amreview-title-review pb-2 text-xl font-bold" itemprop="name">
                                    <?= $escaper->escapeHtml($review->getTitle()) ?>
                                </span>
                                <?= $advancedHelper->getHelpfulHtml($review, true) ?>
                            </div>
                            <?php if (count($review->getRatingVotes())): ?>
                                <?php foreach ($review->getRatingVotes() as $vote): ?>
                                    <p class="amreview-rating-label text-2xl text-gray-500">
                                        <?= $escaper->escapeHtml($vote->getRatingCode()) ?>
                                    </p>
                                    <?= $block->getChildBlock('amasty.reviews.amstars_rating')
                                        ->setData('stars', $vote->getValue())
                                        ->setData('rating', $vote->getPercent())
                                        ->toHtml()
                                    ?>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            <div class="amreview-description text-base my-4 break-words">
                                <?= $block->getChildBlock('amasty.reviews.description')->setData('review', $review)->toHtml() ?>
                                <?= /* @noEscape */ $advancedHelper->getReviewImagesHtml($review->getId()) ?>
                            </div>
                            <?php $answer = $advancedHelper->getReviewAnswerHtml($review) ?>
                            <?php if ($answer): ?>
                                <div class="amreview-adminanswer-block pt-1 pl-3">
                                    <p class="amreview-title font-semibold">
                                        <?= $escaper->escapeHtml(__('Response from store')); ?>
                                    </p>
                                    <div class="amreview-answer mt-1 text-gray-500">
                                        <?= /* @noEscape */ nl2br($escaper->escapeHtml($answer)); ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <div class="amrev-comment-toolbar flex items-center flex-wrap lg:flex-nowrap <?= $advancedHelper->isCommentsAllowed() ? 'my-4' : '' ?>">
                                <?php if ($advancedHelper->isCommentsAllowed()): ?>
                                    <div class="amrev-reply md:border-r border-solid border-gray-700 pr-4 order-2 lg:order-1 basis-full md:basis-2/4 lg:basis-auto">
                                        <button class="amrev-reply-btn btn w-full justify-center"
                                                :class="reply ? 'btn-primary border-2 border-blue-600' : 'btn-secondary text-blue-700'"
                                                @click="toggleReply()">
                                            <?= $heroicons->replyHtml('', 13, 14, ["aria-hidden" => "true"]) ?>
                                            <span class="mx-2"><?= $escaper->escapeHtml(__('Reply')); ?></span>
                                        </button>
                                    </div>
                                    <div class="amrev-comment lg:border-r border-solid border-gray-700 order-3 lg:order-2 basis-full md:basis-2/4 lg:basis-auto">
                                        <button class="amrev-comments-btn btn btn-secondary !shadow-none text-blue-700 !border-0 hover:text-blue-950 w-full justify-center"
                                                @click="toggleComments()">
                                            <span class="mr-2" :class="{'rotate-180': comments}" x-show="qtyComments > 0">
                                                <?= $heroicons->chevronDownHtml('text-blue-700', 16, 16, ["aria-hidden" => "true"]) ?>
                                            </span>
                                            <?= $escaper->escapeHtml(__('Comments')); ?>
                                            (<span x-text="qtyComments"
                                                   @update-qty-comments-review-<?= (int)$review->getId() ?>.window="qtyComments = event.detail.qty">
                                            </span>)
                                        </button>
                                    </div>
                                <?php endif; ?>
                                <?= $advancedHelper->getHelpfulHtml($review) ?>
                            </div>
                            <?php $like = $review->getData('like_about'); ?>
                            <?php $disLike = $review->getData('not_like_about'); ?>
                            <?php if ($advancedHelper->isProsConsEnabled() && ($like || $disLike)): ?>
                                <div class="amreview-proscons-container flex break-words md:flex-row flex-col">
                                    <?php if ($like): ?>
                                        <p class="amreview-proscons -like relative basis-2/4 text-green-600 py-0.5">
                                            <?= $heroiconsSolid->plusCircleHtml('inline-block', 20, 20, ["aria-hidden" => "true"]) ?>
                                            <?= /* @noEscape */ nl2br($escaper->escapeHtml($like)); ?>
                                        </p>
                                    <?php endif; ?>
                                    <?php if ($disLike): ?>
                                        <p class="amreview-proscons -dislike relative basis-2/4 text-red-600 py-0.5">
                                            <?= $heroiconsSolid->minusCircleHtml('inline-block', 20, 20, ["aria-hidden" => "true"]) ?>
                                            <?= /* @noEscape */ nl2br($escaper->escapeHtml($disLike)); ?>
                                        </p>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                            <div class="amrev-comment-block bg-zinc-50 mt-3"
                                 data-amreview-js="comment-block-review-id-<?= /* @noEscape */ (int)$review->getId() ?>">
                            </div>
                        </div>
                    </li>
                <?php endforeach; ?>
            </ol>
            <script>
                'use strict';

                function amReviewItem() {
                    return {
                        reply: false,
                        comments: false,
                        qtyComments: 0,

                        /**
                         * @returns {void}
                         */
                        toggleReply: function () {
                            if (this.comments && !this.reply) {
                                this.reply = true;

                                return;
                            }

                            if (this.comments && this.reply) {
                                this.reply = false;
                                this.comments = false;

                                return;
                            }

                            this.reply = !this.reply;
                        },

                        /**
                         * @returns {void}
                         */
                        toggleComments: function () {
                            if (!this.qtyComments) {
                                return;
                            }

                            if (this.reply && !this.comments) {
                                this.comments = true;

                                return;
                            }

                            this.comments = !this.comments;
                            this.toggleReply();
                        }
                    }
                }
            </script>
        <?php else: ?>
            <?= $block->getChildHtml('amasty.reviews.no_reviews') ?>
        <?php endif; ?>
    </div>
</div>
