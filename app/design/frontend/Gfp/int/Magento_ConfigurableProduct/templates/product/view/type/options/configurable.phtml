<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
?>

<?php
/** @var $block \Magento\ConfigurableProduct\Block\Product\View\Type\Configurable*/
$_product    = $block->getProduct();
$_attributes = $block->decorateArray($block->getAllowAttributes());
?>
<?php if ($_product->isSaleable() && count($_attributes)):?>
    <?php
    /** @var Xortex\Xinfo\Helper\Data $xinfoHelper */
    $xinfoHelper = $this->helper('Xortex\Xinfo\Helper\Data');
    $_description = __("Please choose your set offer:");
    foreach ($_attributes as $attr) {
        if($attr->getProductAttribute()->getAttributeCode() == "set")
            $_description = __("Please choose your set offer") . ":";
        else
            $_description = __("Please choose your color variation") . ":";
    }
    ?>
    <p class="config-addtocart-description"><?php echo $_description; ?></p>
    <?php foreach ($_attributes as $_attribute): ?>
        <div class="field configurable required">
            <label class="label" for="attribute<?= $block->escapeHtmlAttr($_attribute->getAttributeId()) ?>">
                <span><?= $block->escapeHtml($_attribute->getProductAttribute()->getStoreLabel()) ?></span>
                <?php
                // Info Button für das Attribute cms-block Identifier = xinfo-ATTRIBUTECODE
                echo $xinfoHelper->getInfoButton("xinfo-".$_attribute->getProductAttribute()->getAttributeCode());
                ?>
            </label>
            <div class="control">
                <select name="super_attribute[<?= $block->escapeHtmlAttr($_attribute->getAttributeId()) ?>]"
                        data-selector="super_attribute[<?= $block->escapeHtmlAttr($_attribute->getAttributeId()) ?>]"
                        data-validate="{required:true}"
                        id="attribute<?= $block->escapeHtmlAttr($_attribute->getAttributeId()) ?>"
                        class="super-attribute-select">
                    <option value=""><?= $block->escapeHtml(__('')) ?></option>
                </select>
            </div>
        </div>
    <?php endforeach; ?>
<?php endif;?>
