<?php
$_helper = $this->helper('<PERSON><PERSON><PERSON>\Catalog\Helper\Output');
$_product = $block->getProduct();
$_call = "getDetailbottomfeature1()";
$_code = "detailbottomfeature";

?>

<div class="bottomfeature-list">
    <?php for ($i = 1; $i <= 6; $i++) :
        if ($_product->getData($_code . $i)) : ?>
            <div class="column row">
                <?php echo $_product->getResource()->getAttribute($_code . $i)->getFrontend()->getValue($_product); ?>
                <?php echo /* @escapeNotVerified */ $_helper->productAttribute($_product, $_product->getDetailbottomfeature1(), 'detailbottomfeature1') ?>
            </div>
    <?php endif; endfor; ?>
</div>
