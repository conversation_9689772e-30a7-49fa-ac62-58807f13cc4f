<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
?>

<button type="submit"
        form="product_addtocart_form"
        title="<?= $escaper->escapeHtmlAttr(__('Add to Cart')) ?>"
        class="btn btn-primary flex-grow"
        id="product-addtocart-button"
>
    <span class="block text-2xl md:text-xl"><?= $block->getData('is_cart_configure') ?
            $escaper->escapeHtml(__('Update item')) :
            $escaper->escapeHtml(__('Add to Cart')) ?>
    </span>
</button>

<?= $block->getChildHtml('', true) ?>
