<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\LocaleFormatter;
use Magento\Catalog\Block\Product\ProductList\Toolbar;
use Magento\Framework\Escaper;

/** @var Toolbar $block */
/** @var Escaper $escaper */
/** @var LocaleFormatter $localeFormatter */
?>
<p class="toolbar-amount text-sm leading-5 text-gray-700 order-3 sm:order-2 md:order-3
    lg:order-2 col-span-2 gap-x-1 hidden lg:block" id="toolbar-amount">
    <?php if ($block->getLastPageNum() > 1): ?>
        <?= $escaper->escapeHtml(
            __(
                'Items %1-%2 of %3',
                '<span class="toolbar-number">' . $localeFormatter->formatNumber($block->getFirstNum()) . '</span>',
                '<span class="toolbar-number">' . $localeFormatter->formatNumber($block->getLastNum()) . '</span>',
                '<span class="toolbar-number">' . $localeFormatter->formatNumber($block->getTotalNum()) . '</span>'
            ),
            ['span']
        ) ?>
    <?php elseif ($block->getTotalNum() == 1): ?>
        <?= $escaper->escapeHtml(
            __(
                '%1 Item',
                '<span class="toolbar-number">' . $localeFormatter->formatNumber($block->getTotalNum()) . '</span>'
            ),
            ['span']
        ) ?>
    <?php else: ?>
        <?= $escaper->escapeHtml(
            __(
                '%1 Items',
                '<span class="toolbar-number">' . $localeFormatter->formatNumber($block->getTotalNum()) . '</span>'
            ),
            ['span']
        ) ?>
    <?php endif; ?>
</p>
