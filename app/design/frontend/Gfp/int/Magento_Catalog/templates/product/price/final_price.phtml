<?php
declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\ProductPrice;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;


/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPrice $productPriceViewModel */
$productPriceViewModel = $viewModels->require(ProductPrice::class);

$regularPrice = $block->getPriceType('regular_price')->getAmount()->getValue();
/** @var \Magento\Framework\Pricing\Price\PriceInterface $finalPriceModel */
$finalPrice = $block->getPriceType('final_price')->getAmount()->getValue();
$tierPrices = $block->getPriceType('tier_price')->getAmount()->getValue();

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);

$displayTax = $productPriceViewModel->displayPriceIncludingTax();

$product = $block->getSaleableItem();

/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
$request = $viewModels->require(\CopeX\HyvaTheme\ViewModel\Request::class);
$isListView = $request->isListView();
$showSaveInfo = false;
?>


<div class="price-box price-final_price">
    <div class="<?= ($isListView ? "price-container" : "flex flex-col") ?>">
        <?php if ($finalPrice < $regularPrice): ?>
        <span class="old-price sly-old-price mr-3 flex <?= ($isListView ? "" : "justify-center") ?>">
            <span class="price-container mr-2 flex flex-row items-center">
                <span class="price-label text-xs"><?= __("Regular Price") ?></span>
                <span id="product-price-<?= (int)$block->getIdSuffix() ?>" class="price-wrapper <?= ($isListView ? "text-lg" : "text-sm line-through") ?>">
                    <?= /** @noEscape */ $productViewModel->format($regularPrice) ?>
                </span>
            </span>
        </span>
        <?php endif; ?>

        <div class="final-price <?= ($isListView ? "text-left" : "text-center") ?> block">
            <span id="product-price-<?= (int)$block->getIdSuffix() ?>"
                  class="price-wrapper background-gradient font-normal text-xl leading-5 text-right whitespace-no-wrap">
                <span class="price-new price <?= ($isListView ? "" : "font-semibold") ?>">
                    <?= /** @noEscape */ $productViewModel->format($finalPrice) ?>
                </span>
            </span>
        </div>

        <?php if (!$isListView && $finalPrice < $regularPrice && $showSaveInfo): ?>
            <span class="save-info-block text-gfpgreen-lighter" data-role="save-info-block">
                <span class="save-info">
                    <span class="save-label"><?= __("You save:") ?></span>
                    <span class="price"><?= /** @noEscape */ $productViewModel->format($regularPrice - $finalPrice) ?></span>
                </span>
            </span>
        <?php endif; ?>
        <?php if ($finalPrice < $regularPrice): ?>
            <span>
                <span class=" <?= ($isListView ? "justify-start" : "justify-center") ?> flex flex-row w-full">
                    <span class="whitespace-nowrap text-xs"><?= __("30 days best price: ") ?></span>
                    <span id="product-price-<?= (int)$block->getIdSuffix() ?>" class="price-wrapper text-xs ml-0.5">
                        <?= /** @noEscape */ $productViewModel->format($finalPrice, false) ?>
                    </span>
                </span>
            </span>
        <?php endif; ?>
    </div>
</div>
