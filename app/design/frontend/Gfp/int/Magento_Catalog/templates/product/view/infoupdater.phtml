<?php
declare(strict_types = 1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
?>
<script>
    window.addEventListener('configurable-selection-changed', function (event) {
        let detail = event.detail;
        if (!detail.productIndex) return;

        if (detail.candidates.length === 1) {
            currentProductId = detail.candidates[0];
            let selectedProductId = document.querySelector('#product_addtocart_form input[name="item"]');
            selectedProductId.setAttribute('value', currentProductId);
        }

        fetch(BASE_URL + 'extendconfigurable/index/info?productId=' + detail.productIndex + '&parentProductId=' + detail.productId + '&gql=0', {
            method: "GET",
            cache: "default",
            async: true,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        }).then(response => response.json())
            .then((data) => {
                let formKeyValue = hyva.getFormKey();
                if (data.related.trim()) {
                    let relatedBlock = document.querySelector('.product-info-details-related');
                    if(relatedBlock){
                        relatedBlock.innerHTML = data.related;
                        for (let formKeyElement of relatedBlock.querySelectorAll('[name="form_key"]')) {
                            formKeyElement.value = formKeyValue;
                        }
                    }
                }
                if (data.upsell.trim()) {
                    let upsellBlock = document.querySelector('.product-upsell-outer');
                    if(upsellBlock){
                        upsellBlock.outerHTML = data.upsell;
                        for (let formKeyElement of upsellBlock.querySelectorAll('[name="form_key"]')) {
                            formKeyElement.value = formKeyValue;
                        }
                    }
                }
            });
    });
</script>
