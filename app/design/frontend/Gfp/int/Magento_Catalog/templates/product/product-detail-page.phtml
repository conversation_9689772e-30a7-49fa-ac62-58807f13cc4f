<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use GFP\Theme\Model\Config\Source\Page\LazyLoad;
use Magento\Catalog\Block\Product\View;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\StoreConfig;

/** @var ViewModelRegistry $viewModels */
/** @var View $block */

/** @var StoreConfig $storeConfig */
$storeConfig = $viewModels->require(StoreConfig::class);

$blockViewModel = $viewModels->require(\CopeX\HyvaTheme\ViewModel\Cms::class);
$deployMode = $viewModels->require(\Hyva\Theme\ViewModel\DeployMode::class);
$product = $block->getProduct();

$shippingInfo = "";
$categoryCollection = clone $product->getCategoryCollection();
$categoryCollection->addAttributeToSelect("shipping_info");
foreach($categoryCollection as $category){
    $shippingInfo = $category->getShippingInfo();
    if($shippingInfo && trim($shippingInfo)){
        $shippingInfo = $blockViewModel->renderBlockContent($shippingInfo);
        break;
    }
}
if(!$shippingInfo){
    $shippingInfo = $blockViewModel->renderBlockByIdentifier("product-shipping-info");
}

$lazyLoad = $storeConfig->getStoreConfig('catalog/product_detail_page/lazyload');

?>
<style>


    /* ProduktBild overlay Bild-Testsieger und Trustedshops */
    .spl-badget-container {
        position: relative;
    }

    .spl-badget-testsieger{
        position: absolute;
        bottom: 60px !important;
        right: 10px !important;
        z-index: 99;

        .spl-badget-testsieger-img{
            width: 50px;
            height:auto;
        }
    }

    .spl-trustbadge{
        position: absolute;
        bottom: 60px !important;
        right: 80px !important;
        z-index: 99;
        display: none;

        .spl-badget-trusted-img{
            width: 65px;
            height:auto;

            border-radius: 5%;
            border: 1px solid rgba(161, 160, 160, 0.5);

            -webkit-box-shadow: 5px 5px 15px 0px rgba(0,0,0,0.5);
            box-shadow: 5px 5px 15px 0px rgba(0,0,0,0.5);
        }
    }

    /* mobile */
    @media only screen and (min-width: 768px) {
        .spl-badget-testsieger,
        .spl-trustbadge{
            bottom: 160px !important;
        }
    }

    /* ------------------------------------------ */
    /* Anpassungen fur die einzelnen Bildgroessen */
    /* ------------------------------------------ */
    @media only screen and (min-width: 840px) {
        .spl-badget-testsieger {
            right: 40px !important;
        }
        .spl-trustbadge{
            right: 110px !important;
        }
    }
    @media only screen and (min-width: 900px) {
        .spl-badget-testsieger {
            right: 60px !important;
        }
        .spl-trustbadge{
            right: 130px !important;
        }
    }
    @media only screen and (min-width: 950px) {
        .spl-badget-testsieger {
            right: 80px !important;
        }
        .spl-trustbadge{
            right: 150px !important;
        }
    }
    @media only screen and (min-width: 990px) {
        .spl-badget-testsieger {
            right: 100px !important;
        }
        .spl-trustbadge{
            right: 170px !important;
        }
    }
    /* ------------------------------------------ */

    /*  trustbadge und testsieger ueber 1024px de/aktivieren */
    @media only screen and (min-width: 1024px) {
        .spl-badget-testsieger,
        .spl-trustbadge{
            display: none !important;
        }
        .spl-badget-container{
            div._xxobjz{
                display: block !important;
            }
        }
    }

    /* vorhandenes trustbadge und testsieger unter 1024px deaktivieren */
    @media only screen and (max-width: 1023px) {
        div.promotion-footer-badge{
            display: none !important;
        }
        ._t53mel{
            div._xxobjz{
                display: none !important;
            }
        }
    }

    /*  Wenn man nach unten scrollt sollen die Siegel ausgeblendet werden.
        Erst wenn man nach ganz oben auf die Seite scrollt, erscheinen die Siegel wieder. */
    #spl-badgets-hide {
        transition: transform 0.5s ease-out;
    }
    .spl-hidden {
        display: none;
    }
    /* ----------------------------------------------------------------------------------- */

</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {

        var elementToHide = document.getElementById('spl-badgets-hide');
        var lastScrollTop = 0;

        window.addEventListener('scroll', function() {
            var st = window.scrollY;

            // ganz oben
            if (st === 0) {
                elementToHide.classList.remove('spl-hidden');
            }
            // wenn nach unten gescrollt wird
             else if (st > lastScrollTop) {
                elementToHide.classList.add('spl-hidden');
            }
            // wenn nach oben gescrollt wird
             else {
                elementToHide.classList.add('spl-hidden');
            }

            lastScrollTop = st;
        });
    });
</script>

<section class="body-font mx-1 sm:mx-4">
    <div class="flex py-2 lg:flex-row flex-col items-center">
        <div class="flex flex-wrap order-first w-full">
            <div class="flex flex-col order-1 w-full lg:w-1/2 md:h-auto pr-0 md:pr-6">

                <div class="spl-badget-container">

                    <?= $block->getChildHtml('product.media') ?>

                    <div id="spl-badgets-hide">

                        <div class="spl-badget-testsieger">
                            <picture>
                                <source type="image/webp" srcset="/media/wysiwyg/Icons/testsieger-label-langlebigkeit-2021.webp">
                                <img loading="lazy" src="/media/wysiwyg/Icons/testsieger-label-langlebigkeit-2021.png" alt="Test-Sieger" class="spl-badget-testsieger-img">
                            </picture>
                        </div>
                        <div class="spl-trustbadge">
                            <picture>
                                <source type="image/webp" srcset="/media/wysiwyg/Icons/trustedshops-badget-img2.png">
                                <a href="https://www.trustedshops.de/bewertung/info_X5C58BDC7E7C28D8EEBF82A26F50DC2B4.html" target="_blank">
                                <img loading="lazy" src="/media/wysiwyg/Icons/trustedshops-badget-img2.png" alt="Test-Sieger" class="spl-badget-trusted-img">
                                </a>
                            </picture>
                        </div>

                    </div>

                </div>

                <?= $block->getChildHtml('product.supplementary.text.top') ?>
            </div>
            <?= $block->getChildHtml('product.info') ?>
        </div>
    </div>
</section>
<?php if ($shippingInfo): ?>
    <section class="product-info-shipping-container mx-1 sm:mx-4 checklist my-2 md:mt-6">
        <?= $shippingInfo; ?>
    </section>
<?php endif; ?>
<section>
    <?= $block->getChildHtml('product_options_wrapper_bottom') ?>
</section>
<section id="product-details">
    <?php if($lazyLoad == LazyLoad::ENABLED || ($lazyLoad == LazyLoad::PRODUCTION && !$deployMode->isDeveloperMode())): ?>
    <div>
    <script>
        function lazyLoadProductDetails(){
            fetch("<?= $block->getUrl('lazyloadhtml/product/get') ?>id/<?=  $product->getId() ?>", {
                method: "GET",
                async: true,
                cache: "default",
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            }).then(response => response.json()).then((data) => {
                if (!data.details) return false;
                let productDetails = document.getElementById('product-details');
                productDetails.innerHTML = data.details;
                let formKey = hyva.getFormKey();
                Array.from(productDetails.querySelectorAll('input[name="form_key"]')).map((input) => input.value = formKey);
                window.evaluateScriptsByElement(productDetails);
                window.dispatchEvent(new CustomEvent("content-load-after"));
            });
        }
        window.loadOnMove(lazyLoadProductDetails);
    </script>
    <?php else: ?>
        <?php if($lazyLoad  == LazyLoad::ALPINE): ?>
            <div x-data="{ loaded: false }" x-init="$nextTick(() => { loaded=true; })">
                <template x-if="loaded">
                    <div x-data="{}" x-init="window.evaluateScriptsByElement($el)">
                        <?= $block->getChildHtml('product.info.details');  ?>
                    </div>
                </template>
            </div>
        <?php else: ?>
            <?= $block->getChildHtml('product.info.details');  ?>
        <?php endif; ?>
    <?php endif; ?>
</section>
<section>
    <?= $block->getChildHtml('related') ?>
    <?= $block->getChildHtml('upsell') ?>
    <?= $block->getChildHtml('review_list') ?>
    <?= $block->getChildHtml('review_form') ?>
</section>
<script>
    function showProductInfos(id){
        var elem = document.querySelector('.related-product-info.product-' + id);
        var tabRelated = document.querySelector('.product-info-details-related');
        if(elem.style.display === "none"){
            tabRelated.scrollIntoView();
            elem.style.display = "block";
        }
        else {
            elem.style.display = "none";
        }
    }
</script>
