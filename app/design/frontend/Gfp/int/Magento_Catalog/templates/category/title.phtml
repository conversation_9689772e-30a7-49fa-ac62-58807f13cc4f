<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\ViewModel\CurrentCategory;
use Magento\Framework\Escaper;
use Magento\Theme\Block\Html\Title;

/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */

/** @var Escaper $escaper */
/** @var Title $block */

/** @var CurrentCategory $cmsViewModel */
$currentCategory = $viewModels->require(CurrentCategory::class);
$category = $currentCategory->get();

$cssClass = $block->getCssClass() ? ' ' . $block->getCssClass() : 'text-3xl';
$titleHtml = '';
if ($category->getSeoheadline()) {
    $titleHtml = $category->getSeoheadline();
}
?>
<?php if ($titleHtml): ?>
    <div class="container flex flex-col md:flex-row flex-wrap my-0 font-medium  <?= /** @noEscape */ $cssClass ?>">
        <h1 class="page-title title-font m-0"
            <?php if ($block->getId()): ?> id="<?= $escaper->escapeHtmlAttr($block->getId()) ?>" <?php endif; ?>>
            <?= /* @noEscape */ $titleHtml ?>
        </h1>
        <?= $block->getChildHtml() ?>
    </div>
<?php endif; ?>
