<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!-- ko if: isTaxDisplayedInGrandTotal -->
<tr class="grand totals incl">
    <th class="mark" scope="row">
        <strong data-bind="i18n: inclTaxLabel"></strong>
    </th>
    <td data-bind="attr: {'data-th': inclTaxLabel}" class="amount">
        <strong>
            <span class="price" data-bind="text: getValue()"></span>
            &nbsp;
            <span class="price" data-bind="text: getTaxText()"></span>
        </strong>
    </td>
</tr>
<tr class="grand totals excl">
    <th class="mark" scope="row">
        <strong data-bind="i18n: exclTaxLabel"></strong>
    </th>
    <td data-bind="attr: {'data-th': exclTaxLabel}" class="amount">
        <strong>
            <span class="price" data-bind="text: getGrandTotalExclTax()"></span>
            &nbsp;
            <span class="price" data-bind="text: getTaxText()"></span>
        </strong>
    </td>
</tr>
<!-- /ko -->
<!-- ko if: !isTaxDisplayedInGrandTotal -->
<tr class="grand totals">
    <th class="mark" scope="row">
        <strong data-bind="i18n: title"></strong>
    </th>
    <td data-bind="attr: {'data-th': title}" class="amount">
        <strong>
            <span class="price" data-bind="text: getValue()"></span>
            &nbsp;
            <span class="price" data-bind="i18n: 'incl. TAX'"></span>
        </strong>
    </td>
</tr>
<!-- /ko -->
