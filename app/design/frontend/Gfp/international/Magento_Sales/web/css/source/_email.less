// /**
//  * Copyright © 2013-2017 Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Sales Module Styles
//  _____________________________________________

.email-intro {
    td {
        padding-bottom: @indent__base;
    }
}

.email-summary {
    h1 {
        border-bottom: @border-width__base solid @border-color__base;
        margin-bottom: @indent__s;
        padding-bottom: @indent__s;
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__xs) {
    //  Shrink order number in order email so it will fit on single line on small screens
    .email-summary {
        h1 {
            font-size: ceil(1.7 * @font-size__base) !important; // 24px
        }
    }
}

//
//  Order Summary
//  ---------------------------------------------

.order-details {
    width: 100%;

    tr {
        > .address-details,
        > .method-info {
            padding: @email-content__padding__base @email-content__padding__base @email-content__padding__base 0;
            width: 50%;

            h3 {
                margin-top: 0;
            }
        }

        //  Prevent extra spacing on Payment & Shipping Method row
        & + .method-info {
            > td {
                padding: 0 0 @email-content__padding__base;
            }
        }
    }

    .payment-method {
        margin-bottom: @indent__s;

        .title {
            font-weight: @font-weight__regular;
        }

        .data.table {
            > caption {
                display: none;
            }

            th {
                padding-right: @email-content__padding__base;
            }
        }
    }
}

//  Remove address and phone number link color on iOS
.address-details a {
    &:extend(.no-link a);
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__xs) {
    //  Stack columns
    .order-details {
        .address-details,
        .method-info {
            display: block;
            padding: @email-content__padding__base 0 !important;
            width: auto !important;

            h3 {
                margin-bottom: @indent__xs !important;
                margin-top: 0 !important;
            }
        }
    }
}

//
//  Shipment Tracking
//  ---------------------------------------------

.shipment-track {
    .lib-table(@table__width, 0);

    thead,
    tbody {
        > tr {
            > th,
            > td {
                background-color: @email__background-color;
                padding: @email-content__padding__base;
                width: 50%;
            }

            & + tr {
                th,
                td {
                    padding-top: 0;
                }
            }
        }
    }
}

//
//  Items Table
//  ---------------------------------------------

.email-items {
    .lib-table(@table__width, 0);
    tfoot {
        > tr {
            > th,
            > td {
                background-color: @email__background-color;
            }
        }
    }

    > thead,
    > tbody {
        > tr {
            > th {
                padding: @email-content__padding__base;
            }

            > td {
                padding: @email-content__padding__base;

                &.message-gift {
                    border-top: none;
                    padding-top: 0;
                }
            }
        }
    }

    > tbody {
        > tr {
            > th,
            > td {
                border-top: @table__border-width @table__border-style @table__border-color;
            }

            & + tr {
                > th,
                > td {
                    border-top: 0;
                }
            }
        }
    }

    p {
        margin-bottom: 0;
    }

    .product-name {
        font-weight: @font-weight__bold;
        margin-bottom: @email-content__padding__s;
    }

    .has-extra .sku {
        margin-bottom: @email-content__padding__base;
    }

    .item-info {
        dl {
            margin-bottom: 0;
            padding-left: @email-content__padding__m;

            dt,
            dd {
                margin-bottom: 0;
                padding-bottom: 0;
            }

            dd {
                padding-left: @email-content__padding__base;
            }
        }
    }

    .item-qty {
        text-align: center;
    }

    .item-price {
        text-align: right;
    }

    .item-extra {
        padding-top: 0;
    }

    .order-totals {
        > tr {
            > th {
                font-weight: @font-weight__regular;
            }

            > th,
            > td {
                padding: @email-content__padding__base;
                text-align: right;
            }

            & + tr {
                th,
                td {
                    padding-top: 0;
                }
            }
        }

        .price {
            white-space: nowrap;
        }
    }
}

.email-non-inline() {
    .email-items {
        .lib-table-overflow();
    }
}
