<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var \Magento\Customer\Block\Account\AuthorizationLink $block */

$dataPostParam = '';
if ($block->isLoggedIn()) {
    $dataPostParam = sprintf(" data-post='%s'", $block->getPostParams());
}
?>
<li class="authorization-link" data-label="<?= $block->escapeHtml(__('or')) ?>">
    <a <?= /* @noEscape */ $block->getLinkAttributes() ?><?= /* @noEscape */ $dataPostParam ?>>
        <?= $block->escapeHtml($block->getLabel()) ?>
    </a>
</li>
