<?php
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile
?>
<?php
/**
 * Category layered navigation state
 *
 * @var $block \Magento\LayeredNavigation\Block\Navigation\State
 */
?>
<?php $_filters = $block->getActiveFilters() ?>
<?php if (!empty($_filters)): ?>
<div class="filter-current category-sub-name">
    <span><?php /* @escapeNotVerified */ echo __('You are looking for') ?></span>
    <ol class="items">
        <?php foreach ($_filters as $_filter): ?>
            <li class="item">
                <span class="filter-<?php echo $_filter->getFilter()->getRequestVar(); ?>">
                    <?php if($_filter->getFilter()->getRequestVar() == "price") { echo __('for'); } ?>
                    <?php /* @escapeNotVerified */ echo $block->stripTags($_filter->getLabel()) ?>
                </span>
            </li>
        <?php endforeach; ?>
    </ol>
</div>
<?php endif; ?>
