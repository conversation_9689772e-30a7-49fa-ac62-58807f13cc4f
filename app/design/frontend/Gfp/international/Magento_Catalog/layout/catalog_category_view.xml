<?xml version="1.0"?>
<!--
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="category.view.container">
            <container name="page.main.title.container" htmlTag="div" htmlClass="page-main-top-container" before="-">
                <block class="Magento\Catalog\Block\Category\View" name="category.pagetitle" template="Xortex_App::catalog/category/pagetitle.phtml" />
                <block class="Magento\Catalog\Block\Category\View" name="category.subname" template="Xortex_App::catalog/category/subname.phtml" />
                <block class="Magento\Catalog\Block\Category\View" name="category.shortdescription" template="Magento_Catalog::category/shortdescription.phtml" />
            </container>
            <container name="promotion.bannerblock.container" htmlTag="div" htmlClass="promotion-banner-container container" after="page.main.title.container">
                <container name="promotion.bannerblock.inner" htmlTag="div" htmlClass="promotion-banner-inner">
                    <block class="Magento\Cms\Block\Block" name="promotion.bannerbadge">
                        <arguments>
                            <argument name="block_id" xsi:type="string">promobadge</argument>
                        </arguments>
                    </block>
                    <container name="promotion.bannertext.inner" htmlTag="div" htmlClass="promotion-banner-text">
                        <block class="Magento\Cms\Block\Block" name="promotion.bannertext">
                            <arguments>
                                <argument name="block_id" xsi:type="string">promobannertext</argument>
                            </arguments>
                        </block>
                    </container>
                </container>
            </container>
            <block class="Magento\Catalog\Block\Category\View" name="category.view.image.container" template="Xortex_App::catalog/category/imagecontainer.phtml" before="category.description">
            </block>
            <block class="Magento\Catalog\Block\Category\View" name="category.subcategories" template="Xortex_App::catalog/category/subcategories.phtml" after="category.view.image.container">
                <block class="Magento\Cms\Block\Block" name="portait.block.top">
                    <arguments>
                        <argument name="block_id" xsi:type="string">portraitblocktop</argument>
                    </arguments>
                </block>
                <block class="Magento\Cms\Block\Block" name="portait.block.bottom">
                    <arguments>
                        <argument name="block_id" xsi:type="string">portraitblockbottom</argument>
                    </arguments>
                </block>
            </block>
        </referenceContainer>
        <referenceContainer name="content">
            <block class="Magento\Catalog\Block\Category\View" name="category.view" template="Xortex_App::catalog/category/view.phtml" after="-" />
            <!--<block class="Magento\Catalog\Block\Category\View" name="category.parentcategories" before="category.products" template="Xortex_App::catalog/category/parentcategories.phtml" />-->
        </referenceContainer>

        <move element="category.view.container" destination="category.view" />
        <move element="page.main.title.container" destination="content" before="-" />
        <referenceBlock name="page.main.title" remove="true" />
        <move element="categorygallery_bottom" destination="content" after="category.products"/>
        <move element="category.image" destination="category.view.image.container"/>
        <move element="category.description" destination="content" after="-" />
        <referenceBlock name="category.subname" remove="true" />
        <referenceBlock name="category.view.image.container" remove="true" />
    </body>
</page>
