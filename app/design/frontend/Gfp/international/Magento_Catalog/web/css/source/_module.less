// /**
//  * Copyright © 2013-2017 Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

@import 'module/_listings.less';
@import 'module/_toolbar.less';

//
//  Variables
//  _____________________________________________

@product-info-price: @color-gray34;
@product-h1-margin-bottom-desktop: @indent__s + @indent__xs;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    //  Category view
    .old-price,
    .old.price {
        .lib-css(color, @text__color__muted);
    }

    .prices-tier {
        .price-container {
            .price-including-tax {
                + .price-excluding-tax {
                    &:before {
                        content: '(' attr(data-label) ':';
                    }

                    &:last-child:after {
                        content: ')';
                    }
                }
            }

            .weee[data-label] {
                display: inline;

                &:before {
                    content: ' +' attr(data-label) ':';
                }
            }
        }
    }

    .actual-price {
        font-weight: @font-weight__bold;
    }

    .product.name a {
        &:extend(.abs-product-link all);
    }

    .special-price {
        .price-label {
            &:extend(.abs-visually-hidden all);
        }
    }

    .category {
        &-image {
            .image {
                display: block;
                height: auto;
                max-width: 100%;
            }
        }

        &-cms,
        &-image,
        &-description {
            margin-bottom: @indent__base;
        }
    }

    //
    //  Product images general container
    //  -----------------------------------------

    .product-image {
        &-container {
            display: inline-block;
            max-width: 100%;
        }

        &-wrapper {
            display: block;
            height: 0;
            overflow: hidden;
            position: relative;
            z-index: 1;
        }

        &-photo {
            bottom: 0;
            display: block;
            height: auto;
            left: 0;
            margin: auto;
            max-width: 100%;
            position: absolute;
            right: 0;
            top: 0;
        }
    }

    //
    //  Product view
    //  -----------------------------------------

    .product.media {
        .product.photo .photo.image {
            &:extend(.abs-adaptive-images-centered);
        }

        .placeholder .photo.container {
            max-width: 100%;
        }

        .notice {
            margin: @indent__s 0;
            .lib-css(color, @text__color__muted);
            .lib-font-size(@font-size__s);
        }

        .product.thumbs {
            margin: @indent__s 0 @indent__m;
        }

        .items.thumbs {
            .lib-list-inline();
            > li {
                margin: 1rem 0 0;
            }

            img {
                display: block;
            }

            .active {
                display: block;
                line-height: 1;
            }
        }
    }

    .product.info.detailed {
        clear: both;
        margin-bottom: 30px;

        .additional-attributes {
            width: auto;
            .lib-table-bordered(
                @_table_type: clear
            );
            .lib-table-resize(
                @_th-padding-left: 0,
                @_th-padding-right: @indent__l,
                @_th-padding-bottom: @indent__s,
                @_td-padding-bottom: @indent__s
            );
        }
    }

    .product-info-main {
        .product-info-price {
            .lib-css(color, @product-info-price);
            border-bottom: 1px solid @color-gray-light5;
            display: table;
            margin-bottom: @indent__s + @indent__xs;
            width: 100%;

            .price-box {
                display: table-cell;
                vertical-align: top;
                width: 1px;

                .price-container {
                    > span {
                        margin-bottom: @indent__xs;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
            }

            .price-including-tax + .price-excluding-tax,
            .weee + .price-excluding-tax,
            .weee {
                .lib-font-size(14);
                .price {
                    .lib-font-size(14);
                    font-weight: @font-weight__semibold;
                    line-height: 16px;
                }
            }

            .price-wrapper .price {
                .lib-font-size(22);
                font-weight: @font-weight__semibold;
                line-height: 22px;
            }

            .old-price {
                .price-wrapper .price {
                    .lib-font-size(20);
                    font-weight: @font-weight__light;
                }
            }

            .special-price {
                .price-label {
                    &:after {
                        content: ': ';
                    }
                }
            }

            .price {
                white-space: nowrap;
            }
        }

        .product-info-stock-sku {
            display: table-cell;
            padding-bottom: @indent__s;
            padding-left: 10%;
            text-align: right;
            vertical-align: top;
        }

        .product.attribute.sku {
            word-break: break-all;
            word-wrap: break-word;
        }

        .product {
            &-add-form {
                clear: both;
                padding-top: @indent__s + @indent__xs;
            }

            &.attribute {
                &.sku {
                    .type {
                        font-weight: normal;
                        margin-right: @indent__xs;

                        &:after {
                            content: '#:';
                        }
                    }

                    .value {
                        display: inline-block;
                    }
                }

                &.overview {
                    margin: @indent__base 0;
                }
            }

            &.alert {
                margin: @indent__s 0;
            }
        }

        .product-reviews-summary {
            float: left;
        }

        .product-options-bottom {
            .box-tocart {
                margin-top: @form-field__vertical-indent;
            }
        }
    }

    .product-options-wrapper {
        .fieldset-product-options-inner {
            .legend {
                .lib-css(font-weight, @font-weight__semibold);
                .lib-font-size(14px);
                border: none;
                display: inline-block;
                float: none;
                margin: 0 0 8px;
                padding: 0;
            }

            &.required,
            &._required {
                .legend {
                    &:after {
                        content: '*';
                        .lib-typography(
                        @_font-size: @form-field-label-asterisk__font-size,
                        @_color: @form-field-label-asterisk__color,
                        @_font-family: @form-field-label-asterisk__font-family,
                        @_font-weight: @form-field-label-asterisk__font-weight,
                        @_line-height: @form-field-label-asterisk__line-height,
                        @_font-style: @form-field-label-asterisk__font-style
                        );
                    }
                }
            }
        }

        .field {
            .note {
                display: block;
                .lib-css(margin-top, @indent__xs);
            }
        }
    }

    .product-options-bottom .price-box,
    .product-info-price .price-box {
        .lib-css(color, @product-info-price);
        display: table-cell;
        padding-bottom: @indent__s;
        vertical-align: top;

        .old-price {
            font-size: 20px;
            font-weight: @font-weight__light;

            .price-container > span {
                display: inline-block;
            }
        }

        .price-container {
            > span {
                margin-bottom: @indent__xs;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            .price {
                font-size: 22px;
                font-weight: @font-weight__semibold;
                line-height: 22px;
            }

            .price-including-tax + .price-excluding-tax,
            .weee + .price-excluding-tax,
            .weee {
                .lib-font-size(12);
                line-height: 14px;

                .price {
                    .lib-font-size(12);
                    font-weight: @font-weight__bold;
                }
            }
        }
    }

    .box-tocart {
        &:extend(.abs-box-tocart all);
        .field.qty {
        }

        .input-text.qty {
            &:extend(.abs-input-qty all);
        }
    }

    .product-addto-links {
        display: inline;

        .action {
            margin-right: 5%;
        }
    }

    .product-social-links {
        margin: 0 0 20px;
        text-align: center;

        .action.tocompare {
            &:extend(.abs-actions-addto all);
            .lib-icon-font-symbol(
                @_icon-font-content: @icon-compare-full,
                @_icon-font-position: before
            );
        }
    }

    .prices-tier {
        &:extend(.abs-reset-list all);
        margin-bottom: @indent__s + @indent__xs;

        .item {
            margin-bottom: @indent__s;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .price-excluding-tax,
        .price-including-tax {
            display: inline-block;
        }
    }

    .minimal-price-link,
    .price-excluding-tax,
    .price-including-tax {
        display: block;
        white-space: nowrap;
    }

    .ui-dialog-titlebar-close {
        .lib-button-as-link();
    }

    //
    //  Sidebar product view
    //  -----------------------------------------

    .sidebar {
        .product-items {
            .product-item {
                margin-bottom: @indent__base;
                position: relative;

                &-info {
                    position: relative;
                    width: auto;

                    .product-item-photo {
                        left: 0;
                        position: absolute;
                        top: 0;
                    }
                }

                &-name {
                    margin-top: 0;
                }

                &-details {
                    margin: 0 0 0 85px;
                }

                &-actions {
                    display: block;
                    margin-top: @indent__s;
                }
            }

            .price-box {
                display: block;
                margin: 7px 0;
            }

            .text {
                margin-right: 8px;
            }

            .counter {
                .lib-css(color, @primary__color__lighter);
                font-size: 12px;
                white-space: nowrap;
            }

            .minilist {
                .price {
                    display: inline;
                    padding: 0;
                }

                .weee:before {
                    display: inline-block;
                }
            }

            .action {
                &.delete {
                    &:extend(.abs-remove-button-for-blocks all);
                    position: absolute;
                    right: 0;
                    top: 0;
                }
            }
        }

        .action.tocart {
            border-radius: 0;
        }

        .subtitle {
            &:extend(.abs-no-display all);
        }

        //
        //  Product images only
        //  -------------------------------------

        .product-items-images {
            &:extend(.abs-add-clearfix all);
            margin-left: -@indent__xs;

            .product-item {
                &:extend(.abs-add-box-sizing all);
                float: left;
                padding-left: @indent__xs;
            }
        }

        //
        //  Product names only
        //  -------------------------------------

        .product-items-names {
            .product-item {
                margin-bottom: @indent__s;
            }

            .product-item-name {
                margin: 0;
            }
        }
    }
}

//
//  Mobile @screen__m
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .catalog-product-view {
        .column.main {
            .lib-vendor-prefix-display(flex);
            .lib-vendor-prefix-flex-direction(column);
        }

        .product.media {
            .lib-vendor-prefix-order(-1);
        }
    }
}

//
//  Mobile @screen__s
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .product-social-links {
        .action.tocompare {
            display: none;
        }
    }

    .product-info-price {
        margin: 0 -@indent__s 0;
        width: ~'calc(100% + 2*@{indent__s})' !important;

        > *:first-child {
            padding-left: @indent__s;
        }

        > *:last-child {
            padding-right: @indent__s;
        }
    }

    .product.info.detailed {
        .additional-attributes-wrapper {
            &:extend(.abs-no-border-top all);
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .product-info-main {
        .page-title-wrapper {
            h1 {
                .lib-css(margin-bottom, @product-h1-margin-bottom-desktop);
            }
        }

        .product-add-form {
            padding-top: @indent__base;
        }
    }

    .box-tocart {
        .action.tocart {
            margin-bottom: 0;
            margin-right: 1%;
            width: 49%;
        }
    }

    .product-social-links {
        text-align: left;
    }

    .product-options-bottom,
    .product-info-price {
        .price-box {
            .price-container {
                font-size: @font-size__xl;

                .price {
                    font-size: 36px;
                    line-height: 36px;
                }
            }

            .price-including-tax + .price-excluding-tax {
                .price {
                    .lib-font-size(14);
                    line-height: 16px;
                }
            }
        }
    }

    //
    //  Category page layout
    //  -----------------------------------------

    .product-info-main {
        float: right;
    }

    .product.media {
        float: left;
        margin-bottom: @indent__m;
    }

    .page-layout-1column {
        .product-info-main {
            width: 40%;
        }

        .product.media {
            width: 57%;
        }
    }

    .page-layout-2columns-left,
    .page-layout-2columns-right,
    .page-layout-3columns {
        .product-info-main {
            width: 48%;
        }

        .product.media {
            width: 50%;
        }
    }

    .product-add-form {
        .product-options-wrapper .field:not(.date) {
            > .control {
                width: 80%;
            }
        }
    }

    .sidebar {
        .product-items {
            .product-item {
                &-info {
                    .product-item-photo {
                        float: left;
                        left: auto;
                        margin: 0 @indent__s @indent__s 0;
                        position: relative;
                        top: auto;
                    }
                }

                &-details {
                    margin: 0;
                }

                &-actions {
                    clear: left;
                }
            }
        }
    }
}

//
//  Desktop large
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__xl) {
    .sidebar {
        .product-items {
            .product-item {
                &-info {
                    .product-item-photo {
                        float: none;
                        left: 0;
                        margin: 0;
                        position: absolute;
                        top: 0;
                    }
                }

                &-details {
                    margin-left: 85px;
                }
            }
        }
    }
}

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    //
    //  Compare Products Page
    //  -----------------------------------------

    body.catalog-product-compare-index {
        .action.print {
            float: right;
            margin: 15px 0;
        }
    }

    .table-wrapper.comparison {
        clear: both;
        max-width: 100%;
        overflow-x: auto;

        .table-comparison > tbody > tr {
            > th,
            > td {
                border-top: 0;
            }
        }
    }

    .table-comparison {
        table-layout: fixed;

        .cell.label.remove,
        .cell.label.product {
            span {
                &:extend(.abs-visually-hidden all);
            }
        }

        .cell.label,
        td:last-child {
            border-right: @table__border-width @table__border-style @table__border-color;
        }

        .cell {
            padding: 15px;
            width: 180px;

            .attribute.value {
                overflow: hidden;
                width: 100%;
            }

            &.product.info,
            &.product.label {
                border-bottom: @table__border-width @table__border-style @table__border-color;
            }

            &.label {
                .attribute.label {
                    display: block;
                    width: 100%;
                    word-wrap: break-word;
                }
            }

            &.attribute {
                .lib-font-size(13);

                img {
                    height: auto;
                    max-width: 100%;
                }
            }

            &.remove {
                padding-bottom: 0;
                padding-top: 0;
                text-align: right;

                .action.delete {
                    &:extend(.abs-remove-button-for-blocks all);
                    margin-right: .6rem;
                }
            }
        }

        .product-item-photo {
            display: block;
            margin: 0 auto 15px;
        }

        .product-image-photo {
            margin-left: 0;
        }

        .product-item-actions,
        .price-box,
        .product.rating,
        .product-item-name {
            display: block;
            margin: 15px 0;
        }

        .product-addto-links {
            margin-top: 15px;

            .action.split,
            .action.toggle {
                .lib-button-s();
            }

            .action.toggle {
                padding: 0;
            }
        }

        .action {
            &.tocart {
                white-space: nowrap;
            }
        }
    }

    .comparison.headings {
        .lib-css(background, @color-white);
        left: 0;
        position: absolute;
        top: 0;
        width: auto;
        z-index: 2;
    }

    .block-compare {
        .block-title {
            &:extend(.abs-block-widget-title all);
        }

        .product-item .product-item-name {
            margin-left: 22px;
        }

        .action.delete {
            &:extend(.abs-remove-button-for-blocks all);
            left: 0;
            position: absolute;
            top: 0;
        }

        .counter {
            &:extend(.abs-block-items-counter all);
        }

        .actions-toolbar {
            margin: 17px 0 0;
        }

        .action.primary {
            &:extend(.abs-revert-to-action-secondary all);
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .table-wrapper.comparison {
        .table-comparison > tbody > tr {
            > th,
            > td {
                display: table-cell;
                &:extend(.abs-col-no-prefix all);
            }
        }
    }
}
.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .compare.wrapper,
    [class*='block-compare'] {
        display: none;
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .compare.wrapper {
        float: right;
        .lib-list-reset-styles();

        .action.compare {
            line-height: @form-element-input__height;
            .lib-link(
                @_link-color: @primary__color,
                @_link-text-decoration: none,
                @_link-color-visited: @primary__color,
                @_link-text-decoration-visited: none,
                @_link-color-hover: @primary__color,
                @_link-text-decoration-hover: underline,
                @_link-color-active: @primary__color,
                @_link-text-decoration-active: underline
            );
        }

        .counter.qty {
            .lib-css(color, @primary__color__lighter);

            &:before {
                content: '(';
            }

            &:after {
                content: ')';
            }
        }
    }
}

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    .block.related {
        .action.select {
            &:extend(.abs-action-button-as-link all);
            vertical-align: top;
        }

        .product-item-details {
            position: relative;
            z-index: 1;
        }

        .related-available .product-item-name {
            margin-left: 20px;
        }

        .field.choice {
            left: 0;
            position: absolute;
            top: 2px;
    
            .label {
                &:extend(.abs-visually-hidden all);
            }
        }
    }
}

//
//  Desktop large
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .box-tocart {
        .paypal:first-of-type {
            margin-top: 13px;
        }
    }
}
