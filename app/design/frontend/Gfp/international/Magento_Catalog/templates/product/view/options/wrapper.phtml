<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
?>
<?php
$required = '';
/** @var \Magento\Catalog\Block\Product\View $block */
if ($block->hasRequiredOptions()) {
    $required = ' data-hasrequired="' . __('* Required Fields') . '"';
}
$hasAlltopOption = false;
foreach($block->getProduct()->getOptions() as $option){
    foreach ($option->getValues() as $value) {
        if(strpos($value->getTitle(),"[parent-alltop]") !== false){
            $hasAlltopOption = true; break 2;
        }
    }

}
?>
<div class="product-options-wrapper" id="product-options-wrapper"<?= /* @escapeNotVerified */
$required ?>>
    <div class="fieldset" tabindex="0">
        <?= $block->getChildHtml('', true) ?>
        <?php if ($hasAlltopOption) : ?>
        <div class="info-parent-alltop">
            <?= $this->getLayout()
                ->createBlock('Magento\Cms\Block\Block')
                ->setBlockId('info_text_alltop')
                ->toHtml(); ?>
        </div>
        <div class="info-child-alltop">
            <?= $this->getLayout()
                ->createBlock('Magento\Cms\Block\Block')
                ->setBlockId('info_text_options_alltop')
                ->toHtml(); ?>
        </div>
        <?php endif; ?>
    </div>
</div>
<script>
    require(['jquery'], function ($) {
        'use strict';

        $(document).ready(function () {
            let parent = $('.parent-alltop');
            let radioCheckboxes = $('.radio-checkbox');
            let parentInfoText = $('.info-parent-alltop');
            let childrenInfoText = $('.info-child-alltop');

            parent.on('change', function () {
                let children = $('.child-alltop');

                if (this.checked) {
                    children.slideDown(300);
                    parentInfoText.slideUp(300);
                    childrenInfoText.slideDown(300);
                } else {
                    children.slideUp(300);
                    children.find('input:radio[value=""]').prop('checked', true).trigger('change');
                    parentInfoText.slideDown(300);
                    childrenInfoText.slideUp(300);
                }
            });

            radioCheckboxes.on('change', function () {
                if (this.checked) {
                    radioCheckboxes.not(this).prop('checked', false).trigger('change');
                }
            });
        });
    });
</script>
