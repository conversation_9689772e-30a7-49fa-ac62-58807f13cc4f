<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Catalog\Model\Product\Option;

/**
 * @var $block \Magento\Catalog\Block\Product\View\Options\Type\Select\Checkable
 * @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer
 */
/** @var $helper \CopeX\AllTopProductOptions\Helper\OptionTitle */
$helper = $this->helper(CopeX\AllTopProductOptions\Helper\OptionTitle::class);
$option = $block->getOption();
if ($option): ?>
    <?php
    $configValue = $block->getPreconfiguredValue($option);
    $optionType = $option->getType();
    $arraySign = $optionType === Option::OPTION_TYPE_CHECKBOX ? '[]' : '';
    $count = 1;
    ?>

<div class="options-list nested" id="options-<?= $block->escapeHtmlAttr($option->getId()) ?>-list">
    <?php if ($optionType === Option::OPTION_TYPE_RADIO && !$option->getIsRequire()):?>
    <div class="field choice admin__field admin__field-option">
        <input type="radio"
               id="options_<?= $block->escapeHtmlAttr($option->getId()) ?>"
               class="radio admin__control-radio product-custom-option"
               name="options[<?= $block->escapeHtmlAttr($option->getId()) ?>]"
               data-selector="options[<?= $block->escapeHtmlAttr($option->getId()) ?>]"
               value=""
               checked="checked"
        />
        <?php if (!$block->getSkipJsReloadPrice()): ?>
            <?= /* @noEscape */ $secureRenderer->renderEventListenerAsTag(
                'onclick',
                'opConfig.reloadPrice()',
                "options_" . $block->escapeJs($option->getId())
            ) ?>
        <?php endif; ?>
        <label class="label admin__field-label" for="options_<?= $block->escapeHtmlAttr($option->getId()) ?>">
                        <span>
                            <?= $block->escapeHtml(__('None'))  ?>
                        </span>
        </label>
    </div>
<?php endif; ?>

    <?php foreach ($option->getValues() as $value): ?>
        <?php
        $checked = '';
        $count++;
        if ($arraySign) {
            $checked = is_array($configValue) && in_array($value->getOptionTypeId(), $configValue) ? 'checked' : '';
        } else {
            $checked = $configValue == $value->getOptionTypeId() ? 'checked' : '';
        }
        $dataSelector = 'options[' . $option->getId() . ']';
        if ($arraySign) {
            $dataSelector .= '[' . $value->getOptionTypeId() . ']';
        }
        $config = $helper->getTitleConfig($value->getTitle());
        ?>

        <div class="field choice admin__field admin__field-option">
            <input type="<?= $block->escapeHtmlAttr($optionType) ?>"
                   class="<?= $optionType === Option::OPTION_TYPE_RADIO
                       ? 'radio admin__control-radio'
                       : 'checkbox admin__control-checkbox' ?> <?= $option->getIsRequire()
                       ? 'required': '' ?>
                       product-custom-option
                        <?= $block->getSkipJsReloadPrice() ? '' : 'opConfig.reloadPrice()' ?>
                        <?= ' ' . $config ?>"
                   name="options[<?= $block->escapeHtmlAttr($option->getId()) ?>]<?= /* @noEscape */ $arraySign ?>"
                   id="options_<?= $block->escapeHtmlAttr($option->getId() . '_' . $count) ?>"
                   value="<?= $block->escapeHtmlAttr($value->getOptionTypeId()) ?>"
                <?= $block->escapeHtml($checked) ?>
                   data-selector="<?= $block->escapeHtmlAttr($dataSelector) ?>"
                   price="<?= $block->escapeHtmlAttr($block->getCurrencyByStore($value)) ?>"
            />
            <label class="label admin__field-label"
                   for="options_<?= $block->escapeHtmlAttr($option->getId() . '_' . $count) ?>">
                <span>
                    <?= $helper->getFormattedTitleText($value->getTitle()) ?>
                </span>
                <span>
                   <?php if(floatval($value->getPrice())): ?> <span class="price-info">
                       <?= __('Surcharge') ?></span>
                   <?php endif; ?>
                    <?= /* @noEscape */ $block->formatPrice($value) ?>
                </span>
            </label>
        </div>
    <?php endforeach; ?>
    </div>
<?php endif; ?>
