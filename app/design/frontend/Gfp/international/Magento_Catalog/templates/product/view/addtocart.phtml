<?php
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var $block \Magento\Catalog\Block\Product\View */
?>
<?php $_product = $block->getProduct(); ?>
<?php $buttonTitle = __('Add to Cart'); ?>
<?php if ($_product->isSaleable()): ?>

<?php echo $block->getChildHtml('', true) ?>




<div class="box-tocart">
    <div class="fieldset">
        <?php if ($block->shouldRenderQuantity()): ?>
        <div class="field qty">
            <?php /* <label class="label" for="qty"><span><?= __('Qty') ?></span></label> */ ?>
            <select name="qty" id="qty" title="<?php /* @escapeNotVerified */ echo __('Qty') ?>"
                    class="input-text qty" data-validate="<?php echo $block->escapeHtml(json_encode($block->getQuantityValidators())) ?>">
                <?php $i = 1 ; ?>
                <?php
                while( $i < 6) { ?>
                    <option value="<?php echo $block->getProductDefaultQty() * $i; ?>"><?php echo $block->getProductDefaultQty() * $i; ?></option>
                    <?php $i++; ?>
                <?php } ?>
            </select>
        </div>
        <?php endif; ?>
        <div class="actions">
            <button type="submit"
                    title="<?php /* @escapeNotVerified */ echo $buttonTitle ?>"
                    class="btn-main"
                    id="product-addtocart-button">
                <span><?php /* @escapeNotVerified */ echo $buttonTitle ?></span>
            </button>
        </div>
    </div>
</div>
<?php endif; ?>
<script type="text/x-magento-init">
    {
        "#product_addtocart_form": {
            "Magento_Catalog/js/validate-product": {}
        }
    }
</script>
