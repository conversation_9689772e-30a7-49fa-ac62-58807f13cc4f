<?php
$_product = $block->getProduct();
$_tooltip = $_product->getData("cms_block_id");

if($_product->getData('setangebotname1') || $_product->getData('setangebotname2') || $_product->getData('setangebotname3')): ?>

<div class="product-options-info-outer product-options-set-outer">
    <div class="product-options-info-inner">
        <label>
            <span><?php echo __("TOP SET-ANGEBOTE"); ?></span><span><?php echo __("Profitieren Sie von den Sonderpreisen unserer Setangebote"); ?></span> <?php if($_tooltip) { echo "<div class='product-options-info-tooltip'>" . $this->getLayout()->createBlock('Magento\Cms\Block\Block')->setBlockId($_product->getData("cms_block_id"))->toHtml() . "</div>"; } ?>
        </label>
        <?php for($i = 1; $i <= 3; $i++) { ?>
            <?php if($_product->getData('setangebotname'.$i)) { ?>
                <div class="product-options-info-item">
                    <div class="product-options-item-name"><?php echo $_product->getData('setangebotname'.$i); ?>
                        <?php if($_product->getData('setangebotlabel'.$i)) { ?><label class="product-options-set-label"><?php echo $_product->getData('setangebotlabel'.$i); ?></label><?php } ?>
                        <?php if($_product->getData('setangebotdescription'.$i)) { ?><span><?php echo $_product->getData('setangebotdescription'.$i); ?></span><?php } ?>
                    </div>
                </div>
            <?php } ?>
        <?php } ?>
    </div>
</div>

<?php endif;