<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
?>
<?php
/** @var $block \Magento\Catalog\Block\Product\Image */
/** @var $escaper \Magento\Framework\Escaper */
/** @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer */
$width = (int)$block->getWidth();
?>
<span class="product-image-container product-image-container-<?= /* @noEscape */ $block->getProductId() ?>"<?= $width ? (' style="width:'.$width.'px;"' ):''?>>
    <span class="product-image-wrapper">
        <img class="<?= $escaper->escapeHtmlAttr($block->getClass()) ?> lzy_img"
            <?php foreach ($block->getCustomAttributes() as $name => $value): ?>
                <?= $escaper->escapeHtmlAttr($name) ?>="<?= $escaper->escapeHtml($value) ?>"
            <?php endforeach; ?>
            data-src="<?= $escaper->escapeUrl($block->getImageUrl()) ?>"
            loading="lazy"
            width="<?= $escaper->escapeHtmlAttr($block->getWidth()) ?>"
            height="<?= $escaper->escapeHtmlAttr($block->getHeight()) ?>"
            alt="<?= $escaper->escapeHtmlAttr($block->getLabel()) ?>"/></span>
</span>
