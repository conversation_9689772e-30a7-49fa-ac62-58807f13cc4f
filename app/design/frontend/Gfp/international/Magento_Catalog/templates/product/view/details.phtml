<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/** @var \Magento\Catalog\Block\Product\View\Details $block */
/** @var Magento\Framework\Escaper $escaper */
?>
<?php if ($detailedInfoGroup = $block->getGroupSortedChildNames('detailed_info', 'getChildHtml')):?>
    <?php
        $reviewTabId = "reviews.tab";
        if (in_array($reviewTabId, $detailedInfoGroup)) {
            unset($detailedInfoGroup[array_search($reviewTabId, $detailedInfoGroup)]);
            $detailedInfoGroup[] = $reviewTabId;
        }

        $layout = $block->getLayout();
        $tabHtml = [];
        foreach ($detailedInfoGroup as $name) {
            $html = $layout->renderElement($name);
            if (!trim($html)) {
                continue;
            }
            $tabHtml[$name] = $html;
        }
    ?>

    <div class="product info detailed">
        <div class="product data items">
                <div class="anchors-wrapper">
                <?php foreach ($detailedInfoGroup as $name):?>
                    <?php
                    $alias = $layout->getElementAlias($name);
                    $label = $block->getChildData($alias, 'title');
                    $id = $block->getChildBlock($name) ? $block->getChildBlock($name)->getJsId() : $alias;
                    ?>
                    <div class="data item title" >
                        <a class="data switch"
                           href="#section_<?= $escaper->escapeUrl($id) ?>"
                           id="tab-label-<?= $escaper->escapeHtmlAttr($id) ?>-title">
                            <span id="progress-bar_<?= $id ?>" class="scroll-progress-bar"></span>
                            <span><?= /* @noEscape */ $label ?></span>
                        </a>
                    </div>
                <?php endforeach;?>
                </div>
            <div class="sections contents-wrapper">
            <?php foreach ($detailedInfoGroup as $i => $name):?>
                <?php

                if (!array_key_exists($name, $tabHtml)) { continue; }
                $html = $tabHtml[$name];
                $alias = $layout->getElementAlias($name);
                $label = $block->getChildData($alias, 'title');
                $id = $block->getChildBlock($name) ? $block->getChildBlock($name)->getJsId() : $alias;
                ?>
                <div class="section-inner">
                <div class="section-header"><?= /* @noEscape */ $label ?></div>
                <div class="data item content <?php echo str_replace('.','-',$name); ?>" id="section_<?= $escaper->escapeHtmlAttr($id) ?>" data-mage-init='{"scrollingSection":{
                    "anchor" : "[href=\"#section_<?= $id ?>\"]",
                    "progressBar" : "#progress-bar_<?= $id ?>",
                    "offsetTop" : 80
                }}'>

                    <?= /* @noEscape */ $html ?>
                </div>
                </div>
            <?php endforeach;?>
            </div>
        </div>
    </div>
<?php endif; ?>

<script>
    (function() {
        var images = document.querySelectorAll("div[class*='data item content product-tab-'] img");

        for (var i = 0; i < images.length; i++) {
            var image = images[i];
            var src = image.getAttribute('src');
            image.setAttribute('data-src',src);
            image.setAttribute('src','');
            if(!image.classList.contains('lzy_img')){
                image.classList.add('lzy_img')
            }
        }
    })()
</script>
