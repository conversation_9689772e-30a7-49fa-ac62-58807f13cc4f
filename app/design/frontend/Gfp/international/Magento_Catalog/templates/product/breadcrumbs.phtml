<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
/** @var \Magento\Theme\Block\Html\Breadcrumbs $block */
/** @var \Magento\Catalog\ViewModel\Product\Breadcrumbs $viewModel */
$viewModel = $block->getData('viewModel');
?>
<div class="breadcrumbs">
    <ul class="items">
        <div class="breadcrumbs-inner" />
    </ul>
</div>
<?php
$widget = $this->helper(\Magento\Framework\Json\Helper\Data::class)->jsonDecode($viewModel->getJsonConfigurationHtmlEscaped());
$widgetOptions = $this->helper(\Magento\Framework\Json\Helper\Data::class)->jsonEncode($widget['breadcrumbs']);
?>
<script type="text/x-magento-init">
    {
        ".breadcrumbs-inner": {
            "breadcrumbs": <?= /* @noEscape */ $widgetOptions ?>
        }
    }
</script>
