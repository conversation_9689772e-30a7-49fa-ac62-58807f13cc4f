<?php

use Mstage\Debugging\Helper\Logger\BeLogger;
use Mstage\MstageHelper\Helper\Data\Stream;
use Xortex\App\Catalog\Block\Category\HomeCategories;

/** @var $block HomeCategories */

$_product = $block->getCurrentProduct();
$_helper  = $this->helper('Magento\Catalog\Helper\Output');
$imageHelper = $this->helper('Xortex\Xbase\Helper\Image');

// Get corresponding 2. level category and save it in $cat
$cat = null;
try {
    $categoryIds = $_product->getCategoryIds();
    $rootCat = Stream::from($block->getLevelCategories(1))->first();
    $subCats = Stream::from($block->getLevelCategories(2))->distinct();
    $cat = $subCats->first(function ($sub) use ($categoryIds) {
        return in_array($sub->getId(), $categoryIds); });

} catch (Exception $ignore) {
}


// If Category could be found
if($cat) {
    // Get subcategories
    /** @var $categories \Magento\Catalog\Model\Category[] $categories */
    $categories = $block->getLevelCategories(3, $cat);

    // If it has subcategories render linepicker
    if (count($categories) > 0) { ?>
        <h3><?php echo __("Noch nicht das richtige Produkt? Hier geht es direkt zur Produktübersicht!"); ?></h3>
        <div class="product-linepicker-outer">
        <?php foreach ($categories as $subcategory) { ?>
                <div class="product-linepicker-item">
                    <a href="<?php echo $subcategory->getUrl(); ?>" title="<?php echo $subcategory->getName(); ?>">
                        <div class="product-linepicker-item-top">
                            <?php  if ($_imgUrl = $subcategory->getImageUrl()) {
                                $_imgUrl = $imageHelper->init($_imgUrl)->cropImageToDimension(true)->resize(400, 250)->getUrl();

                                $_imgHtml = '<img class="lzy_img product-linepicker-image" data-src="' . $_imgUrl . '" alt="' . $block->escapeHtml($subcategory->getName()) . '" title="' . $block->escapeHtml($subcategory->getName()) . '" class="image" />';
                                $_imgHtml = $_helper->categoryAttribute($subcategory, $_imgHtml, 'image');
                                echo $_imgHtml;
                            }  ?>
                            <div class="featured-product-button">
                                <img class="lzy_img" data-src="<?php echo $this->getViewFileUrl('images/assets/icon_info.svg'); ?>">
                                <span><?php echo __("More"); ?></span>
                            </div>
                        </div>
                        <strong><?php echo $subcategory->getName(); ?></strong>

                        <div class="product-linepicker-description">
                            <?php if($subcategory->getLowestprice()) :
                                echo "<p class='priceDiv'>" . $_helper->categoryAttribute($subcategory, $subcategory->getLowestprice(), 'lowestprice') . "</p>";
                            endif; ?>
                        </div>
                    </a>
                </div>
        <?php } ?>
        </div>
    <?php }
}

