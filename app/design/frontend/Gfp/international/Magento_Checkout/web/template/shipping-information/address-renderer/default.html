<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<if args="visible()">
    <text args="address().prefix"></text> <text args="address().firstname"></text> <text args="address().middlename"></text>
    <text args="address().lastname"></text> <text args="address().suffix"></text><br/>
    <text args="_.values(address().street).join(', ')"></text><br/>
    <text args="address().postcode"></text> <text args="address().city "></text><br/>
    <text args="getCountryName(address().countryId)"></text><br/>
    <a if="address().telephone" attr="'href': 'tel:' + address().telephone" text="address().telephone"></a><br/>

    <each args="data: address().customAttributes, as: 'element'">
        <text args="$parent.getCustomAttributeLabel(element)"></text>
        <br/>
    </each>
</if>
