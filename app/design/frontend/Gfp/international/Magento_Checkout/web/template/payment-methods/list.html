<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
 -->
<div if="isPaymentMethodsAvailable()" class="items payment-methods">
    <div repeat="foreach: paymentGroupsList, item: '$group'" class="payment-group-mobile">
        <div class="payment-methods-title">
            <div if="regionHasElements($group().displayArea)"
                 translate="getGroupTitle($group)"
                 class="step-title-new"
                 data-role="title">
            </div>
        </div>
        <div class="payment-methods-methods-desktop">
            <div class="payment-methods-desktop-container">
                <!-- ko foreach: {data: getRegion($group().displayArea), as: 'method'} -->
                <div class="payment-method-title-desktop" data-bind="attr: {'data-id': getCode(), 'id': getCode() + '-block'}">
                    <div data-bind="attr: {'data-id': getCode(), 'class': getCode() + '-payment-image'}"></div>
                    <div class="desktop-payment-description" data-bind="attr: {'data-id': getCode()}">
                        <div class="desktop-payment-description-content" data-bind="attr: {'data-id': getCode()}">
                            <!-- ko text: getTitle() --><!-- /ko -->
                        </div>
                    </div>
                </div>
                <!-- /ko -->
            </div>
        </div>
        <div id="choose-payment-method-button-wrapper">
            <div style="padding: 0 8px 4px; display: none; gap: 0.5rem; font-size: 1rem; line-height: 1.5rem; font-weight: 600; color: #B91C1C; align-items: center;" class="error">
                <svg xmlns="http://www.w3.org/2000/svg" style="width:20px; height:20px; display: inline;" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <span style="font-size: 1.5rem;" class="error" data-bind="i18n: 'Choose payment method & order'"></span>
            </div>
            <div id="opc-choose-payment" class="button action opc-continue-button opc-choose-payment">
                <span data-bind="i18n: 'Choose payment method & order'"></span>
            </div>
        </div>
    </div>
    <div repeat="foreach: paymentGroupsList, item: '$group'" class="payment-group">
        <div class="payment-methods-title main-payment-title">
            <div if="regionHasElements($group().displayArea)"
                 translate="getGroupTitle($group)"
                 class="step-title-new"
                 data-role="title">
            </div>
        </div>
        <div class="payment-methods-methods">
            <each args="data: getRegion($group().displayArea), as: 'method'" render=""></each>
        </div>
    </div>
</div>
<div ifnot="isPaymentMethodsAvailable()"
     class="no-payments-block"
     translate="'No Payment Methods'">
</div>
