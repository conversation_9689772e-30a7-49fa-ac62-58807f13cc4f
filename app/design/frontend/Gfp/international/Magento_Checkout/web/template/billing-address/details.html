<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div if="isAddressDetailsVisible() && currentBillingAddress()" class="billing-address-details">
    <text args="currentBillingAddress().prefix"></text> <text args="currentBillingAddress().firstname"></text>
    <text args="currentBillingAddress().middlename"></text>
    <text args="currentBillingAddress().lastname"></text> <text args="currentBillingAddress().suffix"></text><br/>
    <text args="currentBillingAddress().street.join(', ')"></text><br/>
    <text args="currentBillingAddress().postcode"></text> <text args="currentBillingAddress().city "></text><br/>
    <text args="getCountryName(currentBillingAddress().countryId)"></text><br/>
    <a if="currentBillingAddress().telephone" attr="'href': 'tel:' + currentBillingAddress().telephone" text="currentBillingAddress().telephone"></a><br/>

    <each args="data: currentBillingAddress().customAttributes, as: 'element'">
        <text args="$parent.getCustomAttributeLabel(element)"></text>
        <br/>
    </each>

    <button visible="!isAddressSameAsShipping()"
            type="button"
            class="action action-edit-address"
            click="editAddress">
        <span translate="'Edit'"></span>
    </button>
</div>

