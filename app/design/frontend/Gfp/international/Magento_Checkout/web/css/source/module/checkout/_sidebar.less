// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@checkout-sidebar__margin: @indent__base;
@checkout-sidebar__margin__xl: 46px;
@checkout-sidebar__columns: 4;

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .opc-sidebar {
        .lib-css(margin, @checkout-sidebar__margin__xl 0 @checkout-sidebar__margin);
        .lib-layout-column(2, 2, @checkout-sidebar__columns);
    }
}
