<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php /** @var $block \Magento\Checkout\Block\Cart\Shipping */ ?>

<div id="block-shipping" class="block shipping" data-mage-init='{"collapsible":{"openedState": "active", "saveState": true}}'>
    <div id="block-summary" data-bind="scope:'block-summary'" class="content" data-role="content" aria-labelledby="block-shipping-heading">
        <!-- ko template: getTemplate() --><!-- /ko -->
        <script type="text/x-magento-init">
            {
                "#block-summary": {
                    "Magento_Ui/js/core/app": <?= /* @escapeNotVerified */ $block->getJsLayout() ?>
                }
            }
        </script>
        <script>
            window.checkoutConfig = <?= /* @escapeNotVerified */ $block->getSerializedCheckoutConfig() ?>;
            window.customerData = window.checkoutConfig.customerData;
            window.isCustomerLoggedIn = window.checkoutConfig.isCustomerLoggedIn;
            require([
                'mage/url',
                'Magento_Ui/js/block-loader'
            ], function(url, blockLoader) {
                blockLoader("<?= /* @escapeNotVerified */ $block->getViewFileUrl('images/loader-1.gif') ?>");
                return url.setBaseUrl('<?= /* @escapeNotVerified */ $block->getBaseUrl() ?>');
            })
        </script>
    </div>
</div>