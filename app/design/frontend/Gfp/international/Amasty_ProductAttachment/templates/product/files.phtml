<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) 2018 Amasty (https://www.amasty.com)
 * @package Amasty_ProductAttachment
 */
?>

<?php
/**
 * @var \Amasty\ProductAttachment\Block\Catalog\Product\Attachment $this
 */

/**
 * @var \Amasty\ProductAttachment\Model\ResourceModel\File\Collection $customerFiles
 */
$attachmentCollection = $this->getAttachmentCollection();
?>

<div class="amasty-product-attachment-block">
    <?php if ($block->displayHead()) : ?>
        <h3 class="label"><?php echo $this->getBlockLabel()?></h3>
    <?php endif;?>
        <ul class="list-unstyled">
            <?php foreach ($attachmentCollection as $file) : ?>
                <?php
                /**
                 * @var \Amasty\ProductAttachment\Model\File $file
                 */
                ?>
                <li>
                  <?php
                    $fileType = $file->getData('file_type');
                    $downloadUrl = $this->getDownloadUrl($file);
                    $label = $file->getLabel();
                    $icon = $file->getIconUrl();

                    if($fileType == 'url'){
                      $downloadUrl = $file->getData('file_url');
                      $icon = $block->getViewFileUrl('images/playbutton.png');
                    }
                  ?>
                    <a class='file' href="<?php echo $downloadUrl ?>" target="_blank">
                        <?php if ($icon) : ?>
                            <img
                                    src="<?php echo $this->escapeHtml($icon) ?>"
                                    alt="<?php echo $this->escapeHtml($file->getFileExtension()) ?>"
                            />
                        <?php endif?>
                        <span><?php echo $label ?>
                            <?php /*if ($size = $file->getFileSize() > 0) : ?>
                                (<?php echo $file->getFullFileSize();?>)
                            <?php endif; */?>
                        </span>
                    </a>
                </li>
            <?php endforeach ?>

            <?php
            // APPEND Datasheet Download from Produkt
            /** @var \Xortex\App\ViewModel\Product $product */
            $product = $this->getProduct();
            if($product && $product->hasDatasheetPdf()) { ?>
                <li>
                    <a id="product-datasheet-pdf" class='file' href="<?php echo $product->getDatasheetPdfUrl()?>" target="_blank">
                        <?php if ($icon = $product->getDatasheetIconUrl()) : ?>
                            <img
                                    src="<?php echo $this->escapeHtml($icon) ?>"
                                    alt="<?php echo $this->escapeHtml($product->getDatasheetFileExtension()) ?>"
                            />
                        <?php endif?>
                        <span><?php echo $product->getDatasheetLabel(); ?>
                        </span>
                    </a>
                </li>
                <?php
            } ?>
        </ul>
</div>
