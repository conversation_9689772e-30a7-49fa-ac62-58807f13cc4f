<?php
/**
 * @var \Amasty\AdvancedReview\Block\Summary $block
 * @var \Magento\Framework\Escaper $escaper
 */

// phpcs:ignoreFile
$rating = $block->getRatingSummary();
$count = $block->getReviewsCount();
?>

<div class="amreview-summary-info">
    <p class="amreview-summary"><?= $escaper->escapeHtml($block->getRatingSummaryValue()) ?></p>
    <div class="amreview-rating-wrapper">
        <div class="amstars-rating-container"
            <?php $stars = $block->getRatingSummaryValue(); ?>
             title="<?= $escaper->escapeHtml($stars > 1 ? __('%1 stars', $stars) : __('%1 star', $stars)) ?>">
            <p class="amstars-stars" style="width:<?= $escaper->escapeHtml($rating); ?>%">
                <span class="hidden"><?= $escaper->escapeHtml($rating); ?>%</span>
            </p>
        </div>
        <?php if($count>=50): ?>
        <p class="amreview-count"><?= /* @noEscape */ $count; ?> <?= $escaper->escapeHtml(__('reviews'));?></p>
        <?php endif; ?>
        <p class="amreview-showmore" data-amreview-js="show-more"><?= $escaper->escapeHtml(__('More info'));?></p>
    </div>
    <?php if ($block->shouldShowRecommended()): ?>
    <p class="amreview-percent-block" data-amreview-js="percent">
        <span class="amreview-value"><?= $escaper->escapeHtml($block->getRecomendedPercent());?>%</span>
        <span class="amreview-desc"><?= $escaper->escapeHtml(__('of customers recommend this product'));?></span>
    </p>
    <?php endif; ?>
</div>
