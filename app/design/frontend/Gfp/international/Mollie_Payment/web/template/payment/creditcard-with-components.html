<div class="payment-method payment-method-block" data-bind="css: {'_active': (getCode() == isChecked())}, afterRender: function () { rendered(true) }">
    <div class="payment-method-title field choice">
        <input type="radio"
               name="payment[method]"
               class="radio"
               data-bind="attr: {'id': getCode()}, value: getCode(), checked: isChecked, click: selectPaymentMethod, visible: isRadioButtonVisible()"/>
        <div class="payment-method-container">
            <div data-bind="attr: {'for': getCode()}" class="creditcard-image-container"></div>
            <div class="payment-method-label-container">
                <label data-bind="attr: {'for': getCode()}" class="label">
                    <span data-bind="text: getTitle()"></span>
                </label>
            </div>
        </div>
    </div>
    <div class="payment-method-content">
        <div class="col-left">
            <!-- ko foreach: getRegion('messages') -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!--/ko-->

            <div class="card-container">
                <label for="card-holder" data-bind="i18n: 'Name on card'"></label>
                <div id="card-holder"></div>
                <div id="card-holder-error" class="component-error"></div>

                <label for="card-number" data-bind="i18n: 'Card Number'"></label>
                <div id="card-number"></div>
                <div id="card-number-error" class="component-error"></div>

                <label for="expiry-date" data-bind="i18n: 'Expiry Date'"></label>
                <div id="expiry-date"></div>
                <div id="expiry-date-error" class="component-error"></div>

                <label for="verification-code" data-bind="i18n: 'CVC/CVV'"></label>
                <div id="verification-code"></div>
                <div id="verification-code-error" class="component-error"></div>
            </div>

            <!-- ko if: (isVaultEnabled())-->
            <div class="field choice">
                <input type="checkbox"
                       name="vault[is_enabled]"
                       class="checkbox"
                       data-bind="attr: {'id': getCode() + '_enable_vault'}, checked: vaultEnabler.isActivePaymentTokenEnabler"/>
                <label class="label" data-bind="attr: {'for': getCode() + '_enable_vault'}">
                    <span><!-- ko i18n: 'Save for later use.'--><!-- /ko --></span>
                </label>
            </div>
            <!-- /ko -->

            <div class="payment-method-billing-address">
                <!-- ko foreach: $parent.getRegion(getBillingAddressFormName()) -->
                <!-- ko template: getTemplate() --><!-- /ko -->
                <!--/ko-->
            </div>
        </div>
        <div class="col-right">
            <div class="checkout-agreements-block">
                <!-- ko foreach: $parent.getRegion('before-place-order') -->
                <!-- ko template: getTemplate() --><!-- /ko -->
                <!--/ko-->
            </div>
            <div class="actions-toolbar">
                <div class="primary">
                    <button class="action primary checkout"
                            type="submit"
                            data-bind="
                            click: placeOrder,
                            attr: {title: $t('Place Order')},
                            css: {disabled: !isPlaceOrderActionAllowed()},
                            enable: (getCode() == isChecked())"
                            disabled>
                        <span data-bind="i18n: 'Place Order'"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
