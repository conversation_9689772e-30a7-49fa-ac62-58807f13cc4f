<div id="amazon-payment"
     class="payment-method payment-method-block"
     data-bind="css: {'_active': (getCode() == isChecked())}, click: selectPaymentMethod">
    <div class="payment-method-title field choice">
        <input type="radio"
               name="payment[method]"
               class="radio"
               data-bind="attr: {'id': getCode()}, value: getCode(), checked: isChecked, visible: isRadioButtonVisible()"/>
        <div class="amazon-image-container">
            <img class="amazon-image-image" alt="Amazon Pay" data-bind="attr: { src: getLogoUrl() } "/>
        </div>
        <div class="payment-method-label-container">
            <label data-bind="attr: {'for': getCode()}" class="label" style="width: 100%;">
                <span data-bind="text: getTitle()"></span>
            </label>
        </div>
        <span data-bind="text: paymentDescriptor"></span>
    </div>
    <div class="payment-method-content">
        <div class="col-left">
        <!-- ko foreach: getRegion('messages') -->
        <!-- ko template: getTemplate() --><!-- /ko -->
        <!--/ko-->
        <!-- ko if: isAmazonCheckout -->
            <div class="amazon-payment-action-container">
                <button class="action action-edit-payment"
                        type="button"
                        data-bind="afterRender: bindEditPaymentAction($element)">
                    <span translate="'Edit Payment Method'"></span>
                </button>
            </div>
        <!--/ko-->
        <!-- ko ifnot: isAmazonCheckout -->
            <div class="amazon-button-container">
                <div class="amazon-button-column">
                    <div id="PayWithAmazonButton"
                         class="amazon-checkout-button"
                         data-bind="mageInit: {'Amazon_Pay/js/amazon-button':{'placement': 'PayNow'}}"></div>
                </div>
                <div class="amazon-button-column amazon-button-column-tooltip">
                    <div class="field-tooltip toggle">
                        <span class="field-tooltip-action action-help"
                              data-bind="mageInit: {'dropdown':{'activeClass': '_active'}}"
                              data-toggle="dropdown"
                              aria-haspopup="true"
                              aria-expanded="false">
                        </span>
                        <div data-bind="i18n: 'Are you an Amazon customer? Pay now with address and payment details stored in your Amazon account.'"
                             class="field-tooltip-content"
                             data-target="dropdown"
                             aria-hidden="true">
                        </div>
                    </div>
                </div>
            </div>
        <!--/ko-->
        <!-- ko if: isBillingAddressVisible -->
            <div class="payment-method-billing-address">
                <!-- ko foreach: $parent.getRegion(getBillingAddressFormName()) -->
                <!-- ko template: getTemplate() --><!-- /ko -->
                <!--/ko-->
            </div>
        <!--/ko-->
        </div>
        <div class="col-right">
            <div class="checkout-agreements-block">
                <!-- ko foreach: $parent.getRegion('before-place-order') -->
                    <!-- ko template: getTemplate() --><!-- /ko -->
                <!--/ko-->
            </div>
            <!-- ko if: isAmazonCheckout() || isIosc() -->
                <div class="actions-toolbar">
                    <div class="primary">
                        <button class="action primary checkout"
                                type="submit"
                                data-bind="
                                click: isAmazonCheckout() ? placeOrder : payNow,
                                attr: {title: $t('Place Order')},
                                enable: (getCode() == isChecked())
                                "
                                disabled>
                            <span data-bind="i18n: 'Place Order'"></span>
                        </button>
                    </div>
                </div>
            <!--/ko-->
        </div>
    </div>
</div>

