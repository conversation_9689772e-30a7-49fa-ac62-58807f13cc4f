define([
    'jquery'
], function ($) {
    'use strict';

    let mixin = {
        urlParams: [],

        initialize: function () {
            this._super();
            this.fillReviewFormWithUrlParams();
            this.scrollToReviewForm();
        },

        fillReviewFormWithUrlParams: function () {
            let url = window.location.search.substring(1);
            this.urlParams = url.split('&');

            let nickname = this.getUrlParameter('nickname');
            let title = this.getUrlParameter('title');
            let detail = this.getUrlParameter('detail');

            if (nickname) {
                this.review().nickname = nickname;
            }

            if (title) {
                this.review().title = title;
            }

            if (detail) {
                this.review().detail = detail;
            }
        },

        scrollToReviewForm: function () {
            let hash = window.location.hash;

            if (hash) {
                $('a[href="' + hash + '"]').trigger('click');
            }
        },

        getUrlParameter: function (param) {
            let self = this;
            let urlParam, i;

            for (i = 0; i < self.urlParams.length; i++) {
                urlParam = self.urlParams[i].split('=');

                if (urlParam[0] === param) {
                    return typeof urlParam[1] === undefined ? false : decodeURIComponent(urlParam[1]);
                }
            }
            return false;
        }
    };

    return function (target) {
        return target.extend(mixin);
    };
});
