<?php
if ($this->_helper->enableModule()): ?>
    <?php if ($this->isproductSet()): ?>
        <?php if ($this->_helper->enableProductRelatedGallery()): ?>

            <?php $galleryImages = $this->getGalleryForCatorPro(); ?>

            <?php if (count($galleryImages)): ?>
                <div class="customer-gallery-outer">

                    <div class="product-shadowbox-gallery-content accordion-content" style="width:90%;" id="gallery">
                        <h2><?php echo __("Customer gallery")?></h2>
                        <div class="fotorama__zoom-icon"></div>
                        <div class="customer-gallery-container slick-slider">
                            <?php foreach ($galleryImages as $_gallery) :
                                $imageFile = $this->_helper->getMediaUrl($_gallery["img_name"]);
                                $imgLabel = $_gallery["img_label"];
                                ?>

                                <div class="customer-gallery-item">
                                    <div class="customer-gallery-item-inner">
                                        <a href="<?php echo $imageFile; ?>" rel="shadowbox[gallery_product_detail]">
                                            <img data-lazy="<?php echo $imageFile; ?>" />
                                        </a>
                                        <?php if($imgLabel): ?>
                                            <div class="customer-gallery-item-label">
                                                <span><?php echo $imgLabel ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                            <?php endforeach ?>
                        </div>

                        <div class="customer-thumbnails-container slick-slider">
                            <?php foreach ($galleryImages as $_gallery) :
                                $targetPath = $this->_helper->getMediaUrl($_gallery["img_name"]);
                                $thumbPath = $this->_helper->getThumbsDirPath($targetPath);
                                $arrayName = explode('/', $_gallery["img_name"]);
                                $thumbnail_path = $thumbPath . $arrayName[3];
                                ?>

                                <div class="customer-thumbnails-item">
                                    <div class="customer-thumbnails-item-inner">
                                        <img data-lazy="<?php echo $thumbnail_path; ?>" />
                                    </div>
                                </div>

                            <?php endforeach ?>
                        </div>
                    </div>
                </div>

                <script type="text/javascript">
                    requirejs(["jquery","slick"],
                        function($) {
                            var slider = $('.customer-gallery-container');
                            var thumbnails = $('.customer-thumbnails-container');
                            slider.slick({
                                infinite: false,
                                slidesToShow: 1,
                                autoplay: false,
                                arrow: true,
                                dots: false,
                                asNavFor: thumbnails,
                                lazyLoad: 'ondemand',
                                adaptiveHeight: false
                            });
                            thumbnails.slick({
                                infinite: false,
                                slidesToShow: 5,
                                autoplay: false,
                                arrow: false,
                                dots: false,
                                asNavFor: slider,
                                focusOnSelect: true,
                                lazyLoad: 'ondemand',
                                responsive: [
                                    {
                                        breakpoint: 767,
                                        settings: {
                                            slidesToShow: 3
                                        }
                                    }
                                ]
                            });
                        }
                    );
                </script>

            <?php endif; ?>




            <?php // @codingStandardsIgnoreStart ?>
            <script>
                require(["jquery", "jqueryfunction", "shadowbox"],
                    function ($, jqueryfunction, shadowbox) {
                        $(document).ready(function () {
                            Shadowbox.init({
                                displayNav: true,
                                overlayOpacity: 0.8,
                                flashVars: {
                                    skin: '<?php echo $this->_helper->getJsUrl('js/skin01.zip') ?>'
                                }
                            });
                        });
                    });
            </script>
            <?php // @codingStandardsIgnoreEnd ?>

        <?php endif; ?>
    <?php endif; ?>
<?php endif; ?>