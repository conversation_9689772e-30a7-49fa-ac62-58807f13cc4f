/* Style the tab */
#loading {width: 100%; display:none; height: 100%;top: 0px;left: 0px;position: fixed;display: block; z-index: 99}

#loading-image {position: absolute;width:50px; height:50px;top: 40%;left: 45%; display:none; z-index: 100}
/* Style the buttons inside the tab */


/* Change background color of buttons on hover */


/* Create an active/current tablink class */


/* Style the tab content */

.tab button.active {    background-color: #ffffff;    color: #fff;}

.tabcontent {    padding: 40px 0px;}
.cbp-l-loadMore-button-link:hover {
    cursor:pointer;
}
.loader {

    border: 7px solid #f3f3f3;
    border-radius: 50%;
    border-top: 7px solid #1979c3;
    width: 50px;
    height: 50px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
    margin-left:50%;
    margin-top: 5%;
    display:none;
}

@-webkit-keyframes spin {
    0% { -webkit-transform: rotate(0deg); }
    100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}