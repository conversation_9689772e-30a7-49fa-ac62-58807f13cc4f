<?php
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile
?>
<?php /** @var \Magento\Cookie\Block\Html\Notices $block */ ?>
<?php if ($this->helper('<PERSON><PERSON><PERSON>\Cookie\Helper\Cookie')->isCookieRestrictionModeEnabled()): ?>
    <div class="message global cookie" id="notice-cookie-block" style="display: none">
        <div class="content">
            <p>

                <strong><?php /* @escapeNotVerified */ echo __('GFP International uses cookies and facebook pixel tracking to give you a better online experience.') ?></strong>
                <span><?php /* @escapeNotVerified */ echo __('You can always change your cookie settings.') ?></span>
                <?php /* @escapeNotVerified */ echo __("Please visit our <a href='%1'>cookie information page</a>.", $block->getPrivacyPolicyLink()) ?></p>
            <div class="actions">
                <button id="btn-cookie-allow" class="action allow primary">
                    <span><?php /* @escapeNotVerified */ echo __('Allow Cookies');?></span>
                </button>
            </div>
        </div>
    </div>
    <script type="text/x-magento-init">
        {
            "#notice-cookie-block": {
                "cookieNotices": {
                    "cookieAllowButtonSelector": "#btn-cookie-allow",
                    "cookieName": "<?php /* @escapeNotVerified */ echo \Magento\Cookie\Helper\Cookie::IS_USER_ALLOWED_SAVE_COOKIE ?>",
                    "cookieValue": <?php /* @escapeNotVerified */ echo $this->helper('Magento\Cookie\Helper\Cookie')->getAcceptedSaveCookiesWebsiteIds() ?>,
                    "cookieLifetime": <?php /* @escapeNotVerified */ echo $this->helper('Magento\Cookie\Helper\Cookie')->getCookieRestrictionLifetime()?>,
                    "noCookiesUrl": "<?php /* @escapeNotVerified */ echo $block->getUrl('cookie/index/noCookies') ?>"
                }
            }
        }
    </script>
<?php endif; ?>
