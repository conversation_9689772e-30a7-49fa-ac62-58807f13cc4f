<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!-- ko if: quoteIsVirtual == 0 -->
    <!-- ko if: isBothPricesDisplayed() -->
    <tr class="totals shipping excl">
        <th class="mark" scope="row">
            <span class="label" data-bind="text: title+ ' ' + excludingTaxMessage"></span>
            <!-- ko if: haveToShowCoupon() -->
                <span class="label description" data-bind="text: getCouponDescription()"></span>
            <!-- /ko -->
            <span class="value" data-bind="text: getShippingMethodTitle()"></span>
        </th>
        <td class="amount">
            <!-- ko if: isCalculated() -->
            <span class="price"
                  data-bind="text: getExcludingValue(), attr: {'data-th': excludingTaxMessage}"></span>
            <!-- /ko -->
            <!-- ko ifnot: isCalculated() -->
            <span class="not-calculated"
                  data-bind="text: getExcludingValue(), attr: {'data-th': excludingTaxMessage}"></span>
            <!-- /ko -->
        </td>
    </tr>
    <tr class="totals shipping incl" data-bind="css:{free: parseInt(getIncludingValue()) == 0}">
        <th class="mark" scope="row">
            <div class="shipping-label">
                <svg xmlns="http://www.w3.org/2000/svg" style="display: inline; width:1.75rem; height: 1.75rem;" viewBox="0 0 640 512"><path d="M48 0C21.5 0 0 21.5 0 48V368c0 26.5 21.5 48 48 48H64c0 53 43 96 96 96s96-43 96-96H384c0 53 43 96 96 96s96-43 96-96h32c17.7 0 32-14.3 32-32s-14.3-32-32-32V288 256 237.3c0-17-6.7-33.3-18.7-45.3L512 114.7c-12-12-28.3-18.7-45.3-18.7H416V48c0-26.5-21.5-48-48-48H48zM416 160h50.7L544 237.3V256H416V160zM112 416a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm368-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"/></svg>
                <span class="label" data-bind="text: title + ' ' + includingTaxMessage"></span>
            </div>
            <!-- ko if: haveToShowCoupon() -->
                <span class="label description" data-bind="text: getCouponDescription()"></span>
            <!-- /ko -->
        </th>
        <td class="amount">
            <!-- ko if: isCalculated() -->
            <!-- ko if: getIncludingValue() == 0 -->
            <span class="price"
                  data-bind="i18n: 'kostenlos'"></span>
            <!-- /ko -->
            <!-- ko if: getIncludingValue() !== 0 -->
            <span class="price"
                  data-bind="text: getIncludingValue(), attr: {'data-th': title + ' ' + excludingTaxMessage}"></span>
            <!-- /ko -->
            <!-- /ko -->
            <!-- ko ifnot: isCalculated() -->
            <span class="not-calculated"
                  data-bind="text: getIncludingValue(), attr: {'data-th': title + ' ' + excludingTaxMessage}"></span>
            <!-- /ko -->
        </td>
    </tr>
    <!-- /ko -->
    <!-- ko if: isIncludingDisplayed() -->
    <tr class="totals shipping incl" data-bind="css:{free: parseInt(getIncludingValue()) == 0}">
        <th class="mark" scope="row">
            <div class="shipping-label">
                <svg xmlns="http://www.w3.org/2000/svg" style="display: inline-block;width: 24px;height: 24px;margin-right: 8px;" viewBox="0 0 640 512" fill="currentColor"><path d="M48 0C21.5 0 0 21.5 0 48V368c0 26.5 21.5 48 48 48H64c0 53 43 96 96 96s96-43 96-96H384c0 53 43 96 96 96s96-43 96-96h32c17.7 0 32-14.3 32-32s-14.3-32-32-32V288 256 237.3c0-17-6.7-33.3-18.7-45.3L512 114.7c-12-12-28.3-18.7-45.3-18.7H416V48c0-26.5-21.5-48-48-48H48zM416 160h50.7L544 237.3V256H416V160zM112 416a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm368-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"></path></svg>
                <span class="label" data-bind="i18n: title"></span>
            </div>
            <!-- ko if: haveToShowCoupon() -->
                <span class="label description" data-bind="text: getCouponDescription()"></span>
            <!-- /ko -->
        </th>
        <td class="amount">
            <!-- ko if: isCalculated() -->
                <!-- ko if: parseInt(getIncludingValue()) == 0 -->
                <span class="price"
                      data-bind="i18n: 'kostenlos'"></span>
                <!-- /ko -->
                <!-- ko if: parseInt(getIncludingValue()) !== 0 -->
                <span class="price"
                      data-bind="text: getIncludingValue(), attr: {'data-th': title}"></span>
                <!-- /ko -->
            <!-- /ko -->
            <!-- ko ifnot: isCalculated() -->
            <span class="not-calculated"
                  data-bind="text: getIncludingValue(), attr: {'data-th': title}"></span>
            <!-- /ko -->
        </td>
    </tr>
    <!-- /ko -->
    <!-- ko if: isExcludingDisplayed() -->
    <tr class="totals shipping excl">
        <th class="mark" scope="row">
            <span class="label" data-bind="i18n: title"></span>
            <!-- ko if: haveToShowCoupon() -->
                <span class="label description" data-bind="text: getCouponDescription()"></span>
            <!-- /ko -->
            <span class="value" data-bind="text: getShippingMethodTitle()"></span>
        </th>
        <td class="amount">
            <!-- ko if: isCalculated() -->
            <span class="price"
                  data-bind="text: getValue(), attr: {'data-th': title}"></span>
            <!-- /ko -->
            <!-- ko ifnot: isCalculated() -->
            <span class="not-calculated"
                  data-bind="text: getValue(), attr: {'data-th': title}"></span>
            <!-- /ko -->
        </td>
    </tr>
    <!-- /ko -->
<!-- /ko -->
