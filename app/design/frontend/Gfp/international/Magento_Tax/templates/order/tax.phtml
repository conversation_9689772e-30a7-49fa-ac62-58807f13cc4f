<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php
    $_order  = $block->getOrder();
    $_source = $block->getSource();
    $_fullInfo = $this->helper('Magento\Tax\Helper\Data')->getCalculatedTaxes($_source);
    global $taxIter; $taxIter++;
?>


<?php if ($_fullInfo && $block->displayFullSummary()): ?>
        <?php foreach ($_fullInfo as $info): ?>
            <?php
                $percent    = $info['percent'];
                $amount     = $info['tax_amount'];
                $baseAmount = $info['base_tax_amount'];
                $title      = $info['title'];
            ?>
            <tr class="totals tax details details-<?= /* @escapeNotVerified */ $taxIter ?> <?= ($block->getIsPlaneMode()) ? ' plane' : '' ?>">
                <td <?= /* @escapeNotVerified */ $block->getLabelProperties() ?>>
                    <?= /* @escapeNotVerified */ __('Tax') ?>
                    <?php if (!is_null($percent)): ?>
                        (<?= (float)$percent ?>%)
                    <?php endif; ?>
                    <br />
                </td>
                <td <?= /* @escapeNotVerified */ $block->getValueProperties() ?> rowspan="1">
                    <?= /* @escapeNotVerified */ $_order->formatPrice($amount) ?>
                </td>
            </tr>
        <?php endforeach; ?>
        <?php if (count($_fullInfo) > 1): ?>
            <?php if (!$block->getIsPlaneMode()): ?>
            <tr class="totals-tax-summary">
            <?php elseif ($block->getIsPlaneMode()): ?>
            <tr class="totals-tax-summary plane">
            <?php endif; ?>
                <th <?= /* @escapeNotVerified */ $block->getLabelProperties() ?> scope="row">
                    <div class="detailed"><?= /* @escapeNotVerified */ __('Tax') ?></div>
                </th>
                <td <?= /* @escapeNotVerified */ $block->getValueProperties() ?> data-th="<?= $block->escapeHtml(__('Tax')) ?>">
                    <?= /* @escapeNotVerified */ $_order->formatPrice($_source->getTaxAmount()) ?>
                </td>
            </tr>
        <?php endif;?>
<?php else: ?>
    <tr class="totals-tax">
        <th <?= /* @escapeNotVerified */ $block->getLabelProperties() ?> scope="row">
            <?php if ($block->displayFullSummary()): ?>
                <div class="detailed"><?= /* @escapeNotVerified */ __('Tax') ?></div>
            <?php else: ?>
                <?= /* @escapeNotVerified */ __('Tax') ?>
            <?php endif;?>
        </th>
        <td <?= /* @escapeNotVerified */ $block->getValueProperties() ?> data-th="<?= $block->escapeHtml(__('Tax')) ?>">
            <?= /* @escapeNotVerified */ $_order->formatPrice($_source->getTaxAmount()) ?>
        </td>
    </tr>
<?php endif;?>
