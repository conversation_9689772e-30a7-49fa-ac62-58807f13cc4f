<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
/**
 * @var Magento\Framework\View\Element\Template $block
 */
/**
 * @var Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer
 */
?>

<div class="cookie-status-message" id="cookie-status">
    <?= $block->escapeHtml(__('The store will not work correctly in the case when cookies are disabled.')); ?>
</div>
<?php
$script = 'document.querySelector("#cookie-status").style.display = "none";';
?>
<?= /* @noEscape */ $secureRenderer->renderTag('script', ['type' => 'text/javascript'], $script, false); ?>
<script type="text/x-magento-init">
    {
        "*": {
            "cookieStatus": {}
        }
    }
</script>
