define([
    'jquery',
    'jquery-ui-modules/widget'
], function ($) {
    'use strict';

    $.widget('theme.scrollingSection', {

        options: {
            anchor: null,
            progressBar : null,
            offsetTop : 50,
            scrollSpeed : 2500
        },

        states : {
            pending: 'pending',
            processing: 'processing',
            complete: 'completed'
        },

        _init: function () {
            this
                ._listen()
                ._showProgress();
        },

        _scrollTo : function(event){
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: $($(this.options.anchor).attr('href')).offset().top - this.options.offsetTop
            }, this.options.scrollSpeed, 'easeInOutQuart');
            return this;
        },

        _showProgress : function(){
            var state,
                winScroll = document.body.scrollTop || document.documentElement.scrollTop,
                elOffsetTop = $(this.element).offset().top,
                elHeight = $(this.element).outerHeight(false),
                scrolled = winScroll  - elOffsetTop + $(window).height(),
                progress = (scrolled / (elHeight ? elHeight : 1)) * 100;

            progress = (progress > 100 ? 100 : progress < 0 ? 0 : progress);
            state = (progress === 100) ? this.states.complete : ((progress === 0) ? this.states.pending : this.states.processing);
            $(this.options.progressBar).css('width', progress + '%').attr('data-state', state);
            return this;
        },

        _listen: function () {
            $(window).on('scroll resize', this._showProgress.bind(this));
            $(this.options.anchor).on('click', this._scrollTo.bind(this));
            return this;
        }
    });

    return $.theme.scrollingSection;

});