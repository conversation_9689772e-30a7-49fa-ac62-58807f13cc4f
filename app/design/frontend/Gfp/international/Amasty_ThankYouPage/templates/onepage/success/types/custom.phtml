<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) 2021 Amasty (https://www.amasty.com)
 * @package Amasty_ThankYouPage
 */
?>

<?php
/**
 * @var \Amasty\ThankYouPage\Block\Onepage\Success\Types\CustomAbstract $block
 * @var Magento\Framework\Escaper $escaper
 */

try {
    $background = $escaper->escapeHtml($block->getBackgroundImage());
} catch (\Magento\Framework\Exception\NoSuchEntityException $e) {
    return;
}
$background = $background ? " background-image:url(" . $background . ")" : '';
?>

<div class="success-block-outer">
    <?php if (!$block->useCmsBlock()) : ?>
        <div>
            <h1 class="page-title">
            <span class="base">
                <?= $escaper->escapeHtml($block->getTitle()); ?>
            </span></h1>
        </div>

        <table>
            <tr>
                <td><?= $escaper->escapeHtml($block->getSubTitle()); ?></td>
            </tr>

            <tr>
                <td><?= $escaper->escapeHtml($block->getText()); ?></td>
            </tr>
        </table>
    <?php else : ?>
        <div class="success-block-inner"><?= $block->getCmsBlockContentHtml(); ?></div>
    <?php endif; ?>
</div>
