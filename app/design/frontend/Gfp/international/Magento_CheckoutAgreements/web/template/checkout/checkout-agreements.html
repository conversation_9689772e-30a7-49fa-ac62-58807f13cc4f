<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div data-role="checkout-agreements">
    <div class="checkout-agreements fieldset" data-bind="visible: isVisible">
        <!-- ko foreach: agreements -->
            <!-- ko if: ($parent.isAgreementRequired($data)) -->
            <div class="checkout-agreement field choice required">
                <input type="checkbox" class="required-entry"
                       data-bind="attr: {
                                    'id': $parent.getCheckboxId($parentContext, agreementId),
                                    'name': 'agreement[' + agreementId + ']',
                                    'value': agreementId
                                    }"/>
                <label class="label" data-bind="attr: {'for': $parent.getCheckboxId($parentContext, agreementId)}">
                    <button type="button"
                            class="action action-show"
                            data-bind="click: function(data, event) { return $parent.showContent(data, event) }"
                            >
                        <span data-bind="html: checkboxText"></span>
                    </button>
                </label>
            </div>
            <!-- /ko -->
        <!-- /ko -->
        <div class="running-agreements">
        <span data-bind="i18n: 'Mit ihrer Bestellung erklären Sie sich mit unseren'"></span>
        <!-- ko foreach: agreements -->
        <!-- ko ifnot: ($parent.isAgreementRequired($data)) -->
            <button type="button" class="action action-show"
                    data-bind="click: function(data, event) { return $parent.showContent(data, event) }">
                <span data-bind="html: checkboxText"></span>
            </button>
        <!-- /ko -->
        <!-- /ko -->
            <span data-bind="i18n: 'einverstanden.'"></span>
        </div>
        <div id="checkout-agreements-modal" data-bind="afterRender: initModal" style="display: none">
            <div class="checkout-agreements-item-content" data-bind="html: modalContent, style: {height: contentHeight, overflow:'auto' }"></div>
        </div>
    </div>
</div>
