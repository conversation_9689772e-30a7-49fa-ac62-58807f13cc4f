@font-face {
    font-family: luma-icons;
    src: url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/fonts/Luma-Icons.woff2') format('woff2'), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/fonts/Luma-Icons.woff') format('woff');
    font-weight: 400;
    font-style: normal;
    font-display: swap
}

@font-face {
    font-family: 'Font Awesome 5 Free';
    font-style: normal;
    font-weight: 900;
    font-display: block;
    src: url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-solid-900.eot');
    src: url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-solid-900.eot?#iefix') format('embedded-opentype'), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-solid-900.woff2') format('woff2'), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-solid-900.woff') format('woff'), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-solid-900.ttf') format('truetype'), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-solid-900.svg#fontawesome') format('svg')
}

@font-face {
    font-family: 'Font Awesome 5 Free';
    font-style: normal;
    font-weight: 400;
    font-display: block;
    src: url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-regular-400.eot');
    src: url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-regular-400.eot?#iefix') format('embedded-opentype'), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-regular-400.woff2') format('woff2'), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-regular-400.woff') format('woff'), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-regular-400.ttf') format('truetype'), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-regular-400.svg#fontawesome') format('svg')
}

@font-face {
    font-family: 'Font Awesome 5 Free';
    font-style: normal;
    font-weight: 900;
    font-display: block;
    src: local('Font Awesome '), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-solid-900.woff2') format(woff2), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-solid-900.woff') format(woff), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-solid-900.ttf') format(truetype), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-solid-900.svg') format(svg), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-solid-900.eot') format(embedded-opentype)
}

@font-face {
    font-family: 'Font Awesome 5 Free';
    font-style: normal;
    font-weight: 400;
    font-display: block;
    src: local('Font Awesome '), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-regular-400.woff2') format(woff2), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-regular-400.woff') format(woff), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-regular-400.ttf') format(truetype), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-regular-400.svg') format(svg), url('http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/webfonts/fa-regular-400.eot') format(embedded-opentype)
}

@font-face {
    font-family: futura-pt;
    src: url("https://use.typekit.net/af/2cd6bf/00000000000000000001008f/27/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n5&v=3") format("woff2"), url("https://use.typekit.net/af/2cd6bf/00000000000000000001008f/27/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n5&v=3") format("woff"), url("https://use.typekit.net/af/2cd6bf/00000000000000000001008f/27/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n5&v=3") format("opentype");
    font-display: auto;
    font-style: normal;
    font-weight: 500
}

@font-face {
    font-family: futura-pt;
    src: url("https://use.typekit.net/af/1eb35a/000000000000000000010090/27/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i5&v=3") format("woff2"), url("https://use.typekit.net/af/1eb35a/000000000000000000010090/27/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i5&v=3") format("woff"), url("https://use.typekit.net/af/1eb35a/000000000000000000010090/27/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i5&v=3") format("opentype");
    font-display: auto;
    font-style: italic;
    font-weight: 500
}

@font-face {
    font-family: futura-pt;
    src: url("https://use.typekit.net/af/309dfe/000000000000000000010091/27/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n7&v=3") format("woff2"), url("https://use.typekit.net/af/309dfe/000000000000000000010091/27/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n7&v=3") format("woff"), url("https://use.typekit.net/af/309dfe/000000000000000000010091/27/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n7&v=3") format("opentype");
    font-display: auto;
    font-style: normal;
    font-weight: 700
}

@font-face {
    font-family: futura-pt;
    src: url("https://use.typekit.net/af/eb729a/000000000000000000010092/27/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i7&v=3") format("woff2"), url("https://use.typekit.net/af/eb729a/000000000000000000010092/27/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i7&v=3") format("woff"), url("https://use.typekit.net/af/eb729a/000000000000000000010092/27/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i7&v=3") format("opentype");
    font-display: auto;
    font-style: italic;
    font-weight: 700
}

@font-face {
    font-family: futura-pt;
    src: url("https://use.typekit.net/af/849347/000000000000000000010093/27/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i3&v=3") format("woff2"), url("https://use.typekit.net/af/849347/000000000000000000010093/27/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i3&v=3") format("woff"), url("https://use.typekit.net/af/849347/000000000000000000010093/27/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i3&v=3") format("opentype");
    font-display: auto;
    font-style: italic;
    font-weight: 300
}

@font-face {
    font-family: futura-pt;
    src: url("https://use.typekit.net/af/9b05f3/000000000000000000013365/27/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n4&v=3") format("woff2"), url("https://use.typekit.net/af/9b05f3/000000000000000000013365/27/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n4&v=3") format("woff"), url("https://use.typekit.net/af/9b05f3/000000000000000000013365/27/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n4&v=3") format("opentype");
    font-display: auto;
    font-style: normal;
    font-weight: 400
}

@font-face {
    font-family: futura-pt;
    src: url("https://use.typekit.net/af/cf3e4e/000000000000000000010095/27/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i4&v=3") format("woff2"), url("https://use.typekit.net/af/cf3e4e/000000000000000000010095/27/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i4&v=3") format("woff"), url("https://use.typekit.net/af/cf3e4e/000000000000000000010095/27/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i4&v=3") format("opentype");
    font-display: auto;
    font-style: italic;
    font-weight: 400
}

@font-face {
    font-family: futura-pt;
    src: url("https://use.typekit.net/af/ae4f6c/000000000000000000010096/27/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n3&v=3") format("woff2"), url("https://use.typekit.net/af/ae4f6c/000000000000000000010096/27/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n3&v=3") format("woff"), url("https://use.typekit.net/af/ae4f6c/000000000000000000010096/27/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n3&v=3") format("opentype");
    font-display: auto;
    font-style: normal;
    font-weight: 300
}

/*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/css/styles-m.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/bootstrap/css/bootstrap.min.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/css/custom.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/css/fontawesome5.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/css/mgz_bootstrap.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_PageBuilder/css/styles.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Payone_Core/css/ratepay.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/CopeX_ExitIntent/css/exitintent.css *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/css/styles-l.css ; media=screen and (min-width: 768px) *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/css/print.css ; media=print *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_PageBuilderIconBox/css/styles.css *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Payolution_Payments/css/payolution.css ; media=screen *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used fontfaces *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/css/styles-m.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/bootstrap/css/bootstrap.min.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/css/custom.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/css/fontawesome5.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/css/mgz_bootstrap.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_PageBuilder/css/styles.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Payone_Core/css/ratepay.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/CopeX_ExitIntent/css/exitintent.css *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/css/styles-l.css ; media=screen and (min-width: 768px) *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/css/print.css ; media=print *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_PageBuilderIconBox/css/styles.css *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Payolution_Payments/css/payolution.css ; media=screen *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used fontfaces *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/css/styles-m.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/bootstrap/css/bootstrap.min.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/css/custom.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/css/fontawesome5.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/css/mgz_bootstrap.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_PageBuilder/css/styles.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Payone_Core/css/ratepay.css ; media=all */
@media all {
    @media (min-width: 768px), print {
        .payment-method-container {
            position: relative
        }

        .payment-method-label-container {
            text-align: center
        }

        .amazon-image-container, .paypal-image-container {
            text-align: center;
            height: 55px
        }

        .klarna-payments-method-cell {
            text-align: center;
            width: 100%
        }

        .klarna-payments-method-cell, .payment-method-label-container label span {
            font-size: 16px
        }

        .banktransfer-image-container {
            width: 100%
        }

        .payment-method {
            margin: 5px
        }

        .main-payment-title, .payment-method-title {
            display: none
        }

        .amazon_payment_v2-payment-image, .banktransfer-image-container, .banktransfer-payment-image, .creditcard-image-container, .klarna_pay_later-payment-image, .payolution-elv-image-container, .payolution-image-container, .payolution_elv-payment-image, .payolution_invoice-payment-image, .payone_creditcard-payment-image, .paypal_express-payment-image {
            margin: 8px auto
        }

        #customer-email-fieldset {
            position: relative
        }

        .email-field {
            width: 45% !important;
            position: absolute;
            margin-left: 1% !important
        }

        .open-fax {
            max-width: 44%;
            position: absolute;
            right: 8%;
            top: -30px;
            line-height: 15px;
            text-decoration: underline;
            cursor: pointer;
            text-align: right
        }

        .checkout-payment-method .payment-method._active .payment-method-content {
            flex-direction: row
        }

        .checkout-payment-method .payment-method-paypal .paypal-buttons {
            width: 33% !important
        }

        #amazon-payment .payment-method-content {
            margin-left: unset
        }

        .checkout-index-index .opc-block-summary {
            box-shadow: rgb(99 99 99 / 20%) 0 2px 8px 0;
            border-radius: 10px
        }

        .opc-block-summary table.table-totals .totals-tax-details {
            bottom: 5px
        }

        .checkout-usps {
            display: block;
            min-height: 110px
        }
    }aside, footer, header, main, nav {
    display: block
}

    nav ul {
        list-style: none
    }

    img {
        border: 0;
        height: auto;
        max-width: 100%
    }

    svg:not(:root) {
        overflow: hidden
    }

    i {
        font-style: italic
    }

    small {
        font-size: 12px
    }

    hr {
        border: 0;
        border-top: 1px solid #ccc
    }

    h1 {
        font-weight: 300
    }

    h6 {
        font-weight: 700
    }

    a, a:visited {
        color: #1979c3;
        text-decoration: none
    }

    a:active {
        color: #85b84b;
        text-decoration: underline
    }

    ol, ul {
        margin-top: 0;
        margin-bottom: 2.5rem
    }

    ol > li, ul > li {
        margin-top: 0;
        margin-bottom: 1rem
    }

    ul ul {
        margin-bottom: 0
    }

    dl {
        margin-bottom: 20px;
        margin-top: 0
    }

    dt {
        font-weight: 700;
        margin-bottom: 5px;
        margin-top: 0
    }

    dd {
        margin-bottom: 10px;
        margin-top: 0;
        margin-left: 0
    }

    table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        max-width: 100%
    }

    table th {
        text-align: left
    }

    table > tbody > tr > td, table > tbody > tr > th {
        vertical-align: top
    }

    table > thead > tr > th {
        vertical-align: bottom
    }

    table > tbody > tr > td, table > tbody > tr > th, table > thead > tr > th {
        padding: 11px 10px
    }

    button {
        background: #eee;
        border: 1px solid #ccc;
        line-height: 1.6rem;
        box-sizing: border-box
    }

    button.disabled, button[disabled] {
        opacity: .5;
        cursor: default;
        pointer-events: none
    }

    input[type=email], input[type=password], input[type=text] {
        background: padding-box #fff;
        border: 2px solid #c2c2c2;
        border-radius: 5px;
        line-height: 1.42857143;
        padding: 0 9px;
        vertical-align: baseline;
        width: 100%;
        box-sizing: border-box
    }

    input[type=checkbox]:disabled, input[type=email]:disabled, input[type=password]:disabled, input[type=radio]:disabled, input[type=text]:disabled, select:disabled {
        opacity: .5
    }

    select {
        background: padding-box #fff;
        font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        line-height: 1.42857143;
        padding: 5px 10px 4px;
        vertical-align: baseline;
        width: 100%;
        box-sizing: border-box
    }

    input[type=checkbox] {
        margin: 2px 5px 0 0
    }

    form {
        -webkit-tap-highlight-color: transparent
    }

    :focus {
        box-shadow: none;
        outline: 0
    }

    input:not([disabled]):focus, select:not([disabled]):focus {
        box-shadow: none
    }

    .footer.content ul, .opc-progress-bar, .opc-wrapper .opc {
        margin: 0;
        padding: 0;
        list-style: none
    }

    .footer.content ul > li, .opc-progress-bar > li, .opc-wrapper .opc > li {
        margin: 0
    }

    .action-auth-toggle, .checkout-agreements-block .action-show {
        line-height: 1.42857143;
        padding: 0;
        color: #1979c3;
        text-decoration: none;
        background: 0 0;
        border: 0;
        display: inline;
        font-weight: 400;
        border-radius: 0
    }

    .action-auth-toggle:visited, .checkout-agreements-block .action-show:visited {
        color: #1979c3;
        text-decoration: none
    }

    .action-auth-toggle:active, .checkout-agreements-block .action-show:active {
        color: #85b84b;
        text-decoration: underline
    }

    .action-auth-toggle:hover, .checkout-agreements-block .action-show:hover {
        text-decoration: underline;
        color: #006bb4
    }

    .action-auth-toggle:active, .action-auth-toggle:focus, .action-auth-toggle:hover, .checkout-agreements-block .action-show:active, .checkout-agreements-block .action-show:focus, .checkout-agreements-block .action-show:hover {
        background: 0 0;
        border: 0
    }

    .action-auth-toggle:active, .action-auth-toggle:not(:focus), .checkout-agreements-block .action-show:active, .checkout-agreements-block .action-show:not(:focus) {
        box-shadow: none
    }

    .block-authentication .action.action-login, .block-minicart .block-content > .actions > .primary .action.primary {
        line-height: 2.2rem;
        padding: 14px 17px;
        font-size: 1.8rem
    }

    .actions-toolbar > .primary .action, .actions-toolbar > .secondary .action {
        width: 100%
    }

    .minicart-items .product-image-wrapper {
        height: auto;
        padding: 0 !important
    }

    .minicart-items .product-image-wrapper .product-image-photo {
        position: static
    }

    .block {
        margin-bottom: 40px
    }

    .product-item-name > a {
        color: #333;
        text-decoration: none
    }

    .product-item-name > a:active, .product-item-name > a:hover, .product-item-name > a:visited {
        color: #333;
        text-decoration: underline
    }

    .action.skip:not(:focus), .checkout-payment-method .payments .legend {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px
    }

    .minicart-items .product.options .toggle > span, .shipping-policy-block.field-tooltip .field-tooltip-action span {
        clip: auto;
        height: auto;
        margin: 0;
        overflow: visible;
        position: static;
        width: auto
    }

    .checkout-container:after, .checkout-container:before, .header.content:after, .header.content:before, .opc-estimated-wrapper:after, .opc-estimated-wrapper:before, .opc-wrapper .field.addresses:after, .opc-wrapper .field.addresses:before, .row:after, .row:before {
        content: '';
        display: table
    }

    .checkout-container:after, .header.content:after, .minicart-items .product-item > .product:after, .opc-estimated-wrapper:after, .opc-wrapper .field.addresses:after, .row:after {
        clear: both
    }

    .columns .column.main, .field .control._with-tooltip, .opc-block-summary, .product-item, .shipping-policy-block.field-tooltip .field-tooltip-content {
        box-sizing: border-box
    }

    .minicart-items .product .toggle {
        border-top: 1px solid #ccc;
        cursor: pointer;
        margin-bottom: 0;
        position: relative;
        display: block;
        text-decoration: none;
        font-size: 14px;
        font-weight: 600
    }

    .minicart-items .product .toggle:after {
        right: 20px;
        top: 10px;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 12px;
        line-height: 12px;
        content: '\e622';
        font-family: luma-icons;
        vertical-align: middle;
        display: inline-block;
        font-weight: 400;
        overflow: hidden;
        speak: none;
        text-align: center
    }

    .minicart-items .product .toggle > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px
    }

    .block-minicart .subtotal .label:after, .minicart-items .details-qty .label:after {
        content: ': '
    }

    .opc-block-summary .table-totals .table-caption {
        display: none
    }

    .field .control._with-tooltip {
        position: relative
    }

    .shipping-policy-block.field-tooltip .field-tooltip-content {
        right: -10px;
        left: auto
    }

    .shipping-policy-block.field-tooltip .field-tooltip-content:after, .shipping-policy-block.field-tooltip .field-tooltip-content:before {
        border: 10px solid transparent;
        height: 0;
        width: 0;
        margin-top: -21px;
        right: 10px;
        left: auto;
        top: 0
    }

    .shipping-policy-block.field-tooltip .field-tooltip-content:before {
        border-bottom-color: #666
    }

    .shipping-policy-block.field-tooltip .field-tooltip-content:after {
        border-bottom-color: #f5f5f5;
        top: 1px
    }

    .opc-wrapper .step-title {
        font-weight: 500;
        font-size: 20px
    }

    .opc-block-summary .table-totals tbody .mark {
        border: 0;
        font-weight: 400;
        padding: 6px 0
    }

    .opc-block-summary .table-totals tbody .amount {
        border: 0;
        font-weight: 400;
        padding: 6px 0 6px 14px;
        text-align: right;
        white-space: nowrap
    }

    .opc-block-summary .table-totals .grand td, .opc-block-summary .table-totals .grand th {
        padding: 0
    }

    .opc-block-summary .table-totals .grand strong {
        display: inline-block;
        font-weight: 600;
        padding: 3px 0 0
    }

    .opc-block-summary .table-totals .grand .mark {
        font-size: 1.8rem;
        height: max-content
    }

    .opc-block-summary .table-totals .grand .amount {
        font-size: 1.8rem
    }

    .opc-block-summary .table-totals .grand .amount, .opc-block-summary .table-totals .grand .mark {
        padding: 10px 10px 14px 0;
        border-top: 1px solid #ccc
    }

    .opc-block-summary .table-totals .totals-tax-details {
        border-bottom: 1px solid #ccc
    }

    html {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        font-size-adjust: 100%;
        font-size: 10px;
        background: #fff;
        color: initial
    }

    body {
        margin: 0;
        padding: 0;
        color: #636d70;
        font: 400 17px/26px futura-pt
    }

    p {
        margin: 0
    }

    strong {
        font-weight: 500
    }

    .block .title {
        margin: 0 0 10px
    }

    a:visited, h1, h6 {
        color: #2f3943
    }

    h1 {
        font-size: 4rem;
        line-height: 4rem;
        margin: 27px 0 17px
    }

    h6 {
        font-size: 1.8rem;
        line-height: 2rem;
        margin: 50px 0 16px
    }

    a {
        color: #2f3943;
        transition: color .4s;
        position: relative;
        text-decoration: none
    }

    a:focus, a:hover {
        color: #85b84b;
        text-decoration: none
    }

    a[href]:after {
        content: normal
    }

    hr {
        height: 1px;
        border: none;
        margin: 20px 0;
        background: #e5e5e5
    }

    .modal-custom button.action-close, .modal-popup button.action-close {
        height: auto
    }

    .modal-custom button.action-close::after, .modal-popup button.action-close::after {
        display: none
    }

    .page-wrapper {
        transition: .4s
    }

    div[role=tablist] div[data-role=title]:after {
        font-family: luma-icons;
        content: "\e622";
        margin-left: 10px;
        vertical-align: middle;
        color: #2f3943
    }

    .items {
        margin: 0;
        padding: 0;
        list-style: none
    }

    @media (max-width: 767px) {
        h1 {
            font-size: 3.2rem;
            line-height: 3.2rem
        }

        h6 {
            font-size: 1.8rem;
            line-height: 2rem;
            margin: 16px 0
        }
    }.columns {
         -webkit-flex-wrap: wrap;
         flex-wrap: wrap;
         box-sizing: border-box
     }

    .columns:after {
        clear: both;
        content: ' ';
        display: block;
        height: 0;
        overflow: hidden;
        visibility: hidden
    }

    .columns .column.main {
        -webkit-flex-basis: 100%;
        flex-basis: 100%;
        -webkit-flex-grow: 1;
        flex-grow: 1;
        -ms-flex-order: 1;
        -webkit-order: 1;
        order: 1;
        width: 100%
    }

    table > caption {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px
    }

    .nav-sections {
        background: #f0f0f0
    }

    .nav-toggle {
        text-decoration: none;
        cursor: pointer;
        display: block;
        left: 15px;
        position: absolute;
        top: 15px;
        z-index: 14
    }

    .nav-toggle > span {
        display: block;
        line-height: 1;
        font-size: 12px
    }

    .loader {
        width: 100%;
        height: 100%;
        position: absolute;
        margin: auto;
        z-index: 100;
        background: rgba(255, 255, 255, .5)
    }

    .loader img {
        bottom: 0;
        left: 0;
        margin: auto;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 100
    }

    .loading-mask {
        bottom: 0;
        left: 0;
        margin: auto;
        position: fixed;
        right: 0;
        top: 0;
        z-index: 100;
        background: rgba(255, 255, 255, .5)
    }

    .loading-mask .loader > img {
        bottom: 0;
        left: 0;
        margin: auto;
        position: fixed;
        right: 0;
        top: 0;
        z-index: 100
    }

    .loading-mask .loader > p {
        display: none
    }

    body > .loading-mask {
        z-index: 9999
    }

    .fieldset {
        border: 0;
        margin: 0 0 40px;
        padding: 0;
        letter-spacing: -.31em
    }

    .fieldset > * {
        letter-spacing: normal
    }

    .fieldset > .legend {
        margin: 0 0 20px;
        padding: 0 0 10px;
        width: 100%;
        box-sizing: border-box;
        float: left;
        font-weight: 300;
        line-height: 1.2;
        font-size: 1.8rem
    }

    .fieldset > .legend + br {
        clear: both;
        display: block;
        height: 0;
        overflow: hidden;
        visibility: hidden
    }

    .fieldset:last-child {
        margin-bottom: 0
    }

    .fieldset > .field {
        margin: 0 0 20px
    }

    .fieldset > .field > .label {
        margin: 0 0 8px;
        display: inline-block;
        font-weight: 400
    }

    .fieldset > .field:last-child {
        margin-bottom: 0
    }

    .fieldset > .field .fields.group:after, .fieldset > .field .fields.group:before {
        content: '';
        display: table
    }

    .fieldset > .field .fields.group:after {
        clear: both
    }

    .fieldset > .field .fields.group .field {
        box-sizing: border-box;
        float: left
    }

    .fieldset > .field .fields.group.group-2 .field {
        width: 50% !important
    }

    .fieldset > .field._required > .label:after, .fieldset > .field.required :not(.checkout-agreement) > .label:after, .fieldset > .field.required > .label:after {
        content: '*';
        color: #e02b27;
        font-size: 1.2rem;
        margin: 0 0 0 5px
    }

    .fieldset > .field .note {
        font-size: 1.2rem;
        margin: 3px 0 0;
        padding: 0;
        display: inline-block;
        text-decoration: none
    }

    .fieldset > .field .note:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 24px;
        line-height: 12px;
        font-family: luma-icons;
        vertical-align: middle;
        display: inline-block;
        font-weight: 400;
        overflow: hidden;
        speak: none;
        text-align: center
    }

    .fieldset > .field .label {
        font-weight: 400
    }

    .legend > span {
        margin-right: 5px
    }

    fieldset.field {
        border: 0;
        padding: 0
    }

    select {
        border: 1px solid #ccc;
        padding-right: 25px;
        text-indent: .01em;
        text-overflow: '';
        border-radius: 5px;
        -webkit-appearance: none;
        -moz-appearance: none;
        -ms-appearance: none;
        appearance: none
    }

    input.input-text, input[type=email], input[type=password], input[type=text], select {
        font-size: 1.8rem;
        height: 35px;
        transition: .4s
    }

    .field .control._with-tooltip input {
        width: 100%;
        margin-right: 0
    }

    .field-tooltip .field-tooltip-action::before {
        color: #333
    }

    [type=checkbox]:checked, [type=checkbox]:not(:checked) {
        position: absolute;
        left: -9999px
    }

    [type=checkbox]:checked + label, [type=checkbox]:not(:checked) + label {
        position: relative;
        padding-left: 1.95em;
        cursor: pointer;
        display: block
    }

    [type=checkbox]:checked + label:before, [type=checkbox]:not(:checked) + label:before {
        content: '\e901';
        font-family: luma-icons;
        font-size: 2.8rem;
        position: absolute;
        left: 0;
        top: 0;
        width: 1.25em;
        height: 1.25em
    }

    [type=checkbox]:checked + label:after, [type=checkbox]:not(:checked) + label:after {
        content: '\f00c';
        font-family: 'Font Awesome 5 Free';
        position: absolute;
        left: 8px;
        font-weight: 600;
        top: 0;
        font-size: 1.2rem;
        color: #000;
        transition: .2s
    }

    [type=checkbox]:not(:checked) + label:after {
        opacity: 0
    }

    [type=checkbox]:checked + label:after {
        opacity: 1
    }

    input[type=radio] {
        border: 0;
        clip: rect(0 0 0 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px
    }

    .checkout-index-index .checkout-container .payment-method .payment-method-title .label:before {
        content: '\e903';
        font-family: luma-icons;
        display: none;
        letter-spacing: 10px;
        color: #535353;
        font-size: 2.3rem;
        vertical-align: text-top
    }

    body:not(._keyfocus) button:focus {
        box-shadow: none
    }

    .action.primary, .button, button, button.action, button.action-auth-toggle {
        font-family: futura-pt;
        min-height: 45px;
        width: auto;
        display: inline-block;
        cursor: pointer;
        text-align: center;
        border: none;
        padding: 8px 20px;
        margin: 5px 10px 5px 0;
        text-decoration: none;
        color: #fff;
        background-color: #677752;
        font-weight: 400;
        font-size: 1.8rem;
        transition: .4s;
        box-shadow: none;
        border-radius: 5px;
        vertical-align: middle;
        box-sizing: border-box
    }

    .action.primary::before, .button::before, button.action-auth-toggle::before, button.action::before, button::before {
        font-family: luma-icons;
        display: inline-block;
        cursor: pointer;
        text-align: center;
        color: inherit;
        border: none;
        transition: .4s;
        width: auto;
        position: inherit;
        bottom: auto;
        left: auto
    }

    .action.primary::before:hover, .button::before:hover, button.action-auth-toggle::before:hover, button.action::before:hover, button::before:hover {
        border: none
    }

    .action.primary.checkout, button.action.checkout, button.checkout {
        color: #fff;
        background-color: #677752;
        border-color: #677752
    }

    .action.primary.checkout::before, button.action.checkout::before, button.checkout::before {
        border-bottom: none
    }

    .action.primary.checkout:hover, button.action.checkout:hover, button.checkout:hover {
        opacity: .6
    }

    .actions-toolbar > .primary, .actions-toolbar > .secondary {
        margin-bottom: 10px;
        text-align: center
    }

    .actions-toolbar > .primary .action, .actions-toolbar > .secondary .action {
        margin-bottom: 10px
    }

    .actions-toolbar > .primary .action:last-child, .actions-toolbar > .primary:last-child, .actions-toolbar > .secondary .action:last-child, .actions-toolbar > .secondary:last-child {
        margin-bottom: 0
    }

    .price-including-tax {
        font-size: 1.8rem;
        line-height: 1
    }

    .price-including-tax .price {
        font-weight: 700
    }

    .modal-popup, .modal-slide {
        bottom: 0;
        min-width: 0;
        position: fixed;
        right: 0;
        top: 0;
        visibility: hidden;
        opacity: 0;
        -webkit-transition: visibility 0s .3s, opacity .3s;
        transition: visibility 0s .3s, opacity .3s
    }

    .modal-popup .modal-inner-wrap, .modal-slide .modal-inner-wrap {
        background-color: #fff;
        opacity: 1;
        pointer-events: auto
    }

    .modal-slide {
        left: 0;
        z-index: 900
    }

    .modal-slide .modal-inner-wrap {
        height: 100%;
        overflow-y: auto;
        position: static;
        -webkit-transform: translateX(100%);
        transform: translateX(100%);
        -webkit-transition: -webkit-transform .3s ease-in-out;
        transition: transform .3s ease-in-out;
        width: auto
    }

    .modal-slide._inner-scroll .modal-inner-wrap {
        overflow-y: visible;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column
    }

    .modal-slide._inner-scroll .modal-footer, .modal-slide._inner-scroll .modal-header {
        -webkit-flex-grow: 0;
        flex-grow: 0;
        -webkit-flex-shrink: 0;
        flex-shrink: 0;
        border: none
    }

    .modal-slide._inner-scroll .modal-content {
        overflow-y: auto
    }

    .modal-slide._inner-scroll .modal-footer {
        margin-top: auto
    }

    .modal-slide .modal-content, .modal-slide .modal-footer, .modal-slide .modal-header {
        padding: 0 2.6rem 2.6rem
    }

    .modal-slide .modal-header {
        padding-bottom: 2.1rem;
        padding-top: 2.1rem
    }

    .modal-popup {
        z-index: 900;
        left: 0;
        overflow-y: auto
    }

    .modal-popup .modal-inner-wrap {
        margin: 5rem auto;
        width: 75%;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        box-sizing: border-box;
        height: auto;
        left: 0;
        position: absolute;
        right: 0;
        -webkit-transform: translateY(-200%);
        transform: translateY(-200%);
        -webkit-transition: -webkit-transform .2s;
        transition: transform .2s
    }

    .modal-popup._inner-scroll {
        overflow-y: visible
    }

    .modal-popup._inner-scroll .modal-inner-wrap {
        max-height: 90%;
        border-radius: 5px;
        max-width: 1570px
    }

    .modal-popup._inner-scroll .modal-content {
        overflow-y: auto;
        border: none;
        box-shadow: none
    }

    .modal-popup .modal-content, .modal-popup .modal-footer, .modal-popup .modal-header {
        padding-left: 3rem;
        padding-right: 3rem;
        box-shadow: none;
        border: none
    }

    .modal-header .action-close span {
        font-size: 1.25rem;
        color: #fff
    }

    .modal-popup .modal-footer, .modal-popup .modal-header {
        -webkit-flex-grow: 0;
        flex-grow: 0;
        -webkit-flex-shrink: 0;
        flex-shrink: 0
    }

    .modal-popup .modal-header {
        padding-bottom: 1.2rem;
        padding-top: 3rem
    }

    .modal-popup .modal-footer {
        margin-top: auto;
        padding-bottom: 3rem;
        padding-top: 3rem
    }

    .modal-custom .action-close, .modal-popup .action-close, .modal-slide .action-close {
        background: 0 0;
        -moz-box-sizing: content-box;
        border: 0;
        box-shadow: none;
        line-height: inherit;
        margin: 0;
        padding: 0 30px 0 0;
        text-shadow: none;
        font-weight: 400;
        display: inline-block;
        text-decoration: none;
        position: absolute;
        right: 0;
        top: 0
    }

    .modal-custom .action-close:active, .modal-custom .action-close:focus, .modal-custom .action-close:hover, .modal-popup .action-close:active, .modal-popup .action-close:focus, .modal-popup .action-close:hover, .modal-slide .action-close:active, .modal-slide .action-close:focus, .modal-slide .action-close:hover {
        background: 0 0;
        border: none
    }

    .modal-custom .action-close > span, .modal-popup .action-close > span, .modal-slide .action-close > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px
    }

    .modal-custom .action-close:before, .modal-popup .action-close:before, .modal-slide .action-close:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 14px;
        line-height: 14px;
        color: inherit;
        content: '\e616';
        font-family: luma-icons;
        margin: 0;
        vertical-align: top;
        display: inline-block;
        font-weight: 400;
        overflow: hidden;
        speak: none;
        text-align: center
    }

    .modal-custom .action-close:active:before, .modal-custom .action-close:hover:before, .modal-popup .action-close:active:before, .modal-popup .action-close:hover:before, .modal-slide .action-close:active:before, .modal-slide .action-close:hover:before {
        color: inherit
    }

    .modal-popup .action-close, .modal-slide .action-close {
        padding: 0 30px 0 0
    }

    .modal-popup .action-close::before, .modal-slide .action-close::before {
        right: 30px;
        top: 50%;
        margin-top: -7px;
        color: inherit
    }

    .modal-popup .action-close:hover, .modal-slide .action-close:hover {
        color: #2f3943
    }

    .modal-popup .action-close > span, .modal-slide .action-close > span {
        clip: unset;
        height: auto;
        width: auto;
        line-height: 1.6rem;
        padding-right: 25px;
        position: relative;
        margin: 0;
        overflow: visible
    }

    .amazon-button-container {
        display: table;
        margin: 0 0 22px
    }

    .amazon-button-container .field-tooltip {
        display: none
    }

    @media all and (max-width: 768px) {
        .amazon-button-container {
            width: 100%
        }
    }.field-error {
         color: #e02b27;
         font-size: 1.2rem
     }

    .product-item {
        vertical-align: top
    }

    .product-item-name {
        font-weight: 400;
        -moz-hyphens: auto;
        -ms-hyphens: auto;
        -webkit-hyphens: auto;
        display: block;
        hyphens: auto;
        margin: 5px 0;
        word-wrap: break-word
    }

    .product-item .old-price {
        margin: 5px 0
    }

    .column.main .product-item {
        padding-left: 20px
    }

    .price-container .price {
        font-size: 1.4rem
    }

    .old-price {
        color: #7d7d7d
    }

    .product-image-container {
        display: inline-block;
        max-width: 100%
    }

    .product-image-wrapper {
        display: block;
        overflow: hidden;
        position: relative;
        z-index: 1
    }

    .product-image-photo {
        bottom: 0;
        display: block;
        height: auto;
        left: 0;
        margin: auto;
        max-width: 100%;
        right: 0;
        top: 0
    }

    .price-including-tax {
        display: block;
        white-space: nowrap
    }

    .block-minicart .items-total {
        float: left;
        margin: 0 10px;
        display: none
    }

    .block-minicart .items-total .count {
        font-weight: 700
    }

    .block-minicart .subtotal {
        margin: 10px 0;
        text-align: right
    }

    .block-minicart .subtotal .amount, .block-minicart .subtotal span.label {
        display: inline-block
    }

    .block-minicart .amount .price-wrapper:first-child .price {
        font-size: 18px;
        font-weight: 700
    }

    .block-minicart .subtitle {
        display: none
    }

    .block-minicart .block-content > .actions {
        margin-top: 15px
    }

    .block-minicart .block-content > .actions > .primary {
        margin: 0 10px 15px
    }

    .block-minicart .block-content > .actions > .primary .action.primary {
        display: block;
        width: 100%
    }

    .minicart-wrapper {
        display: inline-block;
        position: relative;
        float: right
    }

    .minicart-wrapper:after, .minicart-wrapper:before {
        content: '';
        display: table
    }

    .minicart-wrapper:after {
        clear: both
    }

    .minicart-wrapper .action.showcart {
        cursor: pointer;
        display: inline-block;
        text-decoration: none
    }

    .minicart-wrapper .action.showcart:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 22px;
        line-height: 28px;
        color: #fff;
        content: '\e611';
        font-family: luma-icons;
        margin: 0;
        vertical-align: top;
        display: inline-block;
        font-weight: 400;
        overflow: hidden;
        speak: none;
        text-align: center
    }

    .minicart-wrapper .action.showcart:hover:before {
        color: #2f3943
    }

    .minicart-wrapper .action.showcart:active:before {
        color: #fff
    }

    .minicart-wrapper .block-minicart {
        margin: 20px 0 0;
        list-style: none;
        background: #fff;
        border: 1px solid #bbb;
        z-index: 1000;
        box-sizing: border-box;
        display: none;
        position: absolute;
        top: auto;
        box-shadow: 0 3px 3px rgba(0, 0, 0, .15);
        padding: 20px;
        right: 0;
        width: 360px
    }

    .minicart-wrapper .block-minicart li {
        margin: 0;
        position: relative
    }

    .minicart-wrapper .block-minicart li:hover {
        cursor: pointer
    }

    .minicart-wrapper .block-minicart:after, .minicart-wrapper .block-minicart:before {
        content: '';
        display: block;
        height: 0;
        position: absolute;
        width: 0
    }

    .minicart-wrapper .block-minicart:before {
        border: 6px solid;
        border-color: transparent transparent #fff;
        z-index: 99;
        top: -12px
    }

    .minicart-wrapper .block-minicart:after {
        border: 7px solid;
        border-color: transparent transparent #bbb;
        z-index: 98;
        top: -14px;
        left: auto;
        right: 31px
    }

    .minicart-wrapper .block-minicart .block-title {
        display: none
    }

    .minicart-wrapper .block-minicart:before {
        left: auto;
        right: 32px
    }

    .minicart-wrapper .product .actions a::before {
        width: 20px;
        height: 20px
    }

    .minicart-wrapper .product .actions > .secondary {
        display: inline
    }

    .minicart-wrapper .action.close {
        height: 40px;
        position: absolute;
        right: 0;
        top: 0;
        width: 40px;
        background: 0 0;
        -moz-box-sizing: content-box;
        border: 0;
        box-shadow: none;
        line-height: inherit;
        margin: 0;
        padding: 0;
        text-shadow: none;
        font-weight: 400;
        text-decoration: none;
        display: none
    }

    .minicart-wrapper .action.close:active, .minicart-wrapper .action.close:focus, .minicart-wrapper .action.close:hover {
        background: 0 0;
        border: none
    }

    .minicart-wrapper .action.close > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px
    }

    .minicart-wrapper .action.close:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 16px;
        line-height: 16px;
        color: #fff;
        content: '\e616';
        font-family: luma-icons;
        margin: 0;
        vertical-align: top;
        display: inline-block;
        font-weight: 400;
        overflow: hidden;
        speak: none;
        text-align: center
    }

    .minicart-wrapper .action.close:active:before, .minicart-wrapper .action.close:hover:before {
        color: inherit
    }

    .minicart-wrapper .action.showcart .counter.qty {
        color: #fff;
        line-height: 24px;
        margin: 3px 0 0;
        overflow: hidden;
        padding: 0 3px;
        text-align: center;
        white-space: normal
    }

    .minicart-items-wrapper {
        border-bottom: 1px solid #ccc;
        margin: 0 -20px;
        overflow-x: auto;
        padding: 5px 15px 15px
    }

    .minicart-items {
        margin: 0;
        padding: 0;
        list-style: none
    }

    .minicart-items .product-item {
        padding: 20px 0
    }

    .minicart-items .product-item:not(:first-child) {
        border-top: 1px solid #ccc
    }

    .minicart-items .product-item:first-child {
        padding-top: 0
    }

    .minicart-items .product-item-pricing .label {
        display: inline-block;
        width: 4.5rem
    }

    .minicart-items .product-item-name {
        font-weight: 500;
        margin: 0;
        line-height: normal
    }

    .minicart-items .product-item-name a {
        color: #636d70
    }

    .minicart-items .product-item-details .price {
        font-weight: 700
    }

    .minicart-items .product-item-details .price-including-tax {
        margin: 5px 0
    }

    .minicart-items .product-item-details .details-qty {
        margin: 0
    }

    .minicart-items .product-item-details > .actions {
        position: absolute;
        right: 5px;
        top: 25px
    }

    .minicart-items .product > .product-image-container, .minicart-items .product > .product-item-photo {
        float: left
    }

    .minicart-items .product .toggle {
        border: 0;
        padding: 0 40px 5px 0
    }

    .minicart-items .product .toggle:after {
        color: #8f8f8f;
        margin: 0 0 0 5px;
        position: static
    }

    .minicart-items .subtitle {
        display: none
    }

    .minicart-items .action.delete {
        display: inline-block;
        text-decoration: none
    }

    .minicart-items .action.delete > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px
    }

    .minicart-items .action.delete:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 18px;
        line-height: 20px;
        content: '\e601';
        font-family: luma-icons;
        vertical-align: middle;
        display: inline-block;
        font-weight: 400;
        overflow: hidden;
        speak: none;
        text-align: center;
        content: '\e900'
    }

    .checkout-container, .opc-wrapper {
        margin: 0 0 20px
    }

    .opc-wrapper .step-title {
        border-bottom: 1px solid #ccc;
        margin-bottom: 20px;
        margin-top: 20px
    }

    .opc-wrapper .step-content {
        margin: 0
    }

    .checkout-index-index .nav-sections, .checkout-index-index .nav-toggle {
        display: none
    }

    .checkout-index-index .logo {
        margin-left: 0
    }

    .opc-estimated-wrapper {
        border-bottom: 1px solid #ccc;
        margin: 0 0 15px;
        padding: 18px 15px
    }

    .opc-estimated-wrapper .estimated-block {
        font-size: 22px;
        font-weight: 700;
        float: left
    }

    .opc-estimated-wrapper .estimated-block .estimated-label {
        display: block;
        margin: 0 0 5px
    }

    .opc-estimated-wrapper .minicart-wrapper .action.showcart:before, .opc-estimated-wrapper .minicart-wrapper .action.showcart:hover:before {
        color: #636d70
    }

    .opc-progress-bar {
        margin: 0 0 20px;
        counter-reset: i;
        font-size: 0
    }

    .opc-progress-bar-item {
        margin: 0 0 10px;
        display: inline-block;
        position: relative;
        text-align: center;
        vertical-align: top
    }

    .opc-progress-bar-item > span {
        display: inline-block;
        padding-top: 45px;
        width: 100%;
        word-wrap: break-word;
        font-weight: 500;
        font-size: 1.8rem
    }

    .opc-progress-bar-item > span:after, .opc-progress-bar-item > span:before {
        background: #e4e4e4;
        height: 38px;
        margin-left: -19px;
        width: 38px;
        border-radius: 50%;
        content: '';
        left: 50%;
        position: absolute;
        top: 0
    }

    .opc-progress-bar-item > span:after {
        background: #fff;
        height: 26px;
        margin-left: -13px;
        top: 6px;
        width: 26px;
        content: counter(i);
        counter-increment: i;
        color: #333;
        font-weight: 600;
        font-size: 1.8rem
    }

    .opc-progress-bar-item._active:before {
        background: #677752
    }

    .opc-progress-bar-item._active > span {
        color: #636d70
    }

    .opc-progress-bar-item._active > span:before {
        background: #677752
    }

    .opc-progress-bar-item._active > span:after {
        content: '\e610';
        font-family: luma-icons;
        line-height: 1;
        font-size: 2.8rem
    }

    .opc-progress-bar-item._complete {
        cursor: pointer
    }

    .opc-progress-bar-item._complete > span {
        color: #636d70
    }

    .opc-progress-bar-item._complete > span:after {
        content: '\e610';
        font-family: luma-icons;
        line-height: 1;
        font-size: 2.8rem
    }

    .field._error .control input {
        border-color: #ed8380
    }

    .opc-wrapper .fieldset > .field > .label {
        font-weight: 400
    }

    .field-tooltip {
        cursor: pointer;
        position: absolute;
        right: 0;
        top: 1px
    }

    .field-tooltip .field-tooltip-action {
        display: inline-block;
        text-decoration: none
    }

    .field-tooltip .field-tooltip-action > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px
    }

    .checkout-index-index .field input:after {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 21px;
        line-height: inherit;
        color: #bbb;
        content: "\f00c";
        font-family: 'Font Awesome 5 Free';
        vertical-align: middle;
        display: inline-block;
        font-weight: 400;
        overflow: hidden;
        speak: none;
        text-align: center
    }

    .field-tooltip .field-tooltip-action:hover:before {
        color: #333
    }

    .field-tooltip .field-tooltip-content {
        background: #f5f5f5;
        border: 1px solid #999;
        border-radius: 1px;
        font-size: 14px;
        padding: 12px;
        width: 270px;
        display: none;
        left: 38px;
        position: absolute;
        text-transform: none;
        top: -9px;
        word-wrap: break-word;
        z-index: 2
    }

    .field-tooltip .field-tooltip-content:after, .field-tooltip .field-tooltip-content:before {
        border: 10px solid transparent;
        height: 0;
        width: 0;
        border-right-color: #f5f5f5;
        left: -21px;
        top: 12px;
        content: '';
        display: block;
        position: absolute;
        z-index: 3
    }

    .field-tooltip .field-tooltip-content:before {
        border-right-color: #666
    }

    .field-tooltip .field-tooltip-content:after {
        border-right-color: #f5f5f5;
        width: 1px;
        z-index: 4
    }

    .opc-wrapper .form-login, .opc-wrapper .form-shipping-address {
        margin-bottom: 20px
    }

    .opc-wrapper .form-login .fieldset .note {
        font-size: 14px;
        margin-top: 10px
    }

    .checkout-shipping-method .step-title {
        margin-bottom: 0
    }

    .table-checkout-shipping-method thead th {
        display: none
    }

    .table-checkout-shipping-method tbody td {
        border-top: 1px solid #ccc;
        padding-bottom: 20px;
        padding-top: 20px
    }

    .table-checkout-shipping-method tbody td:first-child {
        padding-left: 0;
        padding-right: 0;
        width: 20px
    }

    .table-checkout-shipping-method tbody tr:first-child td {
        border-top: none
    }

    .checkout-shipping-method {
        position: relative
    }

    .shipping-policy-block.field-tooltip {
        top: 12px
    }

    .shipping-policy-block.field-tooltip .field-tooltip-action {
        color: #1979c3;
        cursor: pointer
    }

    .shipping-policy-block.field-tooltip .field-tooltip-action:before {
        display: none
    }

    .shipping-policy-block.field-tooltip .field-tooltip-content {
        width: 420px;
        top: 30px
    }

    .opc-block-summary {
        padding: 22px 30px;
        margin: 0
    }

    .opc-block-summary .table-totals .totals-tax-details {
        display: table-row
    }

    .opc-block-summary .items-in-cart > .title {
        padding: 8px 40px 8px 0;
        cursor: pointer;
        text-decoration: none;
        margin-bottom: 0;
        position: relative
    }

    .opc-block-summary .items-in-cart > .title:after {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 14px;
        line-height: 20px;
        color: inherit;
        content: '\e622';
        font-family: luma-icons;
        margin: 3px 0 0;
        vertical-align: middle;
        display: inline-block;
        font-weight: 400;
        overflow: hidden;
        speak: none;
        text-align: center;
        position: absolute;
        right: 0;
        top: 10px
    }

    .opc-block-summary .items-in-cart > .title strong {
        font-size: 18px;
        font-weight: 500;
        margin: 0
    }

    .opc-block-summary .items-in-cart .product {
        display: flex;
        align-items: flex-start;
        flex-direction: row;
        flex-wrap: nowrap;
        gap: .5em
    }

    .opc-block-summary .items-in-cart .product .product-item-details {
        flex-grow: 1
    }

    .opc-block-summary .minicart-items-wrapper {
        margin: 0 -15px 0 0;
        max-height: 415px;
        padding: 15px 15px 0 0;
        border: 0
    }

    .column.main .opc-block-summary .product-item {
        margin: 0;
        padding-left: 0
    }

    .opc-block-summary .product-item .product-item-inner {
        display: flex;
        justify-content: space-between
    }

    .opc-block-summary .product-item .product-item-name-block {
        display: table-cell;
        padding-right: 5px;
        text-align: left;
        font-size: 1.7rem
    }

    .opc-block-summary .product-item .subtotal {
        display: table-cell;
        text-align: right;
        vertical-align: bottom
    }

    .opc-block-summary .data.table.table-totals {
        margin-bottom: 20px
    }

    .authentication-dropdown {
        box-sizing: border-box
    }

    .authentication-dropdown .modal-inner-wrap {
        padding: 25px
    }

    .authentication-wrapper {
        float: right;
        margin-top: -60px;
        max-width: 50%;
        position: relative;
        z-index: 1
    }

    .block-authentication .block-title {
        font-size: 1.8rem;
        border-bottom: 0;
        margin-bottom: 25px
    }

    .block-authentication .block-title strong {
        font-weight: 300
    }

    .block-authentication .field .label {
        font-weight: 400
    }

    .block-authentication .actions-toolbar {
        margin-bottom: 5px
    }

    .block-authentication .actions-toolbar > .secondary {
        padding-top: 25px;
        text-align: left
    }

    .block-authentication .block[class] {
        margin: 0
    }

    .block-authentication .block[class] .field .control, .block-authentication .block[class] .field .label {
        float: none;
        width: auto
    }

    .checkout-payment-method .payment-method._active .payment-method-content {
        display: flex;
        align-items: flex-end;
        flex-direction: column;
        gap: 2em
    }

    .checkout-payment-method .payment-method-title {
        padding: 20px 0;
        margin: 0
    }

    .checkout-payment-method .payment-method-content {
        display: none;
        padding: 0 0 10px
    }

    .checkout-payment-method .payment-method-content .fieldset:not(:last-child) {
        margin: 0 0 20px
    }

    .checkout-payment-method .ccard .fields > .year {
        padding-left: 5px
    }

    .checkout-payment-method .ccard .number .input-text {
        width: 225px
    }

    .checkout-payment-method .ccard > .field.cvv > .control {
        padding-right: 20px;
        width: auto
    }

    .checkout-payment-method .ccard.fieldset > .field .fields.group.group-2 .field {
        width: auto !important
    }

    .checkout-agreements-block .checkout-agreements {
        margin-bottom: 20px
    }

    .checkout-agreements-block .action-show {
        vertical-align: baseline;
        margin: 0;
        text-align: left
    }

    .column.main .block:last-child {
        margin-bottom: 0
    }

    .block .title {
        margin-bottom: 10px
    }

    .block .title strong {
        font-weight: 700;
        line-height: 1.1;
        font-size: 1.4rem;
        margin-top: 2rem;
        margin-bottom: 2rem
    }

    body {
        background-color: #fff
    }

    .page-header {
        border-bottom: 1px solid #ccc;
        margin-bottom: 20px
    }

    .page-header .panel.wrapper {
        color: #fff;
        margin: 0
    }

    .header.content {
        padding-top: 10px;
        position: relative
    }

    .logo {
        float: left;
        margin: 0 0 10px 40px;
        position: relative
    }

    .logo img {
        display: block
    }

    .action.skip:focus {
        background: #f0f0f0;
        padding: 10px;
        box-sizing: border-box;
        left: 0;
        position: absolute;
        text-align: center;
        top: 0;
        width: 100%;
        z-index: 15
    }

    .message.global p {
        margin: 0
    }

    .message.global.cookie {
        margin: 0;
        padding: 12px 20px 12px 25px;
        display: block;
        border-color: #d6ca8e;
        color: #333;
        bottom: 0;
        left: 0;
        position: fixed;
        right: 0
    }

    .message.global.cookie a:active {
        color: #677752
    }

    .message.global.cookie .actions {
        margin-top: 10px
    }

    .page-footer {
        margin-top: 25px
    }

    .footer.content {
        border-top: 1px solid #cecece
    }

    .footer.content .links a {
        display: block;
        color: #575757;
        text-decoration: none;
        color: inherit;
        font-size: 16px;
        margin: 0;
        padding: 0
    }

    .footer.content .links a:visited {
        color: #575757;
        text-decoration: none
    }

    .footer.content .links a:active, .footer.content .links a:hover {
        color: #333;
        text-decoration: underline
    }

    .widget {
        clear: both
    }

    .page-footer .widget.block {
        margin: 0
    }

    .block-category-link.widget {
        display: block;
        margin-bottom: 20px
    }

    .links .block-cms-link.widget {
        margin-bottom: 0
    }

    @media only screen and (max-width: 767px) {
        .field-tooltip .field-tooltip-content {
            right: -10px;
            top: 40px;
            left: auto
        }

        .field-tooltip .field-tooltip-content:after, .field-tooltip .field-tooltip-content:before {
            border: 10px solid transparent;
            height: 0;
            width: 0;
            margin-top: -21px;
            right: 10px;
            left: auto;
            top: 0
        }

        .field-tooltip .field-tooltip-content:before {
            border-bottom-color: #666
        }

        .field-tooltip .field-tooltip-content:after {
            border-bottom-color: #f5f5f5;
            top: 1px
        }

        .footer.content, .header.content, .navigation, .page-header .header.panel, .page-main, .page-wrapper > .page-bottom {
            padding-left: 15px;
            padding-right: 15px
        }

        .navigation {
            padding: 0
        }

        .navigation .parent .level-top {
            position: relative;
            display: block;
            text-decoration: none
        }

        .nav-sections {
            -webkit-overflow-scrolling: touch;
            -webkit-transition: left .3s;
            -moz-transition: left .3s;
            -ms-transition: left .3s;
            transition: left .3s;
            height: 100%;
            left: -80%;
            left: calc(-1 * (100% - 54px));
            overflow: auto;
            position: fixed;
            top: 0;
            width: 80%;
            width: calc(100% - 54px)
        }

        .nav-sections-items {
            position: relative;
            z-index: 1
        }

        .nav-sections-items:after, .nav-sections-items:before {
            content: '';
            display: table
        }

        .nav-sections-items:after {
            clear: both
        }

        .nav-sections-item-title {
            background: #e3e3e3;
            border: solid #d7d7d7;
            border-width: 0 0 1px 1px;
            box-sizing: border-box;
            float: left;
            height: 71px;
            padding-top: 24px;
            text-align: center;
            width: 33.33%
        }

        .nav-sections-item-title.active {
            background: 0 0;
            border-bottom: 0
        }

        .nav-sections-item-title .nav-sections-item-switch:hover {
            text-decoration: none
        }

        .nav-sections-item-content {
            box-sizing: border-box;
            float: right;
            margin-left: -100%;
            margin-top: 71px;
            width: 100%;
            padding: 25px 0
        }

        .nav-sections-item-content:after, .nav-sections-item-content:before {
            content: '';
            display: table
        }

        .nav-sections-item-content:after {
            clear: both
        }

        .navigation {
            background: #f0f0f0;
            box-sizing: border-box
        }

        .navigation ul {
            margin: 0;
            padding: 0
        }

        .navigation li {
            margin: 0
        }

        .navigation a {
            display: block;
            padding: 10px 0 10px 15px
        }

        .navigation a, .navigation a:hover {
            color: #575757;
            text-decoration: none
        }

        .navigation .level0 {
            border-top: 1px solid #d1d1d1;
            font-size: 1.6rem
        }

        .navigation .level0 > .level-top {
            font-weight: 600;
            padding: 8px 40px 8px 20px;
            text-transform: uppercase;
            word-wrap: break-word
        }

        .navigation .level0 > .level-top:hover {
            color: #333
        }

        .navigation li.level0:last-child {
            border-bottom: 1px solid #d1d1d1
        }

        .navigation .submenu {
            display: none !important
        }

        .navigation .submenu > li {
            word-wrap: break-word
        }

        .navigation .submenu:not(:first-child) {
            font-weight: 400;
            line-height: 1.3;
            left: auto !important;
            overflow-x: hidden;
            padding: 0;
            position: relative;
            top: auto !important;
            transition: left .3s ease-out
        }

        .navigation .submenu:not(:first-child) > li:last-child {
            margin-bottom: 0
        }

        .navigation .submenu:not(:first-child) ul {
            display: block;
            padding-left: 15px
        }

        .navigation .submenu:not(:first-child) ul > li {
            margin: 0
        }

        .navigation .submenu:not(:first-child) ul > li a {
            color: #575757;
            display: block;
            line-height: normal;
            padding: 10px 20px
        }

        .navigation .submenu:not(:first-child) ul > li a:hover {
            color: #333
        }

        .modal-popup.modal-slide {
            left: 0;
            z-index: 900
        }

        .modal-popup.modal-slide .modal-inner-wrap {
            height: 100%;
            overflow-y: auto;
            position: static;
            -webkit-transform: translateX(100%);
            transform: translateX(100%);
            -webkit-transition: -webkit-transform .3s ease-in-out;
            transition: transform .3s ease-in-out;
            width: auto;
            margin: 0;
            max-height: none
        }

        .custom-slide {
            bottom: 0;
            min-width: 0;
            position: fixed;
            right: 0;
            top: 0;
            visibility: hidden;
            opacity: 0;
            -webkit-transition: visibility 0s .3s, opacity .3s;
            transition: visibility 0s .3s, opacity .3s;
            left: 0;
            z-index: 900
        }

        .custom-slide .modal-inner-wrap {
            opacity: 1;
            pointer-events: auto;
            overflow-y: auto;
            position: static;
            -webkit-transform: translateX(100%);
            transform: translateX(100%);
            -webkit-transition: -webkit-transform .3s ease-in-out;
            transition: transform .3s ease-in-out;
            width: auto;
            background-color: #fff;
            box-sizing: border-box;
            height: auto;
            min-height: 100%
        }

        .modal-content table {
            display: block
        }

        .modal-content table td, .modal-content table tr {
            display: block;
            width: 100%
        }

        .modal-popup.modal-slide .modal-inner-wrap[class] {
            background-color: #fff
        }

        .modal-popup.modal-slide._inner-scroll .modal-inner-wrap {
            height: auto;
            min-height: 100%
        }

        .checkout-payment-method .payment-methods {
            margin: 0
        }

        .checkout-payment-method .payment-method-title {
            padding: 15px
        }

        .checkout-payment-method .payment-method-content {
            padding: 0
        }
    }@media only screen and (max-width: 639px) {
    .minicart-wrapper {
        margin-top: 10px
    }

    .opc-wrapper .step-title {
        font-size: 22px
    }

    .opc-wrapper .form-login, .opc-wrapper .form-shipping-address {
        margin: 0;
        padding: 0
    }

    .shipping-policy-block.field-tooltip {
        margin-bottom: 20px;
        position: relative;
        right: auto;
        top: auto
    }

    .shipping-policy-block.field-tooltip .field-tooltip-content {
        width: 300px;
        right: auto
    }

    .shipping-policy-block.field-tooltip .field-tooltip-content:after, .shipping-policy-block.field-tooltip .field-tooltip-content:before {
        right: auto
    }

    .logo {
        margin-bottom: 13px;
        margin-top: 4px
    }
}@media only screen and (max-width: 479px) {
    .minicart-wrapper .block-minicart {
        width: 290px
    }
}@media all and (min-width: 640px) {
    .table > tbody > tr > td, .table > tbody > tr > th {
        border-top: 1px solid #ccc
    }
}.page-header {
     z-index: 100;
     max-width: 100%;
     background: #fff;
     width: 100%;
     height: auto;
     box-sizing: border-box;
     top: 0;
     transition: .4s
 }

    .page-header .panel.wrapper {
        background-color: #fff;
        border-bottom: none
    }

    .page-header .header.content {
        padding: 30px 0 0
    }

    .checkout-index-index .page-header {
        position: static;
        box-shadow: none
    }

    .checkout-index-index .page-header .header.content {
        position: static;
        padding-top: 0;
        margin: 0 auto
    }

    a.logo::before {
        border: none
    }

    .page-header .header.content .minicart-wrapper {
        position: absolute;
        right: 0;
        top: 109px;
        z-index: 10001
    }

    .minicart-wrapper .counterHolder {
        width: 40px;
        height: 40px;
        background: #000;
        border-radius: 20px;
        display: block
    }

    .minicart-wrapper .counterHolder.hasItem {
        background: 0 0
    }

    .minicart-wrapper .action.showcart {
        white-space: nowrap;
        position: relative
    }

    .minicart-wrapper .action.showcart::before {
        content: '\e908';
        color: #fff;
        top: 6px;
        left: 2px;
        position: absolute;
        font-size: 2.3rem
    }

    .minicart-wrapper .action.showcart:hover::before {
        color: #fff
    }

    .minicart-wrapper .action.showcart .counter.qty {
        border-radius: 12px;
        position: absolute;
        top: -13px;
        right: -9px;
        background: #000;
        min-width: 24px;
        box-sizing: border-box;
        height: 24px;
        display: block
    }

    .minicart-wrapper a::before {
        border-bottom: none
    }

    @media (min-width: 767px) and (max-width: 1200px) {
        .logo img {
            width: 70%
        }
    }.footer-wrapper {
         display: flex;
         padding: 70px 120px 60px;
         justify-content: space-between;
         position: relative
     }

    .footer.content {
        padding: 0
    }

    .footer.content .links a:hover {
        color: #677752;
        text-decoration: none
    }

    .footer.content .links li {
        margin: 0
    }

    .footer-headline {
        font-size: 20px;
        display: inline-block;
        font-weight: 600
    }

    .copyright {
        background: 0 0
    }

    .footer.bottom {
        text-align: center;
        padding: 18px 0
    }

    @media (min-width: 768px) {
        .checkout-index-index .opc-wrapper, .opc-sidebar {
            width: 100%
        }

        .nav-sections {
            background: #fff;
            position: relative;
            margin-bottom: 0
        }

        .sections .navigation {
            background: #fff;
            font-size: 2rem;
            font-weight: 400
        }

        .sections .navigation a::before {
            border-bottom: none
        }

        .sections .navigation a .ui-menu-icon {
            display: none
        }

        .sections .navigation > ul {
            position: static;
            padding: 0
        }

        .sections .navigation > ul > li {
            position: static
        }

        .sections .navigation > ul > li.level-top {
            line-height: 2.6rem;
            display: inline
        }

        .sections .navigation > ul > li + li {
            padding-left: 10px
        }

        .sections .navigation > ul > li a {
            text-decoration: none
        }

        .sections .navigation li.level0 {
            position: static;
            padding: 0 12px
        }

        .sections .navigation li.level0 a, .sections .navigation li.level0 > a.level-top {
            padding: 0
        }

        .sections .navigation li.level0 > a.level-top .ui-menu-icon {
            display: none
        }

        .sections .navigation li.level0 ul.level0.submenu {
            color: inherit;
            border: none;
            border-top: 1px solid #e2e2e2;
            box-shadow: 0 7px 7px -7px grey;
            list-style: none;
            position: absolute;
            width: 100%;
            left: 0 !important;
            top: 47px !important;
            flex-wrap: wrap;
            font-weight: 400
        }

        .sections .navigation li.level0 ul.level0.submenu a span:not(.ui-icon) {
            position: relative
        }

        .sections .navigation li.level0 ul.level0.submenu a span:not(.ui-icon)::before {
            display: block;
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 93%
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1 {
            max-width: 25%;
            flex: 1 0 25%;
            padding: 0;
            position: relative
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1::before {
            border-right: 1px solid #e2e2e2;
            display: block;
            content: "";
            position: absolute;
            top: 20px;
            bottom: 0;
            right: 0;
            width: 1px;
            z-index: 10
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1:nth-child(4)::before {
            border-right: none
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1 img {
            margin-top: 10px;
            width: 100%
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1 a {
            padding: 20px 10px 10px
        }

        .sections .navigation li.level0 ul.level0.submenu .submenuWrapper {
            display: flex !important;
            top: auto !important;
            flex-wrap: wrap;
            padding: 0;
            width: 100%
        }

        .sections .navigation li.level0 ul.level0.submenu > li {
            max-width: 1570px;
            margin: 0 auto;
            width: 100%
        }

        .nav-sections .nav-sections-items {
            max-width: 1570px;
            margin: 0 auto;
            padding-bottom: 10px
        }

        .logo {
            margin: -8px auto 5px 0
        }

        .nav-sections-item-content > .navigation {
            display: flex;
            position: static
        }

        .nav-sections-item-content .page-service-tabs {
            position: fixed;
            right: -359px;
            transform: rotateZ(-90deg);
            width: 650px;
            display: flex;
            top: 300px;
            z-index: 999
        }

        .nav-sections-item-content .page-service-tabs .tabs-container {
            transform: translateY(-60px)
        }
    }.header.content {
         max-width: 1570px;
         margin: 0 auto
     }

    .page-wrapper {
        display: block
    }

    .page-main {
        padding: 0
    }

    .panel.header {
        display: none;
        margin: 0
    }

    .block-cms-link.widget, .block-static-block.widget {
        margin: 0
    }

    .block-static-block.widget .block-container {
        max-width: 1570px;
        margin: 0 auto
    }

    .block-static-block.wide .block-container {
        margin: auto;
        max-width: none
    }

    .columns {
        display: block
    }

    .columns .column.main {
        padding-bottom: 0
    }

    @media (min-width: 992px) {
        .col-md-6 {
            width: 50%;
            float: left;
            position: relative;
            min-height: 1px;
            padding-right: 15px;
            padding-left: 15px
        }
    }#notice-cookie-block {
         box-shadow: 0 5px 5px 5px rgba(0, 0, 0, .1)
     }

    #notice-cookie-block .content {
        max-width: 1570px;
        margin: 0 auto
    }

    .opc-progress-bar {
        padding-top: 0;
        padding-bottom: 0
    }

    .opc-progress-bar .opc-progress-bar-item span {
        padding-top: 0
    }

    .opc-progress-bar .opc-progress-bar-item span::after, .opc-progress-bar .opc-progress-bar-item span::before {
        display: none
    }

    .opc-progress-bar .opc-progress-bar-item._active span {
        color: #677752
    }

    .product-item-photo::before, .product-item-photo:hover::before {
        border-bottom: none
    }

    .product-item-name a {
        font-weight: 700
    }

    .product-item-name a:hover {
        text-decoration: none;
        color: #85b84b
    }

    .product-item-name a::before {
        border-bottom: none
    }

    .checkout-index-index .column.main {
        float: none
    }

    .opc-block-summary {
        background: #fff
    }

    .checkout-index-index #checkout {
        max-width: 1570px;
        margin: 0 auto;
        padding: 0
    }

    .checkout-index-index .opc-sidebar button.action {
        min-height: auto;
        background: 0 0;
        border: none;
        padding: 0;
        margin: 0;
        text-align: right;
        position: absolute;
        right: 0
    }

    .checkout-index-index .opc-sidebar button.action > span {
        display: none
    }

    .checkout-index-index .opc-sidebar button.action:hover {
        background: 0 0;
        border: none;
        color: #85b84b
    }

    .checkout-index-index .opc-sidebar button.action:hover:before {
        color: inherit;
        border: none
    }

    .checkout-index-index .opc-sidebar button.action:after {
        content: "";
        margin-left: 0
    }

    .checkout-index-index .opc-sidebar button.action:before {
        font-family: luma-icons;
        display: inline-block;
        cursor: pointer;
        text-align: center;
        color: inherit;
        border: none;
        transition: .4s;
        width: auto;
        position: relative;
        bottom: auto;
        left: 0
    }

    .checkout-index-index .opc-sidebar button.action.action-edit:before {
        font-size: 1.8rem;
        line-height: 2rem;
        content: '\e601'
    }

    .checkout-index-index .opc-sidebar .shipping-information-title {
        margin: 0 0 5px
    }

    .checkout-index-index .opc-sidebar .shipping-information-title > span {
        font-size: 18px;
        font-weight: 500
    }

    .checkout-index-index .opc-sidebar .opc-block-info {
        background: #fff;
        margin: 0;
        padding: 22px 30px
    }

    .checkout-index-index .opc-sidebar .opc-block-info p + p {
        margin: 0
    }

    .checkout-index-index .billing-address-same-as-shipping-block {
        margin: 10px
    }

    #maincontent #checkout .authentication-wrapper button, .checkout-index-index .checkout-shipping-method .step-title, .checkout-index-index .checkout-shipping-method .table-checkout-shipping-method {
        display: none
    }

    #payment #cardexpiremonth iframe, #payment #cardexpireyear iframe {
        height: 30px
    }

    #payolution_elv_dob_day {
        padding-right: 10px
    }

    form fieldset#customer-email-fieldset .field .note {
        display: none !important
    }

    .checkout-agreements-block {
        margin: 20px 0
    }

    .checkout-info-block {
        margin: 0 0 20px
    }

    .checkout-info-block p + p {
        margin: 0
    }

    .message.global.cookie {
        background: #fff;
        font-size: 15px;
        z-index: 100000
    }

    .message.global.cookie a {
        color: #2f3943
    }

    .message.global.cookie a:hover {
        color: #85b84b
    }

    @media (max-width: 767px) {
        .logo {
            display: table;
            margin: auto;
            float: none
        }

        .page-header .header.content {
            padding-top: 0
        }

        .page-header .header.content .service-wrapper-container {
            position: relative;
            z-index: 150
        }

        .page-header .header.content .minicart-wrapper {
            right: 10px;
            top: -5px;
            z-index: inherit
        }

        .nav-toggle {
            right: 17px;
            left: auto;
            top: 63px;
            position: absolute
        }

        .block-static-block.widget .block-container {
            padding: 0 20px
        }

        .navigation {
            background: #fff
        }

        .navigation a::before {
            border-bottom: none
        }

        .navigation .submenuWrapper {
            display: block !important;
            padding: 0
        }

        .navigation a {
            padding: 10px
        }

        .navigation .submenu:not(:first-child) ul {
            padding-left: 0
        }

        .nav-sections-item-title {
            display: none
        }

        .nav-sections-item-content {
            margin-top: 0;
            margin-left: 0;
            padding: 0
        }

        .nav-sections-item-content#store\.quicklinks {
            display: block !important
        }

        .nav-sections-items {
            padding-bottom: 20px
        }

        .page-footer {
            background: #fff;
            padding-bottom: 30px
        }

        .page-footer .footer.content {
            border-top: none
        }

        .page-footer .footer-wrapper {
            flex-direction: column;
            padding: 0;
            background: #fff
        }

        .page-footer .footer-wrapper .footer-column {
            padding: 30px 20px;
            text-align: center
        }

        .page-footer .footer-wrapper .footer-column.links::after {
            display: none
        }

        .page-footer .footer-wrapper .footer-column ul li a {
            display: inline-block
        }

        .page-layout-1column a.action, .page-layout-1column button.action {
            width: 100%
        }

        .actions-toolbar > .primary {
            text-align: left
        }

        .opc-progress-bar {
            padding-left: 10px;
            padding-right: 10px
        }

        .checkout-index-index .opc-wrapper {
            width: 100%;
            box-sizing: border-box
        }

        .checkout-index-index .opc-sidebar {
            width: 100%
        }

        .checkout-index-index .page-header .header.content .service-wrapper-container {
            height: auto;
            padding: 0
        }

        .checkout-index-index .minicart-wrapper .action.showcart .counterHolder.hasItem .counter.qty {
            background: #636d70
        }

        .checkout-index-index .page-header {
            margin-bottom: 0;
            border-bottom: none
        }

        .opc-estimated-wrapper {
            margin: 0
        }
    }@media (min-width: 768px) and (max-width: 1200px) {
    .sections.nav-sections {
        padding: 0 20px
    }

    .opc-progress-bar {
        padding: 20px
    }

    .page-header .header.content {
        padding: 30px 10px 0
    }
}.page-wrapper {
     background: #f5f5f5
 }

    .page-bottom {
        padding-bottom: 100px;
        position: relative;
        z-index: 999;
        background: #f5f5f5
    }

    .page-footer {
        position: relative;
        z-index: 999
    }

    .service-wrapper-inner {
        clear: both;
        margin-top: 20px;
        font-size: 15px;
        color: #6f7f5b;
        display: flex;
        align-items: center;
        flex-direction: row;
        justify-content: space-between;
        max-width: 100%
    }

    .service-wrapper-inner .logo-container {
        order: 2;
        min-width: 250px
    }

    .service-wrapper-inner .service-items-outer {
        order: 4
    }

    .service-wrapper-inner .minicart-wrapper {
        order: 5;
        margin-left: 35px;
        padding: 15px 0
    }

    .service-wrapper-inner .minicart-wrapper > a.action {
        background: linear-gradient(180deg, #ffd119 0, #dcaa28 100%);
        border-radius: 5px;
        padding: 17px 15px;
        font-size: 16px;
        white-space: nowrap
    }

    .service-wrapper-inner .minicart-wrapper > a.action span {
        color: #fff;
        font-weight: 500;
        display: inline-block;
        vertical-align: middle
    }

    .service-wrapper-inner .minicart-wrapper > a.action img {
        display: inline-block;
        vertical-align: middle
    }

    .service-wrapper-inner .minicart-wrapper span.counter.qty {
        font-weight: 500;
        color: #636d70
    }

    .service-wrapper-top {
        display: table;
        width: 100%;
        margin: 5px 0;
        border-bottom: 1px solid #e2e2e2;
        font-size: 14px;
        color: #636d70
    }

    .service-wrapper-top .service-top-items {
        float: left
    }

    .service-wrapper-top .service-top-item {
        display: inline-block;
        padding-left: 10px
    }

    .service-wrapper-top a {
        font-size: 14px;
        color: #636d70
    }

    .service-items-outer .service-items-item {
        background: #f5f5f5;
        padding: 15px 25px;
        font-weight: 500;
        position: relative
    }

    .service-top-items .icon_delivery {
        width: 22px;
        height: 25px;
        margin-left: 10px;
        margin-bottom: 3px
    }

    .service-top-items .icon_mail {
        width: 20px;
        height: 25px;
        margin-left: 10px;
        margin-bottom: 3px
    }

    .service-items-outer .service-items-item.service-item-trustedshops span {
        display: block;
        line-height: normal;
        font-size: 14px;
        color: #636d70
    }

    .service-items-outer .service-items-item.service-item-trustedshops img, .service-items-outer .service-items-item.service-item-trustedshops > .service-item-inner {
        display: inline-block;
        vertical-align: middle
    }

    .service-items-outer .service-items-item.service-item-trustedshops {
        padding: 0 15px 0 0;
        border-radius: 30px 0 0 30px
    }

    .service-items-outer .service-items-item.service-item-trustedshops img {
        margin-right: 10px
    }

    .service-items-outer.footer-service-outer .service-items-item.service-item-trustedshops {
        padding-right: 10px;
        margin: 20px 0
    }

    .header .service-items-outer .service-items-item {
        display: inline-block
    }

    .service-sidebar-inner .service-sidebar-item {
        background: #fff;
        text-align: left;
        font-size: 18px;
        color: #636d70;
        margin-bottom: 7px;
        display: block;
        border-radius: 5px 0 0 5px;
        overflow: hidden;
        width: auto;
        padding-right: 15px;
        box-shadow: 0 0 15px 0 rgba(0, 0, 0, .1)
    }

    .service-sidebar-inner .service-sidebar-item img {
        background: #667751;
        width: 45px;
        height: auto;
        padding: 12px 10px
    }

    .service-sidebar-inner .service-sidebar-item span {
        padding: 5px;
        display: inline-block;
        vertical-align: middle
    }

    .page-title-wrapper h1.page-title {
        font-size: 35px;
        font-weight: 500;
        text-transform: uppercase;
        padding-bottom: 10px;
        text-align: center;
        margin: 0
    }

    .header-language-row {
        float: right;
        font-size: 0
    }

    .header-language-row .header-language-item {
        display: inline-block;
        cursor: pointer
    }

    .header-language-row .header-language-item:not(.header-language-active) {
        opacity: .3
    }

    .header-language-row .header-language-item img {
        margin-left: 7px
    }

    .header-language-row .header-language-item a {
        font-size: 0;
        vertical-align: middle
    }

    .footer-logo {
        max-width: 100px;
        margin: 35px auto 0;
        display: block
    }

    footer.page-footer .block-static-block.widget .block-container {
        text-align: center
    }

    .footer-wrapper-border {
        position: relative
    }

    .footer-wrapper-border .footer-wrapper-border-inner {
        pointer-events: none;
        overflow: hidden;
        width: 100%;
        height: 185px;
        position: absolute;
        top: -160px;
        z-index: 2
    }

    .footer-column .footer-payment {
        margin-top: 20px
    }

    .footer-column .footer-payment img {
        display: inline-block;
        margin-bottom: 10px
    }

    footer.page-footer .promotion-footer-badge {
        position: fixed;
        right: 0;
        bottom: 190px;
        display: none;
        z-index: 10
    }

    footer.page-footer .promotion-footer-badge > div {
        box-shadow: 0 2px 9px 2px rgba(0, 0, 0, .1);
        background: #fff;
        display: block;
        padding: 14px
    }

    .minicart-wrapper a.action .text {
        color: #636d70;
        font-weight: 500
    }

    .minicart-wrapper a.action .text + .text {
        display: none
    }

    .home-header-inner {
        position: relative
    }

    .home-header-inner .home-header-info {
        background: #fff;
        position: absolute;
        border-radius: 3px 3px 0 0;
        top: 25px;
        box-shadow: 0 0 15px 0 rgba(0, 0, 0, .1)
    }

    .home-header-inner .home-header-info-top {
        display: table;
        width: 100%;
        color: #636d70;
        padding: 10px;
        min-height: 180px
    }

    .home-header-inner .home-header-info-top .home-header-info-portrait img {
        position: absolute;
        top: -40px;
        left: -18px;
        pointer-events: none;
        z-index: 1
    }

    .home-header-inner .home-header-info-top .home-header-info-quote {
        text-align: left;
        padding-left: 0;
        padding-right: 0;
        width: 50%;
        float: right;
        padding-top: 20px
    }

    .home-header-info-top .home-header-info-quote span {
        font-size: 15px;
        display: block;
        font-style: italic;
        line-height: normal;
        margin-bottom: 10px
    }

    .home-header-inner .home-header-info-bottom {
        background: #f5f5f5;
        position: relative;
        text-align: center;
        z-index: 0;
        padding: 0 15px 15px;
        margin-top: -15px
    }

    .checkout-index-index .home-header-inner .home-header-info-bottom {
        background: #667751
    }

    .btn-main, .checkout-index-index .payment-method .actions-toolbar button.checkout {
        background: #ffd119;
        background: linear-gradient(180deg, #ffd119 0, #dcaa28 100%);
        border-radius: 5px;
        display: table;
        width: auto;
        margin: auto;
        color: #fff;
        font-size: 20px;
        font-weight: 500;
        text-align: center;
        position: relative
    }

    .checkout-index-index .payment-method .actions-toolbar button.checkout span {
        color: #fff;
        font-size: 20px;
        display: block;
        padding: 8px 15px;
        font-weight: 500;
        text-align: center
    }

    @media (min-width: 768px) and (max-width: 999px) {
        .checkout-index-index .payment-method .actions-toolbar button.checkout span {
            font-size: 16px;
            margin-left: 16px
        }

        .checkout-index-index .row-totals .save {
            flex-direction: column
        }

        .checkout-index-index .checkout-header-second {
            font-size: 13px
        }
    }.checkout-index-index .payment-method .actions-toolbar button.checkout, button.btn-main {
         padding: 0 !important;
         border: none;
         min-height: auto
     }

    .checkout-index-index .payment-method .actions-toolbar button.checkout:after, button.btn-main:after {
        content: none
    }

    .fullwidth-break-outer {
        display: block;
        padding: 50px 0;
        text-align: center
    }

    .fullwidth-break-inner .fullwidth-break-border {
        border-top: 1px solid #e4e4e4
    }

    .fullwidth-break-inner .fullwidth-break-border img {
        margin-top: -30px
    }

    .fullwidth-break-inner span {
        color: #667751;
        text-transform: uppercase;
        font-size: 15px;
        opacity: .5;
        letter-spacing: 7px;
        margin-top: -15px
    }

    .product-additional-modal {
        position: fixed;
        top: 10%;
        bottom: 10%;
        left: 0;
        right: 0;
        z-index: 10000
    }

    .product-additional-modal-bg {
        background: rgba(51, 51, 51, .55);
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0
    }

    .product-additional-modal-inner {
        background: #fff;
        padding: 20px;
        border-radius: 5px;
        position: relative;
        margin: auto;
        max-width: 1200px
    }

    .product-additional-close {
        position: absolute;
        top: 7px;
        right: 10px;
        font-size: 30px;
        cursor: pointer
    }

    .product-additional-modal-inner img {
        margin: auto;
        display: block
    }

    footer.modal-footer button {
        color: #fff
    }

    @media (min-width: 768px) {
        footer.page-footer .promotion-footer-badge {
            display: block
        }

        .sections .navigation .level1 > a .submenu-category-image {
            position: relative
        }

        .sections .navigation .level1 > a .submenu-category-price {
            position: absolute !important;
            top: 80%;
            z-index: 10;
            right: 0;
            font-size: 25px;
            background: linear-gradient(180deg, #ffd119 0, #dcaa28 100%);
            color: #fff;
            font-weight: 500;
            padding: 3px 15px;
            display: block;
            line-height: normal;
            border-radius: 4px 0 0 4px;
            box-shadow: 0 0 15px 0 rgba(0, 0, 0, .1)
        }

        .sections .navigation .level1 > a > .submenu-category-name {
            font-weight: 700;
            text-align: center;
            display: block;
            text-transform: uppercase
        }

        .sections .navigation .level1 > a > .submenu-category-usplist {
            margin-bottom: 10px
        }

        .sections .navigation .level1 > a > .submenu-category-usplist span {
            display: block;
            font-size: 17px;
            position: relative;
            text-align: center
        }

        .sections .navigation .level1 img {
            margin-top: 0
        }

        .service-wrapper-inner .minicart-wrapper > a.action img {
            margin-right: 5px;
            margin-top: -3px
        }

        .checkout-index-index .opc-progress-bar .opc-progress-bar-item > span {
            display: inline;
            padding-top: 5px;
            padding-bottom: 2px;
            color: #2e2e2e
        }

        .checkout-index-index .field[name='billingAddress.firstname'], .checkout-index-index .field[name='billingAddress.telephone'] {
            min-width: 100px !important;
            position: relative !important
        }

        .checkout-index-index .fieldset.address .field[name="billingAddress.prefix"], .checkout-index-index .fieldset.address .field[name="shippingAddress.prefix"], .checkout-index-index .fieldset.address .field[name='billingAddress.city'], .checkout-index-index .fieldset.address .field[name='billingAddress.company'], .checkout-index-index .fieldset.address .field[name='billingAddress.country_id'], .checkout-index-index .fieldset.address .field[name='billingAddress.fax'], .checkout-index-index .fieldset.address .field[name='billingAddress.firstname'], .checkout-index-index .fieldset.address .field[name='billingAddress.lastname'], .checkout-index-index .fieldset.address .field[name='billingAddress.postcode'], .checkout-index-index .fieldset.address .field[name='billingAddress.telephone'], .checkout-index-index .fieldset.address .field[name='shippingAddress.city'], .checkout-index-index .fieldset.address .field[name='shippingAddress.company'], .checkout-index-index .fieldset.address .field[name='shippingAddress.country_id'], .checkout-index-index .fieldset.address .field[name='shippingAddress.firstname'], .checkout-index-index .fieldset.address .field[name='shippingAddress.lastname'], .checkout-index-index .fieldset.address .field[name='shippingAddress.postcode'], .checkout-index-index .fieldset.address .field[name='shippingAddress.telephone'] {
            display: inline-block;
            margin-bottom: 20px;
            margin-right: 1%;
            margin-left: 1%
        }

        .checkout-index-index .fieldset.address .field[name='shippingAddress.fax'] {
            margin-bottom: 20px;
            margin-right: 1%;
            margin-left: 1%
        }

        .checkout-index-index .field[name='shippingAddress.fax'] {
            margin-top: 58px
        }

        .checkout-index-index .field[name='shippingAddress.telephone'] {
            float: right !important;
            margin-right: 7% !important;
            margin-left: unset !important;
            margin-top: -80px
        }

        .checkout-index-index .field[name='shippingAddress.prefix'] {
            margin-top: 80px
        }

        #empty-block, .checkout-index-index .fieldset.address .field[name="billingAddress.prefix"], .checkout-index-index .fieldset.address .field[name="shippingAddress.prefix"], .checkout-index-index .fieldset.address .field[name='billingAddress.city'], .checkout-index-index .fieldset.address .field[name='billingAddress.company'], .checkout-index-index .fieldset.address .field[name='billingAddress.fax'], .checkout-index-index .fieldset.address .field[name='billingAddress.firstname'], .checkout-index-index .fieldset.address .field[name='billingAddress.lastname'], .checkout-index-index .fieldset.address .field[name='billingAddress.postcode'], .checkout-index-index .fieldset.address .field[name='billingAddress.telephone'], .checkout-index-index .fieldset.address .field[name='shippingAddress.city'], .checkout-index-index .fieldset.address .field[name='shippingAddress.company'], .checkout-index-index .fieldset.address .field[name='shippingAddress.fax'], .checkout-index-index .fieldset.address .field[name='shippingAddress.firstname'], .checkout-index-index .fieldset.address .field[name='shippingAddress.lastname'], .checkout-index-index .fieldset.address .field[name='shippingAddress.postcode'], .checkout-index-index .fieldset.address .field[name='shippingAddress.telephone'] {
            width: 47% !important
        }

        .checkout-index-index .fieldset.address .field[name='billingAddress.country_id'], .checkout-index-index .fieldset.address .field[name='shippingAddress.country_id'] {
            width: 97% !important
        }

        .checkout-index-index .fieldset.address .field[name='billingAddress.company'], .checkout-index-index .fieldset.address .field[name='shippingAddress.firstname'] {
            min-width: 100px;
            position: relative
        }

        .checkout-index-index .checkout-billing-address .fieldset .field.street .field, .checkout-index-index .checkout-shipping-address .fieldset.address fieldset.field.street .field[name="shippingAddress.street.0"] {
            width: 92%;
            margin: 0 5px
        }

        .checkout-index-index .fieldset.address .field[name="shippingAddress.country_id"], .checkout-index-index .fieldset.address .field[name="shippingAddress.prefix"] {
            display: block
        }

        .checkout-index-index .checkout-billing-address .fieldset .field.street .field label {
            display: none
        }

        .checkout-index-index .fieldset.address .field[name='billingAddress.company'] .label, .checkout-index-index .fieldset.address .field[name='billingAddress.fax'] .control, .checkout-index-index .fieldset.address .field[name='billingAddress.fax'] .label, .checkout-index-index .fieldset.address .field[name='shippingAddress.company'] .control, .checkout-index-index .fieldset.address .field[name='shippingAddress.company'] .label, .checkout-index-index .fieldset.address .field[name='shippingAddress.fax'] .control, .checkout-index-index .fieldset.address .field[name='shippingAddress.fax'] .label {
            text-decoration: underline;
            font-size: 17px
        }

        .checkout-index-index .fieldset.address .field[name='billingAddress.company'] .label::before, .checkout-index-index .fieldset.address .field[name='billingAddress.fax'] .label::before, .checkout-index-index .fieldset.address .field[name='shippingAddress.company'] .label::before, .checkout-index-index .fieldset.address .field[name='shippingAddress.fax'] .label::before {
            content: '+ '
        }

        .checkout-index-index .fieldset .field-error {
            position: absolute;
            margin-top: -5px
        }

        .checkout-index-index .block.items-in-cart {
            margin-bottom: 0
        }

        .checkout-index-index .checkout-billing-address .fieldset > .field[name*=prefix] {
            display: block
        }

        .checkout-index-index .opc-sidebar.opc-summary-wrapper .opc-block-info, .checkout-index-index .shipping-information .ship-to, .checkout-index-index .shipping-information .ship-via {
            background: #fff;
            margin: 0;
            padding-top: 15px
        }

        .checkout-shipping-method .actions-toolbar > .primary {
            width: 100%
        }

        .sections .navigation > ul {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            align-content: center;
            align-items: center;
            justify-content: flex-start
        }

        #checkout-step-shipping, .billing-form-container, .shipping-form-container {
            margin-left: 2%
        }

        .opc-continue-button {
            margin: 10px !important
        }

        .checkout-payment-method .payment-method.payolution select.dob-day {
            min-width: 80px
        }
    }.checkout-index-index .checkout-shipping-address #checkout-step-shipping .form.form-shipping-address div.field[name*=region] {
         display: none
     }

    .checkout-info-block .info-cms-block ul li {
        list-style: none;
        line-height: normal;
        font-size: 17px;
        margin-bottom: 10px;
        position: relative;
        padding-left: 30px
    }

    .checkout-info-block .info-cms-block ul li:before {
        content: "";
        background: url("data:image/svg+xml;base64,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") center/15px 15px no-repeat;
        width: 15px;
        height: 25px;
        float: left;
        left: 10px;
        top: 0;
        position: absolute
    }

    .payment-method .payment-method-title .label {
        cursor: pointer;
        margin: 0
    }

    .checkout-index-index .mgz-heading-text {
        text-transform: none
    }

    .checkout-index-index .opc-summary-wrapper .content.minicart-items {
        display: block !important
    }

    .checkout-index-index .opc-summary-wrapper .items-in-cart > .title {
        padding: 0;
        cursor: initial
    }

    .checkout-index-index .opc-summary-wrapper .items-in-cart > .title::after {
        content: none
    }

    .modal-popup._inner-scroll {
        z-index: 10001 !important
    }

    .modal-custom.opc-sidebar {
        margin: 0
    }

    .modal-custom.opc-sidebar .modal-content {
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, .2);
        border: none;
        border-radius: 6px
    }

    .modal-custom.opc-sidebar .modal-header {
        display: none;
        border: none
    }

    .opc-wrapper {
        margin: 0 0 5px
    }

    .opc-wrapper .fieldset > .field > legend.label {
        color: inherit;
        font-size: inherit;
        border: none
    }

    .opc-wrapper .field-tooltip .field-tooltip-action {
        padding-right: 5px
    }

    .opc-wrapper .fieldset .field[name^="billingAddress.street"] > label, .opc-wrapper .fieldset .field[name^="shippingAddress.street"] > label {
        display: none
    }

    .checkout-index-index .opc-wrapper > .opc {
        background: #fff;
        border-radius: 5px;
        padding: 15px
    }

    .checkout-index-index .page-main > .page-title-wrapper .page-title, body:not([class*=catalog-]) .page-title-wrapper {
        display: none
    }

    .checkout-index-index.payment-step .opc-wrapper .trusted {
        display: none !important
    }

    .checkout-index-index .opc-info-bottom {
        display: none !important;
        margin-top: 20px
    }

    .checkout-index-index .opc-info-bottom .home-header-inner .home-header-info {
        position: relative;
        max-width: initial
    }

    .opc-info-bottom .home-header-info-portrait {
        position: static;
        width: 180px
    }

    .opc-info-bottom .home-header-inner .home-header-info-top .home-header-info-quote {
        width: calc(100% - 180px);
        padding-top: 10px
    }

    .checkout-index-index.payment-step .opc-info-bottom .home-header-inner .home-header-info-top .home-header-info-quote {
        width: calc(100% - 140px)
    }

    .opc-info-bottom .home-header-info-bottom {
        color: #fff;
        padding-left: 180px;
        text-align: left;
        margin-top: -15px;
        border: none !important;
        padding-top: 0 !important
    }

    .opc-info-bottom .home-header-info-bottom a {
        color: #fff
    }

    .opc-info-bottom .home-header-inner .home-header-info-top {
        min-height: initial
    }

    .opc-info-bottom ul > li {
        flex-grow: 1
    }

    .checkout-index-index #co-payment-form hr {
        display: none
    }

    .checkout-index-index div.field {
        font-size: 20px;
        color: #2e2e2e
    }

    .checkout-index-index fieldset.field.required {
        font-size: 20px
    }

    .checkout-index-index div.field._required > label, .checkout-index-index div.field.required > label, .opc-wrapper .fieldset > .field > legend.label {
        margin: 0 0 -5px
    }

    .checkout-index-index div.field._required {
        font-size: 20px
    }

    .checkout-index-index div.field._required[name="billingAddress.country_id"] {
        display: block
    }

    .checkout-index-index .checkout-billing-address .billing-address-form {
        margin-bottom: 20px
    }

    .checkout-index-index .opc-progress-bar {
        display: flex;
        align-items: flex-start;
        justify-content: space-around;
        counter-reset: step-counter;
        flex-wrap: nowrap;
        margin: 15px auto;
        max-width: 900px !important
    }

    .checkout-index-index .opc-progress-bar .opc-progress-bar-item {
        counter-increment: my-awesome-counter;
        display: flex;
        align-items: center;
        flex-direction: row;
        width: auto
    }

    .checkout-index-index .opc-progress-bar .opc-progress-bar-item::before {
        content: counter(my-awesome-counter);
        color: #636d70;
        font-size: 1.5rem;
        font-weight: 700;
        position: relative;
        display: inline-block;
        --size: 32px;
        left: -10px;
        line-height: var(--size);
        width: var(--size);
        height: var(--size);
        top: 0;
        background: #e3e2e4;
        border-radius: 50%;
        text-align: center;
        flex-shrink: 0
    }

    .checkout-index-index .opc-progress-bar .opc-progress-bar-item._complete::before {
        content: "\f00c" !important;
        font-family: 'Font Awesome 5 Free';
        color: #657753;
        font-size: 1.5rem;
        font-weight: 700;
        position: relative;
        display: inline-block;
        left: -10px;
        line-height: var(--size);
        width: var(--size);
        height: var(--size);
        top: 0;
        background: #eef4ed;
        border-radius: 50%;
        text-align: center
    }

    .checkout-index-index .opc-choose-payment {
        padding: 8px 10px;
        min-height: unset
    }

    .checkout-index-index .opc-progress-bar .opc-progress-bar-item._active::before {
        color: #fff;
        background: #677752
    }

    .checkout-index-index .opc-progress-bar li.opc-progressbar-delimiter {
        border-top: 2px solid #636d70;
        height: 2px;
        min-width: 15px;
        width: 10%;
        align-self: center
    }

    .checkout-index-index .opc-progress-bar li.opc-progressbar-delimiter._active, .checkout-index-index .opc-progress-bar li.opc-progressbar-delimiter._complete {
        border-top: 2px solid #677752
    }

    .checkout-index-index .opc-progress-bar li[data-step=payment] {
        margin-left: 4%
    }

    @media (max-width: 767px) {
        .checkout-index-index .checkout-mobile-hidden-block {
            display: none
        }

        .checkout-index-index .checkout-mobile-center {
            text-align: center;
            display: flex;
            justify-content: center
        }

        .checkout-index-index .checkout-mobile-center > div {
            max-width: 200px;
            margin-bottom: 10px;
            margin-top: 10px !important
        }

        .opc-progress-bar .opc-progress-bar-item span {
            font-size: 1.5rem
        }

        .checkout-index-index .opc-progress-bar .opc-progress-bar-item::before {
            --size: 22px;
            left: 0 !important
        }

        .checkout-index-index .opc-progress-bar li[data-step=payment] {
            margin: 0
        }

        .checkout-index-index .opc-progress-bar .opc-progress-bar-item {
            flex-direction: column
        }
    }.checkout-index-index .field-tooltip .field-tooltip-action:before {
         content: "\f00c";
         font-family: 'Font Awesome 5 Free';
         color: #677752;
         font-size: 10px;
         font-weight: 700;
         position: relative;
         display: inline-block;
         --size: 18px;
         line-height: var(--size);
         width: var(--size);
         height: var(--size);
         top: 4px;
         background: #e3e2e4;
         border-radius: 50%;
         text-align: center
     }

    .checkout-index-index .opc-wrapper .step-title {
        font-weight: 500;
        font-size: 20px
    }

    .checkout-index-index .opc-block-summary {
        padding: unset !important
    }

    .checkout-index-index .form-login .fieldset.hidden-fields {
        display: none !important
    }

    .checkout-index-index .payment-method .actions-toolbar button.checkout:after {
        content: "\f07a";
        font-family: 'Font Awesome 5 Free';
        color: #fff;
        font-size: 18px;
        font-weight: 700;
        position: absolute;
        top: 7px;
        left: 10px
    }

    .checkout-index-index .opc-sidebar {
        display: flex;
        flex-direction: column
    }

    .checkout-index-index .opc-sidebar .minicart-items .product-item-details .product.options .toggle {
        display: none
    }

    .checkout-index-index .opc-sidebar .minicart-items .product-item-details .product.options .content {
        display: block !important
    }

    .checkout-index-index .opc-sidebar .minicart-items .product-item-details .product.options .content .item-options .label {
        display: none
    }

    .checkout-index-index .opc-sidebar .minicart-items .product-item-details .product.options .content .item-options {
        margin-bottom: 6px
    }

    .checkout-index-index .opc-sidebar .minicart-items .product-item-details .product.options .content .item-options .values {
        margin: 0;
        font-size: .8em
    }

    .checkout-index-index .sidebar-hotline-number img {
        width: 40px
    }

    .checkout-index-index .sidebar-hotline-number a {
        display: inline-block
    }

    .checkout-index-index select {
        -webkit-appearance: button;
        -moz-appearance: button;
        -ms-appearance: auto;
        appearance: auto
    }

    .checkout-index-index .form.payments .fieldset > br {
        display: none
    }

    .checkout-index-index .form.form-login {
        margin-bottom: unset !important
    }

    .checkout-index-index input.input-text, .checkout-index-index input[type=email], .checkout-index-index input[type=password], .checkout-index-index input[type=text], .checkout-index-index select {
        height: 50px;
        border: 2px solid #c2c2c2
    }

    footer.modal-footer {
        text-align: center
    }

    .checkout-index-index .page-header {
        display: none
    }

    .checkout-index-index .opc-wrapper {
        padding: 0;
        float: left;
        border: 1px solid #eef4ed;
        border-radius: 10px;
        box-shadow: rgb(99 99 99 / 20%) 0 2px 8px 0;
        height: max-content
    }

    .checkout-index-index .opc-wrapper > .opc {
        width: 100%
    }

    .checkout-index-index .checkout-payment-method .payment-method-title {
        padding: 15px 0
    }

    .checkout-index-index .footer .footer-wrapper {
        display: none
    }

    .checkout-index-index .page-wrapper {
        padding-bottom: 50px
    }

    .checkout-index-index .ship-via {
        display: none
    }

    .banktransfer-image-container, .banktransfer-payment-image {
        background-image: url(http://www.gfp.test/static/frontend/Gfp/international/de_DE/images/paymenticons/payment_vorkasse.png);
        width: 100%;
        height: 55px;
        background-position: center;
        background-repeat: no-repeat
    }

    .payolution-image-container, .payolution_invoice-payment-image {
        background-image: url(http://www.gfp.test/static/frontend/Gfp/international/de_DE/images/paymenticons/payment_invoice.png);
        width: 100%;
        height: 55px;
        background-position: center;
        background-repeat: no-repeat
    }

    .payolution-elv-image-container, .payolution_elv-payment-image {
        background-image: url(http://www.gfp.test/static/frontend/Gfp/international/de_DE/images/paymenticons/payment_sepa.png);
        width: 100%;
        height: 55px;
        background-position: center;
        background-repeat: no-repeat
    }

    .creditcard-image-container, .payone_creditcard-payment-image {
        background-image: url(http://www.gfp.test/static/frontend/Gfp/international/de_DE/images/paymenticons/creditcard-base.svg);
        width: 100%;
        height: 55px;
        background-position: center;
        background-repeat: no-repeat
    }

    .klarna_pay_later-payment-image {
        background-image: url(http://www.gfp.test/static/frontend/Gfp/international/de_DE/images/paymenticons/klarna.svg);
        width: 100%;
        height: 55px;
        background-position: center;
        background-repeat: no-repeat
    }

    .amazon_payment_v2-payment-image {
        background-image: url(http://www.gfp.test/static/frontend/Gfp/international/de_DE/images/paymenticons/amazon.png);
        width: 100%;
        height: 55px;
        background-position: center;
        background-repeat: no-repeat
    }

    .paypal_express-payment-image {
        background-image: url(http://www.gfp.test/static/frontend/Gfp/international/de_DE/images/paymenticons/paypal.png);
        width: 100%;
        height: 55px;
        background-position: center;
        background-repeat: no-repeat
    }

    .desktop-payment-description {
        font-size: 16px;
        color: #2e2e2e;
        font-weight: 500;
        line-height: 45px;
        width: 100%
    }

    .payment-method-title-desktop {
        flex-basis: 23%;
        height: 120px;
        border: 2px solid #eef4ed;
        border-radius: 5px;
        cursor: pointer
    }

    .checkout-index-index .field input:hover, .checkout-index-index .field select:hover, .payment-method-title-desktop:hover {
        transition: background-color .2s ease-in-out;
        background-color: #eef4ed
    }

    .desktop-payment-description-content {
        width: max-content;
        margin: auto
    }

    .payment-methods-desktop-container {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        padding: .5em;
        gap: .5em
    }

    .payment-method-title.field.choice {
        position: relative
    }

    html[lang=de] label[for=payolution_elv_account_holder]:before {
        content: "Bei Zahlung per Lastschrift erfolgt die Abbuchung unmittelbar vor dem Versand der Ware von Ihrem Bankkonto. Diese Einmallastschrift berechtigt uns ausschließlich einmalig dazu, den Rechnungsbetrag von Ihrem Konto abzubuchen.\A\A";
        display: block;
        margin-bottom: 8px
    }

    html[lang=de] #payolution_invoice-form .agreements:before {
        content: "Bei der Zahlungsart „Kauf auf Rechnung“ senden wir Ihnen die Rechnung mit allen Zahlungsinformationen per E-Mail, sobald wir Ihre Ware geliefert haben. Sie überweisen uns anschließend ganz bequem den Rechnungsbetrag.\A\A";
        display: block;
        margin-bottom: 8px
    }

    .payolution-payment-addinfotext {
        color: #bd081c;
        display: block
    }

    .checkout-index-index .minicart-wrapper, .checkout-index-index .service-wrapper-top {
        display: none
    }

    .opc-block-summary .items-in-cart > .title {
        border-bottom: 1px solid #d3d3d3;
        display: none
    }

    #minicart-content-wrapper .block-content > .actions .action, #minicart-content-wrapper .block-content > .actions .btn-main {
        width: 100%
    }

    #minicart-content-wrapper .block-content > .actions .action:after {
        content: none
    }

    #minicart-content-wrapper .block-content > .actions > .primary {
        margin-left: 0;
        margin-right: 0
    }

    .checkout-index-index .page-header .header.content .minicart-wrapper {
        display: none
    }

    @media (min-width: 768px) and (max-width: 1400px) {
        .service-sidebar-inner .service-sidebar-item {
            position: relative;
            left: calc(100% - 45px);
            transition: .2s ease-in-out
        }

        .service-sidebar-inner .service-sidebar-item:hover {
            left: 0
        }
    }@media (min-width: 768px) and (max-width: 1200px) {
    header.page-header .header.content {
        padding: 0
    }

    header .sections .navigation > ul > li.level-top {
        padding: 0 10px;
        font-size: 18px
    }

    .fullwidth-break-inner span {
        margin-top: 0;
        display: block
    }

    .service-items-outer .service-items-item.service-item-trustedshops {
        font-size: 0;
        background: 0 0
    }

    .header .service-items-outer .service-items-item.service-item-trustedshops span, .service-items-outer .service-items-item.service-item-trustedshops img {
        display: none
    }

    .service-wrapper-inner .minicart-wrapper {
        margin-left: 0
    }

    .checkout-index-index .minicart-items .product-item-details {
        padding-left: 0
    }

    .sections .navigation .level1 > a > .submenu-category-usplist span {
        font-size: 15px
    }

    .header .service-items-outer .service-items-item.service-item-trustedshops {
        vertical-align: top
    }

    .payment-icons > * {
        padding-top: 10px;
        padding-bottom: 10px
    }
}@media (max-width: 590px) {
    .opc-info-bottom .home-header-info-bottom {
        padding-left: 50px;
        padding-top: 10px !important
    }

    .opc-info-bottom .home-header-info-bottom .sidebar-box.sidebar-hotline svg {
        left: 15px !important
    }
}@media (max-width: 939px) {
    .sections .navigation .level1 > a .submenu-category-price, .sections .navigation .level1 > a > .submenu-category-usplist {
        display: none
    }
}@media (max-width: 991px) {
    .header .service-items-outer .service-items-item.service-item-trustedshops > .service-item-inner, .service-top-items > label {
        display: none
    }
}@media (max-width: 992px) {
    .home-header-inner .home-header-info-top {
        margin-bottom: 10px
    }

    .home-header-inner .home-header-info-top .home-header-info-portrait img {
        top: 0;
        left: 0
    }
}@media (min-width: 768px) and (max-width: 1000px) {
    div[class^=amazonpay-button-parent-container] {
        width: 140px !important
    }
}@media (max-width: 767px) {
    .checkout-index-index.payment-step .opc-wrapper .opc-info-bottom {
        display: none
    }

    .checkout-index-index .opc-sidebar {
        position: relative;
        opacity: 1;
        width: 100%
    }

    .checkout-index-index .opc-sidebar .modal-inner-wrap {
        visibility: visible;
        transform: none;
        overflow-y: unset
    }

    .checkout-index-index .opc-sidebar .modal-inner-wrap .checkout-info-block {
        margin: 0
    }

    .checkout-index-index .opc-sidebar .home-header-inner .home-header-info {
        box-shadow: none
    }

    .checkout-index-index .opc-sidebar .modal-inner-wrap .modal-content table tr, .checkout-index-index .opc-sidebar .modal-inner-wrap .modal-content table tr:not(.total-old-price) td {
        display: revert;
        padding: unset
    }

    .checkout-index-index .opc-sidebar .opc-info-bottom .opc-block-info {
        padding: 0
    }

    .checkout-index-index #opc-sidebar .opc-info-bottom .opc-block-info .home-header-info-top .home-header-info-portrait {
        position: absolute
    }

    .checkout-index-index.payment-step #opc-sidebar .opc-info-bottom .home-header-inner .home-header-info-top .home-header-info-quote {
        width: calc(100% - 140px)
    }

    .checkout-index-index .opc-sidebar .modal-inner-wrap .modal-content table tr.totals-tax-details {
        display: none
    }

    .page-header .header.content {
        min-height: 50px
    }

    .page-header .header.content .service-wrapper-container {
        height: auto
    }

    .page-wrapper {
        border-color: #fff
    }

    .service-wrapper-top {
        border: none;
        margin: 0
    }

    .service-wrapper-top .service-top-items {
        display: none
    }

    .header-language-row {
        float: left;
        margin-left: 13px;
        margin-bottom: 5px;
        display: none
    }

    .service-wrapper-inner {
        margin-top: 4px;
        display: table;
        width: 100%;
        padding: 0 15px
    }

    .service-wrapper-inner div.action.nav-toggle {
        position: static;
        display: table-cell;
        vertical-align: middle
    }

    .checkout-index-index .service-wrapper-inner div.action.nav-toggle {
        display: none
    }

    .checkout-index-index .opc-progress-bar .opc-progress-bar-item span {
        width: auto
    }

    .service-wrapper-inner .logo-container {
        display: table-cell;
        vertical-align: middle;
        float: none;
        width: auto
    }

    .service-wrapper-inner .service-items-outer {
        display: none
    }

    .minicart-wrapper a.action.minicart-action {
        display: inline-block;
        padding: 5px 22px 10px;
        width: 65px
    }

    .minicart-wrapper a.action.minicart-action > :not(img) {
        display: none
    }

    .page-header .header.content .minicart-wrapper {
        position: static;
        padding: 0;
        display: table-cell;
        vertical-align: middle;
        float: none;
        margin: 0;
        text-align: right
    }

    .page-header .nav-sections {
        top: 61px;
        z-index: 1000;
        height: calc(100% - 61px);
        background: #f5f5f5;
        margin-top: 0
    }

    .service-sidebar-outer {
        padding-top: 30px
    }

    .service-sidebar-inner {
        border-top: 1px solid #d1d1d1
    }

    .service-sidebar-inner .service-sidebar-item {
        border-radius: 0;
        margin-bottom: 0;
        box-shadow: none;
        border-bottom: 1px solid #d1d1d1
    }

    .service-sidebar-inner .service-sidebar-item span {
        font-size: 16px;
        text-transform: uppercase;
        font-weight: 600;
        color: #575757
    }

    .service-sidebar-inner .service-sidebar-item img {
        padding: 11px 10px
    }

    .page-header .nav-sections .submenu:not(:first-child) ul > li:not(:first-child) a {
        border-top: 1px solid #d1d1d1
    }

    .page-header .nav-sections .submenu ul > li a img {
        display: none
    }

    .home-header-inner .home-header-info {
        position: relative;
        max-width: none;
        top: 0
    }

    .home-header-inner .home-header-info-top .home-header-info-quote {
        text-align: left;
        margin-left: 155px;
        float: none;
        width: 45%
    }

    .home-header-inner .home-header-info-bottom::before {
        content: none
    }

    .home-header-inner .home-header-info-bottom {
        padding-top: 10px
    }

    .fullwidth-break-inner span {
        margin: 0;
        display: block
    }

    .footer-wrapper-border {
        margin-top: 90px
    }

    .page-title-wrapper h1.page-title {
        font-size: 30px;
        text-align: left;
        padding-left: 10px
    }

    .block-static-block.widget .block-container, .checkout-index-index .opc-wrapper > .opc {
        padding: 0
    }

    .checkout-index-index .column.main {
        padding: 0 15px
    }

    .opc-estimated-wrapper {
        padding-left: 0
    }

    .opc-estimated-wrapper .estimated-block {
        font-weight: 500;
        font-size: 18px;
        text-transform: capitalize
    }

    .checkout-index-index #shipping-method-buttons-container {
        padding-bottom: 15px
    }

    .checkout-index-index .modal-custom.opc-sidebar .modal-header {
        display: none
    }

    .checkout-index-index .modal-custom.opc-sidebar .modal-header button.action-close {
        right: 15px
    }

    .minicart-wrapper .action.showcart:before {
        position: relative
    }

    .service-items-outer.footer-service-outer .service-items-item.service-item-trustedshops {
        display: table;
        margin-left: auto;
        margin-right: auto
    }

    .checkout-index-index .opc-estimated-wrapper {
        display: none
    }

    .checkout-index-index .opc-wrapper {
        width: 100%;
        float: none !important;
        margin-bottom: 20px;
        margin-top: 20px
    }

    .checkout-index-index .payment-method .actions-toolbar button.checkout {
        width: 100%
    }

    .checkout-index-index .opc-block-summary {
        padding: 10px 20px;
        width: 100%
    }

    .checkout-payment-method .payment-method-paypal .paypal-buttons {
        width: 100% !important
    }

    .open-fax {
        top: 60px !important
    }

    .checkout-index-index .field[name='shippingAddress.telephone'] {
        margin-top: 0 !important
    }

    #amazon-payment .col-left, .checkout-payment-method .payment-method .actions-toolbar, .klarna-payments-method .payment-method-content .col-left, .klarna-payments-method .payment-method-content > div {
        width: 100%
    }

    .opc-block-summary .grand {
        height: 60px
    }

    .opc-block-summary {
        overflow: hidden
    }

    .opc-block-summary table.table-totals .totals-tax-details {
        bottom: -10px
    }

    .checkout-usps .checkout-header {
        display: none
    }

    #opc-shipping_method {
        margin-top: 20px
    }

    .minicart-items .product-image-wrapper .product-image-photo {
        padding-bottom: 15px
    }
}@media (max-width: 379px) {
    .opc-continue-button span {
        font-size: 13px
    }
}@media (min-width: 380px) {
    .opc-continue-button span {
        font-size: 17px
    }
}@media (max-width: 479px) {
    .fullwidth-break-inner {
        position: relative;
        margin-top: 40px
    }

    .fullwidth-break-inner span {
        letter-spacing: 4px;
        font-size: 15px;
        padding: 0
    }

    .fullwidth-break-inner span:first-of-type {
        position: absolute;
        top: -40px;
        left: 0;
        right: 0
    }

    .home-header-inner .home-header-info-top {
        padding: 10px 5px
    }

    .page-bottom {
        padding-bottom: 0
    }

    .page-title-wrapper h1.page-title {
        font-size: 25px
    }

    label[for=banktransfer]:after, label[for=payolution_elv]:after, label[for=payolution_invoice]:after, label[for=payone_creditcard]:after {
        background: 0 0
    }

    .payment-method.payolution .account-details input, .payment-method.payolution .company-details input {
        width: 100% !important
    }
}#backtotop {
     background: #667751;
     border: 2px solid #fff;
     border-radius: 5px;
     -webkit-box-shadow: 1px 2px 5px rgba(0, 0, 0, .3);
     box-shadow: 1px 2px 5px rgba(0, 0, 0, .3);
     color: #fff;
     cursor: pointer;
     font-size: 13px;
     font-weight: 600;
     height: 40px;
     line-height: 18px;
     padding: 10px 2px 2px;
     position: fixed;
     bottom: 20px;
     text-align: center;
     text-transform: uppercase;
     width: 230px;
     z-index: 9999;
     display: none;
     text-decoration: none;
     left: 50%;
     transform: translateX(-50%);
     opacity: .9
 }

    #backtotop span {
        padding: 0 3px
    }

    #backtotop .gt-arrow {
        padding-top: 8px
    }

    @media (max-width: 768px) {
        .opc-block-summary .product-item .product-item-inner {
            flex-direction: column
        }

        #backtotop {
            border-radius: 100%;
            height: 50px;
            width: 50px;
            left: calc(100% - 150px)
        }

        #backtotop span.gt-text {
            display: none
        }

        #backtotop .gt-arrow {
            padding-top: 6px;
            font-size: 16px;
            display: inline-block
        }

        #checkout-step-shipping, .billing-form-container {
            margin: 10px
        }

        .checkout-shipping-method {
            height: 85px
        }

        .shipping-button-container {
            padding-right: 10px;
            padding-left: 10px
        }

        .banktransfer-image-container, .payolution-elv-image-container, .payolution-image-container {
            position: absolute;
            width: 60px;
            right: 5px;
            top: 0
        }

        .creditcard-image-container {
            position: absolute;
            width: 55px;
            right: 5px;
            top: 0
        }

        .amazon-image-image, .paypal-image-image {
            position: absolute;
            right: 6px;
            top: 6px
        }

        .klarna-image-image {
            position: absolute;
            right: 15px;
            top: 15px
        }

        .klarna-payments-method-cell, .payment-method-label-container label span {
            margin-left: 10px
        }

        .payment-method {
            margin-top: 5px;
            margin-bottom: 5px
        }

        .payment-method .payment-method-title {
            border: 2px solid #eef4ed;
            border-radius: 5px
        }

        .payment-method._active .payment-method-title {
            border: 2px solid green
        }

        .amazon_payment_v2-payment-image, .banktransfer-image-container, .banktransfer-payment-image, .creditcard-image-container, .klarna_pay_later-payment-image, .payolution-elv-image-container, .payolution-image-container, .payolution_elv-payment-image, .payolution_invoice-payment-image, .payone_creditcard-payment-image, .paypal_express-payment-image {
            margin: auto
        }
    }@media (max-width: 640px) {
    #backtotop {
        left: 90%
    }
}.checkout-index-index .checkout-container .opc-wrapper .field-tooltip .label {
     display: none
 }

    .checkout-index-index .checkout-container .payment-method._active .payment-method-title .label:before {
        content: '\e904'
    }

    .checkout-index-index .checkout-container .payment-method.payment-method-paypal .label {
        width: 100%;
        height: 28px
    }

    .payment-icons img {
        margin: 0 5px
    }

    .details-deliveryweek {
        font-size: .8em
    }

    tr.totals.shipping.incl.free .amount {
        color: #667751;
        font-weight: 600;
        padding-left: 0;
        text-align: right
    }

    tr.totals.shipping.incl.free .amount, tr.totals.shipping.incl.free .mark {
        border-top: 1px solid #ccc
    }

    .checkout-index-index .checkout-container tr.totals.shipping.incl.free .mark, tr.totals.shipping.incl.free .mark {
        font-weight: 600
    }

    .checkout-index-index .checkout-container tr.totals.shipping.incl.free .amount, .checkout-index-index .checkout-container tr.totals.shipping.incl.free .mark {
        padding: 16px 0
    }

    .checkout-index-index .checkout-container tr.totals.shipping.incl.free {
        color: #2e2e2e;
        font-weight: 600;
        padding: 16px 0
    }

    .checkout-index-index .checkout-container tr.totals.shipping > * {
        border-top: 1px solid #ccc;
        border-bottom: 1px solid #ccc
    }

    .checkout-index-index .running-agreements {
        color: #636d70;
        font-size: 15px;
        display: inline-block;
        border-radius: 5px
    }

    .checkout-index-index .running-agreements button {
        font-size: 15px;
        min-height: 20px;
        display: inline;
        color: #636d70;
        text-decoration: underline
    }

    .checkout-index-index .payment-method-content .checkout-agreements-block .checkout-agreements {
        margin-bottom: 0
    }

    .checkout-index-index .payment-method-content .checkout-agreements-block {
        display: flex;
        flex-direction: row;
        justify-content: flex-end
    }

    .modal-custom.opc-sidebar .modal-inner-wrap > .modal-content {
        box-shadow: none;
        border: none;
        background: 0 0
    }

    .opc-block-summary table.table-totals .totals-tax-details {
        display: none
    }

    .opc-block-summary table.table-totals {
        position: relative
    }

    .minicart-wrapper .reduction .reduction-container .price-wrapper > span {
        color: #677752;
        font-weight: 500
    }

    .minicart-wrapper .reduction .reduction-container {
        color: #85b84b
    }

    .minicart-wrapper .reduction .original-price-container {
        text-decoration: line-through
    }

    .minicart-wrapper .reduction .regular-price > * {
        display: inline
    }

    .minicart-wrapper .reduction {
        text-align: right
    }

    .opc-block-summary .product-item .price {
        font-weight: 600;
        font-size: 15px
    }

    .checkout-index-index .shipping-information-content {
        line-height: 17px;
        padding-bottom: 20px
    }

    .checkout-index-index .product-item-name {
        display: inline-block
    }

    @media (max-width: 767px) {
        .shipping-address-container, .shipping-method-container {
            margin: 10px 0 !important
        }

        .opc-continue-button {
            margin: unset !important
        }

        .actions-toolbar > .primary {
            text-align: center;
            width: 100%
        }
    }span.submenu-category-name {
         white-space: nowrap
     }

    div#amredirect-popup {
        display: none
    }

    .amazon-button-container .field-tooltip {
        margin: 0;
        right: 0;
        top: 0
    }

    @media only screen and (max-width: 767px) {
        .payment-method {
            min-height: 60px
        }

        .payment-group-mobile {
            display: none
        }
    }.checkout-payment-method .payment-method .payment-method-content, .checkout-payment-method .payment-method .payment-method-content div.field {
         margin: 10px;
         font-size: 16px;
         line-height: 1.4
     }

    .checkout-payment-method .payment-method .actions-toolbar {
        margin: 20px 0
    }

    .checkout-payment-method .payment-method .amazon-button-container {
        margin-bottom: 0
    }

    .checkout-index-index .payment-method-content .checkout-agreements-block {
        margin: 0
    }

    .checkout-payment-method .payment-method.payolution .agreements, .checkout-payment-method .payment-method.payolution .running-agreements {
        margin-left: 10px
    }

    .checkout-payment-method .payment-method-content .creditcard_form select {
        width: 225px !important
    }

    @media (min-width: 768px) and (max-width: 991px) {
        .checkout-index-index.payment-step .opc-info-bottom .home-header-info-bottom {
            padding-left: 50px;
            padding-top: 10px !important
        }

        .checkout-index-index.payment-step .opc-info-bottom .home-header-info-bottom .sidebar-box.sidebar-hotline svg {
            left: 15px !important
        }
    }.checkout-payment-method .payment-method.payolution .fieldset.checkout-agreements {
         margin-top: 0
     }

    .checkout-payment-method .payment-method-content .fieldset:not(:last-child), .checkout-payment-method .payolution-payment-addinfotext {
        margin-bottom: 8px
    }

    .checkout-payment-method .payment-method.payolution .min-age {
        font-size: 14px
    }

    .checkout-payment-method .payment-method.payolution .account-details input {
        margin-top: 0
    }

    .checkout-payment-method .payment-method.payolution .agreements br {
        display: none
    }

    .checkout-payment-method .payment-method-content input.input-text, .checkout-payment-method .payment-method-content input[type=text], .checkout-payment-method .payment-method-content select {
        height: 30px;
        margin-top: 0;
        padding: 2px 10px 1px
    }

    .checkout-payment-method .payment-method-content .fieldset > .field > .label {
        margin-bottom: 0;
        font-weight: 500
    }

    .checkout-payment-method .payment-method-content [type=checkbox]:checked + label:after, .checkout-payment-method .payment-method-content [type=checkbox]:checked + label:before, .checkout-payment-method .payment-method-content [type=checkbox]:not(:checked) + label:before {
        width: 18px;
        height: 19px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        margin: 0;
        left: 0
    }

    .checkout-payment-method .payment-method-content [type=checkbox]:checked + label, .checkout-payment-method .payment-method-content [type=checkbox]:not(:checked) + label {
        padding-left: 25px
    }

    .checkout-payment-method .payment-method-content .creditcard_form .fieldset > .field {
        margin-bottom: 10px
    }

    @media screen and (min-width: 768px) {
        .checkout-index-index #opc-sidebar .checkout-info-block {
            margin-bottom: 0
        }
    }.checkout-index-index #opc-sidebar .modal-content {
         overflow: visible
     }

    .checkout-index-index #opc-sidebar .home-header-inner .home-header-info {
        position: relative;
        max-width: unset;
        top: unset
    }

    .checkout-index-index #opc-sidebar .home-header-inner {
        display: flex;
        flex-direction: column
    }

    .checkout-index-index #opc-sidebar .home-header-inner .home-header-info-top {
        display: flex;
        flex-wrap: nowrap
    }

    .checkout-index-index #opc-sidebar .home-header-inner .home-header-info-top .home-header-info-quote {
        width: 100%;
        padding: 10px;
        float: none
    }

    .checkout-index-index #opc-sidebar .home-header-inner .home-header-info-top .home-header-info-portrait {
        position: relative;
        float: none;
        min-height: unset;
        width: auto;
        padding-right: 0
    }

    .checkout-index-index #opc-sidebar .home-header-inner .home-header-info-top .home-header-info-portrait img {
        left: unset;
        top: unset;
        position: relative;
        margin-top: -50px;
        margin-left: -25px;
        max-width: unset;
        height: 220px
    }

    .checkout-index-index #opc-sidebar .sidebar-hotline-number, .checkout-index-index #opc-sidebar .sidebar-support-email {
        display: flex;
        align-items: center;
        margin-bottom: 2px
    }

    @media (max-width: 992px) {
        .checkout-index-index #opc-sidebar .home-header-inner .home-header-info-top {
            flex-direction: column
        }

        .checkout-index-index #opc-sidebar .home-header-inner .home-header-info-top .home-header-info-portrait {
            width: 100%
        }
    }.mgz-container {
         overflow: auto
     }

    @media (max-width: 481px) {
        .checkout-index-index .opc-progress-bar li.opc-progressbar-delimiter {
            border: none !important
        }
    }@media all and (min-width: 1024px), print {
    .payment-method-title-desktop {
        flex-basis: 19%
    }
}.hidden {
     display: none
 }

    @media (min-width: 768px), print {
        .payment-method-container {
            position: relative
        }

        .payment-method-label-container {
            text-align: center
        }

        .amazon-image-container, .paypal-image-container {
            text-align: center;
            height: 55px
        }

        .klarna-payments-method-cell {
            text-align: center;
            width: 100%
        }

        .klarna-payments-method-cell, .payment-method-label-container label span {
            font-size: 16px
        }

        .banktransfer-image-container {
            width: 100%
        }

        .payment-method {
            margin: 5px
        }

        .main-payment-title, .payment-method-title {
            display: none
        }

        .amazon_payment_v2-payment-image, .banktransfer-image-container, .banktransfer-payment-image, .creditcard-image-container, .klarna_pay_later-payment-image, .payolution-elv-image-container, .payolution-image-container, .payolution_elv-payment-image, .payolution_invoice-payment-image, .payone_creditcard-payment-image, .paypal_express-payment-image {
            margin: 8px auto
        }

        #customer-email-fieldset {
            position: relative
        }

        .email-field {
            width: 45% !important;
            position: absolute;
            margin-left: 1% !important
        }

        .open-fax {
            max-width: 44%;
            position: absolute;
            right: 8%;
            top: -30px;
            line-height: 15px;
            text-decoration: underline;
            cursor: pointer;
            text-align: right
        }

        .checkout-payment-method .payment-method._active .payment-method-content {
            flex-direction: row
        }

        .checkout-payment-method .payment-method._active .payment-method-content .col-left {
            width: 66%
        }

        .checkout-payment-method .payment-method._active .payment-method-content .col-right {
            width: 33%
        }

        .checkout-payment-method .payment-method-paypal .paypal-buttons {
            width: 33% !important
        }

        #amazon-payment .payment-method-content {
            margin-left: unset
        }

        .checkout-index-index .opc-block-summary {
            box-shadow: rgb(99 99 99 / 20%) 0 2px 8px 0;
            border-radius: 10px
        }

        .opc-block-summary table.table-totals .totals-tax-details {
            bottom: 5px
        }

        .checkout-usps {
            display: block;
            min-height: 110px
        }
    }.field[name="billingAddress.region_id"] {
         display: none !important
     }

    .billing-address-title {
        display: block !important
    }

    .billing-address-title, .shipping-address-title {
        border-bottom: unset !important;
        background-color: #eef4ed;
        min-height: 40px;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        margin-top: unset !important;
        padding-left: 20px;
        line-height: 40px;
        color: #2e2e2e
    }

    .shipping-address-container, .shipping-method-container {
        border: 1px solid #eef4ed;
        border-radius: 10px;
        box-shadow: rgb(99 99 99 / 20%) 0 2px 8px 0;
        height: max-content
    }

    .shipping-button-container {
        width: 100%;
        margin: auto
    }

    .shipping-button-container .opc-continue-button span {
        display: flex;
        align-items: center;
        justify-content: center
    }

    .shipping-button-container .opc-continue-button span:after {
        content: '\f061';
        font-family: 'Font Awesome 5 Free';
        margin-left: 10px;
        font-weight: 600;
        font-size: 2rem;
        color: #fff
    }

    .opc-continue-button {
        background: linear-gradient(180deg, #ffd119 0, #dcaa28 100%);
        border-radius: 5px;
        padding: 17px 15px;
        font-size: 17px;
        font-weight: 600;
        white-space: nowrap;
        width: min-content;
        float: right
    }

    .opc-continue-button span {
        font-weight: 600;
        white-space: nowrap
    }

    .payment-method div label::before {
        border: 1px solid #eef4ed
    }

    #payment {
        width: 100%;
        box-shadow: rgb(99 99 99 / 20%) 0 2px 8px 0;
        border: 1px solid #eef4ed;
        border-radius: 10px
    }

    .step-title-new {
        font-size: 16px;
        font-weight: 600;
        color: #2e2e2e;
        margin-left: 20px
    }

    .checkout-summary-title, .payment-methods-title {
        width: 100%;
        background-color: #eef4ed;
        min-height: 40px;
        line-height: 40px
    }

    .checkout-summary-summary, .payment-methods-methods {
        margin-right: 10px;
        margin-left: 10px
    }

    .checkout-summary-title span {
        font-size: 16px;
        font-weight: 600;
        color: #2e2e2e;
        margin-left: 20px
    }

    #choose-payment-method-button-wrapper {
        display: flex;
        justify-content: flex-end;
        align-items: center
    }

    .active-payment-block {
        border: 2px solid green
    }

    .payment-group-mobile {
        margin-bottom: 20px
    }

    .checkout-old-label {
        display: none
    }

    .checkout-index-index .column.main {
        background: unset !important
    }

    .checkout-index-index .page-bottom, .checkout-index-index .page-footer {
        display: none
    }

    .checkout-index-index .opc, .checkout-index-index .opc-wrapper {
        border: unset;
        box-shadow: unset
    }

    .grand.totals, .info-block-zero, .items-in-cart, .shipping-information, .totals-tax-details {
        color: #2e2e2e
    }

    #shipping-new-address-form {
        position: relative
    }

    #customer-email-fieldset {
        z-index: 2
    }

    .opc-progress-bar-item span {
        color: #2e2e2e
    }

    .checkout-index-index #amazon-payment .checkout-agreements-block {
        margin-top: 20px
    }

    .checkout-index-index .amazon-button-container {
        padding-top: 10px
    }

    .ccard .month {
        margin-left: 0 !important
    }

    @media (min-width: 349px) {
        .ccard .year {
            margin-left: -12px !important
        }
    }@media (max-width: 348px) {
    .ccard .year {
        margin-left: unset !important;
        padding-left: unset !important
    }
}.checkout-index-index .open-fax {
     font-size: 1.5rem
 }

    @media (min-width: 800px) and (max-width: 939px) {
        .open-fax {
            font-size: 10px
        }

        .opc-block-summary .table-totals .grand .amount {
            font-size: 16px
        }

        .opc-block-summary .table-totals .grand .mark {
            font-size: 18px;
            padding-right: 10px;
            height: max-content
        }

        .opc-block-summary .table-totals .grand strong {
            font-size: 16px
        }
    }@media (min-width: 768px) and (max-width: 799px) {
    .open-fax {
        font-size: 9px
    }

    .opc-block-summary .table-totals .grand strong {
        font-size: 14px
    }
}@media (min-width: 350px) and (max-width: 767px) {
    .open-fax {
        font-size: 18px
    }
}@media (max-width: 349px) {
    .open-fax {
        font-size: 14px
    }
}@media (max-width: 767px) {
    .open-fax {
        line-height: 20px;
        text-decoration: underline;
        cursor: pointer;
        position: absolute;
        top: 60px
    }

    .checkout-index-index .field[name='shippingAddress.telephone'] {
        margin-bottom: 70px
    }

    .checkout-index-index div[name="shippingAddress.company"] {
        margin-bottom: 35px
    }
}#empty-block {
     height: 2px;
     display: none
 }

    .checkout-index-index .fieldset.address .field[name='shippingAddress.fax'] {
        display: none
    }

    .shipping-th {
        padding: unset !important
    }

    #checkoutSteps {
        display: flex;
        padding: unset;
        border-radius: 10px;
        gap: 2rem
    }

    .checkout-index-index .actions-toolbar {
        margin-bottom: 20px
    }

    @media (max-width: 960px) {
        #checkoutSteps {
            flex-direction: column
        }
    }.checkout-index-index .row-totals {
         font-size: 15px
     }

    .checkout-index-index .row-totals .old-price {
        text-decoration: line-through
    }

    .checkout-index-index .row-totals .save {
        color: #dcaa28;
        font-weight: 600;
        display: flex;
        justify-content: flex-end
    }

    .checkout-index-index .row-totals .save .label {
        font-weight: 500
    }

    .checkout-index-index .checkout-usps .mgz-icon-box-wrapper {
        height: 32px
    }

    .checkout-index-index .mgz-icon-box-size-xs .mgz-icon-box-element {
        font-size: 16px
    }

    @media (min-width: 1600px) {
        .checkout-index-index .checkout-header-second .mgz-image-hovers {
            min-width: 50px
        }
    }
}

/*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/CopeX_ExitIntent/css/exitintent.css *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/css/styles-l.css ; media=screen and (min-width: 768px) */
@media screen and (min-width: 768px) {
    .modal-popup .action-close, .modal-slide .action-close {
        padding: 0 30px 0 0
    }

    .modal-popup .action-close::before, .modal-slide .action-close::before {
        right: 30px;
        top: 50%;
        margin-top: -7px;
        color: inherit
    }

    .modal-popup .action-close:hover, .modal-slide .action-close:hover {
        color: #2f3943
    }

    .modal-popup .action-close > span, .modal-slide .action-close > span {
        clip: unset;
        height: auto;
        width: auto;
        line-height: 1.6rem;
        padding-right: 25px;
        position: relative;
        margin: 0;
        overflow: visible
    }

    @media all and (max-width: 768px) {
        .amazon-button-container {
            width: 100%
        }
    }.field-error {
         color: #e02b27;
         font-size: 1.2rem;
         margin-top: 7px
     }

    @media all and (min-width: 768px), print {
        .page-main .block {
            margin-bottom: 50px
        }

        .header.content:after, .header.content:before, .page-header .header.panel:after, .page-header .header.panel:before {
            content: '';
            display: table
        }

        .header.content:after, .page-header .header.panel:after {
            clear: both
        }

        .column.main, .opc-wrapper {
            box-sizing: border-box
        }

        .footer.content, .header.content, .navigation, .page-header .header.panel, .page-main, .page-wrapper > .page-bottom {
            box-sizing: border-box;
            margin-left: auto;
            margin-right: auto;
            max-width: 100%;
            padding-left: 20px;
            padding-right: 20px;
            width: auto
        }

        .page-main {
            width: 100%
        }

        .columns {
            display: block
        }

        .column.main {
            min-height: 300px
        }

        .page-layout-1column .column.main {
            width: 100%;
            -ms-flex-order: 2;
            -webkit-order: 2;
            order: 2
        }

        .panel.header {
            padding: 10px 20px
        }

        .nav-toggle {
            display: none
        }

        .nav-sections {
            -webkit-flex-shrink: 0;
            flex-shrink: 0;
            -webkit-flex-basis: auto;
            flex-basis: auto;
            margin-bottom: 25px
        }

        .nav-sections-item-title {
            display: none
        }

        .nav-sections-item-content {
            display: block !important
        }

        .nav-sections-item-content > * {
            display: none
        }

        .nav-sections-item-content > .navigation {
            display: block
        }

        .navigation {
            background: #f0f0f0;
            font-weight: 700;
            height: inherit;
            left: auto;
            overflow: inherit;
            padding: 0;
            position: relative;
            top: 0;
            width: 100%;
            z-index: 3
        }

        .navigation:empty {
            display: none
        }

        .navigation ul {
            margin-top: 0;
            margin-bottom: 0;
            position: relative;
            padding: 0 8px
        }

        .navigation li.level0 {
            border-top: none
        }

        .navigation li.level1 {
            position: relative
        }

        .navigation .level0 {
            margin: 0 10px 0 0;
            display: inline-block;
            position: relative
        }

        .navigation .level0:last-child {
            margin-right: 0;
            padding-right: 0
        }

        .navigation .level0 > .level-top {
            color: #fff;
            padding: 0 12px;
            text-decoration: none;
            box-sizing: border-box;
            position: relative;
            display: inline-block
        }

        .navigation .level0 > .level-top:hover {
            text-decoration: none
        }

        .navigation .level0.parent:hover > .submenu {
            overflow: visible !important
        }

        .navigation .level0.parent > .level-top {
            padding-right: 20px
        }

        .navigation .level0.parent > .level-top > .ui-menu-icon {
            position: absolute;
            right: 0;
            display: inline-block;
            text-decoration: none
        }

        .navigation .level0.parent > .level-top > .ui-menu-icon:after {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-size: 12px;
            line-height: 20px;
            color: inherit;
            content: '\e622';
            font-family: luma-icons;
            vertical-align: middle;
            display: inline-block;
            font-weight: 400;
            overflow: hidden;
            speak: none;
            text-align: center
        }

        .navigation .level0 .submenu {
            background: #fff;
            border: 1px solid #ccc;
            box-shadow: 0 5px 5px rgba(0, 0, 0, .19);
            font-weight: 400;
            min-width: 230px;
            padding: 0;
            display: none;
            left: 0;
            margin: 0 !important;
            position: absolute;
            z-index: 99999
        }

        .navigation .level0 .submenu a {
            display: block;
            line-height: inherit;
            color: #636d70;
            padding: 8px 20px
        }

        .navigation .level0 .submenu li {
            margin: 0
        }

        .legend {
            border-bottom: 1px solid #c5c5c5
        }

        .actions-toolbar {
            text-align: left
        }

        .actions-toolbar:after, .actions-toolbar:before {
            content: '';
            display: table
        }

        .actions-toolbar:after {
            clear: both
        }

        .actions-toolbar .primary {
            float: left
        }

        .actions-toolbar .primary, .actions-toolbar .secondary, .actions-toolbar .secondary a.action {
            display: inline-block
        }

        .actions-toolbar .primary .action {
            margin: 0 15px 0 0
        }

        .actions-toolbar .secondary a.action {
            margin-top: 6px
        }

        .actions-toolbar > .primary, .actions-toolbar > .secondary {
            margin-bottom: 0
        }

        .actions-toolbar > .primary .action, .actions-toolbar > .secondary .action {
            margin-bottom: 0;
            width: auto
        }

        .modal-popup.modal-slide .modal-footer {
            text-align: center
        }

        .minicart-wrapper {
            margin-left: 13px
        }

        .opc-wrapper {
            -ms-flex-order: 1;
            -webkit-order: 1;
            order: 1;
            padding-right: 30px
        }

        .opc-estimated-wrapper {
            display: none
        }

        .opc-progress-bar-item {
            width: 185px
        }

        .checkout-shipping-method .actions-toolbar > .primary {
            float: right
        }

        .table-checkout-shipping-method {
            width: auto
        }

        .opc-sidebar {
            margin: 46px 0 20px;
            width: 100%;
            float: right;
            -ms-flex-order: 2;
            -webkit-order: 2;
            order: 2
        }

        .opc-summary-wrapper .modal-header .action-close {
            display: none
        }

        .authentication-dropdown {
            background-color: #fff;
            border: 1px solid #aeaeae;
            -webkit-transform: scale(1, 0);
            -webkit-transform-origin: 0 0;
            -webkit-transition: -webkit-transform .1s linear, visibility 0s linear .1s;
            position: absolute;
            text-align: left;
            top: 100%;
            transform: scale(1, 0);
            transform-origin: 0 0;
            transition: transform .1s linear, visibility 0s linear .1s;
            visibility: hidden;
            width: 100%
        }

        .authentication-wrapper {
            width: 33.33333333%;
            text-align: right
        }

        .block-authentication .block-title {
            font-size: 2.6rem;
            border-bottom: 0;
            margin-bottom: 25px
        }

        .block-authentication .actions-toolbar > .primary {
            display: inline;
            float: right;
            margin-right: 0
        }

        .block-authentication .actions-toolbar > .primary .action {
            margin-right: 0
        }

        .block-authentication .actions-toolbar > .secondary {
            float: left;
            margin-right: 2rem;
            padding-top: 1rem
        }

        .checkout-payment-method .actions-toolbar .primary {
            width: 100%
        }

        .checkout-payment-method .actions-toolbar .primary button {
            width: 100% !important
        }

        .checkout-payment-method .payment-method-content .fieldset > .field {
            margin: 0 0 20px
        }

        body, html {
            height: 100%
        }

        .page-header {
            border: 0;
            margin-bottom: 0
        }

        .page-header .panel.wrapper {
            border-bottom: 1px solid #e8e8e8;
            background-color: #7e807e
        }

        .page-header .header.panel {
            padding-bottom: 10px;
            padding-top: 10px
        }

        .page-main > .page-title-wrapper .page-title {
            display: block
        }

        .header.content {
            padding: 30px 20px 0
        }

        .logo {
            margin: 0 auto 20px 0
        }

        .logo img {
            max-height: inherit;
            width: auto
        }

        .page-wrapper {
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-flex-direction: column;
            -ms-flex-direction: column;
            flex-direction: column;
            margin: 0;
            min-height: 100%;
            position: relative;
            transition: margin .3s ease-out
        }

        .page-footer {
            background: #fff;
            margin-top: auto;
            padding-bottom: 50px
        }

        .footer.content {
            border-top: none;
            background: #fff
        }

        .footer.content ul {
            padding-right: 50px
        }

        .footer.content .links li {
            background: 0 0;
            border: none;
            margin: 0 0 8px;
            padding: 0;
            line-height: normal
        }

        .footer.content .links a {
            display: inline
        }

        .checkout-index-index .opc-sidebar .opc-block-info .checkout-info-block:after {
            clear: both;
            content: "";
            display: table
        }
    }@media all and (min-width: 1024px), print {
    .checkout-index-index .opc-wrapper {
        width: 63%
    }

    .opc-sidebar {
        width: 36%
    }

    .checkout-index-index .modal-popup .modal-inner-wrap {
        margin-left: -400px;
        width: 800px;
        left: 50%
    }

    .table-checkout-shipping-method {
        min-width: 500px
    }
}.page-header {
     position: static;
     z-index: 100;
     max-width: 100%;
     background: #fff;
     width: 100%;
     height: auto;
     box-sizing: border-box;
     top: 0;
     transition: .4s;
     padding: 0 15px
 }

    .page-header .panel.wrapper {
        background-color: #fff;
        border-bottom: none;
        margin: 0
    }

    .page-header .header.content {
        padding: 0
    }

    .checkout-index-index .page-header {
        position: static;
        box-shadow: none
    }

    .checkout-index-index .page-header .header.content {
        position: static;
        padding-top: 0;
        margin: 0 auto
    }

    a.logo::before {
        border: none
    }

    .page-header .header.content .minicart-wrapper {
        position: static;
        z-index: 10001
    }

    .minicart-wrapper .counterHolder {
        width: 40px;
        height: 40px;
        background: #000;
        border-radius: 20px;
        display: block
    }

    .minicart-wrapper .counterHolder.hasItem {
        background: #85b84b
    }

    .minicart-wrapper .action.showcart {
        position: relative
    }

    .minicart-wrapper .action.showcart::before {
        content: '\e908';
        color: #fff;
        top: 6px;
        left: 2px;
        position: absolute;
        font-size: 2.3rem
    }

    .minicart-wrapper .action.showcart:hover::before {
        color: #fff
    }

    .minicart-wrapper .action.showcart .counter.qty {
        border-radius: 12px;
        position: absolute;
        top: -13px;
        right: -9px;
        background: #000;
        min-width: 24px;
        box-sizing: border-box;
        height: 24px;
        display: block
    }

    .minicart-wrapper a::before {
        border-bottom: none
    }

    .footer-wrapper {
        position: relative
    }

    .footer-wrapper .footer-column.links {
        width: 25%
    }

    .footer.content {
        padding: 0
    }

    .footer.content .links a {
        color: inherit;
        font-size: 16px;
        margin: 0;
        padding: 0
    }

    .footer.content .links a:hover {
        color: #85b84b;
        text-decoration: none
    }

    .footer.content .links li {
        margin: 0 0 3px
    }

    .footer.content .links p {
        font-size: 16px
    }

    .footer-headline {
        font-size: 20px;
        display: inline-block;
        font-weight: 500
    }

    .copyright {
        display: block;
        background: 0 0;
        margin: 10px 0 0
    }

    .footer.bottom {
        background-color: #fff;
        text-align: center;
        padding: 18px 0
    }

    .footer.bottom > .container {
        border-top: 2px solid #eee
    }

    @media (min-width: 768px) {
        .nav-sections {
            background: #fff;
            position: relative;
            margin-bottom: 0
        }

        .sections.nav-sections {
            background: #677752;
            margin: 0 -15px
        }

        .sections .navigation {
            background: #677752;
            font-size: 2rem;
            font-weight: 400
        }

        .sections .navigation a::before {
            border-bottom: none
        }

        .sections .navigation a .ui-menu-icon {
            display: none
        }

        .sections .navigation > ul {
            position: static;
            padding: 0;
            width: 100%
        }

        .sections .navigation > ul > li {
            position: static
        }

        .sections .navigation > ul > li.level-top {
            line-height: 2;
            display: inline
        }

        .sections .navigation > ul > li + li {
            padding-left: 10px
        }

        .sections .navigation > ul > li a {
            text-decoration: none
        }

        .sections .navigation li.level0 {
            position: static;
            padding: 0 15px;
            margin: 0
        }

        .sections .navigation li.level0 a, .sections .navigation li.level0 > a.level-top {
            padding: 0
        }

        .sections .navigation li.level0 > a.level-top .ui-menu-icon {
            display: none
        }

        .sections .navigation li.level0 ul.level0.submenu {
            color: inherit;
            border: none;
            box-shadow: 0 5px 5px -5px rgba(0, 0, 0, .1);
            list-style: none;
            position: absolute;
            width: 100%;
            left: 0 !important;
            top: 40px !important;
            flex-wrap: wrap;
            font-weight: 400
        }

        .sections .navigation li.level0 ul.level0.submenu a span:not(.ui-icon) {
            position: relative
        }

        .sections .navigation li.level0 ul.level0.submenu a span:not(.ui-icon)::before {
            display: block;
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 93%
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1 {
            max-width: 25%;
            flex: 1 0 25%;
            padding: 0;
            position: relative
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1::before {
            border-right: 1px solid #e2e2e2;
            display: block;
            content: none;
            position: absolute;
            top: 20px;
            bottom: 0;
            right: 0;
            width: 1px;
            z-index: 10
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1:nth-child(4)::before {
            border-right: none
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1 img {
            margin-top: 10px;
            width: 100%
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1 a {
            padding: 10px 5px 0
        }

        .sections .navigation li.level0 ul.level0.submenu .submenuWrapper {
            display: flex !important;
            top: auto !important;
            flex-wrap: wrap;
            padding: 0;
            width: 100%
        }

        .sections .navigation li.level0 ul.level0.submenu > li {
            max-width: 1570px;
            margin: 0 auto;
            width: 100%
        }

        .nav-sections .nav-sections-items {
            max-width: 1570px;
            margin: 0 auto;
            padding-bottom: 0
        }

        .nav-sections-item-content > .navigation {
            display: flex;
            position: static
        }

        .nav-sections-item-content .page-service-tabs {
            position: fixed;
            right: 0;
            transform: none;
            width: auto;
            display: flex;
            top: 20%;
            z-index: 1000
        }

        .nav-sections-item-content .page-service-tabs .tabs-container {
            transform: none
        }
    }.header.content {
         max-width: 1570px;
         margin: 0 auto
     }

    .page-wrapper {
        display: block
    }

    .page-main {
        padding: 0
    }

    .panel.header {
        display: none
    }

    .block-cms-link.widget, .block-static-block.widget {
        margin: 0
    }

    .block-static-block.widget .block-container {
        max-width: 1570px;
        margin: 0 auto
    }

    .block-static-block.wide .block-container {
        margin: auto;
        max-width: none
    }

    .columns {
        display: block
    }

    .columns .column.main {
        background: #f5f5f5;
        padding: 0 15px
    }

    @media (min-width: 992px) {
        .col-md-6 {
            width: 50%;
            float: left;
            position: relative;
            min-height: 1px;
            padding-right: 15px;
            padding-left: 15px
        }
    }#notice-cookie-block {
         box-shadow: 0 5px 5px 5px rgba(0, 0, 0, .1)
     }

    #notice-cookie-block .content {
        max-width: 1570px;
        margin: 0 auto;
        text-align: center
    }

    .opc-progress-bar {
        padding-top: 0;
        padding-bottom: 0
    }

    .opc-progress-bar .opc-progress-bar-item {
        width: 33%;
        padding-bottom: 5px
    }

    .opc-progress-bar .opc-progress-bar-item span {
        padding-top: 0
    }

    .opc-progress-bar .opc-progress-bar-item._complete span {
        font-weight: 500
    }

    .product-item-photo::before, .product-item-photo:hover::before {
        border-bottom: none
    }

    .product-item-name a {
        font-weight: 700
    }

    .product-item-name a:hover {
        text-decoration: none;
        color: #85b84b
    }

    .product-item-name a::before {
        border-bottom: none
    }

    .opc-block-summary {
        background: #fff
    }

    .checkout-index-index #checkout {
        max-width: 1440px;
        margin: 0 auto
    }

    .checkout-index-index .opc-sidebar button.action {
        min-height: auto;
        background: 0 0;
        border: none;
        padding: 0;
        margin: 0;
        text-align: right;
        position: absolute;
        right: 0
    }

    .checkout-index-index .opc-sidebar button.action > span {
        display: none
    }

    .checkout-index-index .opc-sidebar button.action:hover {
        background: 0 0;
        border: none;
        color: #85b84b
    }

    .checkout-index-index .opc-sidebar button.action:hover:before {
        color: inherit;
        border: none
    }

    .checkout-index-index .opc-sidebar button.action:after {
        content: "";
        margin-left: 0
    }

    .checkout-index-index .opc-sidebar button.action:before {
        font-family: luma-icons;
        display: inline-block;
        cursor: pointer;
        text-align: center;
        color: inherit;
        border: none;
        transition: .4s;
        width: auto;
        position: relative;
        bottom: auto;
        left: 0
    }

    .checkout-index-index .opc-sidebar button.action.action-edit:before {
        font-size: 1.8rem;
        line-height: 2rem;
        content: '\e601'
    }

    .checkout-index-index .opc-sidebar .shipping-information-title > span {
        font-size: 18px;
        font-weight: 500
    }

    .checkout-index-index .opc-sidebar .opc-block-info {
        background: #fff;
        margin: 0;
        padding: 22px 30px
    }

    .checkout-index-index .opc-sidebar .opc-block-info p + p {
        margin: 0
    }

    .checkout-index-index .billing-address-same-as-shipping-block {
        margin: 30px
    }

    #maincontent #checkout .authentication-wrapper button, .checkout-index-index .checkout-shipping-method .step-title, .checkout-index-index .checkout-shipping-method .table-checkout-shipping-method {
        display: none
    }

    #payment #cardexpiremonth iframe, #payment #cardexpireyear iframe {
        height: 30px
    }

    #payolution_elv_dob_day {
        padding-right: 10px
    }

    form fieldset#customer-email-fieldset .field .note {
        display: none !important
    }

    .checkout-agreements-block {
        margin: 20px 0
    }

    .checkout-info-block {
        margin: 0 0 15px
    }

    .checkout-info-block p + p {
        margin: 0
    }

    .message.global.cookie {
        background: #fff;
        font-size: 15px;
        z-index: 100000
    }

    .message.global.cookie a {
        color: #2f3943
    }

    .message.global.cookie a:hover {
        color: #85b84b
    }

    @media (max-width: 767px) {
        h1 {
            font-size: 3.2rem;
            line-height: 3.2rem
        }

        h6 {
            font-size: 1.8rem;
            line-height: 2rem;
            margin: 16px 0
        }

        .logo {
            float: none
        }

        .logo img {
            width: 80px;
            float: left;
            padding: 10px 0;
            margin-left: 10px
        }

        .page-header .header.content {
            padding-top: 0
        }

        .page-header .header.content .service-wrapper-container {
            position: relative;
            height: 50px;
            background: #f5f5f5;
            z-index: 150
        }

        .page-header .header.content .minicart-wrapper {
            right: 10px;
            top: -5px;
            z-index: inherit
        }

        .nav-toggle {
            right: 17px;
            left: auto;
            top: 63px;
            position: absolute
        }

        .minicart-wrapper .action.showcart .counter.qty {
            top: -7px
        }

        .minicart-wrapper .action.showcart .counterHolder.hasItem .counter.qty {
            background: #85b84b
        }

        .block-static-block.widget .block-container {
            padding: 0 20px
        }

        .navigation {
            background: #fff
        }

        .navigation a::before {
            border-bottom: none
        }

        .navigation .submenuWrapper {
            display: block !important;
            padding: 0
        }

        .navigation a {
            padding: 10px
        }

        .navigation .submenu:not(:first-child) ul {
            padding-left: 0
        }

        .nav-sections-item-title {
            display: none
        }

        .nav-sections-item-content {
            margin-top: 0;
            margin-left: 0;
            padding: 0
        }

        .nav-sections-item-content#store\.quicklinks {
            display: block !important
        }

        .navigation .parent .level-top:after {
            right: 17px
        }

        .nav-sections-items {
            padding-bottom: 20px
        }

        .page-footer .footer.content {
            border-top: none
        }

        .page-footer .footer-wrapper {
            flex-direction: column;
            padding: 50px 0;
            background: #f5f5f5
        }

        .page-footer .footer-wrapper .footer-column {
            padding: 30px 20px;
            text-align: center
        }

        .page-footer .footer-wrapper .footer-column.links::after {
            display: none
        }

        .page-footer .footer-wrapper .footer-column ul li a {
            display: inline-block
        }

        .page-layout-1column a.action, .page-layout-1column button.action {
            width: 100%
        }

        .actions-toolbar > .primary {
            text-align: left
        }

        .checkout-index-index .opc-wrapper {
            width: 100%;
            box-sizing: border-box
        }

        .checkout-index-index .opc-sidebar {
            width: 100%
        }

        .checkout-index-index .page-header .header.content .service-wrapper-container {
            height: auto;
            padding: 0
        }

        .checkout-index-index .minicart-wrapper .action.showcart .counterHolder.hasItem .counter.qty {
            background: #000
        }

        .checkout-index-index .page-header {
            margin-bottom: 0;
            border-bottom: none
        }

        .opc-estimated-wrapper {
            margin: 0
        }

        #opc-shipping_method {
            padding: 0 20px
        }
    }@media (min-width: 768px) and (max-width: 1200px) {
    .opc-progress-bar, .sections.nav-sections {
        padding: 0
    }

    .page-header .header.content {
        padding: 30px 10px 0
    }
}
}

/*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/css/print.css ; media=print *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_PageBuilderIconBox/css/styles.css *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Payolution_Payments/css/payolution.css ; media=screen *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used from: Embedded *//*! CSS Used fontfaces *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/css/styles-m.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/bootstrap/css/bootstrap.min.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/css/custom.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/css/fontawesome5.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_Core/css/mgz_bootstrap.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_PageBuilder/css/styles.css ; media=all *//*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Payone_Core/css/ratepay.css ; media=all */
@media all {
    nav ul {
        list-style: none
    }

    img {
        height: auto;
        max-width: 100%
    }

    i {
        font-style: italic
    }

    h1 {
        font-weight: 300
    }

    h6 {
        font-weight: 700;
        margin: 50px 0 16px
    }

    a, a:visited {
        color: #1979c3;
        text-decoration: none
    }

    a:active {
        color: #85b84b;
        text-decoration: underline
    }

    ol > li, ul > li {
        margin-top: 0;
        margin-bottom: 1rem
    }

    dt {
        margin-bottom: 5px;
        margin-top: 0
    }

    dd {
        margin-bottom: 10px;
        margin-top: 0
    }

    table {
        width: 100%;
        border-spacing: 0;
        max-width: 100%
    }

    table th {
        text-align: left
    }

    table > tbody > tr > td, table > tbody > tr > th {
        vertical-align: top
    }

    table > thead > tr > th {
        vertical-align: bottom
    }

    table > tbody > tr > td, table > tbody > tr > th, table > thead > tr > th {
        padding: 11px 10px
    }

    button {
        background: #eee;
        border: 1px solid #ccc;
        line-height: 1.6rem;
        box-sizing: border-box
    }

    button.disabled, button[disabled] {
        opacity: .5;
        cursor: default;
        pointer-events: none
    }

    input[type=email], input[type=password], input[type=text] {
        background: padding-box #fff;
        border: 2px solid #c2c2c2;
        border-radius: 5px;
        line-height: 1.42857143;
        padding: 0 9px;
        vertical-align: baseline;
        width: 100%;
        box-sizing: border-box
    }

    input[type=checkbox]:disabled, input[type=email]:disabled, input[type=password]:disabled, input[type=radio]:disabled, input[type=text]:disabled, select:disabled {
        opacity: .5
    }

    select {
        background: padding-box #fff;
        font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        line-height: 1.42857143;
        padding: 5px 10px 4px;
        vertical-align: baseline;
        width: 100%;
        box-sizing: border-box
    }

    form {
        -webkit-tap-highlight-color: transparent
    }

    :focus {
        box-shadow: none;
        outline: 0
    }

    input:not([disabled]):focus, select:not([disabled]):focus {
        box-shadow: none
    }

    .footer.content ul, .opc-progress-bar, .opc-wrapper .opc {
        margin: 0;
        padding: 0;
        list-style: none
    }

    .footer.content ul > li, .opc-progress-bar > li, .opc-wrapper .opc > li {
        margin: 0
    }

    .action-auth-toggle, .checkout-agreements-block .action-show {
        line-height: 1.42857143;
        padding: 0;
        color: #1979c3;
        text-decoration: none;
        background: 0 0;
        border: 0;
        display: inline;
        font-weight: 400;
        border-radius: 0
    }

    .action-auth-toggle:visited, .checkout-agreements-block .action-show:visited {
        color: #1979c3;
        text-decoration: none
    }

    .action-auth-toggle:active, .checkout-agreements-block .action-show:active {
        color: #85b84b;
        text-decoration: underline
    }

    .action-auth-toggle:hover, .checkout-agreements-block .action-show:hover {
        text-decoration: underline;
        color: #006bb4
    }

    .action-auth-toggle:active, .action-auth-toggle:focus, .action-auth-toggle:hover, .checkout-agreements-block .action-show:active, .checkout-agreements-block .action-show:focus, .checkout-agreements-block .action-show:hover {
        background: 0 0;
        border: 0
    }

    .action-auth-toggle:active, .action-auth-toggle:not(:focus), .checkout-agreements-block .action-show:active, .checkout-agreements-block .action-show:not(:focus) {
        box-shadow: none
    }

    .block-authentication .action.action-login, .block-minicart .block-content > .actions > .primary .action.primary {
        line-height: 2.2rem;
        padding: 14px 17px;
        font-size: 1.8rem
    }

    .actions-toolbar > .primary .action, .actions-toolbar > .secondary .action {
        width: 100%
    }

    .minicart-items .product-image-wrapper {
        height: auto;
        padding: 0 !important
    }

    .minicart-items .product-image-wrapper .product-image-photo {
        position: static
    }

    .block {
        margin-bottom: 40px
    }

    .product-item-name > a {
        color: #333;
        text-decoration: none
    }

    .product-item-name > a:active, .product-item-name > a:hover, .product-item-name > a:visited {
        color: #333;
        text-decoration: underline
    }

    .action.skip:not(:focus), .checkout-payment-method .payments .legend {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px
    }

    .minicart-items .product.options .toggle > span, .shipping-policy-block.field-tooltip .field-tooltip-action span {
        clip: auto;
        height: auto;
        margin: 0;
        overflow: visible;
        position: static;
        width: auto
    }

    .checkout-container:after, .checkout-container:before, .header.content:after, .header.content:before, .opc-estimated-wrapper:after, .opc-estimated-wrapper:before, .opc-wrapper .field.addresses:after, .opc-wrapper .field.addresses:before, .row:after, .row:before {
        content: '';
        display: table
    }

    .checkout-container:after, .header.content:after, .minicart-items .product-item > .product:after, .opc-estimated-wrapper:after, .opc-wrapper .field.addresses:after, .row:after {
        clear: both
    }

    .columns .column.main, .field .control._with-tooltip, .opc-block-summary, .product-item, .shipping-policy-block.field-tooltip .field-tooltip-content {
        box-sizing: border-box
    }

    .minicart-items .product .toggle {
        border-top: 1px solid #ccc;
        cursor: pointer;
        margin-bottom: 0;
        position: relative;
        display: block;
        text-decoration: none;
        font-size: 14px;
        font-weight: 600
    }

    .minicart-items .product .toggle:after {
        right: 20px;
        top: 10px;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 12px;
        line-height: 12px;
        content: '\e622';
        font-family: luma-icons;
        vertical-align: middle;
        display: inline-block;
        font-weight: 400;
        overflow: hidden;
        speak: none;
        text-align: center
    }

    .minicart-items .product .toggle > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px
    }

    .block-minicart .subtotal .label:after, .minicart-items .details-qty .label:after {
        content: ': '
    }

    .opc-block-summary .table-totals .table-caption {
        display: none
    }

    .field .control._with-tooltip {
        position: relative
    }

    .shipping-policy-block.field-tooltip .field-tooltip-content {
        right: -10px;
        left: auto
    }

    .shipping-policy-block.field-tooltip .field-tooltip-content:after, .shipping-policy-block.field-tooltip .field-tooltip-content:before {
        border: 10px solid transparent;
        height: 0;
        width: 0;
        margin-top: -21px;
        right: 10px;
        left: auto;
        top: 0
    }

    .shipping-policy-block.field-tooltip .field-tooltip-content:before {
        border-bottom-color: #666
    }

    .shipping-policy-block.field-tooltip .field-tooltip-content:after {
        border-bottom-color: #f5f5f5;
        top: 1px
    }

    .opc-wrapper .step-title {
        font-weight: 500;
        font-size: 20px
    }

    .opc-block-summary .table-totals tbody .mark {
        border: 0;
        font-weight: 400;
        padding: 6px 0
    }

    .opc-block-summary .table-totals tbody .amount {
        border: 0;
        font-weight: 400;
        padding: 6px 0 6px 14px;
        text-align: right;
        white-space: nowrap
    }

    .opc-block-summary .table-totals .grand td, .opc-block-summary .table-totals .grand th {
        padding: 0
    }

    .opc-block-summary .table-totals .grand strong {
        display: inline-block;
        font-weight: 600;
        padding: 3px 0 0
    }

    .opc-block-summary .table-totals .grand .mark {
        font-size: 1.8rem;
        height: max-content
    }

    .opc-block-summary .table-totals .grand .amount {
        font-size: 1.8rem
    }

    .opc-block-summary .table-totals .grand .amount, .opc-block-summary .table-totals .grand .mark {
        padding: 10px 10px 14px 0;
        border-top: 1px solid #ccc
    }

    .opc-block-summary .table-totals .totals-tax-details {
        border-bottom: 1px solid #ccc
    }

    html {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        font-size-adjust: 100%;
        background: #fff;
        color: initial
    }

    body {
        padding: 0;
        color: #636d70;
        font: 400 17px/26px futura-pt
    }

    strong {
        font-weight: 500
    }

    .block .title {
        margin: 0 0 10px
    }

    a:visited, h1, h6 {
        color: #2f3943
    }

    a {
        color: #2f3943;
        transition: color .4s;
        position: relative;
        text-decoration: none
    }

    a:focus, a:hover {
        color: #85b84b;
        text-decoration: none
    }

    a[href]:after {
        content: normal
    }

    hr {
        margin: 20px 0;
        background: #e5e5e5
    }

    .modal-custom button.action-close, .modal-popup button.action-close {
        height: auto
    }

    .modal-custom button.action-close::after, .modal-popup button.action-close::after {
        display: none
    }

    .page-wrapper {
        transition: .4s
    }

    div[role=tablist] div[data-role=title]:after {
        font-family: luma-icons;
        content: "\e622";
        margin-left: 10px;
        vertical-align: middle;
        color: #2f3943
    }

    .items {
        margin: 0;
        padding: 0;
        list-style: none
    }

    @media (max-width: 767px) {
        h1 {
            font-size: 3.2rem;
            line-height: 3.2rem
        }

        h6 {
            font-size: 1.8rem;
            line-height: 2rem;
            margin: 16px 0
        }
    }.columns {
         -webkit-flex-wrap: wrap;
         flex-wrap: wrap;
         box-sizing: border-box
     }

    .columns:after {
        clear: both;
        content: ' ';
        display: block;
        height: 0;
        overflow: hidden;
        visibility: hidden
    }

    .columns .column.main {
        -webkit-flex-basis: 100%;
        flex-basis: 100%;
        -webkit-flex-grow: 1;
        flex-grow: 1;
        -ms-flex-order: 1;
        -webkit-order: 1;
        order: 1;
        width: 100%
    }

    table > caption {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px
    }

    .nav-sections {
        background: #f0f0f0
    }

    .nav-toggle {
        text-decoration: none;
        cursor: pointer;
        display: block;
        left: 15px;
        position: absolute;
        top: 15px;
        z-index: 14
    }

    .nav-toggle > span {
        display: block;
        line-height: 1;
        font-size: 12px
    }

    .loader {
        width: 100%;
        height: 100%;
        position: absolute;
        margin: auto;
        z-index: 100;
        background: rgba(255, 255, 255, .5)
    }

    .loader img {
        bottom: 0;
        left: 0;
        margin: auto;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 100
    }

    .loading-mask {
        bottom: 0;
        left: 0;
        margin: auto;
        position: fixed;
        right: 0;
        top: 0;
        z-index: 100;
        background: rgba(255, 255, 255, .5)
    }

    .loading-mask .loader > img {
        bottom: 0;
        left: 0;
        margin: auto;
        position: fixed;
        right: 0;
        top: 0;
        z-index: 100
    }

    .loading-mask .loader > p {
        display: none
    }

    body > .loading-mask {
        z-index: 9999
    }

    .fieldset {
        border: 0;
        margin: 0 0 40px;
        padding: 0;
        letter-spacing: -.31em
    }

    .fieldset > * {
        letter-spacing: normal
    }

    .fieldset > .legend {
        margin: 0 0 20px;
        padding: 0 0 10px;
        width: 100%;
        box-sizing: border-box;
        float: left;
        font-weight: 300;
        line-height: 1.2;
        font-size: 1.8rem
    }

    .fieldset > .legend + br {
        clear: both;
        display: block;
        height: 0;
        overflow: hidden;
        visibility: hidden
    }

    .fieldset:last-child {
        margin-bottom: 0
    }

    .fieldset > .field {
        margin: 0 0 20px
    }

    .fieldset > .field > .label {
        margin: 0 0 8px;
        display: inline-block;
        font-weight: 400
    }

    .fieldset > .field:last-child {
        margin-bottom: 0
    }

    .fieldset > .field .fields.group:after, .fieldset > .field .fields.group:before {
        content: '';
        display: table
    }

    .fieldset > .field .fields.group:after {
        clear: both
    }

    .fieldset > .field .fields.group .field {
        box-sizing: border-box;
        float: left
    }

    .fieldset > .field .fields.group.group-2 .field {
        width: 50% !important
    }

    .fieldset > .field._required > .label:after, .fieldset > .field.required :not(.checkout-agreement) > .label:after, .fieldset > .field.required > .label:after {
        content: '*';
        color: #e02b27;
        font-size: 1.2rem;
        margin: 0 0 0 5px
    }

    .fieldset > .field .note {
        font-size: 1.2rem;
        margin: 3px 0 0;
        padding: 0;
        display: inline-block;
        text-decoration: none
    }

    .fieldset > .field .note:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 24px;
        line-height: 12px;
        font-family: luma-icons;
        vertical-align: middle;
        display: inline-block;
        font-weight: 400;
        overflow: hidden;
        speak: none;
        text-align: center
    }

    .fieldset > .field .label {
        font-weight: 400
    }

    .legend > span {
        margin-right: 5px
    }

    fieldset.field {
        border: 0;
        padding: 0
    }

    select {
        border: 1px solid #ccc;
        padding-right: 25px;
        text-indent: .01em;
        text-overflow: '';
        border-radius: 5px;
        -webkit-appearance: none;
        -moz-appearance: none;
        -ms-appearance: none;
        appearance: none
    }

    input.input-text, input[type=email], input[type=password], input[type=text], select {
        font-size: 1.8rem;
        height: 35px;
        transition: .4s
    }

    .field .control._with-tooltip input {
        width: 100%;
        margin-right: 0
    }

    .field-tooltip .field-tooltip-action::before {
        color: #333
    }

    [type=checkbox]:checked, [type=checkbox]:not(:checked) {
        position: absolute;
        left: -9999px
    }

    [type=checkbox]:checked + label, [type=checkbox]:not(:checked) + label {
        position: relative;
        padding-left: 1.95em;
        cursor: pointer;
        display: block
    }

    [type=checkbox]:checked + label:before, [type=checkbox]:not(:checked) + label:before {
        content: '\e901';
        font-family: luma-icons;
        font-size: 2.8rem;
        position: absolute;
        left: 0;
        top: 0;
        width: 1.25em;
        height: 1.25em
    }

    [type=checkbox]:checked + label:after, [type=checkbox]:not(:checked) + label:after {
        content: '\f00c';
        font-family: 'Font Awesome 5 Free';
        position: absolute;
        left: 8px;
        font-weight: 600;
        top: 0;
        font-size: 1.2rem;
        color: #000;
        transition: .2s
    }

    [type=checkbox]:not(:checked) + label:after {
        opacity: 0
    }

    [type=checkbox]:checked + label:after {
        opacity: 1
    }

    input[type=radio] {
        border: 0;
        clip: rect(0 0 0 0);
        height: 1px;
        overflow: hidden;
        position: absolute;
        width: 1px
    }

    .checkout-index-index .checkout-container .payment-method .payment-method-title .label:before {
        content: '\e903';
        font-family: luma-icons;
        display: none;
        letter-spacing: 10px;
        color: #535353;
        font-size: 2.3rem;
        vertical-align: text-top
    }

    body:not(._keyfocus) button:focus {
        box-shadow: none
    }

    .action.primary, .button, button, button.action, button.action-auth-toggle {
        font-family: futura-pt;
        min-height: 45px;
        width: auto;
        display: inline-block;
        cursor: pointer;
        text-align: center;
        border: none;
        padding: 8px 20px;
        margin: 5px 10px 5px 0;
        text-decoration: none;
        color: #fff;
        background-color: #677752;
        font-weight: 400;
        font-size: 1.8rem;
        transition: .4s;
        box-shadow: none;
        border-radius: 5px;
        vertical-align: middle;
        box-sizing: border-box
    }

    .action.primary::before, .button::before, button.action-auth-toggle::before, button.action::before, button::before {
        font-family: luma-icons;
        display: inline-block;
        cursor: pointer;
        text-align: center;
        color: inherit;
        border: none;
        transition: .4s;
        width: auto;
        position: inherit;
        bottom: auto;
        left: auto
    }

    .action.primary::before:hover, .button::before:hover, button.action-auth-toggle::before:hover, button.action::before:hover, button::before:hover {
        border: none
    }

    .action.primary.checkout, button.action.checkout, button.checkout {
        color: #fff;
        background-color: #677752;
        border-color: #677752
    }

    .action.primary.checkout::before, button.action.checkout::before, button.checkout::before {
        border-bottom: none
    }

    .action.primary.checkout:hover, button.action.checkout:hover, button.checkout:hover {
        opacity: .6
    }

    .actions-toolbar > .primary, .actions-toolbar > .secondary {
        margin-bottom: 10px;
        text-align: center
    }

    .actions-toolbar > .primary .action, .actions-toolbar > .secondary .action {
        margin-bottom: 10px
    }

    .actions-toolbar > .primary .action:last-child, .actions-toolbar > .primary:last-child, .actions-toolbar > .secondary .action:last-child, .actions-toolbar > .secondary:last-child {
        margin-bottom: 0
    }

    .price-including-tax {
        font-size: 1.8rem;
        line-height: 1
    }

    .price-including-tax .price {
        font-weight: 700
    }

    .modal-popup, .modal-slide {
        bottom: 0;
        min-width: 0;
        position: fixed;
        right: 0;
        top: 0;
        visibility: hidden;
        opacity: 0;
        -webkit-transition: visibility 0s .3s, opacity .3s;
        transition: visibility 0s .3s, opacity .3s
    }

    .modal-popup .modal-inner-wrap, .modal-slide .modal-inner-wrap {
        background-color: #fff;
        opacity: 1;
        pointer-events: auto
    }

    .modal-slide {
        left: 0;
        z-index: 900
    }

    .modal-slide .modal-inner-wrap {
        height: 100%;
        overflow-y: auto;
        position: static;
        -webkit-transform: translateX(100%);
        transform: translateX(100%);
        -webkit-transition: -webkit-transform .3s ease-in-out;
        transition: transform .3s ease-in-out;
        width: auto
    }

    .modal-slide._inner-scroll .modal-inner-wrap {
        overflow-y: visible;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column
    }

    .modal-slide._inner-scroll .modal-footer, .modal-slide._inner-scroll .modal-header {
        -webkit-flex-grow: 0;
        flex-grow: 0;
        -webkit-flex-shrink: 0;
        flex-shrink: 0;
        border: none
    }

    .modal-slide._inner-scroll .modal-content {
        overflow-y: auto
    }

    .modal-slide._inner-scroll .modal-footer {
        margin-top: auto
    }

    .modal-slide .modal-content, .modal-slide .modal-footer, .modal-slide .modal-header {
        padding: 0 2.6rem 2.6rem
    }

    .modal-slide .modal-header {
        padding-bottom: 2.1rem;
        padding-top: 2.1rem
    }

    .modal-popup {
        z-index: 900;
        left: 0;
        overflow-y: auto
    }

    .modal-popup .modal-inner-wrap {
        margin: 5rem auto;
        width: 75%;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        box-sizing: border-box;
        height: auto;
        left: 0;
        position: absolute;
        right: 0;
        -webkit-transform: translateY(-200%);
        transform: translateY(-200%);
        -webkit-transition: -webkit-transform .2s;
        transition: transform .2s
    }

    .modal-popup._inner-scroll {
        overflow-y: visible
    }

    .modal-popup._inner-scroll .modal-inner-wrap {
        max-height: 90%;
        border-radius: 5px;
        max-width: 1570px
    }

    .modal-popup._inner-scroll .modal-content {
        overflow-y: auto;
        border: none;
        box-shadow: none
    }

    .modal-popup .modal-content, .modal-popup .modal-footer, .modal-popup .modal-header {
        padding-left: 3rem;
        padding-right: 3rem;
        box-shadow: none;
        border: none
    }

    .modal-header .action-close span {
        font-size: 1.25rem;
        color: #fff
    }

    .modal-popup .modal-footer, .modal-popup .modal-header {
        -webkit-flex-grow: 0;
        flex-grow: 0;
        -webkit-flex-shrink: 0;
        flex-shrink: 0
    }

    .modal-popup .modal-header {
        padding-bottom: 1.2rem;
        padding-top: 3rem
    }

    .modal-popup .modal-footer {
        margin-top: auto;
        padding-bottom: 3rem;
        padding-top: 3rem
    }

    .modal-custom .action-close, .modal-popup .action-close, .modal-slide .action-close {
        background: 0 0;
        -moz-box-sizing: content-box;
        border: 0;
        box-shadow: none;
        line-height: inherit;
        margin: 0;
        padding: 0 30px 0 0;
        text-shadow: none;
        font-weight: 400;
        display: inline-block;
        text-decoration: none;
        position: absolute;
        right: 0;
        top: 0
    }

    .modal-custom .action-close:active, .modal-custom .action-close:focus, .modal-custom .action-close:hover, .modal-popup .action-close:active, .modal-popup .action-close:focus, .modal-popup .action-close:hover, .modal-slide .action-close:active, .modal-slide .action-close:focus, .modal-slide .action-close:hover {
        background: 0 0;
        border: none
    }

    .modal-custom .action-close > span, .modal-popup .action-close > span, .modal-slide .action-close > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px
    }

    .modal-custom .action-close:before, .modal-popup .action-close:before, .modal-slide .action-close:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 14px;
        line-height: 14px;
        color: inherit;
        content: '\e616';
        font-family: luma-icons;
        margin: 0;
        vertical-align: top;
        display: inline-block;
        font-weight: 400;
        overflow: hidden;
        speak: none;
        text-align: center
    }

    .modal-custom .action-close:active:before, .modal-custom .action-close:hover:before, .modal-popup .action-close:active:before, .modal-popup .action-close:hover:before, .modal-slide .action-close:active:before, .modal-slide .action-close:hover:before {
        color: inherit
    }

    .modal-popup .action-close, .modal-slide .action-close {
        padding: 0 30px 0 0
    }

    .modal-popup .action-close::before, .modal-slide .action-close::before {
        right: 30px;
        top: 50%;
        margin-top: -7px;
        color: inherit
    }

    .modal-popup .action-close:hover, .modal-slide .action-close:hover {
        color: #2f3943
    }

    .modal-popup .action-close > span, .modal-slide .action-close > span {
        clip: unset;
        height: auto;
        width: auto;
        line-height: 1.6rem;
        padding-right: 25px;
        position: relative;
        margin: 0;
        overflow: visible
    }

    .amazon-button-container {
        display: table;
        margin: 0 0 22px
    }

    .amazon-button-container .field-tooltip {
        display: none
    }

    @media all and (max-width: 768px) {
        .amazon-button-container {
            width: 100%
        }
    }.field-error {
         color: #e02b27;
         font-size: 1.2rem
     }

    .product-item {
        vertical-align: top
    }

    .product-item-name {
        font-weight: 400;
        -moz-hyphens: auto;
        -ms-hyphens: auto;
        -webkit-hyphens: auto;
        display: block;
        hyphens: auto;
        margin: 5px 0;
        word-wrap: break-word
    }

    .product-item .old-price {
        margin: 5px 0
    }

    .column.main .product-item {
        padding-left: 20px
    }

    .price-container .price {
        font-size: 1.4rem
    }

    .old-price {
        color: #7d7d7d
    }

    .product-image-container {
        display: inline-block;
        max-width: 100%
    }

    .product-image-wrapper {
        display: block;
        overflow: hidden;
        position: relative;
        z-index: 1
    }

    .product-image-photo {
        bottom: 0;
        display: block;
        height: auto;
        left: 0;
        margin: auto;
        max-width: 100%;
        right: 0;
        top: 0
    }

    .price-including-tax {
        display: block;
        white-space: nowrap
    }

    .block-minicart .items-total {
        float: left;
        margin: 0 10px;
        display: none
    }

    .block-minicart .items-total .count {
        font-weight: 700
    }

    .block-minicart .subtotal {
        margin: 10px 0;
        text-align: right
    }

    .block-minicart .subtotal .amount, .block-minicart .subtotal span.label {
        display: inline-block
    }

    .block-minicart .amount .price-wrapper:first-child .price {
        font-size: 18px;
        font-weight: 700
    }

    .block-minicart .subtitle {
        display: none
    }

    .block-minicart .block-content > .actions {
        margin-top: 15px
    }

    .block-minicart .block-content > .actions > .primary {
        margin: 0 10px 15px
    }

    .block-minicart .block-content > .actions > .primary .action.primary {
        display: block;
        width: 100%
    }

    .minicart-wrapper {
        display: inline-block;
        position: relative;
        float: right
    }

    .minicart-wrapper:after, .minicart-wrapper:before {
        content: '';
        display: table
    }

    .minicart-wrapper:after {
        clear: both
    }

    .minicart-wrapper .action.showcart {
        cursor: pointer;
        display: inline-block;
        text-decoration: none
    }

    .minicart-wrapper .action.showcart:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 22px;
        line-height: 28px;
        color: #fff;
        content: '\e611';
        font-family: luma-icons;
        margin: 0;
        vertical-align: top;
        display: inline-block;
        font-weight: 400;
        overflow: hidden;
        speak: none;
        text-align: center
    }

    .minicart-wrapper .action.showcart:hover:before {
        color: #2f3943
    }

    .minicart-wrapper .action.showcart:active:before {
        color: #fff
    }

    .minicart-wrapper .block-minicart {
        margin: 20px 0 0;
        list-style: none;
        background: #fff;
        border: 1px solid #bbb;
        z-index: 1000;
        box-sizing: border-box;
        display: none;
        position: absolute;
        top: auto;
        box-shadow: 0 3px 3px rgba(0, 0, 0, .15);
        padding: 20px;
        right: 0;
        width: 360px
    }

    .minicart-wrapper .block-minicart li {
        margin: 0;
        position: relative
    }

    .minicart-wrapper .block-minicart li:hover {
        cursor: pointer
    }

    .minicart-wrapper .block-minicart:after, .minicart-wrapper .block-minicart:before {
        content: '';
        display: block;
        height: 0;
        position: absolute;
        width: 0
    }

    .minicart-wrapper .block-minicart:before {
        border: 6px solid;
        border-color: transparent transparent #fff;
        z-index: 99;
        top: -12px
    }

    .minicart-wrapper .block-minicart:after {
        border: 7px solid;
        border-color: transparent transparent #bbb;
        z-index: 98;
        top: -14px;
        left: auto;
        right: 31px
    }

    .minicart-wrapper .block-minicart .block-title {
        display: none
    }

    .minicart-wrapper .block-minicart:before {
        left: auto;
        right: 32px
    }

    .minicart-wrapper .product .actions a::before {
        width: 20px;
        height: 20px
    }

    .minicart-wrapper .product .actions > .secondary {
        display: inline
    }

    .minicart-wrapper .action.close {
        height: 40px;
        position: absolute;
        right: 0;
        top: 0;
        width: 40px;
        background: 0 0;
        -moz-box-sizing: content-box;
        border: 0;
        box-shadow: none;
        line-height: inherit;
        margin: 0;
        padding: 0;
        text-shadow: none;
        font-weight: 400;
        text-decoration: none;
        display: none
    }

    .minicart-wrapper .action.close:active, .minicart-wrapper .action.close:focus, .minicart-wrapper .action.close:hover {
        background: 0 0;
        border: none
    }

    .minicart-wrapper .action.close > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px
    }

    .minicart-wrapper .action.close:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 16px;
        line-height: 16px;
        color: #fff;
        content: '\e616';
        font-family: luma-icons;
        margin: 0;
        vertical-align: top;
        display: inline-block;
        font-weight: 400;
        overflow: hidden;
        speak: none;
        text-align: center
    }

    .minicart-wrapper .action.close:active:before, .minicart-wrapper .action.close:hover:before {
        color: inherit
    }

    .minicart-wrapper .action.showcart .counter.qty {
        color: #fff;
        line-height: 24px;
        margin: 3px 0 0;
        overflow: hidden;
        padding: 0 3px;
        text-align: center;
        white-space: normal
    }

    .minicart-items-wrapper {
        border-bottom: 1px solid #ccc;
        margin: 0 -20px;
        overflow-x: auto;
        padding: 5px 15px 15px
    }

    .minicart-items {
        margin: 0;
        padding: 0;
        list-style: none
    }

    .minicart-items .product-item {
        padding: 20px 0
    }

    .minicart-items .product-item:not(:first-child) {
        border-top: 1px solid #ccc
    }

    .minicart-items .product-item:first-child {
        padding-top: 0
    }

    .minicart-items .product-item-pricing .label {
        display: inline-block;
        width: 4.5rem
    }

    .minicart-items .product-item-name {
        font-weight: 500;
        margin: 0;
        line-height: normal
    }

    .minicart-items .product-item-name a {
        color: #636d70
    }

    .minicart-items .product-item-details .price {
        font-weight: 700
    }

    .minicart-items .product-item-details .price-including-tax {
        margin: 5px 0
    }

    .minicart-items .product-item-details .details-qty {
        margin: 0
    }

    .minicart-items .product-item-details > .actions {
        position: absolute;
        right: 5px;
        top: 25px
    }

    .minicart-items .product > .product-image-container, .minicart-items .product > .product-item-photo {
        float: left
    }

    .minicart-items .product .toggle {
        border: 0;
        padding: 0 40px 5px 0
    }

    .minicart-items .product .toggle:after {
        color: #8f8f8f;
        margin: 0 0 0 5px;
        position: static
    }

    .minicart-items .subtitle {
        display: none
    }

    .minicart-items .action.delete {
        display: inline-block;
        text-decoration: none
    }

    .minicart-items .action.delete > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px
    }

    .minicart-items .action.delete:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 18px;
        line-height: 20px;
        content: '\e601';
        font-family: luma-icons;
        vertical-align: middle;
        display: inline-block;
        font-weight: 400;
        overflow: hidden;
        speak: none;
        text-align: center;
        content: '\e900'
    }

    .checkout-container, .opc-wrapper {
        margin: 0 0 20px
    }

    .opc-wrapper .step-title {
        border-bottom: 1px solid #ccc;
        margin-bottom: 20px;
        margin-top: 20px
    }

    .opc-wrapper .step-content {
        margin: 0
    }

    .checkout-index-index .nav-sections, .checkout-index-index .nav-toggle {
        display: none
    }

    .checkout-index-index .logo {
        margin-left: 0
    }

    .opc-estimated-wrapper {
        border-bottom: 1px solid #ccc;
        margin: 0 0 15px;
        padding: 18px 15px
    }

    .opc-estimated-wrapper .estimated-block {
        font-size: 22px;
        font-weight: 700;
        float: left
    }

    .opc-estimated-wrapper .estimated-block .estimated-label {
        display: block;
        margin: 0 0 5px
    }

    .opc-estimated-wrapper .minicart-wrapper .action.showcart:before, .opc-estimated-wrapper .minicart-wrapper .action.showcart:hover:before {
        color: #636d70
    }

    .opc-progress-bar {
        margin: 0 0 20px;
        counter-reset: i;
        font-size: 0
    }

    .opc-progress-bar-item {
        margin: 0 0 10px;
        display: inline-block;
        position: relative;
        text-align: center;
        vertical-align: top
    }

    .opc-progress-bar-item > span {
        display: inline-block;
        padding-top: 45px;
        width: 100%;
        word-wrap: break-word;
        font-weight: 500;
        font-size: 1.8rem
    }

    .opc-progress-bar-item > span:after, .opc-progress-bar-item > span:before {
        background: #e4e4e4;
        height: 38px;
        margin-left: -19px;
        width: 38px;
        border-radius: 50%;
        content: '';
        left: 50%;
        position: absolute;
        top: 0
    }

    .opc-progress-bar-item > span:after {
        background: #fff;
        height: 26px;
        margin-left: -13px;
        top: 6px;
        width: 26px;
        content: counter(i);
        counter-increment: i;
        color: #333;
        font-weight: 600;
        font-size: 1.8rem
    }

    .opc-progress-bar-item._active:before {
        background: #677752
    }

    .opc-progress-bar-item._active > span {
        color: #636d70
    }

    .opc-progress-bar-item._active > span:before {
        background: #677752
    }

    .opc-progress-bar-item._active > span:after {
        content: '\e610';
        font-family: luma-icons;
        line-height: 1;
        font-size: 2.8rem
    }

    .opc-progress-bar-item._complete {
        cursor: pointer
    }

    .opc-progress-bar-item._complete > span {
        color: #636d70
    }

    .opc-progress-bar-item._complete > span:after {
        content: '\e610';
        font-family: luma-icons;
        line-height: 1;
        font-size: 2.8rem
    }

    .field._error .control input {
        border-color: #ed8380
    }

    .opc-wrapper .fieldset > .field > .label {
        font-weight: 400
    }

    .field-tooltip {
        cursor: pointer;
        position: absolute;
        right: 0;
        top: 1px
    }

    .field-tooltip .field-tooltip-action {
        display: inline-block;
        text-decoration: none
    }

    .field-tooltip .field-tooltip-action > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px
    }

    .checkout-index-index .field input:after {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 21px;
        line-height: inherit;
        color: #bbb;
        content: "\f00c";
        font-family: 'Font Awesome 5 Free';
        vertical-align: middle;
        display: inline-block;
        font-weight: 400;
        overflow: hidden;
        speak: none;
        text-align: center
    }

    .field-tooltip .field-tooltip-action:hover:before {
        color: #333
    }

    .field-tooltip .field-tooltip-content {
        background: #f5f5f5;
        border: 1px solid #999;
        border-radius: 1px;
        font-size: 14px;
        padding: 12px;
        width: 270px;
        display: none;
        left: 38px;
        position: absolute;
        text-transform: none;
        top: -9px;
        word-wrap: break-word;
        z-index: 2
    }

    .field-tooltip .field-tooltip-content:after, .field-tooltip .field-tooltip-content:before {
        border: 10px solid transparent;
        height: 0;
        width: 0;
        border-right-color: #f5f5f5;
        left: -21px;
        top: 12px;
        content: '';
        display: block;
        position: absolute;
        z-index: 3
    }

    .field-tooltip .field-tooltip-content:before {
        border-right-color: #666
    }

    .field-tooltip .field-tooltip-content:after {
        border-right-color: #f5f5f5;
        width: 1px;
        z-index: 4
    }

    .opc-wrapper .form-login, .opc-wrapper .form-shipping-address {
        margin-bottom: 20px
    }

    .opc-wrapper .form-login .fieldset .note {
        font-size: 14px;
        margin-top: 10px
    }

    .checkout-shipping-method .step-title {
        margin-bottom: 0
    }

    .table-checkout-shipping-method thead th {
        display: none
    }

    .table-checkout-shipping-method tbody td {
        border-top: 1px solid #ccc;
        padding-bottom: 20px;
        padding-top: 20px
    }

    .table-checkout-shipping-method tbody td:first-child {
        padding-left: 0;
        padding-right: 0;
        width: 20px
    }

    .table-checkout-shipping-method tbody tr:first-child td {
        border-top: none
    }

    .checkout-shipping-method {
        position: relative
    }

    .shipping-policy-block.field-tooltip {
        top: 12px
    }

    .shipping-policy-block.field-tooltip .field-tooltip-action {
        color: #1979c3;
        cursor: pointer
    }

    .shipping-policy-block.field-tooltip .field-tooltip-action:before {
        display: none
    }

    .shipping-policy-block.field-tooltip .field-tooltip-content {
        width: 420px;
        top: 30px
    }

    .opc-block-summary {
        padding: 22px 30px;
        margin: 0
    }

    .opc-block-summary .table-totals .totals-tax-details {
        display: table-row
    }

    .opc-block-summary .items-in-cart > .title {
        padding: 8px 40px 8px 0;
        cursor: pointer;
        text-decoration: none;
        margin-bottom: 0;
        position: relative
    }

    .opc-block-summary .items-in-cart > .title:after {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 14px;
        line-height: 20px;
        color: inherit;
        content: '\e622';
        font-family: luma-icons;
        margin: 3px 0 0;
        vertical-align: middle;
        display: inline-block;
        font-weight: 400;
        overflow: hidden;
        speak: none;
        text-align: center;
        position: absolute;
        right: 0;
        top: 10px
    }

    .opc-block-summary .items-in-cart > .title strong {
        font-size: 18px;
        font-weight: 500;
        margin: 0
    }

    .opc-block-summary .items-in-cart .product {
        display: flex;
        align-items: flex-start;
        flex-direction: row;
        flex-wrap: nowrap;
        gap: .5em
    }

    .opc-block-summary .items-in-cart .product .product-item-details {
        flex-grow: 1
    }

    .opc-block-summary .minicart-items-wrapper {
        margin: 0 -15px 0 0;
        max-height: 415px;
        padding: 15px 15px 0 0;
        border: 0
    }

    .column.main .opc-block-summary .product-item {
        margin: 0;
        padding-left: 0
    }

    .opc-block-summary .product-item .product-item-inner {
        display: flex;
        justify-content: space-between
    }

    .opc-block-summary .product-item .product-item-name-block {
        display: table-cell;
        padding-right: 5px;
        text-align: left;
        font-size: 1.7rem
    }

    .opc-block-summary .product-item .subtotal {
        display: table-cell;
        text-align: right;
        vertical-align: bottom
    }

    .opc-block-summary .data.table.table-totals {
        margin-bottom: 20px
    }

    .authentication-dropdown {
        box-sizing: border-box
    }

    .authentication-dropdown .modal-inner-wrap {
        padding: 25px
    }

    .authentication-wrapper {
        float: right;
        margin-top: -60px;
        max-width: 50%;
        position: relative;
        z-index: 1
    }

    .block-authentication .block-title {
        font-size: 1.8rem;
        border-bottom: 0;
        margin-bottom: 25px
    }

    .block-authentication .block-title strong {
        font-weight: 300
    }

    .block-authentication .field .label {
        font-weight: 400
    }

    .block-authentication .actions-toolbar {
        margin-bottom: 5px
    }

    .block-authentication .actions-toolbar > .secondary {
        padding-top: 25px;
        text-align: left
    }

    .block-authentication .block[class] {
        margin: 0
    }

    .block-authentication .block[class] .field .control, .block-authentication .block[class] .field .label {
        float: none;
        width: auto
    }

    .checkout-payment-method .payment-method._active .payment-method-content {
        display: flex;
        align-items: flex-end;
        flex-direction: column;
        gap: 2em
    }

    .checkout-payment-method .payment-method-title {
        padding: 20px 0;
        margin: 0
    }

    .checkout-payment-method .payment-method-content {
        display: none;
        padding: 0 0 10px
    }

    .checkout-payment-method .payment-method-content .fieldset:not(:last-child) {
        margin: 0 0 20px
    }

    .checkout-payment-method .ccard .fields > .year {
        padding-left: 5px
    }

    .checkout-payment-method .ccard .number .input-text {
        width: 225px
    }

    .checkout-payment-method .ccard > .field.cvv > .control {
        padding-right: 20px;
        width: auto
    }

    .checkout-payment-method .ccard.fieldset > .field .fields.group.group-2 .field {
        width: auto !important
    }

    .checkout-agreements-block .checkout-agreements {
        margin-bottom: 20px
    }

    .checkout-agreements-block .action-show {
        vertical-align: baseline;
        margin: 0;
        text-align: left
    }

    .column.main .block:last-child {
        margin-bottom: 0
    }

    .block .title {
        margin-bottom: 10px
    }

    .block .title strong {
        font-weight: 700;
        line-height: 1.1;
        font-size: 1.4rem;
        margin-top: 2rem;
        margin-bottom: 2rem
    }

    body {
        background-color: #fff;
        margin: 0
    }

    .page-header {
        border-bottom: 1px solid #ccc;
        margin-bottom: 20px
    }

    .page-header .panel.wrapper {
        color: #fff;
        margin: 0
    }

    .header.content {
        padding-top: 10px;
        position: relative
    }

    .logo {
        float: left;
        margin: 0 0 10px 40px;
        position: relative
    }

    .logo img {
        display: block
    }

    .action.skip:focus {
        background: #f0f0f0;
        padding: 10px;
        box-sizing: border-box;
        left: 0;
        position: absolute;
        text-align: center;
        top: 0;
        width: 100%;
        z-index: 15
    }

    .message.global p {
        margin: 0
    }

    .message.global.cookie {
        margin: 0;
        padding: 12px 20px 12px 25px;
        display: block;
        border-color: #d6ca8e;
        color: #333;
        bottom: 0;
        left: 0;
        position: fixed;
        right: 0
    }

    .message.global.cookie a:active {
        color: #677752
    }

    .message.global.cookie .actions {
        margin-top: 10px
    }

    .page-footer {
        margin-top: 25px
    }

    .footer.content {
        border-top: 1px solid #cecece
    }

    .footer.content .links a {
        display: block;
        color: #575757;
        text-decoration: none;
        color: inherit;
        font-size: 16px;
        margin: 0;
        padding: 0
    }

    .footer.content .links a:visited {
        color: #575757;
        text-decoration: none
    }

    .footer.content .links a:active, .footer.content .links a:hover {
        color: #333;
        text-decoration: underline
    }

    .widget {
        clear: both
    }

    .page-footer .widget.block {
        margin: 0
    }

    .block-category-link.widget {
        display: block;
        margin-bottom: 20px
    }

    .links .block-cms-link.widget {
        margin-bottom: 0
    }

    @media only screen and (max-width: 767px) {
        .field-tooltip .field-tooltip-content {
            right: -10px;
            top: 40px;
            left: auto
        }

        .field-tooltip .field-tooltip-content:after, .field-tooltip .field-tooltip-content:before {
            border: 10px solid transparent;
            height: 0;
            width: 0;
            margin-top: -21px;
            right: 10px;
            left: auto;
            top: 0
        }

        .field-tooltip .field-tooltip-content:before {
            border-bottom-color: #666
        }

        .field-tooltip .field-tooltip-content:after {
            border-bottom-color: #f5f5f5;
            top: 1px
        }

        .footer.content, .header.content, .navigation, .page-header .header.panel, .page-main, .page-wrapper > .page-bottom {
            padding-left: 15px;
            padding-right: 15px
        }

        .navigation {
            padding: 0
        }

        .navigation .parent .level-top {
            position: relative;
            display: block;
            text-decoration: none
        }

        .nav-sections {
            -webkit-overflow-scrolling: touch;
            -webkit-transition: left .3s;
            -moz-transition: left .3s;
            -ms-transition: left .3s;
            transition: left .3s;
            height: 100%;
            left: -80%;
            left: calc(-1 * (100% - 54px));
            overflow: auto;
            position: fixed;
            top: 0;
            width: 80%;
            width: calc(100% - 54px)
        }

        .nav-sections-items {
            position: relative;
            z-index: 1
        }

        .nav-sections-items:after, .nav-sections-items:before {
            content: '';
            display: table
        }

        .nav-sections-items:after {
            clear: both
        }

        .nav-sections-item-title {
            background: #e3e3e3;
            border: solid #d7d7d7;
            border-width: 0 0 1px 1px;
            box-sizing: border-box;
            float: left;
            height: 71px;
            padding-top: 24px;
            text-align: center;
            width: 33.33%
        }

        .nav-sections-item-title.active {
            background: 0 0;
            border-bottom: 0
        }

        .nav-sections-item-title .nav-sections-item-switch:hover {
            text-decoration: none
        }

        .nav-sections-item-content {
            box-sizing: border-box;
            float: right;
            margin-left: -100%;
            margin-top: 71px;
            width: 100%;
            padding: 25px 0
        }

        .nav-sections-item-content:after, .nav-sections-item-content:before {
            content: '';
            display: table
        }

        .nav-sections-item-content:after {
            clear: both
        }

        .navigation {
            background: #f0f0f0;
            box-sizing: border-box
        }

        .navigation ul {
            margin: 0;
            padding: 0
        }

        .navigation li {
            margin: 0
        }

        .navigation a {
            display: block;
            padding: 10px 0 10px 15px
        }

        .navigation a, .navigation a:hover {
            color: #575757;
            text-decoration: none
        }

        .navigation .level0 {
            border-top: 1px solid #d1d1d1;
            font-size: 1.6rem
        }

        .navigation .level0 > .level-top {
            font-weight: 600;
            padding: 8px 40px 8px 20px;
            text-transform: uppercase;
            word-wrap: break-word
        }

        .navigation .level0 > .level-top:hover {
            color: #333
        }

        .navigation li.level0:last-child {
            border-bottom: 1px solid #d1d1d1
        }

        .navigation .submenu {
            display: none !important
        }

        .navigation .submenu > li {
            word-wrap: break-word
        }

        .navigation .submenu:not(:first-child) {
            font-weight: 400;
            line-height: 1.3;
            left: auto !important;
            overflow-x: hidden;
            padding: 0;
            position: relative;
            top: auto !important;
            transition: left .3s ease-out
        }

        .navigation .submenu:not(:first-child) > li:last-child {
            margin-bottom: 0
        }

        .navigation .submenu:not(:first-child) ul {
            display: block;
            padding-left: 15px
        }

        .navigation .submenu:not(:first-child) ul > li {
            margin: 0
        }

        .navigation .submenu:not(:first-child) ul > li a {
            color: #575757;
            display: block;
            line-height: normal;
            padding: 10px 20px
        }

        .navigation .submenu:not(:first-child) ul > li a:hover {
            color: #333
        }

        .modal-popup.modal-slide {
            left: 0;
            z-index: 900
        }

        .modal-popup.modal-slide .modal-inner-wrap {
            height: 100%;
            overflow-y: auto;
            position: static;
            -webkit-transform: translateX(100%);
            transform: translateX(100%);
            -webkit-transition: -webkit-transform .3s ease-in-out;
            transition: transform .3s ease-in-out;
            width: auto;
            margin: 0;
            max-height: none
        }

        .custom-slide {
            bottom: 0;
            min-width: 0;
            position: fixed;
            right: 0;
            top: 0;
            visibility: hidden;
            opacity: 0;
            -webkit-transition: visibility 0s .3s, opacity .3s;
            transition: visibility 0s .3s, opacity .3s;
            left: 0;
            z-index: 900
        }

        .custom-slide .modal-inner-wrap {
            opacity: 1;
            pointer-events: auto;
            overflow-y: auto;
            position: static;
            -webkit-transform: translateX(100%);
            transform: translateX(100%);
            -webkit-transition: -webkit-transform .3s ease-in-out;
            transition: transform .3s ease-in-out;
            width: auto;
            background-color: #fff;
            box-sizing: border-box;
            height: auto;
            min-height: 100%
        }

        .modal-content table {
            display: block
        }

        .modal-content table td, .modal-content table tr {
            display: block;
            width: 100%
        }

        .modal-popup.modal-slide .modal-inner-wrap[class] {
            background-color: #fff
        }

        .modal-popup.modal-slide._inner-scroll .modal-inner-wrap {
            height: auto;
            min-height: 100%
        }

        .checkout-payment-method .payment-methods {
            margin: 0
        }

        .checkout-payment-method .payment-method-title {
            padding: 15px
        }

        .checkout-payment-method .payment-method-content {
            padding: 0
        }
    }@media only screen and (max-width: 639px) {
    .minicart-wrapper {
        margin-top: 10px
    }

    .opc-wrapper .step-title {
        font-size: 22px
    }

    .opc-wrapper .form-login, .opc-wrapper .form-shipping-address {
        margin: 0;
        padding: 0
    }

    .shipping-policy-block.field-tooltip {
        margin-bottom: 20px;
        position: relative;
        right: auto;
        top: auto
    }

    .shipping-policy-block.field-tooltip .field-tooltip-content {
        width: 300px;
        right: auto
    }

    .shipping-policy-block.field-tooltip .field-tooltip-content:after, .shipping-policy-block.field-tooltip .field-tooltip-content:before {
        right: auto
    }

    .logo {
        margin-bottom: 13px;
        margin-top: 4px
    }
}@media only screen and (max-width: 479px) {
    .minicart-wrapper .block-minicart {
        width: 290px
    }
}@media all and (min-width: 640px) {
    .table > tbody > tr > td, .table > tbody > tr > th {
        border-top: 1px solid #ccc
    }
}.page-header {
     z-index: 100;
     max-width: 100%;
     background: #fff;
     width: 100%;
     height: auto;
     box-sizing: border-box;
     top: 0;
     transition: .4s
 }

    .page-header .panel.wrapper {
        background-color: #fff;
        border-bottom: none
    }

    .page-header .header.content {
        padding: 30px 0 0
    }

    .checkout-index-index .page-header {
        position: static;
        box-shadow: none
    }

    .checkout-index-index .page-header .header.content {
        position: static;
        padding-top: 0;
        margin: 0 auto
    }

    a.logo::before {
        border: none
    }

    .page-header .header.content .minicart-wrapper {
        position: absolute;
        right: 0;
        top: 109px;
        z-index: 10001
    }

    .minicart-wrapper .counterHolder {
        width: 40px;
        height: 40px;
        background: #000;
        border-radius: 20px;
        display: block
    }

    .minicart-wrapper .counterHolder.hasItem {
        background: 0 0
    }

    .minicart-wrapper .action.showcart {
        white-space: nowrap;
        position: relative
    }

    .minicart-wrapper .action.showcart::before {
        content: '\e908';
        color: #fff;
        top: 6px;
        left: 2px;
        position: absolute;
        font-size: 2.3rem
    }

    .minicart-wrapper .action.showcart:hover::before {
        color: #fff
    }

    .minicart-wrapper .action.showcart .counter.qty {
        border-radius: 12px;
        position: absolute;
        top: -13px;
        right: -9px;
        background: #000;
        min-width: 24px;
        box-sizing: border-box;
        height: 24px;
        display: block
    }

    .minicart-wrapper a::before {
        border-bottom: none
    }

    @media (min-width: 767px) and (max-width: 1200px) {
        .logo img {
            width: 70%
        }
    }.footer-wrapper {
         display: flex;
         padding: 70px 120px 60px;
         justify-content: space-between;
         position: relative
     }

    .footer.content {
        padding: 0
    }

    .footer.content .links a:hover {
        color: #677752;
        text-decoration: none
    }

    .footer.content .links li {
        margin: 0
    }

    .footer-headline {
        font-size: 20px;
        display: inline-block;
        font-weight: 600
    }

    .copyright {
        background: 0 0
    }

    .footer.bottom {
        text-align: center;
        padding: 18px 0
    }

    @media (min-width: 768px) {
        .checkout-index-index .opc-wrapper, .opc-sidebar {
            width: 100%
        }

        .nav-sections {
            background: #fff;
            position: relative;
            margin-bottom: 0
        }

        .sections .navigation {
            background: #fff;
            font-size: 2rem;
            font-weight: 400
        }

        .sections .navigation a::before {
            border-bottom: none
        }

        .sections .navigation a .ui-menu-icon {
            display: none
        }

        .sections .navigation > ul {
            position: static;
            padding: 0
        }

        .sections .navigation > ul > li {
            position: static
        }

        .sections .navigation > ul > li.level-top {
            line-height: 2.6rem;
            display: inline
        }

        .sections .navigation > ul > li + li {
            padding-left: 10px
        }

        .sections .navigation > ul > li a {
            text-decoration: none
        }

        .sections .navigation li.level0 {
            position: static;
            padding: 0 12px
        }

        .sections .navigation li.level0 a, .sections .navigation li.level0 > a.level-top {
            padding: 0
        }

        .sections .navigation li.level0 > a.level-top .ui-menu-icon {
            display: none
        }

        .sections .navigation li.level0 ul.level0.submenu {
            color: inherit;
            border: none;
            border-top: 1px solid #e2e2e2;
            box-shadow: 0 7px 7px -7px grey;
            list-style: none;
            position: absolute;
            width: 100%;
            left: 0 !important;
            top: 47px !important;
            flex-wrap: wrap;
            font-weight: 400
        }

        .sections .navigation li.level0 ul.level0.submenu a span:not(.ui-icon) {
            position: relative
        }

        .sections .navigation li.level0 ul.level0.submenu a span:not(.ui-icon)::before {
            display: block;
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 93%
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1 {
            max-width: 25%;
            flex: 1 0 25%;
            padding: 0;
            position: relative
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1::before {
            border-right: 1px solid #e2e2e2;
            display: block;
            content: "";
            position: absolute;
            top: 20px;
            bottom: 0;
            right: 0;
            width: 1px;
            z-index: 10
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1:nth-child(4)::before {
            border-right: none
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1 img {
            margin-top: 10px;
            width: 100%
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1 a {
            padding: 20px 10px 10px
        }

        .sections .navigation li.level0 ul.level0.submenu .submenuWrapper {
            display: flex !important;
            top: auto !important;
            flex-wrap: wrap;
            padding: 0;
            width: 100%
        }

        .sections .navigation li.level0 ul.level0.submenu > li {
            max-width: 1570px;
            margin: 0 auto;
            width: 100%
        }

        .nav-sections .nav-sections-items {
            max-width: 1570px;
            margin: 0 auto;
            padding-bottom: 10px
        }

        .logo {
            margin: -8px auto 5px 0
        }

        .nav-sections-item-content > .navigation {
            display: flex;
            position: static
        }

        .nav-sections-item-content .page-service-tabs {
            position: fixed;
            right: -359px;
            transform: rotateZ(-90deg);
            width: 650px;
            display: flex;
            top: 300px;
            z-index: 999
        }

        .nav-sections-item-content .page-service-tabs .tabs-container {
            transform: translateY(-60px)
        }
    }.header.content {
         max-width: 1570px;
         margin: 0 auto
     }

    .page-wrapper {
        display: block
    }

    .page-main {
        padding: 0
    }

    .panel.header {
        display: none;
        margin: 0
    }

    .block-cms-link.widget, .block-static-block.widget {
        margin: 0
    }

    .block-static-block.widget .block-container {
        max-width: 1570px;
        margin: 0 auto
    }

    .block-static-block.wide .block-container {
        margin: auto;
        max-width: none
    }

    .columns {
        display: block
    }

    .columns .column.main {
        padding-bottom: 0
    }

    @media (min-width: 992px) {
        .col-md-6 {
            width: 50%;
            float: left;
            position: relative;
            min-height: 1px;
            padding-right: 15px;
            padding-left: 15px
        }
    }#notice-cookie-block {
         box-shadow: 0 5px 5px 5px rgba(0, 0, 0, .1)
     }

    #notice-cookie-block .content {
        max-width: 1570px;
        margin: 0 auto
    }

    .opc-progress-bar {
        padding-top: 0;
        padding-bottom: 0
    }

    .opc-progress-bar .opc-progress-bar-item span {
        padding-top: 0
    }

    .opc-progress-bar .opc-progress-bar-item span::after, .opc-progress-bar .opc-progress-bar-item span::before {
        display: none
    }

    .opc-progress-bar .opc-progress-bar-item._active span {
        color: #677752
    }

    .product-item-photo::before, .product-item-photo:hover::before {
        border-bottom: none
    }

    .product-item-name a {
        font-weight: 700
    }

    .product-item-name a:hover {
        text-decoration: none;
        color: #85b84b
    }

    .product-item-name a::before {
        border-bottom: none
    }

    .checkout-index-index .column.main {
        float: none
    }

    .opc-block-summary {
        background: #fff
    }

    .checkout-index-index #checkout {
        max-width: 1570px;
        margin: 0 auto;
        padding: 0
    }

    .checkout-index-index .opc-sidebar .opc-block-info {
        background: #fff;
        margin: 0;
        padding: 22px 30px
    }

    .checkout-index-index .opc-sidebar .opc-block-info p + p {
        margin: 0
    }

    .checkout-index-index .billing-address-same-as-shipping-block {
        margin: 10px
    }

    #maincontent #checkout .authentication-wrapper button, .checkout-index-index .checkout-shipping-method .step-title, .checkout-index-index .checkout-shipping-method .table-checkout-shipping-method {
        display: none
    }

    #payment #cardexpiremonth iframe, #payment #cardexpireyear iframe {
        height: 30px
    }

    #payolution_elv_dob_day {
        padding-right: 10px
    }

    form fieldset#customer-email-fieldset .field .note {
        display: none !important
    }

    .checkout-agreements-block {
        margin: 20px 0
    }

    .checkout-info-block {
        margin: 0 0 20px
    }

    .checkout-info-block p + p {
        margin: 0
    }

    .message.global.cookie {
        background: #fff;
        font-size: 15px;
        z-index: 100000
    }

    .message.global.cookie a {
        color: #2f3943
    }

    .message.global.cookie a:hover {
        color: #85b84b
    }

    @media (max-width: 767px) {
        .logo {
            display: table;
            margin: auto;
            float: none
        }

        .page-header .header.content {
            padding-top: 0
        }

        .page-header .header.content .service-wrapper-container {
            position: relative;
            z-index: 150
        }

        .page-header .header.content .minicart-wrapper {
            right: 10px;
            top: -5px;
            z-index: inherit
        }

        .nav-toggle {
            right: 17px;
            left: auto;
            top: 63px;
            position: absolute
        }

        .block-static-block.widget .block-container {
            padding: 0 20px
        }

        .navigation {
            background: #fff
        }

        .navigation a::before {
            border-bottom: none
        }

        .navigation .submenuWrapper {
            display: block !important;
            padding: 0
        }

        .navigation a {
            padding: 10px
        }

        .navigation .submenu:not(:first-child) ul {
            padding-left: 0
        }

        .nav-sections-item-title {
            display: none
        }

        .nav-sections-item-content {
            margin-top: 0;
            margin-left: 0;
            padding: 0
        }

        .nav-sections-item-content#store\.quicklinks {
            display: block !important
        }

        .nav-sections-items {
            padding-bottom: 20px
        }

        .page-footer {
            background: #fff;
            padding-bottom: 30px
        }

        .page-footer .footer.content {
            border-top: none
        }

        .page-footer .footer-wrapper {
            flex-direction: column;
            padding: 0;
            background: #fff
        }

        .page-footer .footer-wrapper .footer-column {
            padding: 30px 20px;
            text-align: center
        }

        .page-footer .footer-wrapper .footer-column.links::after {
            display: none
        }

        .page-footer .footer-wrapper .footer-column ul li a {
            display: inline-block
        }

        .page-layout-1column a.action, .page-layout-1column button.action {
            width: 100%
        }

        .actions-toolbar > .primary {
            text-align: left
        }

        .opc-progress-bar {
            padding-left: 10px;
            padding-right: 10px
        }

        .checkout-index-index .opc-wrapper {
            width: 100%;
            box-sizing: border-box
        }

        .checkout-index-index .opc-sidebar {
            width: 100%
        }

        .checkout-index-index .page-header .header.content .service-wrapper-container {
            height: auto;
            padding: 0
        }

        .checkout-index-index .minicart-wrapper .action.showcart .counterHolder.hasItem .counter.qty {
            background: #636d70
        }

        .checkout-index-index .page-header {
            margin-bottom: 0;
            border-bottom: none
        }

        .opc-estimated-wrapper {
            margin: 0
        }
    }@media (min-width: 768px) and (max-width: 1200px) {
    .sections.nav-sections {
        padding: 0 20px
    }

    .opc-progress-bar {
        padding: 20px
    }

    .page-header .header.content {
        padding: 30px 10px 0
    }
}html {
     font-family: sans-serif;
     -webkit-text-size-adjust: 100%;
     -ms-text-size-adjust: 100%
 }

    aside, footer, header, main, nav {
        display: block
    }

    a {
        background-color: transparent
    }

    a:active, a:hover {
        outline: 0
    }

    img {
        border: 0;
        vertical-align: middle
    }

    svg:not(:root) {
        overflow: hidden
    }

    hr {
        height: 0;
        -webkit-box-sizing: content-box;
        -moz-box-sizing: content-box;
        box-sizing: content-box;
        margin-top: 20px;
        margin-bottom: 20px;
        border: 0;
        border-top: 1px solid #eee
    }

    button, input, select {
        margin: 0;
        font: inherit;
        color: inherit
    }

    button {
        overflow: visible;
        -webkit-appearance: button;
        cursor: pointer
    }

    button, select {
        text-transform: none
    }

    button[disabled], html input[disabled] {
        cursor: default
    }

    input {
        line-height: normal
    }

    input[type=checkbox], input[type=radio] {
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        padding: 0;
        margin: 4px 0 0;
        line-height: normal
    }

    table {
        border-spacing: 0;
        border-collapse: collapse;
        background-color: transparent
    }

    td, th {
        padding: 0
    }

    @media print {
        *, :after, :before {
            color: #000 !important;
            text-shadow: none !important;
            background: 0 0 !important;
            -webkit-box-shadow: none !important;
            box-shadow: none !important
        }

        a, a:visited {
            text-decoration: underline
        }

        a[href]:after {
            content: " (" attr(href) ")"
        }

        a[href^="#"]:after {
            content: ""
        }

        thead {
            display: table-header-group
        }

        img, tr {
            page-break-inside: avoid
        }

        img {
            max-width: 100% !important
        }

        p {
            orphans: 3;
            widows: 3
        }

        .label {
            border: 1px solid #000
        }

        .table {
            border-collapse: collapse !important
        }

        .table td, .table th {
            background-color: #fff !important
        }
    }*, :after, :before {
         -webkit-box-sizing: border-box;
         -moz-box-sizing: border-box;
         box-sizing: border-box
     }

    html {
        font-size: 10px;
        -webkit-tap-highlight-color: transparent
    }

    button, input, select {
        font-family: inherit;
        font-size: inherit;
        line-height: inherit
    }

    [role=button] {
        cursor: pointer
    }

    h1, h6 {
        font-family: inherit;
        line-height: 1.1;
        color: inherit
    }

    h1 {
        margin: 20px 0 10px;
        font-size: 36px
    }

    h6 {
        margin-top: 10px;
        margin-bottom: 10px;
        font-size: 12px
    }

    p {
        margin: 0 0 10px
    }

    small {
        font-size: 85%
    }

    .mark {
        padding: .2em
    }

    ol, ul {
        margin-top: 0;
        margin-bottom: 10px
    }

    ul ul {
        margin-bottom: 0
    }

    dl {
        margin-top: 0;
        margin-bottom: 20px
    }

    dd, dt {
        line-height: 1.42857143
    }

    dt {
        font-weight: 700
    }

    dd {
        margin-left: 0
    }

    .container {
        padding-right: 20px;
        padding-left: 20px;
        margin-right: auto;
        margin-left: auto
    }

    @media (min-width: 768px) {
        .container {
            width: 750px
        }
    }@media (min-width: 992px) {
    .container {
        width: 970px
    }
}@media (min-width: 1200px) {
    .container {
        width: 1200px
    }
}.col-md-3, .col-md-6 {
     position: relative;
     min-height: 1px;
     padding-right: 15px;
     padding-left: 15px
 }

    @media (min-width: 992px) {
        .col-md-3, .col-md-6 {
            float: left
        }

        .col-md-6 {
            width: 50%
        }

        .col-md-3 {
            width: 25%
        }
    }caption {
         padding-top: 8px;
         padding-bottom: 8px;
         color: #777;
         text-align: left
     }

    th {
        text-align: left
    }

    .table {
        width: 100%;
        max-width: 100%;
        margin-bottom: 20px
    }

    .table > tbody > tr > td, .table > tbody > tr > th {
        padding: 8px;
        line-height: 1.42857143;
        vertical-align: top;
        border-top: 1px solid #ddd
    }

    table td[class*=col-], table th[class*=col-] {
        position: static;
        display: table-cell;
        float: none
    }

    fieldset {
        min-width: 0;
        padding: 0;
        margin: 0;
        border: 0
    }

    legend {
        display: block;
        width: 100%;
        padding: 0;
        margin-bottom: 20px;
        font-size: 21px;
        line-height: inherit;
        color: #333;
        border: 0;
        border-bottom: 1px solid #e5e5e5
    }

    label {
        display: inline-block;
        max-width: 100%;
        margin-bottom: 5px;
        font-weight: 500
    }

    input[type=checkbox]:focus, input[type=radio]:focus {
        outline: -webkit-focus-ring-color auto 5px;
        outline-offset: -2px
    }

    .radio {
        position: relative;
        display: block;
        margin-top: 10px;
        margin-bottom: 10px
    }

    input[type=checkbox].disabled, input[type=checkbox][disabled] {
        cursor: not-allowed
    }

    .nav {
        padding-left: 0;
        margin-bottom: 0;
        list-style: none
    }

    .label:empty {
        display: none
    }

    .panel {
        -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .05)
    }

    .close {
        float: right;
        font-size: 21px;
        font-weight: 700;
        line-height: 1;
        color: #000;
        text-shadow: 0 1px 0 #fff;
        opacity: .2
    }

    .close:focus, .close:hover {
        color: #000;
        text-decoration: none;
        cursor: pointer;
        opacity: .5
    }

    button.close {
        -webkit-appearance: none;
        padding: 0;
        cursor: pointer;
        background: 0 0;
        border: 0
    }

    .modal-content {
        position: relative;
        background-color: #fff;
        -webkit-background-clip: padding-box;
        background-clip: padding-box;
        border: 1px solid rgba(0, 0, 0, .2);
        border-radius: 6px;
        outline: 0;
        -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, .5);
        box-shadow: 0 3px 9px rgba(0, 0, 0, .5)
    }

    .modal-header {
        padding: 15px;
        border-bottom: 1px solid #e5e5e5
    }

    .modal-footer {
        padding: 15px;
        text-align: right;
        border-top: 1px solid #e5e5e5
    }

    @media (min-width: 768px) {
        .modal-content {
            -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, .5);
            box-shadow: 0 5px 15px rgba(0, 0, 0, .5)
        }
    }.container:after, .container:before, .modal-footer:after, .modal-footer:before, .modal-header:after, .modal-header:before, .nav:after, .nav:before, .row:after, .row:before {
         display: table;
         content: " "
     }

    .container:after, .modal-footer:after, .modal-header:after, .nav:after, .row:after {
        clear: both
    }

    .hidden, .visible-xs {
        display: none !important
    }

    @media (max-width: 767px) {
        .visible-xs {
            display: block !important
        }

        .hidden-xs {
            display: none !important
        }
    }@media (min-width: 768px) and (max-width: 991px) {
    .hidden-sm {
        display: none !important
    }
}.page-wrapper {
     background: #f5f5f5
 }

    .page-bottom {
        padding-bottom: 100px;
        position: relative;
        z-index: 999;
        background: #f5f5f5
    }

    .page-footer {
        position: relative;
        z-index: 999
    }

    .service-wrapper-inner {
        clear: both;
        margin-top: 20px;
        font-size: 15px;
        color: #6f7f5b;
        display: flex;
        align-items: center;
        flex-direction: row;
        justify-content: space-between;
        max-width: 100%
    }

    .service-wrapper-inner .logo-container {
        order: 2;
        min-width: 250px
    }

    .service-wrapper-inner .service-items-outer {
        order: 4
    }

    .service-wrapper-inner .minicart-wrapper {
        order: 5;
        margin-left: 35px;
        padding: 15px 0
    }

    .service-wrapper-inner .minicart-wrapper > a.action {
        background: linear-gradient(180deg, #ffd119 0, #dcaa28 100%);
        border-radius: 5px;
        padding: 17px 15px;
        font-size: 16px;
        white-space: nowrap
    }

    .service-wrapper-inner .minicart-wrapper > a.action span {
        color: #fff;
        font-weight: 500;
        display: inline-block;
        vertical-align: middle
    }

    .service-wrapper-inner .minicart-wrapper > a.action img {
        display: inline-block;
        vertical-align: middle
    }

    .service-wrapper-inner .minicart-wrapper span.counter.qty {
        font-weight: 500;
        color: #636d70
    }

    .service-wrapper-top {
        display: table;
        width: 100%;
        margin: 5px 0;
        border-bottom: 1px solid #e2e2e2;
        font-size: 14px;
        color: #636d70
    }

    .service-wrapper-top .service-top-items {
        float: left
    }

    .service-wrapper-top .service-top-item {
        display: inline-block;
        padding-left: 10px
    }

    .service-wrapper-top a {
        font-size: 14px;
        color: #636d70
    }

    .service-items-outer .service-items-item {
        background: #f5f5f5;
        padding: 15px 25px;
        font-weight: 500;
        position: relative
    }

    .service-top-items .icon_delivery {
        width: 22px;
        height: 25px;
        margin-left: 10px;
        margin-bottom: 3px
    }

    .service-top-items .icon_mail {
        width: 20px;
        height: 25px;
        margin-left: 10px;
        margin-bottom: 3px
    }

    .service-items-outer .service-items-item.service-item-trustedshops span {
        display: block;
        line-height: normal;
        font-size: 14px;
        color: #636d70
    }

    .service-items-outer .service-items-item.service-item-trustedshops img, .service-items-outer .service-items-item.service-item-trustedshops > .service-item-inner {
        display: inline-block;
        vertical-align: middle
    }

    .service-items-outer .service-items-item.service-item-trustedshops {
        padding: 0 15px 0 0;
        border-radius: 30px 0 0 30px
    }

    .service-items-outer .service-items-item.service-item-trustedshops img {
        margin-right: 10px
    }

    .service-items-outer.footer-service-outer .service-items-item.service-item-trustedshops {
        padding-right: 10px;
        margin: 20px 0
    }

    .header .service-items-outer .service-items-item {
        display: inline-block
    }

    .service-sidebar-inner .service-sidebar-item {
        background: #fff;
        text-align: left;
        font-size: 18px;
        color: #636d70;
        margin-bottom: 7px;
        display: block;
        border-radius: 5px 0 0 5px;
        overflow: hidden;
        width: auto;
        padding-right: 15px;
        box-shadow: 0 0 15px 0 rgba(0, 0, 0, .1)
    }

    .service-sidebar-inner .service-sidebar-item img {
        background: #667751;
        width: 45px;
        height: auto;
        padding: 12px 10px
    }

    .service-sidebar-inner .service-sidebar-item span {
        padding: 5px;
        display: inline-block;
        vertical-align: middle
    }

    .page-title-wrapper h1.page-title {
        font-size: 35px;
        font-weight: 500;
        text-transform: uppercase;
        padding-bottom: 10px;
        text-align: center;
        margin: 0
    }

    .header-language-row {
        float: right;
        font-size: 0
    }

    .header-language-row .header-language-item {
        display: inline-block;
        cursor: pointer
    }

    .header-language-row .header-language-item:not(.header-language-active) {
        opacity: .3
    }

    .header-language-row .header-language-item img {
        margin-left: 7px
    }

    .header-language-row .header-language-item a {
        font-size: 0;
        vertical-align: middle
    }

    .footer-logo {
        max-width: 100px;
        margin: 35px auto 0;
        display: block
    }

    footer.page-footer .block-static-block.widget .block-container {
        text-align: center
    }

    .footer-wrapper-border {
        position: relative
    }

    .footer-wrapper-border .footer-wrapper-border-inner {
        pointer-events: none;
        overflow: hidden;
        width: 100%;
        height: 185px;
        position: absolute;
        top: -160px;
        z-index: 2
    }

    .footer-column .footer-payment {
        margin-top: 20px
    }

    .footer-column .footer-payment img {
        display: inline-block;
        margin-bottom: 10px
    }

    footer.page-footer .promotion-footer-badge {
        position: fixed;
        right: 0;
        bottom: 190px;
        display: none;
        z-index: 10
    }

    footer.page-footer .promotion-footer-badge > div {
        box-shadow: 0 2px 9px 2px rgba(0, 0, 0, .1);
        background: #fff;
        display: block;
        padding: 14px
    }

    .minicart-wrapper a.action .text {
        color: #636d70;
        font-weight: 500
    }

    .minicart-wrapper a.action .text + .text {
        display: none
    }

    .home-header-inner {
        position: relative
    }

    .home-header-inner .home-header-info {
        background: #fff;
        position: absolute;
        border-radius: 3px 3px 0 0;
        top: 25px;
        box-shadow: 0 0 15px 0 rgba(0, 0, 0, .1)
    }

    .home-header-inner .home-header-info-top {
        display: table;
        width: 100%;
        color: #636d70;
        padding: 10px;
        min-height: 180px
    }

    .home-header-inner .home-header-info-top .home-header-info-portrait img {
        position: absolute;
        top: -40px;
        left: -18px;
        pointer-events: none;
        z-index: 1
    }

    .home-header-inner .home-header-info-top .home-header-info-quote {
        text-align: left;
        padding-left: 0;
        padding-right: 0;
        width: 50%;
        float: right;
        padding-top: 20px
    }

    .home-header-info-top .home-header-info-quote span {
        font-size: 15px;
        display: block;
        font-style: italic;
        line-height: normal;
        margin-bottom: 10px
    }

    .home-header-inner .home-header-info-bottom {
        background: #f5f5f5;
        position: relative;
        text-align: center;
        z-index: 0;
        padding: 0 15px 15px;
        margin-top: -15px
    }

    .checkout-index-index .home-header-inner .home-header-info-bottom {
        background: #667751
    }

    .btn-main, .checkout-index-index .payment-method .actions-toolbar button.checkout {
        background: #ffd119;
        background: linear-gradient(180deg, #ffd119 0, #dcaa28 100%);
        border-radius: 5px;
        display: table;
        width: auto;
        margin: auto;
        color: #fff;
        font-size: 20px;
        font-weight: 500;
        text-align: center;
        position: relative
    }

    .checkout-index-index .payment-method .actions-toolbar button.checkout span {
        color: #fff;
        font-size: 20px;
        display: block;
        padding: 8px 15px;
        font-weight: 500;
        text-align: center
    }

    @media (min-width: 768px) and (max-width: 999px) {
        .checkout-index-index .payment-method .actions-toolbar button.checkout span {
            font-size: 16px;
            margin-left: 16px
        }

        .checkout-index-index .row-totals .save {
            flex-direction: column
        }

        .checkout-index-index .checkout-header-second {
            font-size: 13px
        }
    }.checkout-index-index .payment-method .actions-toolbar button.checkout, button.btn-main {
         padding: 0 !important;
         border: none;
         min-height: auto
     }

    .checkout-index-index .payment-method .actions-toolbar button.checkout:after, button.btn-main:after {
        content: none
    }

    .fullwidth-break-outer {
        display: block;
        padding: 50px 0;
        text-align: center
    }

    .fullwidth-break-inner .fullwidth-break-border {
        border-top: 1px solid #e4e4e4
    }

    .fullwidth-break-inner .fullwidth-break-border img {
        margin-top: -30px
    }

    .fullwidth-break-inner span {
        color: #667751;
        text-transform: uppercase;
        font-size: 15px;
        opacity: .5;
        letter-spacing: 7px;
        margin-top: -15px
    }

    .product-additional-modal {
        position: fixed;
        top: 10%;
        bottom: 10%;
        left: 0;
        right: 0;
        z-index: 10000
    }

    .product-additional-modal-bg {
        background: rgba(51, 51, 51, .55);
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0
    }

    .product-additional-modal-inner {
        background: #fff;
        padding: 20px;
        border-radius: 5px;
        position: relative;
        margin: auto;
        max-width: 1200px
    }

    .product-additional-close {
        position: absolute;
        top: 7px;
        right: 10px;
        font-size: 30px;
        cursor: pointer
    }

    .product-additional-modal-inner img {
        margin: auto;
        display: block
    }

    footer.modal-footer button {
        color: #fff
    }

    @media (min-width: 768px) {
        footer.page-footer .promotion-footer-badge {
            display: block
        }

        .sections .navigation .level1 > a .submenu-category-image {
            position: relative
        }

        .sections .navigation .level1 > a .submenu-category-price {
            position: absolute !important;
            top: 80%;
            z-index: 10;
            right: 0;
            font-size: 25px;
            background: linear-gradient(180deg, #ffd119 0, #dcaa28 100%);
            color: #fff;
            font-weight: 500;
            padding: 3px 15px;
            display: block;
            line-height: normal;
            border-radius: 4px 0 0 4px;
            box-shadow: 0 0 15px 0 rgba(0, 0, 0, .1)
        }

        .sections .navigation .level1 > a > .submenu-category-name {
            font-weight: 700;
            text-align: center;
            display: block;
            text-transform: uppercase
        }

        .sections .navigation .level1 > a > .submenu-category-usplist {
            margin-bottom: 10px
        }

        .sections .navigation .level1 > a > .submenu-category-usplist span {
            display: block;
            font-size: 17px;
            position: relative;
            text-align: center
        }

        .sections .navigation .level1 img {
            margin-top: 0
        }

        .service-wrapper-inner .minicart-wrapper > a.action img {
            margin-right: 5px;
            margin-top: -3px
        }

        .checkout-index-index .opc-progress-bar .opc-progress-bar-item > span {
            display: inline;
            padding-top: 5px;
            padding-bottom: 2px;
            color: #2e2e2e
        }

        .checkout-index-index .field[name='billingAddress.firstname'], .checkout-index-index .field[name='billingAddress.telephone'] {
            min-width: 100px !important;
            position: relative !important
        }

        .checkout-index-index .fieldset.address .field[name="billingAddress.prefix"], .checkout-index-index .fieldset.address .field[name="shippingAddress.prefix"], .checkout-index-index .fieldset.address .field[name='billingAddress.city'], .checkout-index-index .fieldset.address .field[name='billingAddress.company'], .checkout-index-index .fieldset.address .field[name='billingAddress.country_id'], .checkout-index-index .fieldset.address .field[name='billingAddress.fax'], .checkout-index-index .fieldset.address .field[name='billingAddress.firstname'], .checkout-index-index .fieldset.address .field[name='billingAddress.lastname'], .checkout-index-index .fieldset.address .field[name='billingAddress.postcode'], .checkout-index-index .fieldset.address .field[name='billingAddress.telephone'], .checkout-index-index .fieldset.address .field[name='shippingAddress.city'], .checkout-index-index .fieldset.address .field[name='shippingAddress.company'], .checkout-index-index .fieldset.address .field[name='shippingAddress.country_id'], .checkout-index-index .fieldset.address .field[name='shippingAddress.firstname'], .checkout-index-index .fieldset.address .field[name='shippingAddress.lastname'], .checkout-index-index .fieldset.address .field[name='shippingAddress.postcode'], .checkout-index-index .fieldset.address .field[name='shippingAddress.telephone'] {
            display: inline-block;
            margin-bottom: 20px;
            margin-right: 1%;
            margin-left: 1%
        }

        .checkout-index-index .fieldset.address .field[name='shippingAddress.fax'] {
            margin-bottom: 20px;
            margin-right: 1%;
            margin-left: 1%
        }

        .checkout-index-index .field[name='shippingAddress.fax'] {
            margin-top: 58px
        }

        .checkout-index-index .field[name='shippingAddress.telephone'] {
            float: right !important;
            margin-right: 7% !important;
            margin-left: unset !important;
            margin-top: -80px
        }

        .checkout-index-index .field[name='shippingAddress.prefix'] {
            margin-top: 80px
        }

        #empty-block, .checkout-index-index .fieldset.address .field[name="billingAddress.prefix"], .checkout-index-index .fieldset.address .field[name="shippingAddress.prefix"], .checkout-index-index .fieldset.address .field[name='billingAddress.city'], .checkout-index-index .fieldset.address .field[name='billingAddress.company'], .checkout-index-index .fieldset.address .field[name='billingAddress.fax'], .checkout-index-index .fieldset.address .field[name='billingAddress.firstname'], .checkout-index-index .fieldset.address .field[name='billingAddress.lastname'], .checkout-index-index .fieldset.address .field[name='billingAddress.postcode'], .checkout-index-index .fieldset.address .field[name='billingAddress.telephone'], .checkout-index-index .fieldset.address .field[name='shippingAddress.city'], .checkout-index-index .fieldset.address .field[name='shippingAddress.company'], .checkout-index-index .fieldset.address .field[name='shippingAddress.fax'], .checkout-index-index .fieldset.address .field[name='shippingAddress.firstname'], .checkout-index-index .fieldset.address .field[name='shippingAddress.lastname'], .checkout-index-index .fieldset.address .field[name='shippingAddress.postcode'], .checkout-index-index .fieldset.address .field[name='shippingAddress.telephone'] {
            width: 47% !important
        }

        .checkout-index-index .fieldset.address .field[name='billingAddress.country_id'], .checkout-index-index .fieldset.address .field[name='shippingAddress.country_id'] {
            width: 97% !important
        }

        .checkout-index-index .fieldset.address .field[name='billingAddress.company'], .checkout-index-index .fieldset.address .field[name='shippingAddress.firstname'] {
            min-width: 100px;
            position: relative
        }

        .checkout-index-index .checkout-billing-address .fieldset .field.street .field, .checkout-index-index .checkout-shipping-address .fieldset.address fieldset.field.street .field[name="shippingAddress.street.0"] {
            width: 92%;
            margin: 0 5px
        }

        .checkout-index-index .fieldset.address .field[name="shippingAddress.country_id"], .checkout-index-index .fieldset.address .field[name="shippingAddress.prefix"] {
            display: block
        }

        .checkout-index-index .checkout-billing-address .fieldset .field.street .field label {
            display: none
        }

        .checkout-index-index .fieldset.address .field[name='billingAddress.company'] .label, .checkout-index-index .fieldset.address .field[name='billingAddress.fax'] .control, .checkout-index-index .fieldset.address .field[name='billingAddress.fax'] .label, .checkout-index-index .fieldset.address .field[name='shippingAddress.company'] .control, .checkout-index-index .fieldset.address .field[name='shippingAddress.company'] .label, .checkout-index-index .fieldset.address .field[name='shippingAddress.fax'] .control, .checkout-index-index .fieldset.address .field[name='shippingAddress.fax'] .label {
            text-decoration: underline;
            font-size: 17px
        }

        .checkout-index-index .fieldset.address .field[name='billingAddress.company'] .label::before, .checkout-index-index .fieldset.address .field[name='billingAddress.fax'] .label::before, .checkout-index-index .fieldset.address .field[name='shippingAddress.company'] .label::before, .checkout-index-index .fieldset.address .field[name='shippingAddress.fax'] .label::before {
            content: '+ '
        }

        .checkout-index-index .fieldset .field-error {
            position: absolute;
            margin-top: -5px
        }

        .checkout-index-index .block.items-in-cart {
            margin-bottom: 0
        }

        .checkout-index-index .checkout-billing-address .fieldset > .field[name*=prefix] {
            display: block
        }

        .checkout-index-index .opc-sidebar.opc-summary-wrapper .opc-block-info {
            background: #fff;
            margin: 0;
            padding-top: 15px
        }

        .checkout-shipping-method .actions-toolbar > .primary {
            width: 100%
        }

        .sections .navigation > ul {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            align-content: center;
            align-items: center;
            justify-content: flex-start
        }

        #checkout-step-shipping, .billing-form-container, .shipping-form-container {
            margin-left: 2%
        }

        .opc-continue-button {
            margin: 10px !important
        }

        .checkout-payment-method .payment-method.payolution select.dob-day {
            min-width: 80px
        }
    }.checkout-index-index .checkout-shipping-address #checkout-step-shipping .form.form-shipping-address div.field[name*=region] {
         display: none
     }

    .checkout-info-block .info-cms-block ul li {
        list-style: none;
        line-height: normal;
        font-size: 17px;
        margin-bottom: 10px;
        position: relative;
        padding-left: 30px
    }

    .checkout-info-block .info-cms-block ul li:before {
        content: "";
        background: url("data:image/svg+xml;base64,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") center/15px 15px no-repeat;
        width: 15px;
        height: 25px;
        float: left;
        left: 10px;
        top: 0;
        position: absolute
    }

    .payment-method .payment-method-title .label {
        cursor: pointer;
        margin: 0
    }

    .checkout-index-index .mgz-heading-text {
        text-transform: none
    }

    .checkout-index-index .opc-summary-wrapper .content.minicart-items {
        display: block !important
    }

    .checkout-index-index .opc-summary-wrapper .items-in-cart > .title {
        padding: 0;
        cursor: initial
    }

    .checkout-index-index .opc-summary-wrapper .items-in-cart > .title::after {
        content: none
    }

    .modal-popup._inner-scroll {
        z-index: 10001 !important
    }

    .modal-custom.opc-sidebar {
        margin: 0
    }

    .modal-custom.opc-sidebar .modal-content {
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, .2);
        border: none;
        border-radius: 6px
    }

    .modal-custom.opc-sidebar .modal-header {
        display: none;
        border: none
    }

    .opc-wrapper {
        margin: 0 0 5px
    }

    .opc-wrapper .fieldset > .field > legend.label {
        color: inherit;
        font-size: inherit;
        border: none
    }

    .opc-wrapper .field-tooltip .field-tooltip-action {
        padding-right: 5px
    }

    .opc-wrapper .fieldset .field[name^="billingAddress.street"] > label, .opc-wrapper .fieldset .field[name^="shippingAddress.street"] > label {
        display: none
    }

    .checkout-index-index .opc-wrapper > .opc {
        background: #fff;
        border-radius: 5px;
        padding: 15px
    }

    .checkout-index-index .page-main > .page-title-wrapper .page-title, .checkout-index-index:not(.payment-step) aside, body:not([class*=catalog-]) .page-title-wrapper {
        display: none
    }

    .checkout-index-index:not(.payment-step) .opc-wrapper {
        width: 100%
    }

    .checkout-index-index:not(.payment-step) .opc-wrapper > .opc li {
        width: 50%;
        margin: 0 auto
    }

    .checkout-index-index:not(.payment-step) .opc-info-bottom {
        width: 100%
    }

    .checkout-index-index .opc-info-bottom {
        display: none !important;
        margin-top: 20px
    }

    .checkout-index-index .opc-info-bottom .home-header-inner .home-header-info {
        position: relative;
        max-width: initial
    }

    .opc-info-bottom .home-header-info-portrait {
        position: static;
        width: 180px
    }

    .opc-info-bottom .home-header-inner .home-header-info-top .home-header-info-quote {
        width: calc(100% - 180px);
        padding-top: 10px
    }

    .opc-info-bottom .home-header-info-bottom {
        color: #fff;
        padding-left: 180px;
        text-align: left;
        margin-top: -15px;
        border: none !important;
        padding-top: 0 !important
    }

    .opc-info-bottom .home-header-info-bottom a {
        color: #fff
    }

    .opc-info-bottom .home-header-inner .home-header-info-top {
        min-height: initial
    }

    .opc-info-bottom ul > li {
        flex-grow: 1
    }

    .checkout-index-index #co-payment-form hr {
        display: none
    }

    .checkout-index-index div.field {
        font-size: 20px;
        color: #2e2e2e
    }

    .checkout-index-index fieldset.field.required {
        font-size: 20px
    }

    .checkout-index-index div.field._required > label, .checkout-index-index div.field.required > label, .opc-wrapper .fieldset > .field > legend.label {
        margin: 0 0 -5px
    }

    .checkout-index-index div.field._required {
        font-size: 20px
    }

    .checkout-index-index div.field._required[name="billingAddress.country_id"] {
        display: block
    }

    .checkout-index-index .checkout-billing-address .billing-address-form {
        margin-bottom: 20px
    }

    .checkout-index-index .opc-progress-bar {
        display: flex;
        align-items: flex-start;
        justify-content: space-around;
        counter-reset: step-counter;
        flex-wrap: nowrap;
        margin: 15px auto;
        max-width: 900px !important
    }

    .checkout-index-index .opc-progress-bar .opc-progress-bar-item {
        counter-increment: my-awesome-counter;
        display: flex;
        align-items: center;
        flex-direction: row;
        width: auto
    }

    .checkout-index-index .opc-progress-bar .opc-progress-bar-item::before {
        content: counter(my-awesome-counter);
        color: #636d70;
        font-size: 1.5rem;
        font-weight: 700;
        position: relative;
        display: inline-block;
        --size: 32px;
        left: -10px;
        line-height: var(--size);
        width: var(--size);
        height: var(--size);
        top: 0;
        background: #e3e2e4;
        border-radius: 50%;
        text-align: center;
        flex-shrink: 0
    }

    .checkout-index-index .opc-progress-bar .opc-progress-bar-item._complete::before {
        content: "\f00c" !important;
        font-family: 'Font Awesome 5 Free';
        color: #657753;
        font-size: 1.5rem;
        font-weight: 700;
        position: relative;
        display: inline-block;
        left: -10px;
        line-height: var(--size);
        width: var(--size);
        height: var(--size);
        top: 0;
        background: #eef4ed;
        border-radius: 50%;
        text-align: center
    }

    .checkout-index-index .opc-choose-payment {
        padding: 8px 10px;
        min-height: unset
    }

    .checkout-index-index .opc-progress-bar .opc-progress-bar-item._active::before {
        color: #fff;
        background: #677752
    }

    .checkout-index-index .opc-progress-bar li.opc-progressbar-delimiter {
        border-top: 2px solid #636d70;
        height: 2px;
        min-width: 15px;
        width: 10%;
        align-self: center
    }

    .checkout-index-index .opc-progress-bar li.opc-progressbar-delimiter._active {
        border-top: 2px solid #677752
    }

    .checkout-index-index .opc-progress-bar li[data-step=payment] {
        margin-left: 4%
    }

    @media (max-width: 767px) {
        .checkout-index-index .checkout-mobile-hidden-block {
            display: none
        }

        .checkout-index-index .checkout-mobile-center {
            text-align: center;
            display: flex;
            justify-content: center
        }

        .checkout-index-index .checkout-mobile-center > div {
            max-width: 200px;
            margin-bottom: 10px;
            margin-top: 10px !important
        }

        .opc-progress-bar .opc-progress-bar-item span {
            font-size: 1.5rem
        }

        .checkout-index-index .opc-progress-bar .opc-progress-bar-item::before {
            --size: 22px;
            left: 0 !important
        }

        .checkout-index-index .opc-progress-bar li[data-step=payment] {
            margin: 0
        }

        .checkout-index-index .opc-progress-bar .opc-progress-bar-item {
            flex-direction: column
        }
    }.checkout-index-index .field-tooltip .field-tooltip-action:before {
         content: "\f00c";
         font-family: 'Font Awesome 5 Free';
         color: #677752;
         font-size: 10px;
         font-weight: 700;
         position: relative;
         display: inline-block;
         --size: 18px;
         line-height: var(--size);
         width: var(--size);
         height: var(--size);
         top: 4px;
         background: #e3e2e4;
         border-radius: 50%;
         text-align: center
     }

    .checkout-index-index .opc-wrapper .step-title {
        font-weight: 500;
        font-size: 20px
    }

    .checkout-index-index .opc-block-summary {
        padding: unset !important
    }

    .checkout-index-index .form-login .fieldset.hidden-fields {
        display: none !important
    }

    .checkout-index-index .payment-method .actions-toolbar button.checkout:after {
        content: "\f07a";
        font-family: 'Font Awesome 5 Free';
        color: #fff;
        font-size: 18px;
        font-weight: 700;
        position: absolute;
        top: 7px;
        left: 10px
    }

    .checkout-index-index .opc-sidebar {
        display: flex;
        flex-direction: column
    }

    .checkout-index-index .opc-sidebar .minicart-items .product-item-details .product.options .toggle {
        display: none
    }

    .checkout-index-index .opc-sidebar .minicart-items .product-item-details .product.options .content {
        display: block !important
    }

    .checkout-index-index .opc-sidebar .minicart-items .product-item-details .product.options .content .item-options .label {
        display: none
    }

    .checkout-index-index .opc-sidebar .minicart-items .product-item-details .product.options .content .item-options {
        margin-bottom: 6px
    }

    .checkout-index-index .opc-sidebar .minicart-items .product-item-details .product.options .content .item-options .values {
        margin: 0;
        font-size: .8em
    }

    .checkout-index-index .sidebar-hotline-number img {
        width: 40px
    }

    .checkout-index-index .sidebar-hotline-number a {
        display: inline-block
    }

    .checkout-index-index select {
        -webkit-appearance: button;
        -moz-appearance: button;
        -ms-appearance: auto;
        appearance: auto
    }

    .checkout-index-index .form.payments .fieldset > br {
        display: none
    }

    .checkout-index-index .form.form-login {
        margin-bottom: unset !important
    }

    .checkout-index-index input.input-text, .checkout-index-index input[type=email], .checkout-index-index input[type=password], .checkout-index-index input[type=text], .checkout-index-index select {
        height: 50px;
        border: 2px solid #c2c2c2
    }

    footer.modal-footer {
        text-align: center
    }

    .checkout-index-index .page-header {
        display: none
    }

    .checkout-index-index .opc-wrapper {
        padding: 0;
        float: left;
        border: 1px solid #eef4ed;
        border-radius: 10px;
        box-shadow: rgb(99 99 99 / 20%) 0 2px 8px 0;
        height: max-content
    }

    .checkout-index-index .opc-wrapper > .opc {
        width: 100%
    }

    .checkout-index-index .checkout-payment-method .payment-method-title {
        padding: 15px 0
    }

    .checkout-index-index .footer .footer-wrapper {
        display: none
    }

    .checkout-index-index .page-wrapper {
        padding-bottom: 50px
    }

    .banktransfer-image-container, .banktransfer-payment-image {
        background-image: url(http://www.gfp.test/static/frontend/Gfp/international/de_DE/images/paymenticons/payment_vorkasse.png);
        width: 100%;
        height: 55px;
        background-position: center;
        background-repeat: no-repeat
    }

    .payolution-image-container, .payolution_invoice-payment-image {
        background-image: url(http://www.gfp.test/static/frontend/Gfp/international/de_DE/images/paymenticons/payment_invoice.png);
        width: 100%;
        height: 55px;
        background-position: center;
        background-repeat: no-repeat
    }

    .payolution-elv-image-container, .payolution_elv-payment-image {
        background-image: url(http://www.gfp.test/static/frontend/Gfp/international/de_DE/images/paymenticons/payment_sepa.png);
        width: 100%;
        height: 55px;
        background-position: center;
        background-repeat: no-repeat
    }

    .creditcard-image-container, .payone_creditcard-payment-image {
        background-image: url(http://www.gfp.test/static/frontend/Gfp/international/de_DE/images/paymenticons/creditcard-base.svg);
        width: 100%;
        height: 55px;
        background-position: center;
        background-repeat: no-repeat
    }

    .klarna_pay_later-payment-image {
        background-image: url(http://www.gfp.test/static/frontend/Gfp/international/de_DE/images/paymenticons/klarna.svg);
        width: 100%;
        height: 55px;
        background-position: center;
        background-repeat: no-repeat
    }

    .amazon_payment_v2-payment-image {
        background-image: url(http://www.gfp.test/static/frontend/Gfp/international/de_DE/images/paymenticons/amazon.png);
        width: 100%;
        height: 55px;
        background-position: center;
        background-repeat: no-repeat
    }

    .paypal_express-payment-image {
        background-image: url(http://www.gfp.test/static/frontend/Gfp/international/de_DE/images/paymenticons/paypal.png);
        width: 100%;
        height: 55px;
        background-position: center;
        background-repeat: no-repeat
    }

    .desktop-payment-description {
        font-size: 16px;
        color: #2e2e2e;
        font-weight: 500;
        line-height: 45px;
        width: 100%
    }

    .payment-method-title-desktop {
        flex-basis: 23%;
        height: 120px;
        border: 2px solid #eef4ed;
        border-radius: 5px;
        cursor: pointer
    }

    .checkout-index-index .field input:hover, .checkout-index-index .field select:hover, .payment-method-title-desktop:hover {
        transition: background-color .2s ease-in-out;
        background-color: #eef4ed
    }

    .desktop-payment-description-content {
        width: max-content;
        margin: auto
    }

    .payment-methods-desktop-container {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        padding: .5em;
        gap: .5em
    }

    .payment-method-title.field.choice {
        position: relative
    }

    html[lang=de] label[for=payolution_elv_account_holder]:before {
        content: "Bei Zahlung per Lastschrift erfolgt die Abbuchung unmittelbar vor dem Versand der Ware von Ihrem Bankkonto. Diese Einmallastschrift berechtigt uns ausschließlich einmalig dazu, den Rechnungsbetrag von Ihrem Konto abzubuchen.\A\A";
        display: block;
        margin-bottom: 8px
    }

    html[lang=de] #payolution_invoice-form .agreements:before {
        content: "Bei der Zahlungsart „Kauf auf Rechnung“ senden wir Ihnen die Rechnung mit allen Zahlungsinformationen per E-Mail, sobald wir Ihre Ware geliefert haben. Sie überweisen uns anschließend ganz bequem den Rechnungsbetrag.\A\A";
        display: block;
        margin-bottom: 8px
    }

    .payolution-payment-addinfotext {
        color: #bd081c;
        display: block
    }

    .checkout-index-index .minicart-wrapper, .checkout-index-index .service-wrapper-top {
        display: none
    }

    .opc-block-summary .items-in-cart > .title {
        border-bottom: 1px solid #d3d3d3;
        display: none
    }

    #minicart-content-wrapper .block-content > .actions .action, #minicart-content-wrapper .block-content > .actions .btn-main {
        width: 100%
    }

    #minicart-content-wrapper .block-content > .actions .action:after {
        content: none
    }

    #minicart-content-wrapper .block-content > .actions > .primary {
        margin-left: 0;
        margin-right: 0
    }

    .checkout-index-index .page-header .header.content .minicart-wrapper {
        display: none
    }

    @media (min-width: 768px) and (max-width: 1400px) {
        .service-sidebar-inner .service-sidebar-item {
            position: relative;
            left: calc(100% - 45px);
            transition: .2s ease-in-out
        }

        .service-sidebar-inner .service-sidebar-item:hover {
            left: 0
        }
    }@media (min-width: 768px) and (max-width: 1200px) {
    header.page-header .header.content {
        padding: 0
    }

    header .sections .navigation > ul > li.level-top {
        padding: 0 10px;
        font-size: 18px
    }

    .fullwidth-break-inner span {
        margin-top: 0;
        display: block
    }

    .service-items-outer .service-items-item.service-item-trustedshops {
        font-size: 0;
        background: 0 0
    }

    .header .service-items-outer .service-items-item.service-item-trustedshops span, .service-items-outer .service-items-item.service-item-trustedshops img {
        display: none
    }

    .service-wrapper-inner .minicart-wrapper {
        margin-left: 0
    }

    .checkout-index-index .minicart-items .product-item-details {
        padding-left: 0
    }

    .sections .navigation .level1 > a > .submenu-category-usplist span {
        font-size: 15px
    }

    .header .service-items-outer .service-items-item.service-item-trustedshops {
        vertical-align: top
    }

    .payment-icons > * {
        padding-top: 10px;
        padding-bottom: 10px
    }
}@media (max-width: 590px) {
    .opc-info-bottom .home-header-info-bottom {
        padding-left: 50px;
        padding-top: 10px !important
    }

    .opc-info-bottom .home-header-info-bottom .sidebar-box.sidebar-hotline svg {
        left: 15px !important
    }
}@media (max-width: 939px) {
    .sections .navigation .level1 > a .submenu-category-price, .sections .navigation .level1 > a > .submenu-category-usplist {
        display: none
    }
}@media (max-width: 991px) {
    .header .service-items-outer .service-items-item.service-item-trustedshops > .service-item-inner, .service-top-items > label {
        display: none
    }
}@media (max-width: 992px) {
    .home-header-inner .home-header-info-top {
        margin-bottom: 10px
    }

    .home-header-inner .home-header-info-top .home-header-info-portrait img {
        top: 0;
        left: 0
    }
}@media (min-width: 768px) and (max-width: 1000px) {
    div[class^=amazonpay-button-parent-container] {
        width: 140px !important
    }
}@media (max-width: 767px) {
    .checkout-index-index .opc-sidebar {
        position: relative;
        opacity: 1;
        width: 100%
    }

    .checkout-index-index .opc-sidebar .modal-inner-wrap {
        visibility: visible;
        transform: none;
        overflow-y: unset
    }

    .checkout-index-index .opc-sidebar .modal-inner-wrap .checkout-info-block {
        margin: 0
    }

    .checkout-index-index .opc-sidebar .home-header-inner .home-header-info {
        box-shadow: none
    }

    .checkout-index-index .opc-sidebar .modal-inner-wrap .modal-content table tr, .checkout-index-index .opc-sidebar .modal-inner-wrap .modal-content table tr:not(.total-old-price) td {
        display: revert;
        padding: unset
    }

    .checkout-index-index .opc-sidebar .opc-info-bottom .opc-block-info {
        padding: 0
    }

    .checkout-index-index #opc-sidebar .opc-info-bottom .opc-block-info .home-header-info-top .home-header-info-portrait {
        position: absolute
    }

    .checkout-index-index .opc-sidebar .modal-inner-wrap .modal-content table tr.totals-tax-details {
        display: none
    }

    .page-header .header.content {
        min-height: 50px
    }

    .page-header .header.content .service-wrapper-container {
        height: auto
    }

    .page-wrapper {
        border-color: #fff
    }

    .service-wrapper-top {
        border: none;
        margin: 0
    }

    .service-wrapper-top .service-top-items {
        display: none
    }

    .header-language-row {
        float: left;
        margin-left: 13px;
        margin-bottom: 5px;
        display: none
    }

    .service-wrapper-inner {
        margin-top: 4px;
        display: table;
        width: 100%;
        padding: 0 15px
    }

    .service-wrapper-inner div.action.nav-toggle {
        position: static;
        display: table-cell;
        vertical-align: middle
    }

    .checkout-index-index .service-wrapper-inner div.action.nav-toggle {
        display: none
    }

    .checkout-index-index .opc-progress-bar .opc-progress-bar-item span {
        width: auto
    }

    .service-wrapper-inner .logo-container {
        display: table-cell;
        vertical-align: middle;
        float: none;
        width: auto
    }

    .service-wrapper-inner .service-items-outer {
        display: none
    }

    .minicart-wrapper a.action.minicart-action {
        display: inline-block;
        padding: 5px 22px 10px;
        width: 65px
    }

    .minicart-wrapper a.action.minicart-action > :not(img) {
        display: none
    }

    .page-header .header.content .minicart-wrapper {
        position: static;
        padding: 0;
        display: table-cell;
        vertical-align: middle;
        float: none;
        margin: 0;
        text-align: right
    }

    .page-header .nav-sections {
        top: 61px;
        z-index: 1000;
        height: calc(100% - 61px);
        background: #f5f5f5;
        margin-top: 0
    }

    .service-sidebar-outer {
        padding-top: 30px
    }

    .service-sidebar-inner {
        border-top: 1px solid #d1d1d1
    }

    .service-sidebar-inner .service-sidebar-item {
        border-radius: 0;
        margin-bottom: 0;
        box-shadow: none;
        border-bottom: 1px solid #d1d1d1
    }

    .service-sidebar-inner .service-sidebar-item span {
        font-size: 16px;
        text-transform: uppercase;
        font-weight: 600;
        color: #575757
    }

    .service-sidebar-inner .service-sidebar-item img {
        padding: 11px 10px
    }

    .page-header .nav-sections .submenu:not(:first-child) ul > li:not(:first-child) a {
        border-top: 1px solid #d1d1d1
    }

    .page-header .nav-sections .submenu ul > li a img {
        display: none
    }

    .home-header-inner .home-header-info {
        position: relative;
        max-width: none;
        top: 0
    }

    .home-header-inner .home-header-info-top .home-header-info-quote {
        text-align: left;
        margin-left: 155px;
        float: none;
        width: 45%
    }

    .home-header-inner .home-header-info-bottom::before {
        content: none
    }

    .home-header-inner .home-header-info-bottom {
        padding-top: 10px
    }

    .fullwidth-break-inner span {
        margin: 0;
        display: block
    }

    .footer-wrapper-border {
        margin-top: 90px
    }

    .page-title-wrapper h1.page-title {
        font-size: 30px;
        text-align: left;
        padding-left: 10px
    }

    .block-static-block.widget .block-container, .checkout-index-index .opc-wrapper > .opc {
        padding: 0
    }

    .checkout-index-index .column.main {
        padding: 0 15px
    }

    .opc-estimated-wrapper {
        padding-left: 0
    }

    .opc-estimated-wrapper .estimated-block {
        font-weight: 500;
        font-size: 18px;
        text-transform: capitalize
    }

    .checkout-index-index #shipping-method-buttons-container {
        padding-bottom: 15px
    }

    .checkout-index-index .modal-custom.opc-sidebar .modal-header {
        display: none
    }

    .checkout-index-index .modal-custom.opc-sidebar .modal-header button.action-close {
        right: 15px
    }

    .minicart-wrapper .action.showcart:before {
        position: relative
    }

    .service-items-outer.footer-service-outer .service-items-item.service-item-trustedshops {
        display: table;
        margin-left: auto;
        margin-right: auto
    }

    .checkout-index-index .opc-estimated-wrapper {
        display: none
    }

    .checkout-index-index .opc-wrapper {
        width: 100%;
        float: none !important;
        margin-bottom: 20px;
        margin-top: 20px
    }

    .checkout-index-index .payment-method .actions-toolbar button.checkout {
        width: 100%
    }

    .checkout-index-index .opc-block-summary {
        padding: 10px 20px;
        width: 100%
    }

    .checkout-payment-method .payment-method-paypal .paypal-buttons {
        width: 100% !important
    }

    .open-fax {
        top: 60px !important
    }

    .checkout-index-index .field[name='shippingAddress.telephone'] {
        margin-top: 0 !important
    }

    #amazon-payment .col-left, .checkout-payment-method .payment-method .actions-toolbar, .klarna-payments-method .payment-method-content .col-left, .klarna-payments-method .payment-method-content > div {
        width: 100%
    }

    .opc-block-summary .grand {
        height: 60px
    }

    .opc-block-summary {
        overflow: hidden
    }

    .opc-block-summary table.table-totals .totals-tax-details {
        bottom: -10px
    }

    .checkout-usps .checkout-header {
        display: none
    }

    #opc-shipping_method {
        margin-top: 20px
    }

    .minicart-items .product-image-wrapper .product-image-photo {
        padding-bottom: 15px
    }
}@media (max-width: 379px) {
    .opc-continue-button span {
        font-size: 13px
    }
}@media (min-width: 380px) {
    .opc-continue-button span {
        font-size: 17px
    }
}@media (max-width: 479px) {
    .fullwidth-break-inner {
        position: relative;
        margin-top: 40px
    }

    .fullwidth-break-inner span {
        letter-spacing: 4px;
        font-size: 15px;
        padding: 0
    }

    .fullwidth-break-inner span:first-of-type {
        position: absolute;
        top: -40px;
        left: 0;
        right: 0
    }

    .home-header-inner .home-header-info-top {
        padding: 10px 5px
    }

    .page-bottom {
        padding-bottom: 0
    }

    .page-title-wrapper h1.page-title {
        font-size: 25px
    }

    label[for=banktransfer]:after, label[for=payolution_elv]:after, label[for=payolution_invoice]:after, label[for=payone_creditcard]:after {
        background: 0 0
    }

    .payment-method.payolution .account-details input, .payment-method.payolution .company-details input {
        width: 100% !important
    }
}#backtotop {
     background: #667751;
     border: 2px solid #fff;
     border-radius: 5px;
     -webkit-box-shadow: 1px 2px 5px rgba(0, 0, 0, .3);
     box-shadow: 1px 2px 5px rgba(0, 0, 0, .3);
     color: #fff;
     cursor: pointer;
     font-size: 13px;
     font-weight: 600;
     height: 40px;
     line-height: 18px;
     padding: 10px 2px 2px;
     position: fixed;
     bottom: 20px;
     text-align: center;
     text-transform: uppercase;
     width: 230px;
     z-index: 9999;
     display: none;
     text-decoration: none;
     left: 50%;
     transform: translateX(-50%);
     opacity: .9
 }

    #backtotop span {
        padding: 0 3px
    }

    #backtotop .gt-arrow {
        padding-top: 8px
    }

    @media (max-width: 768px) {
        .opc-block-summary .product-item .product-item-inner {
            flex-direction: column
        }

        #backtotop {
            border-radius: 100%;
            height: 50px;
            width: 50px;
            left: calc(100% - 150px)
        }

        #backtotop span.gt-text {
            display: none
        }

        #backtotop .gt-arrow {
            padding-top: 6px;
            font-size: 16px;
            display: inline-block
        }

        #checkout-step-shipping, .billing-form-container {
            margin: 10px
        }

        .checkout-shipping-method {
            height: 85px
        }

        .shipping-button-container {
            padding-right: 10px;
            padding-left: 10px
        }

        .banktransfer-image-container, .payolution-elv-image-container, .payolution-image-container {
            position: absolute;
            width: 60px;
            right: 5px;
            top: 0
        }

        .creditcard-image-container {
            position: absolute;
            width: 55px;
            right: 5px;
            top: 0
        }

        .amazon-image-image, .paypal-image-image {
            position: absolute;
            right: 6px;
            top: 6px
        }

        .klarna-image-image {
            position: absolute;
            right: 15px;
            top: 15px
        }

        .klarna-payments-method-cell, .payment-method-label-container label span {
            margin-left: 10px
        }

        .payment-method {
            margin-top: 5px;
            margin-bottom: 5px
        }

        .payment-method .payment-method-title {
            border: 2px solid #eef4ed;
            border-radius: 5px
        }

        .payment-method._active .payment-method-title {
            border: 2px solid green
        }

        .amazon_payment_v2-payment-image, .banktransfer-image-container, .banktransfer-payment-image, .creditcard-image-container, .klarna_pay_later-payment-image, .payolution-elv-image-container, .payolution-image-container, .payolution_elv-payment-image, .payolution_invoice-payment-image, .payone_creditcard-payment-image, .paypal_express-payment-image {
            margin: auto
        }
    }@media (max-width: 640px) {
    #backtotop {
        left: 90%
    }
}.checkout-index-index .checkout-container .opc-wrapper .field-tooltip .label {
     display: none
 }

    .checkout-index-index .checkout-container .payment-method._active .payment-method-title .label:before {
        content: '\e904'
    }

    .checkout-index-index .checkout-container .payment-method.payment-method-paypal .label {
        width: 100%;
        height: 28px
    }

    .payment-icons img {
        margin: 0 5px
    }

    .details-deliveryweek {
        font-size: .8em
    }

    tr.totals.shipping.incl.free .amount {
        color: #667751;
        font-weight: 600;
        padding-left: 0;
        text-align: right
    }

    tr.totals.shipping.incl.free .amount, tr.totals.shipping.incl.free .mark {
        border-top: 1px solid #ccc
    }

    .checkout-index-index .checkout-container tr.totals.shipping.incl.free .mark, tr.totals.shipping.incl.free .mark {
        font-weight: 600
    }

    .checkout-index-index .checkout-container tr.totals.shipping.incl.free .amount, .checkout-index-index .checkout-container tr.totals.shipping.incl.free .mark {
        padding: 16px 0
    }

    .checkout-index-index .checkout-container tr.totals.shipping.incl.free {
        color: #2e2e2e;
        font-weight: 600;
        padding: 16px 0
    }

    .checkout-index-index .checkout-container tr.totals.shipping > * {
        border-top: 1px solid #ccc;
        border-bottom: 1px solid #ccc
    }

    .checkout-index-index .running-agreements {
        color: #636d70;
        font-size: 15px;
        display: inline-block;
        border-radius: 5px
    }

    .checkout-index-index .running-agreements button {
        font-size: 15px;
        min-height: 20px;
        display: inline;
        color: #636d70;
        text-decoration: underline
    }

    .checkout-index-index .payment-method-content .checkout-agreements-block .checkout-agreements {
        margin-bottom: 0
    }

    .checkout-index-index .payment-method-content .checkout-agreements-block {
        display: flex;
        flex-direction: row;
        justify-content: flex-end
    }

    .modal-custom.opc-sidebar .modal-inner-wrap > .modal-content {
        box-shadow: none;
        border: none;
        background: 0 0
    }

    .opc-block-summary table.table-totals .totals-tax-details {
        display: none
    }

    .opc-block-summary table.table-totals {
        position: relative
    }

    .minicart-wrapper .reduction .reduction-container .price-wrapper > span {
        color: #677752;
        font-weight: 500
    }

    .minicart-wrapper .reduction .reduction-container {
        color: #85b84b
    }

    .minicart-wrapper .reduction .original-price-container {
        text-decoration: line-through
    }

    .minicart-wrapper .reduction .regular-price > * {
        display: inline
    }

    .minicart-wrapper .reduction {
        text-align: right
    }

    .opc-block-summary .product-item .price {
        font-weight: 600;
        font-size: 15px
    }

    .checkout-index-index .product-item-name {
        display: inline-block
    }

    @media (max-width: 767px) {
        .shipping-address-container, .shipping-method-container {
            margin: 10px 0 !important
        }

        .opc-continue-button {
            margin: unset !important
        }

        .actions-toolbar > .primary {
            text-align: center;
            width: 100%
        }
    }span.submenu-category-name {
         white-space: nowrap
     }

    div#amredirect-popup {
        display: none
    }

    .amazon-button-container .field-tooltip {
        margin: 0;
        right: 0;
        top: 0
    }

    @media only screen and (max-width: 767px) {
        .payment-method {
            min-height: 60px
        }

        .payment-group-mobile {
            display: none
        }
    }.checkout-payment-method .payment-method .payment-method-content, .checkout-payment-method .payment-method .payment-method-content div.field {
         margin: 10px;
         font-size: 16px;
         line-height: 1.4
     }

    .checkout-payment-method .payment-method .actions-toolbar {
        margin: 20px 0
    }

    .checkout-payment-method .payment-method .amazon-button-container {
        margin-bottom: 0
    }

    .checkout-index-index .payment-method-content .checkout-agreements-block {
        margin: 0
    }

    .checkout-payment-method .payment-method.payolution .agreements, .checkout-payment-method .payment-method.payolution .running-agreements {
        margin-left: 10px
    }

    .checkout-payment-method .payment-method-content .creditcard_form select {
        width: 225px !important
    }

    .checkout-payment-method .payment-method.payolution .fieldset.checkout-agreements {
        margin-top: 0
    }

    .checkout-payment-method .payment-method-content .fieldset:not(:last-child), .checkout-payment-method .payolution-payment-addinfotext {
        margin-bottom: 8px
    }

    .checkout-payment-method .payment-method.payolution .min-age {
        font-size: 14px
    }

    .checkout-payment-method .payment-method.payolution .account-details input {
        margin-top: 0
    }

    .checkout-payment-method .payment-method.payolution .agreements br {
        display: none
    }

    .checkout-payment-method .payment-method-content input.input-text, .checkout-payment-method .payment-method-content input[type=text], .checkout-payment-method .payment-method-content select {
        height: 30px;
        margin-top: 0;
        padding: 2px 10px 1px
    }

    .checkout-payment-method .payment-method-content .fieldset > .field > .label {
        margin-bottom: 0;
        font-weight: 500
    }

    .checkout-payment-method .payment-method-content [type=checkbox]:checked + label:after, .checkout-payment-method .payment-method-content [type=checkbox]:checked + label:before, .checkout-payment-method .payment-method-content [type=checkbox]:not(:checked) + label:before {
        width: 18px;
        height: 19px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        margin: 0;
        left: 0
    }

    .checkout-payment-method .payment-method-content [type=checkbox]:checked + label, .checkout-payment-method .payment-method-content [type=checkbox]:not(:checked) + label {
        padding-left: 25px
    }

    .checkout-payment-method .payment-method-content .creditcard_form .fieldset > .field {
        margin-bottom: 10px
    }

    @media screen and (min-width: 768px) {
        .checkout-index-index #opc-sidebar .checkout-info-block {
            margin-bottom: 0
        }
    }.checkout-index-index #opc-sidebar .modal-content {
         overflow: visible
     }

    .checkout-index-index #opc-sidebar .home-header-inner .home-header-info {
        position: relative;
        max-width: unset;
        top: unset
    }

    .checkout-index-index #opc-sidebar .home-header-inner {
        display: flex;
        flex-direction: column
    }

    .checkout-index-index #opc-sidebar .home-header-inner .home-header-info-top {
        display: flex;
        flex-wrap: nowrap
    }

    .checkout-index-index #opc-sidebar .home-header-inner .home-header-info-top .home-header-info-quote {
        width: 100%;
        padding: 10px;
        float: none
    }

    .checkout-index-index #opc-sidebar .home-header-inner .home-header-info-top .home-header-info-portrait {
        position: relative;
        float: none;
        min-height: unset;
        width: auto;
        padding-right: 0
    }

    .checkout-index-index #opc-sidebar .home-header-inner .home-header-info-top .home-header-info-portrait img {
        left: unset;
        top: unset;
        position: relative;
        margin-top: -50px;
        margin-left: -25px;
        max-width: unset;
        height: 220px
    }

    .checkout-index-index #opc-sidebar .sidebar-hotline-number, .checkout-index-index #opc-sidebar .sidebar-support-email {
        display: flex;
        align-items: center;
        margin-bottom: 2px
    }

    @media (max-width: 992px) {
        .checkout-index-index #opc-sidebar .home-header-inner .home-header-info-top {
            flex-direction: column
        }

        .checkout-index-index #opc-sidebar .home-header-inner .home-header-info-top .home-header-info-portrait {
            width: 100%
        }
    }@media (max-width: 481px) {
    .checkout-index-index .opc-progress-bar li.opc-progressbar-delimiter {
        border: none !important
    }
}@media all and (min-width: 1024px), print {
    .payment-method-title-desktop {
        flex-basis: 19%
    }
}.hidden {
     display: none
 }

    @media (min-width: 768px), print {
        .payment-method-container {
            position: relative
        }

        .payment-method-label-container {
            text-align: center
        }

        .amazon-image-container, .paypal-image-container {
            text-align: center;
            height: 55px
        }

        .klarna-payments-method-cell {
            text-align: center;
            width: 100%
        }

        .klarna-payments-method-cell, .payment-method-label-container label span {
            font-size: 16px
        }

        .banktransfer-image-container {
            width: 100%
        }

        .payment-method {
            margin: 5px
        }

        .main-payment-title, .payment-method-title {
            display: none
        }

        .amazon_payment_v2-payment-image, .banktransfer-image-container, .banktransfer-payment-image, .creditcard-image-container, .klarna_pay_later-payment-image, .payolution-elv-image-container, .payolution-image-container, .payolution_elv-payment-image, .payolution_invoice-payment-image, .payone_creditcard-payment-image, .paypal_express-payment-image {
            margin: 8px auto
        }

        #customer-email-fieldset {
            position: relative
        }

        .email-field {
            width: 45% !important;
            position: absolute;
            margin-left: 1% !important
        }

        .open-fax {
            max-width: 44%;
            position: absolute;
            right: 8%;
            top: -30px;
            line-height: 15px;
            text-decoration: underline;
            cursor: pointer;
            text-align: right
        }

        .checkout-payment-method .payment-method._active .payment-method-content {
            flex-direction: row
        }

        .checkout-payment-method .payment-method._active .payment-method-content .col-left {
            width: 66%
        }

        .checkout-payment-method .payment-method._active .payment-method-content .col-right {
            width: 33%
        }

        .checkout-payment-method .payment-method-paypal .paypal-buttons {
            width: 33% !important
        }

        #amazon-payment .payment-method-content {
            margin-left: unset
        }

        .checkout-index-index .opc-block-summary {
            box-shadow: rgb(99 99 99 / 20%) 0 2px 8px 0;
            border-radius: 10px
        }

        .opc-block-summary table.table-totals .totals-tax-details {
            bottom: 5px
        }

        .checkout-usps {
            display: block;
            min-height: 110px
        }
    }.field[name="billingAddress.region_id"] {
         display: none !important
     }

    .billing-address-title {
        display: block !important
    }

    .billing-address-title, .shipping-address-title {
        border-bottom: unset !important;
        background-color: #eef4ed;
        min-height: 40px;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        margin-top: unset !important;
        padding-left: 20px;
        line-height: 40px;
        color: #2e2e2e
    }

    .shipping-address-container, .shipping-method-container {
        border: 1px solid #eef4ed;
        border-radius: 10px;
        box-shadow: rgb(99 99 99 / 20%) 0 2px 8px 0;
        height: max-content
    }

    .shipping-button-container {
        width: 100%;
        margin: auto
    }

    .shipping-button-container .opc-continue-button span {
        display: flex;
        align-items: center;
        justify-content: center
    }

    .shipping-button-container .opc-continue-button span:after {
        content: '\f061';
        font-family: 'Font Awesome 5 Free';
        margin-left: 10px;
        font-weight: 600;
        font-size: 2rem;
        color: #fff
    }

    .opc-continue-button {
        background: linear-gradient(180deg, #ffd119 0, #dcaa28 100%);
        border-radius: 5px;
        padding: 17px 15px;
        font-size: 17px;
        font-weight: 600;
        white-space: nowrap;
        width: min-content;
        float: right
    }

    .opc-continue-button span {
        font-weight: 600;
        white-space: nowrap
    }

    .payment-method div label::before {
        border: 1px solid #eef4ed
    }

    #payment {
        width: 100%;
        box-shadow: rgb(99 99 99 / 20%) 0 2px 8px 0;
        border: 1px solid #eef4ed;
        border-radius: 10px
    }

    .step-title-new {
        font-size: 16px;
        font-weight: 600;
        color: #2e2e2e;
        margin-left: 20px
    }

    .checkout-summary-title, .payment-methods-title {
        width: 100%;
        background-color: #eef4ed;
        min-height: 40px;
        line-height: 40px
    }

    .checkout-summary-summary, .payment-methods-methods {
        margin-right: 10px;
        margin-left: 10px
    }

    .checkout-summary-title span {
        font-size: 16px;
        font-weight: 600;
        color: #2e2e2e;
        margin-left: 20px
    }

    #choose-payment-method-button-wrapper {
        display: flex;
        justify-content: flex-end;
        align-items: center
    }

    .active-payment-block {
        border: 2px solid green
    }

    .payment-group-mobile {
        margin-bottom: 20px
    }

    .checkout-old-label {
        display: none
    }

    .checkout-index-index .column.main {
        background: unset !important
    }

    .checkout-index-index .page-bottom, .checkout-index-index .page-footer {
        display: none
    }

    .checkout-index-index .opc, .checkout-index-index .opc-wrapper {
        border: unset;
        box-shadow: unset
    }

    .grand.totals, .info-block-zero, .items-in-cart, .totals-tax-details {
        color: #2e2e2e
    }

    #shipping-new-address-form {
        position: relative
    }

    #customer-email-fieldset {
        z-index: 2
    }

    .opc-progress-bar-item span {
        color: #2e2e2e
    }

    .checkout-index-index #amazon-payment .checkout-agreements-block {
        margin-top: 20px
    }

    .checkout-index-index .amazon-button-container {
        padding-top: 10px
    }

    .ccard .month {
        margin-left: 0 !important
    }

    @media (min-width: 349px) {
        .ccard .year {
            margin-left: -12px !important
        }
    }@media (max-width: 348px) {
    .ccard .year {
        margin-left: unset !important;
        padding-left: unset !important
    }
}.checkout-index-index .open-fax {
     font-size: 1.5rem
 }

    @media (min-width: 800px) and (max-width: 939px) {
        .open-fax {
            font-size: 10px
        }

        .opc-block-summary .table-totals .grand .amount {
            font-size: 16px
        }

        .opc-block-summary .table-totals .grand .mark {
            font-size: 18px;
            padding-right: 10px;
            height: max-content
        }

        .opc-block-summary .table-totals .grand strong {
            font-size: 16px
        }
    }@media (min-width: 768px) and (max-width: 799px) {
    .open-fax {
        font-size: 9px
    }

    .opc-block-summary .table-totals .grand strong {
        font-size: 14px
    }
}@media (min-width: 350px) and (max-width: 767px) {
    .open-fax {
        font-size: 18px
    }
}@media (max-width: 349px) {
    .open-fax {
        font-size: 14px
    }
}@media (max-width: 767px) {
    .open-fax {
        line-height: 20px;
        text-decoration: underline;
        cursor: pointer;
        position: absolute;
        top: 60px
    }

    .checkout-index-index .field[name='shippingAddress.telephone'] {
        margin-bottom: 70px
    }

    .checkout-index-index div[name="shippingAddress.company"] {
        margin-bottom: 35px
    }
}#empty-block {
     height: 2px;
     display: none
 }

    .checkout-index-index .fieldset.address .field[name='shippingAddress.fax'] {
        display: none
    }

    #checkoutSteps {
        display: flex;
        padding: unset;
        border-radius: 10px;
        gap: 2rem
    }

    .checkout-index-index .actions-toolbar {
        margin-bottom: 20px
    }

    @media (max-width: 960px) {
        #checkoutSteps {
            flex-direction: column
        }

        .checkout-index-index:not(.payment-step) .opc-wrapper > .opc li {
            width: 100%
        }
    }.checkout-index-index .row-totals {
         font-size: 15px
     }

    .checkout-index-index .row-totals .old-price {
        text-decoration: line-through
    }

    .checkout-index-index .row-totals .save {
        color: #dcaa28;
        font-weight: 600;
        display: flex;
        justify-content: flex-end
    }

    .checkout-index-index .row-totals .save .label {
        font-weight: 500
    }

    .checkout-index-index .checkout-usps .mgz-icon-box-wrapper {
        height: 32px
    }

    .checkout-index-index .mgz-icon-box-size-xs .mgz-icon-box-element {
        font-size: 16px
    }

    @media (min-width: 1600px) {
        .checkout-index-index .checkout-header-second .mgz-image-hovers {
            min-width: 50px
        }
    }.fas {
         -moz-osx-font-smoothing: grayscale;
         -webkit-font-smoothing: antialiased;
         display: inline-block;
         font-style: normal;
         font-variant: normal;
         text-rendering: auto;
         line-height: 1;
         font-family: 'Font Awesome 5 Free';
         font-weight: 900
     }

    .mgz-fa-arrow-right:before {
        content: "\f061"
    }

    .mgz-fa-exclamation-triangle:before {
        content: "\f071"
    }

    [class*=mgz-col-] {
        width: 100%;
        float: left
    }

    .mgz-col-md-1, .mgz-col-md-12, .mgz-col-md-2, .mgz-col-md-3, .mgz-col-md-9, .mgz-col-xs-12 {
        position: relative;
        min-height: 1px;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box
    }

    .mgz-col-xs-12 {
        float: left;
        width: 100%
    }

    .mgz-container {
        overflow: auto;
        width: 1280px;
        max-width: 100%;
        margin: 0 auto
    }

    @media (min-width: 768px) {
        .mgz-col-md-1, .mgz-col-md-12, .mgz-col-md-2, .mgz-col-md-3, .mgz-col-md-9 {
            float: left
        }

        .mgz-col-md-12 {
            width: 100%
        }

        .mgz-col-md-9 {
            width: 75%
        }

        .mgz-col-md-3 {
            width: 25%
        }

        .mgz-col-md-2 {
            width: 16.66666667%
        }

        .mgz-col-md-1 {
            width: 8.33333333%
        }
    }@media (max-width: 575px) {
    .mgz-hidden-xs {
        display: none !important
    }
}@media (min-width: 576px) and (max-width: 767px) {
    .mgz-hidden-sm {
        display: none !important
    }
}@media (min-width: 768px) and (max-width: 991px) {
    .mgz-hidden-md {
        display: none !important
    }
}@media (min-width: 992px) and (max-width: 1200px) {
    .mgz-hidden-lg {
        display: none !important
    }
}@media (min-width: 1200px) {
    .mgz-hidden-xl {
        display: none !important
    }
}a.mgz-btn {
     color: #333
 }

    .mgz-btn:not(.primary) {
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none
    }

    .mgz-btn {
        -webkit-transition: .2s ease-in-out;
        -moz-transition: .2s ease-in-out;
        -ms-transition: .2s ease-in-out;
        -o-transition: .2s ease-in-out;
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        border-radius: 5px;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        margin: 0;
        display: inline-block;
        text-align: center;
        vertical-align: middle;
        cursor: pointer;
        background-image: none;
        word-wrap: break-word;
        text-decoration: none;
        position: relative;
        line-height: normal;
        padding: 10px 20px;
        color: #333;
        background-color: #e3e3e3;
        font-size: 1.4rem;
        max-width: 100%;
        height: auto
    }

    .mgz-btn:hover {
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
        color: #5e5e5e;
        background-color: #dcdcdc;
        text-decoration: none
    }

    .mgz-btn:focus {
        outline: 0
    }

    .mgz-icon-list .mgz-icon-list-item .mgz-icon-list-item-icon {
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
        padding: 3px
    }

    .mgz-icon-list-horizontal .mgz-icon-list-item {
        display: inline-block
    }

    .row {
        margin: 5px 0
    }

    .panel {
        display: block;
        background: #fff;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 3px;
        box-sizing: border-box;
        box-shadow: 0 1px 1px rgba(0, 0, 0, .05)
    }
}

/*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/CopeX_ExitIntent/css/exitintent.css */
.exit-intent-popup {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 10002;
    background: rgba(33, 33, 33, .8);
    transform: translateY(60%) scale(0);
    transition: transform .3s cubic-bezier(.4, 0, .2, 1);
    display: flex;
    justify-content: center;
    align-items: center
}

.exit-intent-popup .content {
    position: absolute;
    background: #fff;
    border-radius: 3px;
    max-height: 100%;
    overflow-y: auto
}

.exit-intent-popup .content > .close {
    position: absolute;
    right: 24px;
    top: 10px;
    width: 24px;
    height: 24px;
    cursor: pointer;
    z-index: 3;
    text-shadow: none;
    opacity: 1
}

.exit-intent-popup .content > .close:after, .exit-intent-popup .content > .close:before {
    position: absolute;
    left: 15px;
    content: ' ';
    height: 24px;
    width: 2px
}

.exit-intent-popup .content > .close:before {
    transform: rotate(45deg)
}

.exit-intent-popup .content > .close:after {
    transform: rotate(-45deg)
}

/*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/css/styles-l.css ; media=screen and (min-width: 768px) */
@media screen and (min-width: 768px) {
    .modal-popup .action-close, .modal-slide .action-close {
        padding: 0 30px 0 0
    }

    .modal-popup .action-close::before, .modal-slide .action-close::before {
        right: 30px;
        top: 50%;
        margin-top: -7px;
        color: inherit
    }

    .modal-popup .action-close:hover, .modal-slide .action-close:hover {
        color: #2f3943
    }

    .modal-popup .action-close > span, .modal-slide .action-close > span {
        clip: unset;
        height: auto;
        width: auto;
        line-height: 1.6rem;
        padding-right: 25px;
        position: relative;
        margin: 0;
        overflow: visible
    }

    @media all and (max-width: 768px) {
        .amazon-button-container {
            width: 100%
        }
    }.field-error {
         color: #e02b27;
         font-size: 1.2rem;
         margin-top: 7px
     }

    @media all and (min-width: 768px), print {
        .page-main .block {
            margin-bottom: 50px
        }

        .header.content:after, .header.content:before, .page-header .header.panel:after, .page-header .header.panel:before {
            content: '';
            display: table
        }

        .header.content:after, .page-header .header.panel:after {
            clear: both
        }

        .column.main, .opc-wrapper {
            box-sizing: border-box
        }

        .footer.content, .header.content, .navigation, .page-header .header.panel, .page-main, .page-wrapper > .page-bottom {
            box-sizing: border-box;
            margin-left: auto;
            margin-right: auto;
            max-width: 100%;
            padding-left: 20px;
            padding-right: 20px;
            width: auto
        }

        .page-main {
            width: 100%
        }

        .columns {
            display: block
        }

        .column.main {
            min-height: 300px
        }

        .page-layout-1column .column.main {
            width: 100%;
            -ms-flex-order: 2;
            -webkit-order: 2;
            order: 2
        }

        .panel.header {
            padding: 10px 20px
        }

        .nav-toggle {
            display: none
        }

        .nav-sections {
            -webkit-flex-shrink: 0;
            flex-shrink: 0;
            -webkit-flex-basis: auto;
            flex-basis: auto;
            margin-bottom: 25px
        }

        .nav-sections-item-title {
            display: none
        }

        .nav-sections-item-content {
            display: block !important
        }

        .nav-sections-item-content > * {
            display: none
        }

        .nav-sections-item-content > .navigation {
            display: block
        }

        .navigation {
            background: #f0f0f0;
            font-weight: 700;
            height: inherit;
            left: auto;
            overflow: inherit;
            padding: 0;
            position: relative;
            top: 0;
            width: 100%;
            z-index: 3
        }

        .navigation:empty {
            display: none
        }

        .navigation ul {
            margin-top: 0;
            margin-bottom: 0;
            position: relative;
            padding: 0 8px
        }

        .navigation li.level0 {
            border-top: none
        }

        .navigation li.level1 {
            position: relative
        }

        .navigation .level0 {
            margin: 0 10px 0 0;
            display: inline-block;
            position: relative
        }

        .navigation .level0:last-child {
            margin-right: 0;
            padding-right: 0
        }

        .navigation .level0 > .level-top {
            color: #fff;
            padding: 0 12px;
            text-decoration: none;
            box-sizing: border-box;
            position: relative;
            display: inline-block
        }

        .navigation .level0 > .level-top:hover {
            text-decoration: none
        }

        .navigation .level0.parent:hover > .submenu {
            overflow: visible !important
        }

        .navigation .level0.parent > .level-top {
            padding-right: 20px
        }

        .navigation .level0.parent > .level-top > .ui-menu-icon {
            position: absolute;
            right: 0;
            display: inline-block;
            text-decoration: none
        }

        .navigation .level0.parent > .level-top > .ui-menu-icon:after {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-size: 12px;
            line-height: 20px;
            color: inherit;
            content: '\e622';
            font-family: luma-icons;
            vertical-align: middle;
            display: inline-block;
            font-weight: 400;
            overflow: hidden;
            speak: none;
            text-align: center
        }

        .navigation .level0 .submenu {
            background: #fff;
            border: 1px solid #ccc;
            box-shadow: 0 5px 5px rgba(0, 0, 0, .19);
            font-weight: 400;
            min-width: 230px;
            padding: 0;
            display: none;
            left: 0;
            margin: 0 !important;
            position: absolute;
            z-index: 99999
        }

        .navigation .level0 .submenu a {
            display: block;
            line-height: inherit;
            color: #636d70;
            padding: 8px 20px
        }

        .navigation .level0 .submenu li {
            margin: 0
        }

        .legend {
            border-bottom: 1px solid #c5c5c5
        }

        .actions-toolbar {
            text-align: left
        }

        .actions-toolbar:after, .actions-toolbar:before {
            content: '';
            display: table
        }

        .actions-toolbar:after {
            clear: both
        }

        .actions-toolbar .primary {
            float: left
        }

        .actions-toolbar .primary, .actions-toolbar .secondary, .actions-toolbar .secondary a.action {
            display: inline-block
        }

        .actions-toolbar .primary .action {
            margin: 0 15px 0 0
        }

        .actions-toolbar .secondary a.action {
            margin-top: 6px
        }

        .actions-toolbar > .primary, .actions-toolbar > .secondary {
            margin-bottom: 0
        }

        .actions-toolbar > .primary .action, .actions-toolbar > .secondary .action {
            margin-bottom: 0;
            width: auto
        }

        .modal-popup.modal-slide .modal-footer {
            text-align: center
        }

        .minicart-wrapper {
            margin-left: 13px
        }

        .opc-wrapper {
            -ms-flex-order: 1;
            -webkit-order: 1;
            order: 1;
            padding-right: 30px
        }

        .opc-estimated-wrapper {
            display: none
        }

        .opc-progress-bar-item {
            width: 185px
        }

        .checkout-shipping-method .actions-toolbar > .primary {
            float: right
        }

        .table-checkout-shipping-method {
            width: auto
        }

        .opc-sidebar {
            margin: 46px 0 20px;
            width: 100%;
            float: right;
            -ms-flex-order: 2;
            -webkit-order: 2;
            order: 2
        }

        .opc-summary-wrapper .modal-header .action-close {
            display: none
        }

        .authentication-dropdown {
            background-color: #fff;
            border: 1px solid #aeaeae;
            -webkit-transform: scale(1, 0);
            -webkit-transform-origin: 0 0;
            -webkit-transition: -webkit-transform .1s linear, visibility 0s linear .1s;
            position: absolute;
            text-align: left;
            top: 100%;
            transform: scale(1, 0);
            transform-origin: 0 0;
            transition: transform .1s linear, visibility 0s linear .1s;
            visibility: hidden;
            width: 100%
        }

        .authentication-wrapper {
            width: 33.33333333%;
            text-align: right
        }

        .block-authentication .block-title {
            font-size: 2.6rem;
            border-bottom: 0;
            margin-bottom: 25px
        }

        .block-authentication .actions-toolbar > .primary {
            display: inline;
            float: right;
            margin-right: 0
        }

        .block-authentication .actions-toolbar > .primary .action {
            margin-right: 0
        }

        .block-authentication .actions-toolbar > .secondary {
            float: left;
            margin-right: 2rem;
            padding-top: 1rem
        }

        .checkout-payment-method .actions-toolbar .primary {
            width: 100%
        }

        .checkout-payment-method .actions-toolbar .primary button {
            width: 100% !important
        }

        .checkout-payment-method .payment-method-content .fieldset > .field {
            margin: 0 0 20px
        }

        body, html {
            height: 100%
        }

        .page-header {
            border: 0;
            margin-bottom: 0
        }

        .page-header .panel.wrapper {
            border-bottom: 1px solid #e8e8e8;
            background-color: #7e807e
        }

        .page-header .header.panel {
            padding-bottom: 10px;
            padding-top: 10px
        }

        .page-main > .page-title-wrapper .page-title {
            display: block
        }

        .header.content {
            padding: 30px 20px 0
        }

        .logo {
            margin: 0 auto 20px 0
        }

        .logo img {
            max-height: inherit;
            width: auto
        }

        .page-wrapper {
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-flex-direction: column;
            -ms-flex-direction: column;
            flex-direction: column;
            margin: 0;
            min-height: 100%;
            position: relative;
            transition: margin .3s ease-out
        }

        .page-footer {
            background: #fff;
            margin-top: auto;
            padding-bottom: 50px
        }

        .footer.content {
            border-top: none;
            background: #fff
        }

        .footer.content ul {
            padding-right: 50px
        }

        .footer.content .links li {
            background: 0 0;
            border: none;
            margin: 0 0 8px;
            padding: 0;
            line-height: normal
        }

        .footer.content .links a {
            display: inline
        }

        .checkout-index-index .opc-sidebar .opc-block-info .checkout-info-block:after {
            clear: both;
            content: "";
            display: table
        }
    }@media all and (min-width: 1024px), print {
    .checkout-index-index .opc-wrapper {
        width: 63%
    }

    .opc-sidebar {
        width: 36%
    }

    .checkout-index-index .modal-popup .modal-inner-wrap {
        margin-left: -400px;
        width: 800px;
        left: 50%
    }

    .table-checkout-shipping-method {
        min-width: 500px
    }
}.page-header {
     position: static;
     z-index: 100;
     max-width: 100%;
     background: #fff;
     width: 100%;
     height: auto;
     box-sizing: border-box;
     top: 0;
     transition: .4s;
     padding: 0 15px
 }

    .page-header .panel.wrapper {
        background-color: #fff;
        border-bottom: none;
        margin: 0
    }

    .page-header .header.content {
        padding: 0
    }

    .checkout-index-index .page-header {
        position: static;
        box-shadow: none
    }

    .checkout-index-index .page-header .header.content {
        position: static;
        padding-top: 0;
        margin: 0 auto
    }

    a.logo::before {
        border: none
    }

    .page-header .header.content .minicart-wrapper {
        position: static;
        z-index: 10001
    }

    .minicart-wrapper .counterHolder {
        width: 40px;
        height: 40px;
        background: #000;
        border-radius: 20px;
        display: block
    }

    .minicart-wrapper .counterHolder.hasItem {
        background: #85b84b
    }

    .minicart-wrapper .action.showcart {
        position: relative
    }

    .minicart-wrapper .action.showcart::before {
        content: '\e908';
        color: #fff;
        top: 6px;
        left: 2px;
        position: absolute;
        font-size: 2.3rem
    }

    .minicart-wrapper .action.showcart:hover::before {
        color: #fff
    }

    .minicart-wrapper .action.showcart .counter.qty {
        border-radius: 12px;
        position: absolute;
        top: -13px;
        right: -9px;
        background: #000;
        min-width: 24px;
        box-sizing: border-box;
        height: 24px;
        display: block
    }

    .minicart-wrapper a::before {
        border-bottom: none
    }

    .footer-wrapper {
        position: relative
    }

    .footer-wrapper .footer-column.links {
        width: 25%
    }

    .footer.content {
        padding: 0
    }

    .footer.content .links a {
        color: inherit;
        font-size: 16px;
        margin: 0;
        padding: 0
    }

    .footer.content .links a:hover {
        color: #85b84b;
        text-decoration: none
    }

    .footer.content .links li {
        margin: 0 0 3px
    }

    .footer.content .links p {
        font-size: 16px
    }

    .footer-headline {
        font-size: 20px;
        display: inline-block;
        font-weight: 500
    }

    .copyright {
        display: block;
        background: 0 0;
        margin: 10px 0 0
    }

    .footer.bottom {
        background-color: #fff;
        text-align: center;
        padding: 18px 0
    }

    .footer.bottom > .container {
        border-top: 2px solid #eee
    }

    @media (min-width: 768px) {
        .nav-sections {
            background: #fff;
            position: relative;
            margin-bottom: 0
        }

        .sections.nav-sections {
            background: #677752;
            margin: 0 -15px
        }

        .sections .navigation {
            background: #677752;
            font-size: 2rem;
            font-weight: 400
        }

        .sections .navigation a::before {
            border-bottom: none
        }

        .sections .navigation a .ui-menu-icon {
            display: none
        }

        .sections .navigation > ul {
            position: static;
            padding: 0;
            width: 100%
        }

        .sections .navigation > ul > li {
            position: static
        }

        .sections .navigation > ul > li.level-top {
            line-height: 2;
            display: inline
        }

        .sections .navigation > ul > li + li {
            padding-left: 10px
        }

        .sections .navigation > ul > li a {
            text-decoration: none
        }

        .sections .navigation li.level0 {
            position: static;
            padding: 0 15px;
            margin: 0
        }

        .sections .navigation li.level0 a, .sections .navigation li.level0 > a.level-top {
            padding: 0
        }

        .sections .navigation li.level0 > a.level-top .ui-menu-icon {
            display: none
        }

        .sections .navigation li.level0 ul.level0.submenu {
            color: inherit;
            border: none;
            box-shadow: 0 5px 5px -5px rgba(0, 0, 0, .1);
            list-style: none;
            position: absolute;
            width: 100%;
            left: 0 !important;
            top: 40px !important;
            flex-wrap: wrap;
            font-weight: 400
        }

        .sections .navigation li.level0 ul.level0.submenu a span:not(.ui-icon) {
            position: relative
        }

        .sections .navigation li.level0 ul.level0.submenu a span:not(.ui-icon)::before {
            display: block;
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 93%
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1 {
            max-width: 25%;
            flex: 1 0 25%;
            padding: 0;
            position: relative
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1::before {
            border-right: 1px solid #e2e2e2;
            display: block;
            content: none;
            position: absolute;
            top: 20px;
            bottom: 0;
            right: 0;
            width: 1px;
            z-index: 10
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1:nth-child(4)::before {
            border-right: none
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1 img {
            margin-top: 10px;
            width: 100%
        }

        .sections .navigation li.level0 ul.level0.submenu li.level1 a {
            padding: 10px 5px 0
        }

        .sections .navigation li.level0 ul.level0.submenu .submenuWrapper {
            display: flex !important;
            top: auto !important;
            flex-wrap: wrap;
            padding: 0;
            width: 100%
        }

        .sections .navigation li.level0 ul.level0.submenu > li {
            max-width: 1570px;
            margin: 0 auto;
            width: 100%
        }

        .nav-sections .nav-sections-items {
            max-width: 1570px;
            margin: 0 auto;
            padding-bottom: 0
        }

        .nav-sections-item-content > .navigation {
            display: flex;
            position: static
        }

        .nav-sections-item-content .page-service-tabs {
            position: fixed;
            right: 0;
            transform: none;
            width: auto;
            display: flex;
            top: 20%;
            z-index: 1000
        }

        .nav-sections-item-content .page-service-tabs .tabs-container {
            transform: none
        }
    }.header.content {
         max-width: 1570px;
         margin: 0 auto
     }

    .page-wrapper {
        display: block
    }

    .page-main {
        padding: 0
    }

    .panel.header {
        display: none
    }

    .block-cms-link.widget, .block-static-block.widget {
        margin: 0
    }

    .block-static-block.widget .block-container {
        max-width: 1570px;
        margin: 0 auto
    }

    .block-static-block.wide .block-container {
        margin: auto;
        max-width: none
    }

    .columns {
        display: block
    }

    .columns .column.main {
        background: #f5f5f5;
        padding: 0 15px
    }

    @media (min-width: 992px) {
        .col-md-6 {
            width: 50%;
            float: left;
            position: relative;
            min-height: 1px;
            padding-right: 15px;
            padding-left: 15px
        }
    }#notice-cookie-block {
         box-shadow: 0 5px 5px 5px rgba(0, 0, 0, .1)
     }

    #notice-cookie-block .content {
        max-width: 1570px;
        margin: 0 auto;
        text-align: center
    }

    .opc-progress-bar {
        padding-top: 0;
        padding-bottom: 0
    }

    .opc-progress-bar .opc-progress-bar-item {
        width: 33%;
        padding-bottom: 5px
    }

    .opc-progress-bar .opc-progress-bar-item span {
        padding-top: 0
    }

    .opc-progress-bar .opc-progress-bar-item._complete span {
        font-weight: 500
    }

    .product-item-photo::before, .product-item-photo:hover::before {
        border-bottom: none
    }

    .product-item-name a {
        font-weight: 700
    }

    .product-item-name a:hover {
        text-decoration: none;
        color: #85b84b
    }

    .product-item-name a::before {
        border-bottom: none
    }

    .opc-block-summary {
        background: #fff
    }

    .checkout-index-index #checkout {
        max-width: 1440px;
        margin: 0 auto
    }

    .checkout-index-index .opc-sidebar .opc-block-info {
        background: #fff;
        margin: 0;
        padding: 22px 30px
    }

    .checkout-index-index .opc-sidebar .opc-block-info p + p {
        margin: 0
    }

    .checkout-index-index .billing-address-same-as-shipping-block {
        margin: 30px
    }

    #maincontent #checkout .authentication-wrapper button, .checkout-index-index .checkout-shipping-method .step-title, .checkout-index-index .checkout-shipping-method .table-checkout-shipping-method {
        display: none
    }

    #payment #cardexpiremonth iframe, #payment #cardexpireyear iframe {
        height: 30px
    }

    #payolution_elv_dob_day {
        padding-right: 10px
    }

    form fieldset#customer-email-fieldset .field .note {
        display: none !important
    }

    .checkout-agreements-block {
        margin: 20px 0
    }

    .checkout-info-block {
        margin: 0 0 15px
    }

    .checkout-info-block p + p {
        margin: 0
    }

    .message.global.cookie {
        background: #fff;
        font-size: 15px;
        z-index: 100000
    }

    .message.global.cookie a {
        color: #2f3943
    }

    .message.global.cookie a:hover {
        color: #85b84b
    }

    @media (max-width: 767px) {
        h1 {
            font-size: 3.2rem;
            line-height: 3.2rem
        }

        h6 {
            font-size: 1.8rem;
            line-height: 2rem;
            margin: 16px 0
        }

        .logo {
            float: none
        }

        .logo img {
            width: 80px;
            float: left;
            padding: 10px 0;
            margin-left: 10px
        }

        .page-header .header.content {
            padding-top: 0
        }

        .page-header .header.content .service-wrapper-container {
            position: relative;
            height: 50px;
            background: #f5f5f5;
            z-index: 150
        }

        .page-header .header.content .minicart-wrapper {
            right: 10px;
            top: -5px;
            z-index: inherit
        }

        .nav-toggle {
            right: 17px;
            left: auto;
            top: 63px;
            position: absolute
        }

        .minicart-wrapper .action.showcart .counter.qty {
            top: -7px
        }

        .minicart-wrapper .action.showcart .counterHolder.hasItem .counter.qty {
            background: #85b84b
        }

        .block-static-block.widget .block-container {
            padding: 0 20px
        }

        .navigation {
            background: #fff
        }

        .navigation a::before {
            border-bottom: none
        }

        .navigation .submenuWrapper {
            display: block !important;
            padding: 0
        }

        .navigation a {
            padding: 10px
        }

        .navigation .submenu:not(:first-child) ul {
            padding-left: 0
        }

        .nav-sections-item-title {
            display: none
        }

        .nav-sections-item-content {
            margin-top: 0;
            margin-left: 0;
            padding: 0
        }

        .nav-sections-item-content#store\.quicklinks {
            display: block !important
        }

        .navigation .parent .level-top:after {
            right: 17px
        }

        .nav-sections-items {
            padding-bottom: 20px
        }

        .page-footer .footer.content {
            border-top: none
        }

        .page-footer .footer-wrapper {
            flex-direction: column;
            padding: 50px 0;
            background: #f5f5f5
        }

        .page-footer .footer-wrapper .footer-column {
            padding: 30px 20px;
            text-align: center
        }

        .page-footer .footer-wrapper .footer-column.links::after {
            display: none
        }

        .page-footer .footer-wrapper .footer-column ul li a {
            display: inline-block
        }

        .page-layout-1column a.action, .page-layout-1column button.action {
            width: 100%
        }

        .actions-toolbar > .primary {
            text-align: left
        }

        .checkout-index-index .opc-wrapper {
            width: 100%;
            box-sizing: border-box
        }

        .checkout-index-index .opc-sidebar {
            width: 100%
        }

        .checkout-index-index .page-header .header.content .service-wrapper-container {
            height: auto;
            padding: 0
        }

        .checkout-index-index .minicart-wrapper .action.showcart .counterHolder.hasItem .counter.qty {
            background: #000
        }

        .checkout-index-index .page-header {
            margin-bottom: 0;
            border-bottom: none
        }

        .opc-estimated-wrapper {
            margin: 0
        }

        #opc-shipping_method {
            padding: 0 20px
        }
    }@media (min-width: 768px) and (max-width: 1200px) {
    .opc-progress-bar, .sections.nav-sections {
        padding: 0
    }

    .page-header .header.content {
        padding: 30px 10px 0
    }
}
}

/*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/css/print.css ; media=print */
@media print {
    @media print {
        * {
            -webkit-filter: none !important;
            background: 0 0 !important;
            color: #000 !important;
            filter: none !important;
            text-shadow: none !important
        }

        a, a:visited {
            text-decoration: underline !important
        }

        thead {
            display: table-header-group
        }

        img, tr {
            page-break-inside: avoid
        }

        img {
            max-width: 100% !important
        }

        .block-content, p {
            orphans: 3;
            widows: 3
        }

        .block-content {
            page-break-before: avoid
        }

        .block-title {
            page-break-after: avoid
        }

        .nav-toggle {
            display: none !important
        }

        .footer.content > [class], .header.content > [class], .nav-sections, .panel.wrapper > [class] {
            display: none
        }

        .footer .copyright, .logo {
            display: block !important;
            margin: 10px 0
        }

        .column.main {
            float: none !important;
            width: 100% !important
        }

        .footer.content {
            padding: 0
        }
    }
}

/*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Magezon_PageBuilderIconBox/css/styles.css */
.mgz-icon-box-wrapper {
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    display: inline-block;
    line-height: 0;
    position: relative
}

.mgz-icon-box-wrapper .mgz-icon-box-element {
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    position: absolute;
    top: 50%;
    left: 50%
}

.mgz-icon-box-size-xs {
    width: 2.5em;
    height: 2.5em
}

.mgz-icon-box-size-xs .mgz-icon-box-element {
    font-size: 1.2em
}

.mgz-heading-text {
    margin: 0 0 10px;
    color: inherit
}

.btn-position {
    display: inline-block;
    width: 100%
}

.mgz-icon-box-left {
    width: 30%;
    margin: auto
}

.mgz-description {
    margin-bottom: 25px
}

/*! CSS Used from: http://www.gfp.test/static/frontend/Gfp/international/de_DE/Payolution_Payments/css/payolution.css ; media=screen */
@media screen {
    @media (min-width: 768px) {
        .payment-method.payolution select.dob-day {
            width: 65px
        }

        .payment-method.payolution select.dob-month {
            width: 150px
        }

        .payment-method.payolution select.dob-year {
            width: 100px
        }
    }@media (max-width: 767px) {
    .payment-method.payolution select.dob-day, .payment-method.payolution select.dob-month, .payment-method.payolution select.dob-year {
        width: 30%
    }
}.payment-method.payolution .method-unavailable {
    color: red;
    font-size: .8em
}

    .payment-method.payolution .account-details input, .payment-method.payolution .company-details input {
        width: 400px;
        margin-top: 5px
    }

    .payment-method.payolution .account-details label, .payment-method.payolution .company-details label {
        display: block;
        margin-top: 10px
    }

    .payment-method.payolution .fieldset {
        margin-top: 10px
    }

    .payment-method.payolution .fieldset .field {
        margin: 0 0 10px
    }
}

/*! CSS Used from: Embedded */
.checkout-index-index .checkout-shipping-address #checkout-step-shipping .form.form-shipping-address div.field[name*=region], .checkout-index-index fieldset#billing-new-address-form div[name="billingAddress.region"] {
    display: none
}

.page-main, .page-wrapper {
    background: #fff
}

@media (min-width: 584px) {
    h1 {
        font-weight: 600;
        text-transform: uppercase;
        font-size: 2.4rem;
        margin-bottom: 0 !important;
        margin-top: 0 !important
    }
}

@media (max-width: 583px) {
    h1 {
        font-weight: 500 !important;
        font-size: 1.8rem !important;
        margin-bottom: 0;
        margin-top: 0
    }
}

body {
    font-size: 18px
}

div.mgz-icon-list-item {
    padding-left: 21px;
    text-indent: -11px
}

div.mgz-element.mgz-child.mgz-element-icon_list {
    padding-top: 10px
}

@media (max-width: 783px) {
    .mgz-child:not(:last-child) > .mgz-element-inner, .mgz-element-column > .mgz-element-inner {
        margin-bottom: 0;
        padding: 0
    }

    header.page-header {
        margin: 0
    }
}

.full_width_row {
    padding-left: 6px
}

.my-2 {
    margin-top: 0;
    margin-bottom: 0
}

/*! CSS Used from: Embedded */
@media (min-width: 1550px) {
    .magezon-builder .mgz-container {
        width: 1550px
    }
}

/*! CSS Used from: Embedded */
.lgfyfk0-s {
    padding: 2% !important;
    background-color: #b91c1c !important
}

.mgz-element.lgfyfk0 .mgz-icon-list-item {
    margin-right: 15px
}

.mgz-element.lgfyfk0 .mgz-icon-list-item-icon {
    font-size: 22px;
    color: #fff
}

.mgz-element.lgfyfk0 .mgz-icon-list-item-text {
    font-size: 22px;
    color: #fff;
    font-weight: 500
}

.ynyevxl-s {
    padding-right: 1% !important;
    padding-left: 1% !important
}

.xfdaeja-s {
    text-align: center;
    margin-top: 2% !important
}

.q84ooub-s {
    text-align: center
}

.g69cwha {
    z-index: 999999999
}

.g69cwha-s {
    text-align: center
}

.nkxl6b1-s, .wd8gv77-s {
    padding: 0 !important
}

.klr3lkw-s {
    padding-top: 0 !important
}

.wd8gv77 > .mgz-element-inner {
    padding: 0;
    margin: 0
}

.iueiyaf-s {
    text-align: center
}

.oskowgn-s {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
    background-color: #f2f2f2 !important
}

.eareluu-s, .y78ayvb-s {
    text-align: center
}

.d6w4paf-s {
    background-color: #f2f2f2 !important
}

.mp4nsol-s {
    text-align: center
}

.r12makd-s {
    text-align: center;
    margin-top: 1% !important
}

.q4u7j83-s {
    text-align: center;
    margin-bottom: 2% !important
}

.usrtomv-s {
    margin-top: -3px !important;
    background-color: #f2f2f2 !important
}

.aa75tlx > .mgz-element-inner, .ybvnut8 > .mgz-element-inner {
    padding: 0;
    margin: 0
}

.mgz-element.i04cd0c > .mgz-element-inner > .inner-content {
    width: 900px;
    max-width: 100%;
    margin: 0 auto
}

.heading-vorauskasse {
    color: #4c4c4c !important;
    font-size: 45px;
    text-align: center;
    line-height: 1
}

.heading-tipp {
    color: #4c4c4c !important;
    font-size: 30px;
    text-align: center
}

.mgz-element-inner.andi-image {
    padding: 0 !important;
    margin-top: -15%
}

.mgz-element.mgz-element-row.full_width_row {
    padding-left: 0
}

button.banktransfer {
    background: linear-gradient(180deg, #ffd119 0, #dcaa28 100%);
    border-radius: 5px;
    margin: 0;
    color: #fff;
    font-size: 20px;
    font-weight: 500;
    text-align: center !important;
    padding: 12px
}

button.close {
    border: 2px solid #dcaa28;
    color: #dcaa28;
    background-color: #f2f2f2;
    border-radius: 5px;
    padding: 12px;
    font-weight: 500;
    font-size: 20px;
    opacity: 1;
    margin-left: 5px
}

.align-center {
    display: flex;
    justify-content: center;
    padding: 10px 0
}

.icon-list-styling {
    padding-top: 0 !important
}

.exit-intent-popup.payment-error .content > .close:after, .exit-intent-popup.payment-error .content > .close:before {
    background: #fff
}

.mgz-element.oh43bv5 .mgz-icon-box-wrapper {
    color: #fff
}

.mgz-element.oh43bv5 .mgz-heading-text {
    color: #fff;
    line-height: 2
}

.mgz-element.vngwmy2 .mgz-icon-box-wrapper {
    color: #fff
}

.mgz-element.vngwmy2 .mgz-heading-text {
    color: #fff;
    line-height: 2
}

.mgz-element.pqphfgt .mgz-icon-box-wrapper {
    color: #fff
}

.mgz-element.pqphfgt .mgz-heading-text {
    color: #fff;
    line-height: 2
}

.mgz-element.r8fopl7 .mgz-icon-box-wrapper {
    color: #fff
}

.mgz-element.r8fopl7 .mgz-heading-text {
    color: #fff;
    line-height: 2
}

.xd1vbmw-s {
    background-color: #e6b733 !important
}

.mgz-element.xd1vbmw > .mgz-element-inner > .inner-content {
    width: 1440px;
    max-width: 100%;
    margin: 0 auto
}

.enrg3tn-s {
    margin-top: 20px !important;
    margin-right: 10px !important;
    margin-left: 10px !important
}

.oujayp0-s, .p0jco6c-s, .qsypfd4-s, .r4dctfv-s {
    text-align: right;
    margin-right: 10px !important
}

.g029i3p-s {
    margin-top: 20px !important;
    margin-right: 10px !important;
    margin-left: 20px !important
}

.v2we6ns {
    float: right
}

.hgqgc65-s {
    margin-top: 10px !important;
    margin-left: 30px !important
}

.mgz-element.hh5w2bg > .mgz-element-inner > .inner-content {
    width: 1440px;
    max-width: 100%;
    margin: 0 auto
}

.magezon-builder .mgz-container {
    width: 100%
}

.checkout-header .mgz-icon-box-wrapper {
    min-width: 50px
}

.checkout-header .btn-position {
    white-space: nowrap
}

@media (max-width: 1023px) {
    .checkout-header .mgz-icon-box-container {
        flex-direction: column;
        align-items: center;
        text-align: center
    }

    .checkout-header .mgz-description {
        display: none
    }
}

@media (min-width: 768px) {
    .service-wrapper-inner {
        justify-content: center
    }
}

@media (max-width: 1259px) {
    .checkout-header .btn-position {
        white-space: normal
    }
}

@media (min-width: 1260px) {
    .magezon-builder .mgz-container {
        width: 100%
    }
}

.info-cont .mgz-heading-text {
    font-size: 14px;
    height: 100%
}

.info-cont, .info-cont .mgz-icon-box-container {
    max-height: 40px !important;
    width: max-content !important;
    margin-right: .5%;
    margin-left: .5%
}

.info-cont .btn-position {
    margin-top: 1px
}

.info-cont .mgz-icon-box-left {
    min-width: 30px !important
}

.checkout-second-block .mgz-element-inner {
    display: flex
}

.checkout-header-second {
    font-size: 1.5rem;
    line-height: 1.8rem
}

#item-1 {
    min-width: 160px
}

#item-2 {
    min-width: 150px
}

#item-3 {
    min-width: 185px
}

#item-4 {
    min-width: 170px
}

.mgz-description {
    margin-bottom: unset
}

.checkout-header .mgz-heading-text {
    margin-bottom: 0;
    width: max-content
}

.mgz-icon-box-container {
    display: flex;
    flex-direction: unset !important
}

.checkout-index-index .checkout-second-block .mgz-element-inner {
    display: block
}

.checkout-usps {
    box-shadow: rgb(99 99 99 / 20%) 0 0 7px 0;
    margin-bottom: 4px
}

.popup_button {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    background: #ffd119;
    background: linear-gradient(180deg, #ffd119 0, #dcaa28 100%);
    border-radius: 5px;
    margin: 0 4%;
    color: #fff;
    font-size: 20px;
    font-weight: 500;
    text-align: center
}

.popup_button > span {
    margin-left: 10px;
    font-weight: 600;
    color: #fff
}

/*! CSS Used from: Embedded */
._t53mel {
    -webkit-box-pack: inherit !important;
    background: 0 0 !important;
    padding: 0 !important;
    position: static !important;
    word-break: normal !important;
    margin: 0 !important;
    font-size: inherit !important;
    font-family: inherit !important;
    font-weight: inherit !important;
    color: inherit !important;
    line-height: inherit !important;
    text-align: inherit !important;
    width: auto !important;
    float: none !important;
    display: flex !important;
    border: none !important;
    box-shadow: none !important;
    box-sizing: content-box !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    justify-content: inherit !important;
    white-space: normal !important
}

._1c4uh60 {
    background: #fff !important;
    padding: 8px 0 !important;
    position: fixed !important;
    word-break: normal !important;
    margin: 0 !important;
    font-size: inherit !important;
    font-family: sans-serif !important;
    font-weight: 400 !important;
    color: inherit !important;
    line-height: normal !important;
    text-align: inherit !important;
    width: 93px !important;
    float: none !important;
    display: block !important;
    border: none !important;
    box-shadow: rgba(0, 0, 0, .1) 0 2px 9px 4px !important;
    box-sizing: content-box !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    height: auto !important;
    border-radius: 8px !important;
    inset: auto 20px 14px auto !important;
    transition: opacity .2s linear !important;
    z-index: 0 !important;
    cursor: pointer !important;
    -webkit-font-smoothing: auto !important;
    opacity: 1 !important;
    visibility: visible !important
}

._1c4uh60:hover {
    border: 1px solid #ffdc0f !important;
    box-shadow: rgba(0, 0, 0, .1) 0 2px 9px 2px !important;
    inset: auto 19px 13px auto !important;
    transition: none !important
}

@media only screen and (max-width: 648px) {
    ._1c4uh60 {
        inset: auto 10px 10px auto !important;
        width: 60px !important;
        border-radius: 68px !important;
        padding: 4px !important
    }

    ._1c4uh60:hover {
        inset: auto 9px 9px auto !important
    }
}

._ov91zl {
    background: 0 0 !important;
    padding: 0 0 10px !important;
    position: absolute !important;
    word-break: normal !important;
    font-size: 16px !important;
    font-family: inherit !important;
    font-weight: inherit !important;
    color: #4d4d4d !important;
    line-height: normal !important;
    text-align: inherit !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    display: block !important;
    float: none !important;
    margin: 0 !important;
    width: auto !important;
    right: 6px !important;
    top: -5px !important;
    cursor: pointer !important
}

@media only screen and (max-width: 648px) {
    ._ov91zl {
        display: none !important
    }
}

._v43da2 {
    background: 0 0 !important;
    padding: 0 !important;
    position: static !important;
    word-break: normal !important;
    margin: 0 !important;
    font-size: inherit !important;
    font-family: inherit !important;
    font-weight: 400 !important;
    color: inherit !important;
    line-height: inherit !important;
    text-align: inherit !important;
    display: inline !important;
    width: auto !important;
    float: none !important;
    text-transform: none !important;
    letter-spacing: normal !important
}

._1tcd14j {
    background: 0 0 !important;
    padding: 3px 0 0 !important;
    position: static !important;
    word-break: normal !important;
    margin: 0 !important;
    font-size: inherit !important;
    font-family: inherit !important;
    font-weight: 400 !important;
    color: inherit !important;
    line-height: inherit !important;
    text-align: center !important;
    width: auto !important;
    float: none !important;
    display: block !important;
    border: none !important;
    box-shadow: none !important;
    box-sizing: content-box !important;
    text-transform: none !important;
    letter-spacing: normal !important
}

@media only screen and (max-width: 648px) {
    ._1tcd14j {
        padding: 3px 0 0 !important
    }
}

._yq12oi {
    display: block !important;
    overflow: auto !important;
    position: static !important;
    word-break: normal !important;
    font-size: inherit !important;
    font-family: inherit !important;
    font-weight: inherit !important;
    text-align: inherit !important;
    color: inherit !important;
    background: 0 0 !important;
    padding: 0 14px !important;
    margin: auto !important;
    float: none !important;
    line-height: inherit !important;
    height: 50px !important;
    width: 50px !important;
    max-width: none !important;
    max-height: none !important;
    box-sizing: content-box !important
}

@media only screen and (max-width: 648px) {
    ._yq12oi {
        margin: 0 !important;
        padding: 0 4px !important;
        border-radius: 100px !important;
        width: 52px !important;
        height: 52px !important
    }
}

._1udgpwx {
    background: 0 0 !important;
    padding: 0 !important;
    position: static !important;
    word-break: normal !important;
    font-size: 13px !important;
    font-family: inherit !important;
    font-weight: 400 !important;
    color: #000 !important;
    line-height: 15px !important;
    text-align: center !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    display: block !important;
    float: none !important;
    margin: 5px 0 !important;
    width: auto !important
}

@media only screen and (max-width: 648px) {
    ._1udgpwx {
        display: none !important
    }
}

._18uerla {
    background: #e5e5e5 !important;
    padding: 0 !important;
    position: static !important;
    word-break: normal !important;
    margin: auto !important;
    font-size: inherit !important;
    font-family: inherit !important;
    font-weight: inherit !important;
    color: inherit !important;
    line-height: inherit !important;
    text-align: inherit !important;
    width: 77px !important;
    float: none !important;
    display: block !important;
    border: none !important;
    box-shadow: none !important;
    box-sizing: border-box !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    height: 1px !important
}

@media only screen and (max-width: 648px) {
    ._18uerla {
        display: none !important
    }
}

._15odmu7 {
    background: 0 0 !important;
    padding: 5px 0 3px !important;
    position: static !important;
    word-break: normal !important;
    margin: auto !important;
    font-size: 16px !important;
    font-family: inherit !important;
    font-weight: 400 !important;
    color: inherit !important;
    line-height: inherit !important;
    text-align: center !important;
    width: 100% !important;
    float: none !important;
    display: block !important;
    border: none !important;
    box-shadow: none !important;
    box-sizing: border-box !important;
    text-transform: none !important;
    letter-spacing: normal !important
}

@media only screen and (max-width: 648px) {
    ._15odmu7 {
        width: auto !important;
        padding: 0 0 1px !important
    }
}

._1wkl6o {
    background: 0 0 !important;
    padding: 0 !important;
    position: relative !important;
    word-break: normal !important;
    margin: 0 !important;
    font-size: 16px !important;
    font-family: inherit !important;
    font-weight: inherit !important;
    color: inherit !important;
    line-height: normal !important;
    text-align: center !important;
    width: 77.2875px !important;
    float: none !important;
    display: inline-block !important;
    border: none !important;
    box-shadow: none !important;
    box-sizing: content-box !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    vertical-align: top !important;
    overflow: hidden !important;
    height: 13px !important
}

._13jbtvd {
    background: 0 0 !important;
    padding: 0 !important;
    position: static !important;
    word-break: normal !important;
    margin: 0 !important;
    font-size: inherit !important;
    font-family: inherit !important;
    font-weight: inherit !important;
    color: inherit !important;
    line-height: inherit !important;
    text-align: inherit !important;
    width: 100% !important;
    float: none !important;
    display: block !important;
    border: none !important;
    box-shadow: none !important;
    box-sizing: content-box !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    height: 13px !important
}

._5z28yd {
    display: inline !important;
    overflow: auto !important;
    position: static !important;
    word-break: normal !important;
    font-size: inherit !important;
    font-family: inherit !important;
    font-weight: inherit !important;
    text-align: inherit !important;
    color: inherit !important;
    background: 0 0 !important;
    padding: 0 !important;
    margin: 0 2.03125px 0 0 !important;
    float: none !important;
    line-height: inherit !important;
    height: 13px !important;
    width: 13.8125px !important;
    box-sizing: content-box !important;
    max-width: none !important;
    max-height: none !important;
    vertical-align: top !important
}

._hy023o {
    display: inline !important;
    overflow: auto !important;
    position: static !important;
    word-break: normal !important;
    font-size: inherit !important;
    font-family: inherit !important;
    font-weight: inherit !important;
    text-align: inherit !important;
    color: inherit !important;
    background: 0 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    float: none !important;
    line-height: inherit !important;
    height: 13px !important;
    width: 13.8125px !important;
    box-sizing: content-box !important;
    max-width: none !important;
    max-height: none !important;
    vertical-align: top !important
}

._12jtipy {
    background: #fff !important;
    padding: 0 !important;
    position: absolute !important;
    word-break: normal !important;
    margin: 0 !important;
    font-size: inherit !important;
    font-family: inherit !important;
    font-weight: inherit !important;
    color: inherit !important;
    line-height: inherit !important;
    text-align: inherit !important;
    width: 96.6% !important;
    float: none !important;
    display: block !important;
    border: none !important;
    box-shadow: none !important;
    box-sizing: content-box !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    height: 13px !important;
    left: 0 !important;
    top: 0 !important;
    white-space: nowrap !important;
    overflow: hidden !important
}

._kmuwj1 {
    background: inherit !important;
    padding: 0 !important;
    position: static !important;
    word-break: normal !important;
    font-size: 12px !important;
    font-family: inherit !important;
    font-weight: inherit !important;
    color: #000 !important;
    line-height: 17px !important;
    text-align: center !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    display: block !important;
    float: none !important;
    margin: 0 !important;
    width: auto !important
}

@media only screen and (max-width: 648px) {
    ._kmuwj1 {
        padding: 5px 0 8px !important
    }
}

._368rpk {
    background: 0 0 !important;
    padding: 0 !important;
    position: static !important;
    word-break: normal !important;
    margin: 0 !important;
    font-size: 14px !important;
    font-family: inherit !important;
    font-weight: 700 !important;
    color: inherit !important;
    line-height: 10px !important;
    text-align: inherit !important;
    display: inline !important;
    width: auto !important;
    float: none !important;
    text-transform: none !important;
    letter-spacing: normal !important
}

._r1ba24 {
    background: 0 0 !important;
    padding: 0 !important;
    position: static !important;
    word-break: normal !important;
    font-size: 13px !important;
    font-family: inherit !important;
    font-weight: inherit !important;
    color: #000 !important;
    line-height: 16px !important;
    text-align: center !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    display: block !important;
    float: none !important;
    margin: 1px 0 0 !important;
    width: auto !important
}

@media only screen and (max-width: 648px) {
    ._r1ba24 {
        display: none !important
    }
}

/*! CSS Used from: Embedded */
.action.primary.close:hover {
    color: #fff
}

/*! CSS Used from: Embedded */
.exit-intent-popup .content > .close:after, .exit-intent-popup .content > .close:before {
    background: #000
}

.exit-intent-popup .content {
    transform: scale(0);
    transition: transform .7s cubic-bezier(.4, 0, .2, 1)
}

/*! CSS Used from: Embedded */
#zoid-paypal-buttons-uid_cde35c7849_mte6mty6ndg {
    position: relative;
    display: inline-block;
    width: 100%;
    min-height: 35px;
    min-width: 200px;
    max-width: 750px;
    font-size: 0
}

#zoid-paypal-buttons-uid_cde35c7849_mte6mty6ndg > iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

#zoid-paypal-buttons-uid_cde35c7849_mte6mty6ndg > iframe.component-frame {
    z-index: 100
}

#zoid-paypal-buttons-uid_cde35c7849_mte6mty6ndg > iframe.visible {
    opacity: 1
}

#zoid-paypal-buttons-uid_cde35c7849_mte6mty6ndg > .smart-menu {
    position: absolute;
    z-index: 300;
    top: 0;
    left: 0;
    width: 100%
}

/*! CSS Used from: Embedded */
#customer-email-fieldset label:not(.choice), .checkout-index-index .fieldset.address .field:not(.choice) .label, .checkout-index-index .fieldset.address .field:not(.choice) .label::after, .checkout-index-index .fieldset.address .field:not(.choice) .label::before {
    display: none
}

/*! CSS Used from: Embedded */
.fieldset > .field._required > .label::after, .fieldset > .field.required > .label::after {
    content: '*'
}

/*! CSS Used from: Embedded */
.header.content .service-items-outer {
    display: none
}

/*! CSS Used fontfaces */