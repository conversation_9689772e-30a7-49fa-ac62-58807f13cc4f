/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
@media (max-width: 767px) {
    h1 {
        font-size: 3.2rem;
        line-height: 3.2rem;
    }
    h2 {
        font-size: 2.6rem;
        line-height: 2.8rem;
        margin: 27px 0 16px 0;
    }
    h3 {
        font-size: 2.4rem;
        line-height: 2.6rem;
        margin: 20px 0 16px 0;
    }
    h4 {
        font-size: 2rem;
        line-height: 2.3rem;
        margin: 20px 0 16px 0;
    }
    h5 {
        font-size: 1.9rem;
        line-height: 2.1rem;
        margin: 16px 0 16px 0;
    }
    h6 {
        font-size: 1.8rem;
        line-height: 2rem;
        margin: 16px 0 16px 0;
    }
}
.modal-popup .action-close,
.modal-slide .action-close {
    padding: 0 30px 0 0;
}
.modal-popup .action-close::before,
.modal-slide .action-close::before {
    right: 30px;
    top: 50%;
    margin-top: -7px;
    color: inherit;
}
.modal-popup .action-close:hover,
.modal-slide .action-close:hover {
    color: #2f3943;
}
.modal-popup .action-close > span,
.modal-slide .action-close > span {
    clip: unset;
    height: auto;
    width: auto;
    line-height: 1.6rem;
    padding-right: 25px;
    position: relative;
    margin: 0;
    overflow: visible;
}
.main-faqs-page-index-view,
.main-faqs-page-index {
    width: 100%;
}
.main-faqs-page-index-view .faq_search,
.main-faqs-page-index .faq_search {
    padding: 8px 14px 14px 14px;
    background: #e4e4e4;
    margin: 0 0 20px 0;
    border-radius: 4px;
}
.main-faqs-page-index-view .faq_search .ui-helper-hidden-accessible,
.main-faqs-page-index .faq_search .ui-helper-hidden-accessible {
    padding: 0 0 6px;
    display: block;
    font-weight: bold;
}
.main-faqs-page-index-view ul,
.main-faqs-page-index ul {
    margin: 0;
    padding: 0;
    list-style: none;
}
.main-faqs-page-index-view ul li,
.main-faqs-page-index ul li {
    font-size: 14px;
    color: #000;
    text-decoration: none;
    margin: 0;
}
.main-faqs-page-index-view ul li a,
.main-faqs-page-index ul li a {
    color: #333;
    text-decoration: none;
    font-weight: bold;
}
.main-faqs-page-index-view .md_faq_main_left,
.main-faqs-page-index .md_faq_main_left {
    border: #ddd solid 1px;
    border-radius: 4px;
}
.main-faqs-page-index-view .md_faq_main_left ul li a,
.main-faqs-page-index .md_faq_main_left ul li a {
    padding: 14px 32px 14px 16px;
    border-bottom: #ddd solid 1px;
    display: block;
}
.main-faqs-page-index-view .md_faq_main_left ul li a:after,
.main-faqs-page-index .md_faq_main_left ul li a:after {
    content: "\f105";
    margin: 0px -16px 0 0;
    color: #333;
    font-size: 18px;
    font-family: 'FontAwesome';
    float: right;
    line-height: 18px;
}
.main-faqs-page-index-view .md_faq_main_left ul li a:hover,
.main-faqs-page-index .md_faq_main_left ul li a:hover {
    background: #1979c3;
    color: #fff;
}
.main-faqs-page-index-view .md_faq_main_left ul li a:hover:after,
.main-faqs-page-index .md_faq_main_left ul li a:hover:after {
    color: #fff;
}
.main-faqs-page-index-view .md_faq_main_left ul li.ui-state-active a,
.main-faqs-page-index .md_faq_main_left ul li.ui-state-active a {
    background: #ddd;
}
.main-faqs-page-index-view .md_faq_main_left ul li.ui-state-active a:after,
.main-faqs-page-index .md_faq_main_left ul li.ui-state-active a:after {
    color: #333;
}
.main-faqs-page-index-view .md_faq_main_left ul li.ui-state-active a:hover,
.main-faqs-page-index .md_faq_main_left ul li.ui-state-active a:hover {
    color: #333;
}
.main-faqs-page-index-view .md_faq_main_left ul li.active a,
.main-faqs-page-index .md_faq_main_left ul li.active a {
    background: #1979c3;
    color: #ffffff;
}
.main-faqs-page-index-view .md_faq_main_left ul li.active a:after,
.main-faqs-page-index .md_faq_main_left ul li.active a:after {
    color: #ffffff;
}
.main-faqs-page-index-view .md_faq_main_right ul li,
.main-faqs-page-index .md_faq_main_right ul li {
    margin: 0 0 18px 0;
}

.main-faqs-page-tags .md_faq_main_left {
    border: #ddd solid 1px;
    border-radius: 4px;
}
.main-faqs-page-tags .md_faq_main_left ul {
    margin: 0;
    padding: 0;
    list-style: none;
}
.main-faqs-page-tags .md_faq_main_left ul li a {
    padding: 14px 16px;
    border-bottom: #ddd solid 1px;
    display: block;
}
.main-faqs-page-tags .md_faq_main_left ul li a:hover {
    background: #1979c3;
    color: #fff;
}
.main-faqs-page-tags .md_faq_main_left ul li a:hover:after {
    color: #fff;
}
.main-faqs-page-tags .md_faq_main_left ul li.ui-state-active a {
    background: #ddd;
}
.main-faqs-page-tags .md_faq_main_left ul li.ui-state-active a:after {
    color: #333;
}
.main-faqs-page-tags .md_faq_main_left ul li.ui-state-active a:hover {
    color: #333;
}
.faqs-index-category .ui-autocomplete,
.faqs-index-search .ui-autocomplete,
.faqs-index-index .ui-autocomplete {
    background: #fff;
    border-radius: 0 0 4px 4px;
    box-shadow: 2px 2px 5px #cccccc;
    list-style: none;
    border: #ccc solid 1px;
    padding: 10px;
    width: 45% !important;
    margin: -1px 0 0 0 !important;
}
.faqs-index-category .ui-autocomplete .ui-autocomplete-category,
.faqs-index-search .ui-autocomplete .ui-autocomplete-category,
.faqs-index-index .ui-autocomplete .ui-autocomplete-category {
    font-weight: bold;
    font-size: 16px;
}
.faqs-index-category .ui-autocomplete .ui-menu-item a,
.faqs-index-search .ui-autocomplete .ui-menu-item a,
.faqs-index-index .ui-autocomplete .ui-menu-item a {
    font-size: 13px;
    text-decoration: none;
    font-weight: bold;
    cursor: pointer;
}
.faqs-index-category .ui-autocomplete .ui-menu-item a:hover,
.faqs-index-search .ui-autocomplete .ui-menu-item a:hover,
.faqs-index-index .ui-autocomplete .ui-menu-item a:hover {
    text-decoration: underline;
}
.faqs-index-category a.accordion-toggle.faq_questions_title,
.faqs-index-search a.accordion-toggle.faq_questions_title,
.faqs-index-index a.accordion-toggle.faq_questions_title {
    text-decoration: none;
}
.faqs-index-category .faq-index-list-view .faq-category,
.faqs-index-search .faq-index-list-view .faq-category,
.faqs-index-index .faq-index-list-view .faq-category {
    line-height: 100px;
}
.faqs-index-category .faqcat-block.col-md-6.col-xs-12,
.faqs-index-search .faqcat-block.col-md-6.col-xs-12,
.faqs-index-index .faqcat-block.col-md-6.col-xs-12 {
    height: auto;
    min-height: 309px;
}
.faqs-index-category .panel-group .panel-heading,
.faqs-index-search .panel-group .panel-heading,
.faqs-index-index .panel-group .panel-heading {
    border-bottom: 1px solid #cccccc;
}
.faqs-index-category .panel-default > .panel-heading + .panel-collapse > .panel-body,
.faqs-index-search .panel-default > .panel-heading + .panel-collapse > .panel-body,
.faqs-index-index .panel-default > .panel-heading + .panel-collapse > .panel-body {
    border: none;
    margin: 15px 0 20px 0px;
}
.faqs-index-category .faq-index-list-view .faqcat-desc,
.faqs-index-search .faq-index-list-view .faqcat-desc,
.faqs-index-index .faq-index-list-view .faqcat-desc {
    margin-bottom: 15px;
    text-align: justify;
}
.faqs-question-tags h3.faqcat-title {
    margin-top: 0px;
    margin-bottom: 2rem;
}
.faqs-question-view .mage-faq-social {
    float: right;
    margin-top: 20px;
}
.main-faqs-page-index-view .md_faq_main_heading h3 {
    clear: both;
    display: block;
    float: left;
    font-size: 18px;
    font-weight: bold;
    margin: 20px 0 16px;
    padding: 0;
    width: 100%;
}
.main-faqs-page-index-view .md_faq_main_heading h3 a {
    color: #000;
    text-decoration: none;
    background: #ddd;
    display: block;
    padding: 12px 32px 12px 16px;
    border-radius: 4px;
}
.main-faqs-page-index-view .md_faq_main_heading h3 a:after {
    content: "\f105";
    line-height: 18px;
    margin: 0px -16px 0 0;
    color: #333;
    font-size: 20px;
    font-family: 'FontAwesome';
    float: right;
}
.main-faqs-page-index-view .md_faq_main_heading h3 a:hover {
    background: #1979c3;
    color: #fff;
}
.main-faqs-page-index-view .md_faq_main_heading h3 a:hover:after {
    color: #fff;
}
.faqs-index-category .panel-default > .panel-heading + .panel-collapse > .panel-body {
    border: none;
}
.faq-index-list-view .faq-category {
    border-bottom: 1px solid #e6e6e6;
}
.faq-index-list-view .faq-category .faq-category-icon {
    height: 35px;
    margin: 0 25px 0 0;
    width: 35px;
    font-size: 45px;
    display: table-cell;
    vertical-align: middle;
    padding-right: 20px;
}
.faq-index-list-view .faq-category .faq-category-title {
    display: table-cell;
    vertical-align: middle;
    width: 100%;
}
.faq-index-list-view .faq-category-title h3 {
    margin: 0;
    font-size: 24px;
}
.faq-index-list-view .faqcat-desc {
    margin-top: 10px;
    font-size: 15px;
    line-height: 20px;
    color: #8a8a8a;
}
.faq-index-list-view .faq-category a {
    display: table;
    min-height: 75px;
    padding: 20px 10px;
    width: 100%;
    text-decoration: none;
}
.question-list .panel-heading {
    border: 1px solid #cccccc;
    cursor: pointer;
}
.panel_title {
    padding: 10px 0 10px 15px;
    margin: 0;
    font-size: 14px;
    line-height: 1.2;
}
.mage-faq-social {
    float: right;
}
.question-details .like-text {
    font-weight: 600;
    float: left;
    padding: 8px 8px 8px 0px;
}
.product-custometab-faq .main-faqs-page-index-view h3.faqcat-title {
    margin: 0 0 15px 0;
}
.catalog-product-view .panel-default > .panel-heading + .panel-collapse > .panel-body {
    border-top: none;
    margin: 15px 0 0 0;
}
.catalog-product-view .main-faqs-page-index-view ul,
.catalog-product-view .main-faqs-page-index ul {
    margin: 0 0 12px 0;
}
.catalog-product-view .main-faqs-page-index-view ul li,
.catalog-product-view .main-faqs-page-index ul li {
    padding: 0 0 5px 0;
}
.main-faqs-page-index .block-title,
.main-faqs-page-tags .block-title {
    border-bottom: 1px solid #cccccc;
    background-color: #f4f4f4;
    color: #636363;
    text-transform: uppercase;
    padding: 14px 16px;
}
.main-faqs-page-tags .tags-all-list {
    margin: 0;
    padding: 0;
    list-style: none;
}
.main-faqs-page-tags .md_faq_main_left ul li a {
    border-bottom: 0 none;
    border-radius: 10px;
    color: #333333;
    display: block;
    font-weight: bold;
    padding: 10px 8px;
    text-decoration: none;
}
.main-faqs-page-tags .faq-tag-list {
    display: inline-block;
    margin: 5px 0 5px 5px;
}
.main-faqs-page-tags .faq-tag-list.active a {
    background: #1979c3;
    color: #ffffff;
}
.panel_title {
    padding: 10px 0 10px 15px;
    margin: 0;
    font-size: 14px;
    line-height: 1.2;
}
.faqs-cat-image,
.faqs-cat-desc {
    margin-bottom: 20px;
}
.faqs-cat-desc {
    text-align: justify;
}
.question-list .panel-heading {
    border: 1px solid #cccccc;
    cursor: pointer;
}
.product-custometab-faq .block-title {
    font-weight: bold;
    font-size: 18px;
    color: #333;
    padding: 0 0 10px 0;
    margin: 0 0 20px 0;
    border-bottom: #ddd dashed 1px;
}
.product-custometab-faq .faq-block-content {
    margin: 0 0 30px 0;
    display: block;
}
.product-custometab-faq .faq-block-content ul {
    list-style: none;
    margin: 0;
    padding: 0;
}
.product-custometab-faq .faq-block-content ul li {
    margin: 0 0 18px 0;
}
.faqs-question-tags .panel-default > .panel-heading + .panel-collapse > .panel-body {
    border: none;
}
/**
 * Copyright 2016 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *  http://aws.amazon.com/apache2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
/**
 * Copyright 2016 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *  http://aws.amazon.com/apache2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
/**
* Amazon Pay variables
**/
@media all and (max-width: 768px) {
    .amazon-button-container {
        width: 100%;
    }
}
.amazon-logout-widget {
    display: none;
}
/**
 * Copyright 2016 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *  http://aws.amazon.com/apache2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
@media all and (max-width: 768px) {
    .amazon-minicart-container .amazon-button-container {
        width: auto;
    }
}
.multi-checkout-fields label {
    display: block;
    margin-bottom: 7px;
}
.multi-checkout-fields .field {
    margin: 1.5em 0;
}
.multi-checkout-fields .field.checkbox label {
    display: inline-block;
    margin: 0 0 0 12px;
}
.collection-point-country,
.collection-point-postalcode,
.collection-point-button {
    display: inline-block;
    padding: 20px 10px 0 0;
}
.collection-point-list {
    border: 1px solid #c2c2c2;
    max-height: 200px;
    overflow: auto;
}
.collection-point-postalcode {
    max-width: 9em;
    vertical-align: top;
}
.field-error {
    color: #e02b27;
    font-size: 1.2rem;
    margin-top: 7px;
}
table.collection-point-opening-hours > tbody > tr > td {
    padding-bottom: 2px;
}
.collection-point-message {
    padding-top: 10px;
}
.box-order-shipping-address .box-subtitle {
    font-weight: 600;
}
.box-order-shipping-address .order-shipping-secondary-address {
    margin-top: 1rem;
}
@media only screen and (min-device-width: 320px) and (max-device-width: 780px) and (orientation: landscape) {
    .product-video {
        height: 100%;
        width: 81%;
    }
}
@media all and (min-width: 768px), print {
    .abs-product-options-list-desktop dt,
    .block-giftregistry-shared .item-options dt {
        clear: left;
        float: left;
        margin: 0 10px 5px 0;
    }
    .block-giftregistry-shared .item-options dt:after {
        content: ': ';
    }
    .abs-product-options-list-desktop dd,
    .block-giftregistry-shared .item-options dd {
        display: inline-block;
        float: left;
        margin: 0 0 5px;
    }
    .abs-button-desktop {
        width: auto;
    }
    .abs-blocks-2columns,
    .abs-discount-block-desktop .block,
    .amazon-addresses .amazon-address,
    .magento-rma-guest-returns .column.main .block:not(.widget) .block-content .box,
    .login-container .block,
    .account .column.main .block:not(.widget) .block-content .box,
    .block-addresses-list .items.addresses > .item,
    .form-address-edit .fieldset:not(.fieldset-fullname),
    .form-edit-account .fieldset:not(.fieldset-fullname),
    .cart-discount .block {
        width: 48%;
    }
    .abs-discount-block-desktop .block:nth-child(1),
    .amazon-addresses .amazon-address:nth-child(1),
    .magento-rma-guest-returns .column.main .block:not(.widget) .block-content .box:nth-child(1),
    .login-container .block:nth-child(1),
    .account .column.main .block:not(.widget) .block-content .box:nth-child(1),
    .block-addresses-list .items.addresses > .item:nth-child(1),
    .form-address-edit .fieldset:nth-child(1),
    .form-edit-account .fieldset:nth-child(1),
    .cart-discount .block:nth-child(1) {
        clear: left;
        float: left;
    }
    .abs-discount-block-desktop .block:nth-child(2),
    .amazon-addresses .amazon-address:nth-child(2),
    .magento-rma-guest-returns .column.main .block:not(.widget) .block-content .box:nth-child(2),
    .login-container .block:nth-child(2),
    .account .column.main .block:not(.widget) .block-content .box:nth-child(2),
    .block-addresses-list .items.addresses > .item:nth-child(2),
    .form-address-edit .fieldset:nth-child(2),
    .form-edit-account .fieldset:nth-child(2),
    .cart-discount .block:nth-child(2) {
        float: right;
    }
    .abs-discount-block-desktop .block:nth-child(2) + *,
    .amazon-addresses .amazon-address:nth-child(2) + *,
    .magento-rma-guest-returns .column.main .block:not(.widget) .block-content .box:nth-child(2) + *,
    .login-container .block:nth-child(2) + *,
    .account .column.main .block:not(.widget) .block-content .box:nth-child(2) + *,
    .block-addresses-list .items.addresses > .item:nth-child(2) + *,
    .form-address-edit .fieldset:nth-child(2) + *,
    .form-edit-account .fieldset:nth-child(2) + *,
    .cart-discount .block:nth-child(2) + * {
        clear: both;
    }
    .abs-margin-for-blocks-and-widgets-desktop,
    .page-main .block {
        margin-bottom: 50px;
    }
    .abs-reset-left-margin-desktop,
    .gift-summary .actions-toolbar,
    .cart.table-wrapper .gift-summary .actions-toolbar,
    .column.main .block-giftregistry-shared-items .actions-toolbar,
    .form-new-agreement .fieldset .legend,
    .form-new-agreement .actions-toolbar,
    .column.main .paypal-review .actions-toolbar,
    .wishlist-index-index .main .form-wishlist-items .actions-toolbar {
        margin-left: 0;
    }
    .abs-action-remove-desktop,
    .abs-add-fields-desktop .fieldset .additional .action.remove,
    .form-giftregistry-share .fieldset .additional .action.remove,
    .form-giftregistry-edit .fieldset .additional .action.remove,
    .form-add-invitations .fieldset .additional .action.remove,
    .form-create-return .fieldset .additional .action.remove,
    .form.send.friend .fieldset .additional .action.remove {
        margin-left: 90%;
    }
    .abs-add-fields-desktop .fieldset .field .control,
    .form-giftregistry-share .fieldset .field .control,
    .form-giftregistry-edit .fieldset .field .control,
    .form-add-invitations .fieldset .field .control,
    .form-create-return .fieldset .field .control,
    .form.send.friend .fieldset .field .control {
        width: auto;
    }
    .abs-margin-for-forms-desktop {
        margin-left: 25.8%;
    }
    .abs-visually-hidden-desktop,
    .modes-label,
    .block-search .label,
    .block-collapsible-nav .title {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }
    .abs-add-clearfix-desktop:before,
    .abs-add-clearfix-desktop:after,
    .amazon-addresses:before,
    .amazon-addresses:after,
    .block-cart-failed .block-content:before,
    .block-cart-failed .block-content:after,
    .block-giftregistry-shared .item-options:before,
    .block-giftregistry-shared .item-options:after,
    .gift-wrapping .nested:before,
    .gift-wrapping .nested:after,
    .table .gift-wrapping .content:before,
    .table .gift-wrapping .content:after,
    .block-wishlist-management:before,
    .block-wishlist-management:after,
    .paypal-review .block-content:before,
    .paypal-review .block-content:after,
    .magento-rma-guest-returns .column.main .block:not(.widget) .block-content:before,
    .magento-rma-guest-returns .column.main .block:not(.widget) .block-content:after,
    .cart-container:before,
    .cart-container:after,
    .login-container:before,
    .login-container:after,
    .account .page-title-wrapper:before,
    .account .page-title-wrapper:after,
    .account .column.main .block:not(.widget) .block-content:before,
    .account .column.main .block:not(.widget) .block-content:after,
    .block-addresses-list .items.addresses:before,
    .block-addresses-list .items.addresses:after,
    .order-links:before,
    .order-links:after,
    .account .column.main .block.block-order-details-view:before,
    .account .column.main .block.block-order-details-view:after,
    [class^='sales-guest-'] .column.main .block.block-order-details-view:before,
    [class^='sales-guest-'] .column.main .block.block-order-details-view:after,
    .sales-guest-view .column.main .block.block-order-details-view:before,
    .sales-guest-view .column.main .block.block-order-details-view:after,
    .page-header .header.panel:before,
    .page-header .header.panel:after,
    .header.content:before,
    .header.content:after {
        content: '';
        display: table;
    }
    .abs-add-clearfix-desktop:after,
    .amazon-addresses:after,
    .block-cart-failed .block-content:after,
    .block-giftregistry-shared .item-options:after,
    .gift-wrapping .nested:after,
    .table .gift-wrapping .content:after,
    .block-wishlist-management:after,
    .paypal-review .block-content:after,
    .magento-rma-guest-returns .column.main .block:not(.widget) .block-content:after,
    .cart-container:after,
    .login-container:after,
    .account .page-title-wrapper:after,
    .account .column.main .block:not(.widget) .block-content:after,
    .block-addresses-list .items.addresses:after,
    .order-links:after,
    .account .column.main .block.block-order-details-view:after,
    [class^='sales-guest-'] .column.main .block.block-order-details-view:after,
    .sales-guest-view .column.main .block.block-order-details-view:after,
    .page-header .header.panel:after,
    .header.content:after {
        clear: both;
    }
    .abs-add-box-sizing-desktop,
    .abs-shopping-cart-items-desktop,
    .column.main,
    .sidebar-main,
    .sidebar-additional,
    .bundle-options-container .block-bundle-summary,
    .block.crosssell,
    .account .column.main .block.block-order-details-view .block-content:not(.widget) .box,
    [class^='sales-guest-'] .column.main .block.block-order-details-view .block-content:not(.widget) .box,
    .sales-guest-view .column.main .block.block-order-details-view .block-content:not(.widget) .box,
    .block-cart-failed,
    .cart-container .cart-gift-item,
    .cart-container .form-cart {
        box-sizing: border-box;
    }
    .abs-add-box-sizing-desktop-m,
    .opc-wrapper {
        box-sizing: border-box;
    }
    .abs-revert-field-type-desktop .fieldset > .field,
    .abs-revert-field-type-desktop .fieldset .fields > .field {
        margin: 0 0 20px;
    }
    .abs-revert-field-type-desktop .fieldset > .field:not(.choice) > .label,
    .abs-revert-field-type-desktop .fieldset .fields > .field:not(.choice) > .label {
        box-sizing: content-box;
        float: none;
        width: auto;
        text-align: left;
        padding: 0;
    }
    .abs-revert-field-type-desktop .fieldset > .field:not(.choice) > .control,
    .abs-revert-field-type-desktop .fieldset .fields > .field:not(.choice) > .control {
        float: none;
        width: auto;
    }
    .abs-revert-field-type-desktop .fieldset > .field > .label,
    .abs-revert-field-type-desktop .fieldset .fields > .field > .label {
        margin: 0 0 8px;
        display: inline-block;
    }
    .abs-revert-field-type-desktop .fieldset > .field.choice:before,
    .abs-revert-field-type-desktop .fieldset .fields > .field.choice:before,
    .abs-revert-field-type-desktop .fieldset > .field.no-label:before,
    .abs-revert-field-type-desktop .fieldset .fields > .field.no-label:before {
        display: none;
    }
    .abs-form-field-column-2 .fieldset .field {
        padding: 0 12px 0 0;
        box-sizing: border-box;
        display: inline-block;
        width: 50%;
        vertical-align: top;
    }
    .abs-form-field-column-2 .fieldset .field + .fieldset {
        clear: both;
    }
    .abs-form-field-column-2 .fieldset .field .field {
        padding: 0;
        width: 100%;
    }
    .abs-form-field-revert-column-1 {
        width: 100%;
    }
    .abs-forms-general-desktop {
        max-width: 500px;
    }
    .abs-forms-general-desktop .legend {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }
    .abs-forms-general-desktop .legend + br {
        display: none;
    }
    .abs-revert-side-paddings,
    .checkout-cart-index .page-title-wrapper,
    .cart-summary .block .content,
    .cart-empty {
        padding-left: 0;
        padding-right: 0;
    }
    .abs-account-block-font-size,
    .block-addresses-list address,
    .box-billing-address .box-content,
    .box-shipping-address .box-content,
    .box-information .box-content {
        font-size: 1.6rem;
    }
    .abs-account-table-margin-desktop,
    .table-wrapper.orders-recent {
        margin-top: -25px;
    }
    .abs-action-print {
        display: inline-block;
        text-decoration: none;
    }
    .abs-action-print:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 16px;
        line-height: 16px;
        color: inherit;
        content: '\e624';
        font-family: 'luma-icons';
        margin: 0 4px 0 0;
        vertical-align: middle;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    }
    .abs-no-display-desktop,
    .sidebar .block.widget .pager .item:not(.pages-item-next):not(.pages-item-previous) {
        display: none;
    }
    .abs-status-desktop,
    .order-status {
        margin-top: 6px;
        padding: 5px 10px;
    }
    .abs-title-orders-desktop .page-main .page-title-wrapper .order-date,
    .account .page-main .page-title-wrapper .order-date,
    [class^='sales-guest-'] .page-main .page-title-wrapper .order-date,
    .sales-guest-view .page-main .page-title-wrapper .order-date {
        margin: -14px 0 18px;
    }
    .abs-table-bordered-desktop {
        border: none;
    }
    .abs-table-bordered-desktop > thead > tr > th,
    .abs-table-bordered-desktop > tbody > tr > th,
    .abs-table-bordered-desktop > tfoot > tr > th,
    .abs-table-bordered-desktop > thead > tr > td,
    .abs-table-bordered-desktop > tbody > tr > td,
    .abs-table-bordered-desktop > tfoot > tr > td {
        border: none;
    }
    .abs-table-bordered-desktop > thead > tr > th,
    .abs-table-bordered-desktop > thead > tr > td {
        border-bottom: 1px solid #cccccc;
    }
    .abs-pager-toolbar,
    .toolbar-giftregistry-results,
    .toolbar-wishlist-results,
    .account .toolbar {
        position: relative;
    }
    .abs-pager-toolbar .toolbar-amount,
    .abs-pager-toolbar .limiter,
    .toolbar-giftregistry-results .toolbar-amount,
    .toolbar-giftregistry-results .limiter,
    .toolbar-wishlist-results .toolbar-amount,
    .toolbar-wishlist-results .limiter,
    .account .toolbar .toolbar-amount,
    .account .toolbar .limiter {
        position: relative;
        z-index: 1;
    }
    .abs-pager-toolbar .toolbar-amount,
    .toolbar-giftregistry-results .toolbar-amount,
    .toolbar-wishlist-results .toolbar-amount,
    .account .toolbar .toolbar-amount {
        line-height: 30px;
        padding: 0;
    }
    .abs-pager-toolbar .pages,
    .toolbar-giftregistry-results .pages,
    .toolbar-wishlist-results .pages,
    .account .toolbar .pages {
        position: absolute;
        width: 100%;
        z-index: 0;
    }
    .abs-shopping-cart-items-desktop,
    .block-cart-failed,
    .cart-container .cart-gift-item,
    .cart-container .form-cart {
        width: 75%;
        float: left;
        -ms-flex-order: 1;
        -webkit-order: 1;
        order: 1;
        padding-right: 4%;
        position: relative;
    }
    .abs-discount-block-desktop .block > .title,
    .cart-discount .block > .title {
        border: 0;
        padding: 0 0 10px;
    }
    .abs-discount-block-desktop .block > .title strong,
    .cart-discount .block > .title strong {
        font-size: 1.6rem;
    }
    .abs-discount-block-desktop .block .content,
    .cart-discount .block .content {
        padding: 0 0 20px;
    }
    .abs-discount-block-desktop .actions-toolbar .secondary,
    .cart-discount .actions-toolbar .secondary {
        bottom: -30px;
        left: 0;
        position: absolute;
    }
    .navigation,
    .breadcrumbs,
    .page-header .header.panel,
    .header.content,
    .footer.content,
    .page-wrapper > .widget,
    .page-wrapper > .page-bottom,
    .block.category.event,
    .top-container,
    .page-main {
        box-sizing: border-box;
        margin-left: auto;
        margin-right: auto;
        max-width: 100%;
        padding-left: 20px;
        padding-right: 20px;
        width: auto;
    }
    .page-main {
        width: 100%;
    }
    .ie9 .page-main {
        width: auto;
    }
    .columns {
        display: block;
    }
    .column.main {
        min-height: 300px;
    }
    .page-layout-1column .column.main {
        width: 100%;
        -ms-flex-order: 2;
        -webkit-order: 2;
        order: 2;
    }
    .page-layout-3columns .column.main {
        width: 58.33333333%;
        display: inline-block;
        -ms-flex-order: 2;
        -webkit-order: 2;
        order: 2;
    }
    .page-layout-2columns-left .column.main {
        width: calc(79.167% - 30px);
        float: right;
        -ms-flex-order: 2;
        -webkit-order: 2;
        order: 2;
    }
    .page-layout-2columns-right .column.main {
        width: calc(79.167% - 30px);
        float: left;
        -ms-flex-order: 1;
        -webkit-order: 1;
        order: 1;
    }
    .sidebar-main {
        padding-right: 2%;
    }
    .page-layout-3columns .sidebar-main {
        width: 20.83333333%;
        float: left;
        -ms-flex-order: 1;
        -webkit-order: 1;
        order: 1;
    }
    .page-layout-2columns-left .sidebar-main {
        width: 20.83333333%;
        float: left;
        -ms-flex-order: 1;
        -webkit-order: 1;
        order: 1;
    }
    .page-layout-2columns-right .sidebar-main {
        width: 20.83333333%;
        float: left;
        -ms-flex-order: 1;
        -webkit-order: 1;
        order: 1;
    }
    .page-layout-2columns-right .sidebar-main {
        padding-left: 2%;
        padding-right: 0;
    }
    .sidebar-additional {
        clear: right;
        padding-left: 2%;
    }
    .page-layout-3columns .sidebar-additional {
        width: 20.83333333%;
        float: right;
        -ms-flex-order: 3;
        -webkit-order: 3;
        order: 3;
    }
    .page-layout-2columns-left .sidebar-additional {
        width: 20.83333333%;
        float: right;
        -ms-flex-order: 2;
        -webkit-order: 2;
        order: 2;
    }
    .page-layout-2columns-right .sidebar-additional {
        width: 20.83333333%;
        float: right;
        -ms-flex-order: 2;
        -webkit-order: 2;
        order: 2;
    }
    .page-layout-2columns-left .sidebar-additional {
        clear: left;
        float: left;
        padding-left: 0;
        padding-right: 2%;
    }
    .panel.header {
        padding: 10px 20px;
    }
    .nav-toggle {
        display: none;
    }
    .nav-sections {
        -webkit-flex-shrink: 0;
        flex-shrink: 0;
        -webkit-flex-basis: auto;
        flex-basis: auto;
        margin-bottom: 25px;
    }
    .nav-sections-item-title {
        display: none;
    }
    .nav-sections-item-content {
        display: block !important;
    }
    .nav-sections-item-content > * {
        display: none;
    }
    .nav-sections-item-content > .navigation {
        display: block;
    }
    .navigation {
        background: #f0f0f0;
        font-weight: 700;
        height: inherit;
        left: auto;
        overflow: inherit;
        padding: 0;
        position: relative;
        top: 0;
        width: 100%;
        z-index: 3;
    }
    .navigation:empty {
        display: none;
    }
    .navigation ul {
        margin-top: 0;
        margin-bottom: 0;
        padding: 0;
        position: relative;
    }
    .navigation li.level0 {
        border-top: none;
    }
    .navigation li.level1 {
        position: relative;
    }
    .navigation .level0 {
        margin: 0 10px 0 0;
        display: inline-block;
        position: relative;
    }
    .navigation .level0:last-child {
        margin-right: 0;
        padding-right: 0;
    }
    .navigation .level0 > .level-top {
        color: #fff;
        padding: 0 12px;
        text-decoration: none;
        box-sizing: border-box;
        position: relative;
        display: inline-block;
    }
    .navigation .level0 > .level-top:hover,
    .navigation .level0 > .level-top.ui-state-focus {
        text-decoration: none;
    }
    .navigation .level0.active > .level-top,
    .navigation .level0.has-active > .level-top {
        text-decoration: none;
        display: inline-block;
    }
    .navigation .level0.parent:hover > .submenu {
        overflow: visible !important;
    }
    .navigation .level0.parent > .level-top {
        padding-right: 20px;
    }
    .navigation .level0.parent > .level-top > .ui-menu-icon {
        position: absolute;
        right: 0;
        display: inline-block;
        text-decoration: none;
    }
    .navigation .level0.parent > .level-top > .ui-menu-icon > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }
    .navigation .level0.parent > .level-top > .ui-menu-icon:after {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 12px;
        line-height: 20px;
        color: inherit;
        content: '\e622';
        font-family: 'luma-icons';
        vertical-align: middle;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    }
    .navigation .level0 .submenu {
        background: #ffffff;
        border: 1px solid #cccccc;
        box-shadow: 0 5px 5px rgba(0, 0, 0, 0.19);
        font-weight: 400;
        min-width: 230px;
        padding: 15px 0;
        display: none;
        left: 0;
        margin: 0 !important;
        padding: 0;
        position: absolute;
        z-index: 99999;
        margin-top: 11px;
    }
    .navigation .level0 .submenu > ul {
        margin-top: 11px;
    }
    .navigation .level0 .submenu > ul:before,
    .navigation .level0 .submenu > ul:after {
        content: '';
        display: block;
        overflow: hidden;
        position: absolute;
    }
    .navigation .level0 .submenu > ul:before {
        color: #ffffff;
        left: 20px;
        top: -20px;
        border: 10px solid transparent;
        height: 0;
        width: 0;
        border-bottom-color: #ffffff;
        z-index: 4;
    }
    .navigation .level0 .submenu > ul:after {
        border: 11px solid transparent;
        height: 0;
        width: 0;
        border-bottom-color: #cccccc;
        color: #cccccc;
        left: 19px;
        top: -22px;
        z-index: 3;
    }
    .navigation .level0 .submenu a {
        display: block;
        line-height: inherit;
        color: #636d70;
        padding: 8px 20px;
    }
    .navigation .level0 .submenu a:hover,
    .navigation .level0 .submenu a.ui-state-focus {

    }
    .navigation .level0 .submenu .active > a {
        border: none;
    }
    .navigation .level0 .submenu .submenu {
        top: 0 !important;
        left: 100% !important;
    }
    .navigation .level0 .submenu .submenu-reverse {
        left: auto !important;
        right: 100%;
    }
    .navigation .level0 .submenu li {
        margin: 0;
    }
    .navigation .level0 .submenu li.parent > a > .ui-menu-icon {
        position: absolute;
        right: 3px;
        display: inline-block;
        text-decoration: none;
    }
    .navigation .level0 .submenu li.parent > a > .ui-menu-icon > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }
    .navigation .level0 .submenu li.parent > a > .ui-menu-icon:after {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 12px;
        line-height: 20px;
        color: inherit;
        content: '\e608';
        font-family: 'luma-icons';
        vertical-align: middle;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    }
    .navigation .level0.more {
        position: relative;
        display: inline-block;
        text-decoration: none;
    }
    .navigation .level0.more:after {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 26px;
        line-height: inherit;
        color: inherit;
        content: '\e607';
        font-family: 'luma-icons';
        vertical-align: middle;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    }
    .navigation .level0.more:before {
        display: none;
    }
    .navigation .level0.more:after {
        cursor: pointer;
        padding: 8px 12px;
        position: relative;
        z-index: 1;
    }
    .navigation .level0.more:hover > .submenu {
        overflow: visible !important;
    }
    .navigation .level0.more li {
        display: block;
    }
    .panel.header .links,
    .panel.header .switcher {
        display: inline-block;
    }
    .legend {
        border-bottom: 1px solid #c5c5c5;
    }
    .product.data.items {
        position: relative;
        z-index: 1;
        border-bottom: 0;
        margin-left: 0;
        margin-right: 0;
        margin-bottom: 0;
        background: #6f7d81;
    }
    .product.data.items:before,
    .product.data.items:after {
        content: '';
        display: table;
    }
    .product.data.items:after {
        clear: both;
    }
    .product.data.items .item.title {
        width: auto;
        flex: 1;
    }
    .product.data.items .item.title > .switch {
        height: 20px;
        display: block;
        position: relative;
        z-index: 2;
    }
    .product.data.items .item.content {
        margin-top: 20px;
        box-sizing: border-box;
        margin-left: 0;
        width: 100%;
        position: relative;
    }
    .product.data.items .section-header {
        position: sticky;
        top: 45px;
        left: 15px;
        font-size: 20px;
    }

    .product.data.items .item.content:before,
    .product.data.items .item.content:after {
        content: '';
        display: table;
    }
    .product.data.items .item.content:after {
        clear: both;
    }
    .product.data.items .item.content.active {
        display: block;
    }
    .product.data.items .item.title {
        margin: 0 -1px 0 0;
    }
    .product.data.items .item.title > .switch {
        font-weight: 400;
        line-height: 45px;
        text-decoration: none;
        background: #f6f6f6;
        border: 1px solid #cccccc;
        border-bottom: none;
        height: 45px;
        padding: 1px 10px 1px 10px;
        font-size: 17px;
    }
    .product.data.items .item.title > .switch:visited {
        color: #fff;
        text-decoration: none;
    }
    .product.data.items .item.title > .switch:hover {
        color: #fff;
        text-decoration: none;
    }
    .product.data.items .item.title > .switch:active {
        color: #333333;
        text-decoration: none;
    }
    .product.data.items .item.title:not(.disabled) > .switch:focus,
    .product.data.items .item.title:not(.disabled) > .switch:hover {
        background: #ffffff;
    }
    .product.data.items .item.title:not(.disabled) > .switch:active,
    .product.data.items .item.title.active > .switch,
    .product.data.items .item.title.active > .switch:focus,
    .product.data.items .item.title.active > .switch:hover {
        background: #ffffff;
        color: #333333;
    }
    .product.data.items .item.title.active > .switch,
    .product.data.items .item.title.active > .switch:focus,
    .product.data.items .item.title.active > .switch:hover {
        padding-bottom: 2px;
    }
    .product.data.items .item.content {
        background: #ffffff;
        margin-top: 0;
        padding: 0 35px 35px 35px;
    }
    .product.data.items .item.title a:after {
        display: none;
    }
    .actions-toolbar {
        text-align: left;
    }
    .actions-toolbar:before,
    .actions-toolbar:after {
        content: '';
        display: table;
    }
    .actions-toolbar:after {
        clear: both;
    }
    .actions-toolbar .primary {
        float: left;
    }
    .actions-toolbar .primary,
    .actions-toolbar .secondary {
        display: inline-block;
    }
    .actions-toolbar .primary a.action,
    .actions-toolbar .secondary a.action {
        display: inline-block;
    }
    .actions-toolbar .primary .action {
        margin: 0 15px 0 0;
    }
    .actions-toolbar .secondary a.action {
        margin-top: 6px;
    }
    .actions-toolbar > .primary,
    .actions-toolbar > .secondary {
        margin-bottom: 0;
    }
    .actions-toolbar > .primary .action,
    .actions-toolbar > .secondary .action {
        margin-bottom: 0;
        width: auto;
    }
    .popup-content .fieldset .actions-toolbar .secondary {
        display: inline-block;
        float: none;
    }
    .popup-content .fieldset .actions-toolbar .action.cancel {
        margin-top: 6px;
    }
    .modal-popup.modal-slide .modal-footer {
        text-align: center;
    }
    .login-container .block-amazon-login {
        clear: none;
        float: right;
    }
    .login-container .block-amazon-login .actions-toolbar {
        margin-top: 25px;
    }
    .login-container .block:nth-child(2) + .block-amazon-login {
        clear: none;
    }
    .amazon-validate-container {
        margin: 40px auto 0 auto;
        width: 500px;
    }
    .amazon-validate-container .primary {
        width: 190px;
    }
    .amazon-validate-container .continue-as-guest {
        width: 130px;
    }
    .amazon-validate-container .forgot-password {
        text-align: right;
        width: 175px;
    }
    .amazon-addresses .amazon-address {
        margin-bottom: 0;
    }
    .block-cart-failed .actions {
        text-align: left;
    }
    .block-cart-failed .actions.primary {
        float: right;
    }
    .payment-method-braintree .cvv .field-tooltip {
        left: 6rem;
    }
    .braintree-paypal-account {
        border-color: #cccccc;
        border-style: solid;
        border-width: 1px 0;
        display: inline-block;
        width: 50%;
    }
    .account .table-credit-cards .col.actions {
        width: 100px;
    }
    .block-category-event.block:last-child {
        margin-bottom: 30px;
        padding: 10px 0 30px;
    }
    .block-category-event .block-title {
        margin: 0;
    }
    .block-category-event .block-title strong {
        font-size: 2.4rem;
    }
    .block-category-event .ticker li {
        display: none;
        margin: 0 50px;
    }
    .block-category-event .ticker .value {
        font-size: 6rem;
    }
    .block-category-event .ticker .label {
        font-size: 1.4rem;
        text-transform: none;
    }
    .block-category-event .dates .date {
        font-size: 5rem;
    }
    .block-category-event .dates .start {
        padding-right: 50px;
    }
    .block-category-event .dates .start:after {
        font-size: 5rem;
        right: 10px;
    }
    .gift-message .field {
        margin-bottom: 20px;
    }
    .gift-options {
        position: relative;
        z-index: 1;
    }
    .gift-options .actions-toolbar {
        clear: both;
        float: right;
        position: static;
    }
    .gift-options .actions-toolbar .secondary {
        float: right;
    }
    .gift-options .actions-toolbar .secondary .action {
        float: right;
        margin-left: 20px;
    }
    .gift-options .actions-toolbar .secondary .action-cancel {
        display: block;
        float: left;
        margin-top: 6px;
    }
    .gift-options .actions-toolbar:nth-child(3):before {
        border-left: 1px solid #f5f5f5;
        bottom: 5rem;
        content: '';
        display: block;
        left: 50%;
        overflow: hidden;
        position: absolute;
        top: 0;
        width: 0;
    }
    .gift-options-title {
        font-weight: 300;
        font-size: 1.8rem;
    }
    .cart.table-wrapper .action-gift {
        float: left;
    }
    .order-options .gift-wrapping,
    .table-order-review .gift-wrapping {
        max-width: 50%;
    }
    .form-giftregistry-search .fieldset {
        margin-bottom: 29px;
    }
    .block-giftregistry-shared-items .product-item-photo {
        display: table-cell;
        max-width: 100%;
        padding: 0 20px 0 0;
        vertical-align: top;
        width: 1%;
    }
    .block-giftregistry-shared-items .product-item-details {
        display: table-cell;
        vertical-align: top;
        width: 99%;
        word-break: normal;
    }
    .block-giftregistry-shared-items .col.product {
        width: 48%;
    }
    .block-giftregistry-shared-items .col:not(.product) {
        text-align: center;
    }
    .block-giftregistry-shared-items .col.price {
        padding-top: 17px;
    }
    .block-giftregistry-shared-items .input-text.qty {
        margin-top: -4px;
    }
    .gift-options-cart-item .gift-wrapping,
    .cart-gift-item .gift-wrapping {
        box-sizing: border-box;
        float: left;
        padding-right: 20px;
        width: 50%;
    }
    .gift-options-cart-item .gift-wrapping + .gift-message,
    .cart-gift-item .gift-wrapping + .gift-message {
        border-left: 1px solid #f5f5f5;
        box-sizing: border-box;
        float: left;
        padding-left: 4.5rem;
        width: 50%;
    }
    .form-add-invitations .additional,
    .form-add-invitations .field.text {
        margin-top: 29px;
    }
    .table-invitations .col {
        width: 50%;
    }
    .wishlist.window.popup {
        bottom: auto;
        top: 20%;
        left: 50%;
        margin-left: -212px;
        width: 380px;
        right: auto;
    }
    .wishlist.window.popup .field {
        margin: 0 0 20px;
    }
    .wishlist.window.popup .field:not(.choice) > .label {
        box-sizing: content-box;
        float: none;
        width: auto;
        text-align: left;
        padding: 0;
    }
    .wishlist.window.popup .field:not(.choice) > .control {
        float: none;
        width: auto;
    }
    .wishlist.window.popup .field > .label {
        margin: 0 0 8px;
        display: inline-block;
    }
    .wishlist.window.popup .field.choice:before,
    .wishlist.window.popup .field.no-label:before {
        display: none;
    }
    .block-wishlist-management {
        margin-bottom: 20px;
    }
    .block-wishlist-management .wishlist-select {
        border-bottom: 1px solid #e8e8e8;
        display: table;
        margin-bottom: 15px;
        width: 100%;
    }
    .block-wishlist-management .wishlist-select .wishlist-name {
        display: table-cell;
        margin-right: 10px;
        padding: 5px 10px 10px;
        vertical-align: top;
        white-space: nowrap;
        width: 5%;
    }
    .block-wishlist-management .wishlist-select-items {
        display: table-cell;
        padding-right: 160px;
        vertical-align: top;
    }
    .block-wishlist-management .wishlist-select-items .item {
        display: inline-block;
        margin-right: 10px;
        padding: 5px 10px 10px;
    }
    .block-wishlist-management .wishlist-select-items .item:last-child {
        margin-right: 0;
    }
    .block-wishlist-management .wishlist-select-items .current {
        border-bottom: 3px solid #85b84b;
        font-weight: 600;
    }
    .block-wishlist-management .wishlist-select .wishlist-name-current {
        display: none;
    }
    .block-wishlist-management .wishlist-add.item {
        position: absolute;
        right: 0;
        top: 0;
    }
    .block-wishlist-management .wishlist-title strong {
        font-size: 4rem;
    }
    .block-wishlist-management .wishlist-info {
        float: left;
    }
    .block-wishlist-management .wishlist-toolbar {
        float: right;
    }
    .block-wishlist-info-items .product-item-photo {
        margin-left: 0;
    }
    .products-grid.wishlist .product-item-checkbox {
        float: left;
    }
    .products-grid.wishlist .product-item-checkbox + .product-item-name {
        margin-left: 25px;
    }
    .block.newsletter {
        width: 32%;
    }
    .block.newsletter .field {
        margin-right: 5px;
    }
    .block.newsletter .field .control {
        width: 100%;
    }
    .block.newsletter .action.subscribe {
        border-radius: 3px;
    }
    .paypal-review .paypal-review-title {
        border-bottom: 1px solid #cccccc;
    }
    .paypal-review .block-content .box-order-shipping-address,
    .paypal-review .block-content .box-order-shipping-method,
    .paypal-review .block-content .box-order-shipping-method + .box-order-billing-address {
        box-sizing: border-box;
        float: left;
        width: 33%;
    }
    .paypal-review .block-content .box-order-shipping-address {
        padding: 0 5%;
        width: 34%;
    }
    .paypal-review .col.subtotal,
    .paypal-review .mark,
    .paypal-review .amount {
        text-align: right;
    }
    .products.wrapper.list .product-reviews-summary {
        margin: 0;
    }
    .reward-settings + .actions-toolbar {
        margin-top: -32px;
    }
    .form-create-return .additional .field:last-child {
        margin-top: 29px;
    }
    .magento-rma-guest-returns .column.main .block:not(.widget) .block-content .box {
        margin-bottom: 20px;
    }
    .block-returns-tracking .block-title .action {
        margin: 0 0 0 30px;
    }
    .block-returns-tracking .block-title .actions-track {
        float: right;
        margin-top: 12px;
    }
    .my-credit-cards .card-type img {
        display: block;
    }
    .products-grid.wishlist .product-item-tooltip {
        display: inline-block;
    }
    .products-grid.wishlist .product-item-actions {
        margin: 10px 0 0;
    }
    .products-grid.wishlist .product-item .fieldset .field.qty {
        margin-bottom: 10px;
        padding-right: 10px;
    }
    .products-grid.wishlist .product-item .fieldset .field.qty .label {
        width: auto;
    }
    .products-grid.wishlist .product-item .box-tocart .actions-primary {
        margin: 0;
    }
    .products-grid.wishlist .product-item .box-tocart .stock {
        margin: 20px 0 0;
    }
    .wishlist-index-index .product-item-info {
        width: 240px;
    }
    .bundle-actions .action.primary.customize {
        width: auto;
    }
    .bundle-options-container .legend.title {
        font-size: 40px;
    }
    .bundle-options-container .bundle-options-wrapper,
    .bundle-options-container .product-options-wrapper {
        float: left;
        width: 57%;
    }
    .bundle-options-container .block-bundle-summary {
        float: right;
        margin-top: 66px;
        padding: 10px 20px;
        position: relative;
        width: 40%;
    }
    .bundle-options-container .block-bundle-summary .price-box .price-wrapper,
    .bundle-options-container .block-bundle-summary .price-box .price-wrapper > .price {
        color: #575757;
        font-size: 36px;
        font-weight: 600;
        line-height: 36px;
    }
    .bundle-options-container .block-bundle-summary .price-container .weee {
        color: #575757;
    }
    .bundle-options-container .block-bundle-summary .price-container .weee .price {
        font-size: 1.2rem;
        font-weight: 700;
    }
    .bundle-options-container .block-bundle-summary .price-including-tax + .price-excluding-tax .price {
        font-size: 1.4rem;
        line-height: 16px;
    }
    .bundle-options-container .block-bundle-summary .box-tocart .action.primary {
        margin-right: 1%;
        width: 49%;
    }
    .bundle-options-container .block-bundle-summary .product-addto-links {
        text-align: left;
    }
    .page-layout-2columns-left .bundle-options-container .bundle-options-wrapper,
    .page-layout-2columns-left .bundle-options-container .block-bundle-summary,
    .page-layout-2columns-right .bundle-options-container .bundle-options-wrapper,
    .page-layout-2columns-right .bundle-options-container .block-bundle-summary,
    .page-layout-3columns .bundle-options-container .bundle-options-wrapper,
    .page-layout-3columns .bundle-options-container .block-bundle-summary {
        width: 48%;
    }
    .page-products .products-grid .product-item {
        width: 33.33333333%;
    }
    .page-products.page-layout-1column .products-grid .product-item {
    }
    .page-products.page-layout-3columns .products-grid .product-item {
        width: 50%;
    }
    .page-products .columns {
        padding-top: 0;
        position: relative;
        z-index: 1;
    }
    .toolbar-amount {
        display: block;
        float: left;
        position: static;
    }
    .products.wrapper ~ .toolbar .pages {
        float: left;
        margin-bottom: 0;
    }
    .modes {
        display: inline-block;
        float: left;
        margin-right: 20px;
    }
    .products.wrapper ~ .toolbar .modes {
        display: none;
    }
    .modes-mode {
        background-color: #f0f0f0;
        box-shadow: inset 0 1px 0 0 #ffffff, inset 0 -1px 0 0 rgba(204, 204, 204, 0.3);
        color: #7d7d7d;
        border: 1px solid #cccccc;
        border-right: 0;
        float: left;
        font-weight: 400;
        line-height: 1;
        padding: 7px 10px;
        text-align: center;
        display: inline-block;
        text-decoration: none;
    }
    .modes-label + .modes-mode {
        border-radius: 3px 0 0 3px;
    }
    .modes-mode:hover {
        color: #7d7d7d;
        background: #ebebeb;
    }
    .modes-mode:last-child {
        border-radius: 0 3px 3px 0;
        border-right: 1px solid #cccccc;
    }
    .modes-mode.active {
        box-shadow: inset 0 1px 0 0 rgba(204, 204, 204, 0.8), inset 0 -1px 0 0 rgba(204, 204, 204, 0.3);
        background: #dedede;
        color: #9e9e9e;
    }
    .modes-mode > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }
    .modes-mode:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 16px;
        line-height: inherit;
        color: #7d7d7d;
        content: '\e60d';
        font-family: 'luma-icons';
        vertical-align: middle;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    }
    .modes-mode:hover:before {
        color: #7d7d7d;
    }
    .page-products .sorter {
        position: static;
    }
    .mode-list:before {
        content: '\e60b';
    }
    .limiter {
        float: right;
    }
    .products.wrapper ~ .toolbar .limiter {
        display: block;
    }
    .product-info-main .page-title-wrapper h1 {
        margin-bottom: 10px;
        margin-top: 0;
        text-align: left;
    }
    .product-info-main .product-add-form {
        padding-top: 20px;
    }
    .box-tocart .action.tocart {
        margin-bottom: 0;
        margin-right: 1%;
        width: 49%;
    }
    .product-social-links {
        text-align: left;
    }
    .product-options-bottom .price-box .price-container,
    .product-info-price .price-box .price-container {
        font-size: 21px;
    }
    .product-options-bottom .price-box .price-container .price,
    .product-info-price .price-box .price-container .price {
        font-size: 36px;
        line-height: 36px;
    }
    .product-options-bottom .price-box .price-including-tax + .price-excluding-tax .price,
    .product-info-price .price-box .price-including-tax + .price-excluding-tax .price {
        font-size: 1.4rem;
        line-height: 16px;
    }
    .product-info-main {
        float: right;
    }
    .product.media {
        float: left;
        margin-bottom: 25px;
    }
    .page-layout-1column .product-info-main {
        width: 40%;
    }
    .page-layout-1column .product.media {
        width: 57%;
    }
    .page-layout-2columns-left .product-info-main,
    .page-layout-2columns-right .product-info-main,
    .page-layout-3columns .product-info-main {
        width: 48%;
    }
    .page-layout-2columns-left .product.media,
    .page-layout-2columns-right .product.media,
    .page-layout-3columns .product.media {
        width: 50%;
    }
    .product-add-form .product-options-wrapper .field:not(.date) > .control {

    }
    .sidebar .product-items .product-item-info .product-item-photo {
        float: left;
        left: auto;
        margin: 0 10px 10px 0;
        position: relative;
        top: auto;
    }
    .sidebar .product-items .product-item-details {
        margin: 0;
    }
    .sidebar .product-items .product-item-actions {
        clear: left;
    }
    .compare.wrapper {
        float: right;
        margin: 0;
        padding: 0;
        list-style: none none;
    }
    .compare.wrapper .action.compare {
        line-height: 32px;
        color: #333333;
        text-decoration: none;
    }
    .compare.wrapper .action.compare:visited {
        color: #333333;
        text-decoration: none;
    }
    .compare.wrapper .action.compare:hover {
        color: #333333;
        text-decoration: underline;
    }
    .compare.wrapper .action.compare:active {
        color: #333333;
        text-decoration: underline;
    }
    .compare.wrapper .counter.qty {
        color: #7d7d7d;
    }
    .compare.wrapper .counter.qty:before {
        content: '(';
    }
    .compare.wrapper .counter.qty:after {
        content: ')';
    }
    .block-search {
        float: right;
        padding-left: 15px;
        position: relative;
        width: 250px;
        z-index: 4;
    }
    .block-search .control {
        border-top: 0;
        margin: 0;
        padding: 0;
    }
    .block-search input {
        margin: 0;
        padding-right: 35px;
        position: static;
    }
    .block-search input::-webkit-input-placeholder {
        color: #c2c2c2;
    }
    .block-search input:-moz-placeholder {
        color: #c2c2c2;
    }
    .block-search input::-moz-placeholder {
        color: #c2c2c2;
    }
    .block-search input:-ms-input-placeholder {
        color: #c2c2c2;
    }
    .block-search .action.search {
        display: inline-block;
        background-image: none;
        background: none;
        -moz-box-sizing: content-box;
        border: 0;
        box-shadow: none;
        line-height: inherit;
        margin: 0;
        padding: 0;
        text-decoration: none;
        text-shadow: none;
        font-weight: 400;
        position: absolute;
        right: 10px;
        top: 0;
        z-index: 1;
    }
    .block-search .action.search > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }
    .block-search .action.search:before {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 16px;
        line-height: 32px;
        color: #8f8f8f;
        content: '\e615';
        font-family: 'luma-icons';
        margin: 0;
        vertical-align: top;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    }
    .block-search .action.search:hover:before {
        color: #333333;
    }
    .block-search .action.search:active:before {
        color: inherit;
    }
    .block-search .action.search:focus,
    .block-search .action.search:active {
        background: none;
        border: none;
    }
    .block-search .action.search:hover {
        background: none;
        border: none;
    }
    .block-search .action.search.disabled,
    .block-search .action.search[disabled],
    fieldset[disabled] .block-search .action.search {
        pointer-events: none;
        opacity: 0.5;
    }
    .block-search .action.search:focus:before {
        color: #333333;
    }
    .search-autocomplete {
        margin-top: 0;
    }
    .checkout-cart-index .page-main {
        padding-left: 15px;
        padding-right: 15px;
    }
    .cart-container .form-cart .actions.main {
        text-align: right;
    }
    .cart-container .form-cart .actions.main button {
        margin-right: 0;
    }
    .cart-container .widget {
        float: left;
    }
    .cart-container-inner-container {
        width: 25%;
        float: right;
        -ms-flex-order: 2;
        -webkit-order: 2;
        order: 2;
        padding: 0 0 25px;
        position: relative;
    }
    .cart-summary > .title {
        display: block;
    }
    .cart-summary .fieldset .actions-toolbar {
        margin-left: 0;
    }
    .cart-summary .fieldset .actions-toolbar > .secondary {
        float: none;
    }
    .cart-summary .block > .title {
        padding-left: 0;
    }
    .cart-summary .block .fieldset .field {
        margin: 0 0 20px;
        margin: 0 0 10px;
    }
    .cart-summary .block .fieldset .field:not(.choice) > .label {
        box-sizing: content-box;
        float: none;
        width: auto;
        text-align: left;
        padding: 0;
    }
    .cart-summary .block .fieldset .field:not(.choice) > .control {
        float: none;
        width: auto;
    }
    .cart-summary .block .fieldset .field > .label {
        margin: 0 0 8px;
        display: inline-block;
    }
    .cart-summary .block .fieldset .field.choice:before,
    .cart-summary .block .fieldset .field.no-label:before {
        display: none;
    }
    .cart-summary .checkout-methods-items {
        padding: 0;
    }
    .cart.table-wrapper .items {
        min-width: 100%;
        width: auto;
    }
    .cart.table-wrapper tbody td {
        padding-top: 20px;
    }

    .cart.table-wrapper .item .col.price,
    .cart.table-wrapper .item .col.subtotal {
        width: 100px;
    }
    .cart.table-wrapper .item .col.subtotal .save-info-block {
        width: 250px;
    }
    .cart.table-wrapper .item-actions td {
        padding: 0;
    }
    .cart.table-wrapper .product-item-photo {
        display: table-cell;
        max-width: 100%;
        padding-right: 20px;
        position: static;
        vertical-align: top;
        width: 1%;
    }
    .cart.table-wrapper .product-item-details {
        padding-bottom: 35px;
    }
    .cart.table-wrapper .product-item-details {
        display: table-cell;
        vertical-align: top;
        white-space: normal;
        width: 99%;
    }
    .cart-products-toolbar {
        margin: 2px 0 0;
    }
    .cart-products-toolbar .toolbar-amount {
        line-height: 30px;
        margin: 0;
    }
    .cart-products-toolbar .pages {
        float: right;
    }
    .cart-products-toolbar .pages .item:last-child {
        margin-right: 0;
    }
    .cart.table-wrapper .cart-products-toolbar + .cart thead tr th.col {
        padding-bottom: 10px;
        padding-top: 10px;
    }
    .cart.table-wrapper .cart + .cart-products-toolbar {
        margin-top: 25px;
    }
    .cart.table-wrapper .actions-toolbar > .action > span {

    }
    .cart-discount {
        width: 75%;
        float: left;
        -ms-flex-order: 1;
        -webkit-order: 1;
        order: 1;
        border: 0;
        box-sizing: border-box;
        padding-right: 4%;
    }
    .cart-discount .block .title:after {
        display: inline;
        margin-left: 10px;
        position: static;
    }
    .block.crosssell {
        width: 75%;
        float: left;
        -ms-flex-order: 1;
        -webkit-order: 1;
        order: 1;
        padding: 0 4% 0 0;
    }
    .block.crosssell .products-grid .product-item {
        width: 25%;
    }
    .cart-gift-container {
        float: left;
        width: 75%;
        margin-top: -80px;
    }
    .minicart-wrapper {
        margin-left: 13px;
    }
    .minilist .action.delete:before,
    .minicart-wrapper .action.edit:before {
        font-size: 16px;
        line-height: inherit;
    }
    .opc-wrapper {
        -ms-flex-order: 1;
        -webkit-order: 1;
        order: 1;
        padding-right: 30px;
    }
    .checkout-onepage-success .print {
        display: block;
        float: right;
        margin: 22px 0 0;
    }
    .opc-estimated-wrapper {
        display: none;
    }
    .opc-progress-bar-item {
        width: 185px;
    }
    .checkout-index-index .modal-popup .form-shipping-address {
        max-width: 600px;
    }
    .checkout-index-index .modal-popup .modal-footer .action-save-address {
        float: right;
        margin: 0 0 0 10px;
    }
    .checkout-shipping-method .actions-toolbar > .primary {
        float: right;
    }
    .checkout-shipping-method .actions-toolbar .action.primary {
        margin: 0;
    }
    .opc-wrapper .form-login,
    .opc-wrapper .form-shipping-address {
    }
    .table-checkout-shipping-method {
        width: auto;
    }
    .opc-sidebar {
        margin: 46px 0 20px;
        width: 100%;
        float: right;
        -ms-flex-order: 2;
        -webkit-order: 2;
        order: 2;
    }
    .opc-sidbar .opc-background-wrapper {
        border: 1px solid #EEF4ED;
        border-radius: 10px;
        box-shadow: rgb(99 99 99 / 20%) 0px 2px 8px 0px;
        height: max-content;
    }
    .opc-summary-wrapper .modal-header .action-close {
        display: none;
    }
    .authentication-dropdown {
        background-color: #ffffff;
        border: 1px solid #aeaeae;
        -webkit-transform: scale(1, 0);
        -webkit-transform-origin: 0 0;
        -webkit-transition: -webkit-transform linear 0.1s, visibility 0s linear 0.1s;
        position: absolute;
        text-align: left;
        top: 100%;
        transform: scale(1, 0);
        transform-origin: 0 0;
        transition: transform linear .1s, visibility 0s linear .1s;
        visibility: hidden;
        width: 100%;
    }
    .authentication-dropdown._show {
        z-index: 100;
        -webkit-transform: scale(1, 1);
        -webkit-transition: -webkit-transform linear 0.1s, visibility 0s linear 0s;
        transform: scale(1, 1);
        transition: transform linear .1s, visibility 0s linear 0s;
        visibility: visible;
    }
    .authentication-wrapper {
        width: 33.33333333%;
        text-align: right;
    }
    .block-authentication .block-title {
        font-size: 2.6rem;
        border-bottom: 0;
        margin-bottom: 25px;
    }
    .block-authentication .actions-toolbar > .primary {
        display: inline;
        float: right;
        margin-right: 0;
    }
    .block-authentication .actions-toolbar > .primary .action {
        margin-right: 0;
    }
    .block-authentication .actions-toolbar > .secondary {
        float: left;
        margin-right: 2rem;
        padding-top: 1rem;
    }
    .popup-authentication .modal-inner-wrap {
        min-width: 768px;
        width: 60%;
    }
    .popup-authentication .block-authentication {
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-flex-direction: row;
        -ms-flex-direction: row;
        flex-direction: row;
        border-top: 1px solid #f5f5f5;
    }
    .popup-authentication .block[class],
    .popup-authentication .form-login,
    .popup-authentication .fieldset,
    .popup-authentication .block-content {
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-flex-grow: 1;
        flex-grow: 1;
    }
    .popup-authentication .block[class] {
        box-sizing: border-box;
        float: left;
        padding: 10px 30px 0 0;
        width: 50%;
    }
    .popup-authentication .block[class] + .block {
        border-left: 1px solid #f5f5f5;
        border-top: 0;
        margin: 0;
        padding: 10px 0 0 40px;
    }
    .popup-authentication .block[class] + .block:before {
        left: 0;
        top: 50%;
    }
    .popup-authentication .actions-toolbar {
        margin-bottom: 0;
        margin-top: auto;
    }
    .checkout-payment-method .actions-toolbar .primary {
        width: 100%
    }
    .checkout-payment-method .actions-toolbar .primary button {
        width: 100% !important;
    }
    .checkout-payment-method .fieldset > .field-select-billing > .control {
        float: none;
        width: 100%;
    }
    .checkout-payment-method .payment-method-content .fieldset > .field {
        margin: 0 0 20px;
    }
    .checkout-payment-method .payment-method-content .fieldset > .field.type .control {
        margin-left: 25.8%;
    }
    .checkout-payment-method .payment-method-content .fieldset > .field.type.no-detection .control {
        margin-left: 0;
    }
    .checkout-billing-address .action-update {
        float: right;
    }
    .checkout-billing-address .actions-toolbar .action-cancel {
        margin: 6px 20px 0 0;
    }
    .checkout-payment-method .payment-option-title {
        padding-left: 22px;
    }
    .checkout-payment-method .payment-option-content .payment-option-inner + .actions-toolbar {
        margin-left: 0;
    }
    .login-container .block.login .actions-toolbar > .primary {
        margin-bottom: 0;
        margin-right: 30px;
    }
    .login-container .block.login .actions-toolbar > .secondary {
        float: left;
    }
    .login-container .fieldset > .field > .control {
        width: 55%;
    }
    .form-create-account .fieldset-fullname .fields .field {
        float: left;
        margin: 0 10px 10px 0;
    }
    .form-create-account .fieldset-fullname .field-name-prefix,
    .form-create-account .fieldset-fullname .field-name-suffix {
    }
    .form.password.reset,
    .form.send.confirmation,
    .form.password.forget,
    .form.create.account {
        min-width: 600px;
        width: 50%;
    }
    .account.page-layout-2columns-left .sidebar-main,
    .account.page-layout-2columns-left .sidebar-additional {
    }
    .account.page-layout-2columns-left .column.main {
    }
    .account .data.table {
        margin-bottom: 0;
    }
    .account .data.table .col.actions {
        white-space: nowrap;
    }
    .block-addresses-list .items.addresses > .item {
        margin-bottom: 20px;
    }
    .block-addresses-list .items.addresses > .item:nth-last-child(1),
    .block-addresses-list .items.addresses > .item:nth-last-child(2) {
        margin-bottom: 0;
    }
    .form-edit-account .fieldset .fieldset {
        margin-bottom: 20px;
        width: 100%;
    }
    .control.captcha-image .captcha-img {
        margin: 0 10px 10px 0;
    }
    .page-product-downloadable .product-options-wrapper {
        float: left;
        width: 55%;
    }
    .page-product-downloadable .product-options-bottom {
        float: right;
        width: 40%;
    }
    .page-product-grouped .product-info-price {
        float: none;
    }
    .page-product-grouped .minimal-price {
        margin-top: -8px;
    }
    .filter.block {
        margin-bottom: 40px;
    }
    .filter-title {
        display: none;
    }
    .filter-content .item {
        margin: 0;
    }
    .filter-actions {
        margin-bottom: 30px;
    }
    .filter.active .filter-options,
    .filter-options {
        background: transparent;
        clear: both;
        display: block;
        overflow: initial;
        position: static;
    }
    .filter-subtitle {
        display: block;
        position: static;
    }
    .page-layout-1column .products ~ .toolbar-products {
        position: static;
    }
    .page-layout-1column.page-with-filter .column.main {
        padding-top: 0;
        position: relative;
        z-index: 1;
    }
    .page-layout-1column .filter.block {
        border-top: 1px solid #cccccc;
    }
    .page-layout-1column .filter-content {
    }
    .page-layout-1column .filter-subtitle {
        display: none;
    }
    .page-layout-1column .filter-options-item {
        border: 0;
        display: inline-block;
        margin-right: 25px;
        position: relative;
    }
    .page-layout-1column .filter-options-item.active {
        z-index: 2;
    }
    .page-layout-1column .filter-options-item.active .filter-options-content {
        visibility: visible;
    }
    .page-layout-1column .filter-options-item.active:hover {
        z-index: 3;
    }
    .page-layout-1column .filter-options-item.active:after,
    .page-layout-1column .filter-options-item.active:before {
        border: 8px solid transparent;
        height: 0;
        width: 0;
        border-bottom-color: #000000;
        bottom: -1px;
        content: '';
        display: block;
        left: 5px;
        position: absolute;
        z-index: 3;
    }
    .page-layout-1column .filter-options-item.active:after {
        border-bottom-color: #ffffff;
        margin-top: 2px;
        z-index: 4;
    }
    .page-layout-1column .filter-options-title {
        padding: 0 20px 0 0;
    }
    .page-layout-1column .filter-options-title:after {
        right: 2px;
        top: 3px;
        z-index: 3;
    }
    .page-layout-1column .filter-options-content {
        background: #ffffff;
        -webkit-box-shadow: 0 3px 5px 0 rgba(50, 50, 50, 0.75);
        -moz-box-shadow: 0 3px 5px 0 rgba(50, 50, 50, 0.75);
        -ms-box-shadow: 0 3px 5px 0 rgba(50, 50, 50, 0.75);
        box-shadow: 0 3px 5px 0 rgba(50, 50, 50, 0.75);
        border: 1px solid #cccccc;
        padding: 5px 0;
        position: absolute;
        top: 100%;
        visibility: hidden;
        width: 180px;
        z-index: 2;
    }
    .page-layout-1column .filter-options-content .item {
        margin: 0;
        padding: 5px;
    }
    .page-layout-1column .filter-options-content .item a {
        margin-left: 0;
    }
    .page-layout-1column .filter-options-content .item:hover {
        background-color: #e8e8e8;
    }
    .page-layout-1column .filter-current {
        display: inline;
        line-height: 35px;
    }
    .page-layout-1column .filter-current-subtitle {
        color: #7d7d7d;
        display: inline;
        font-size: 14px;
        font-weight: normal;
        padding: 0;
    }
    .page-layout-1column .filter-current-subtitle:after {
        content: ':';
    }
    .page-layout-1column .filter-current .item,
    .page-layout-1column .filter-current .items {
        display: inline;
    }
    .page-layout-1column .filter-current .item {
        margin-right: 0;
        white-space: nowrap;
    }
    .page-layout-1column .filter-current .action.remove {
        line-height: normal;
    }
    .page-layout-1column .filter-actions {
        display: inline;
        white-space: nowrap;
    }
    .page-layout-1column .filter-actions ~ .filter-options {
        margin-top: 25px;
    }
    .order-links .item {
        float: left;
        margin: 0 -1px 0 0;
    }
    .order-links .item a {
        padding: 1px 35px;
    }
    .order-links .item strong {
        border-bottom: 0;
        margin-bottom: -1px;
        padding: 1px 35px 2px 35px;
    }
    .order-actions-toolbar .action.print {
        display: block;
        float: right;
    }
    .account .column.main .block.block-order-details-view .block-content:not(.widget) .box,
    [class^='sales-guest-'] .column.main .block.block-order-details-view .block-content:not(.widget) .box,
    .sales-guest-view .column.main .block.block-order-details-view .block-content:not(.widget) .box {
        clear: none;
        float: left;
        width: 25%;
    }
    .block-order-details-comments {
        margin: 0 0 60px;
    }
    .block-order-details-comments .comment-date {
        clear: left;
        float: left;
        margin-right: 50px;
        max-width: 90px;
    }
    .block-order-details-comments .comment-content {
        overflow: hidden;
    }
    .order-details-items {
        margin-top: -1px;
        padding: 25px;
    }
    .order-details-items .col.name {
        padding-left: 0;
    }
    .order-details-items .col.price {
        text-align: center;
    }
    .order-details-items .col.subtotal {
        text-align: right;
    }
    .order-details-items tbody td {
        padding-bottom: 20px;
        padding-top: 20px;
    }
    .order-details-items tfoot .amount,
    .order-details-items tfoot .mark {
        text-align: right;
    }
    .order-details-items.ordered .order-title {
        display: none;
    }
    .order-pager-wrapper .order-pager-wrapper-top {
        padding-left: 0;
        padding-right: 0;
    }
    .order-pager-wrapper .toolbar-amount {
        position: relative;
    }
    .order-pager-wrapper .pages {
        float: right;
    }
    .table-order-items tbody .col.label,
    .table-order-items tbody .col.value {
        padding-left: 0;
    }
    .table-order-items.invoice .col.qty,
    .table-order-items.shipment .col.qty {
        text-align: center;
    }
    .table-order-items.creditmemo .col.qty,
    .table-order-items.creditmemo .col.discount,
    .table-order-items.creditmemo .col.subtotal {
        text-align: center;
    }
    .table-order-items.creditmemo .col.total {
        text-align: right;
    }
    html,
    body {
        height: 100%;
    }
    .ie9 body {
        background-color: #8f8f8f;
    }
    .navigation ul {
        padding: 0 8px;
    }
    .page-header {
        border: 0;
        margin-bottom: 0;
    }
    .page-header .panel.wrapper {
        border-bottom: 1px solid #e8e8e8;
        background-color: #7e807e;
    }
    .page-header .header.panel {
        padding-bottom: 10px;
        padding-top: 10px;
    }
    .page-header .switcher {
        float: right;
        margin-left: 15px;
        margin-right: 0;
        -ms-flex-order: 1;
        -webkit-order: 1;
        order: 1;
    }
    .page-main > .page-title-wrapper .page-title {
        display: block;
    }
    .page-main > .page-title-wrapper .page-title + .action {
        float: right;
        margin-top: 20px;
    }
    .customer-welcome {
        display: inline-block;
        position: relative;
    }
    .customer-welcome:before,
    .customer-welcome:after {
        content: '';
        display: table;
    }
    .customer-welcome:after {
        clear: both;
    }
    .customer-welcome .action.switch {
        padding: 0;
        cursor: pointer;
        display: inline-block;
        text-decoration: none;
    }
    .customer-welcome .action.switch > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }
    .customer-welcome .action.switch:after {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 10px;
        line-height: 22px;
        color: inherit;
        content: '\e622';
        font-family: 'luma-icons';
        margin: 0;
        vertical-align: top;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    }
    .customer-welcome .action.switch:hover:after {
        color: inherit;
    }
    .customer-welcome .action.switch:active:after {
        color: inherit;
    }
    .customer-welcome .action.switch.active {
        display: inline-block;
        text-decoration: none;
    }
    .customer-welcome .action.switch.active > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }
    .customer-welcome .action.switch.active:after {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 10px;
        line-height: 22px;
        color: inherit;
        content: '\e621';
        font-family: 'luma-icons';
        margin: 0;
        vertical-align: top;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        speak: none;
        text-align: center;
    }
    .customer-welcome .action.switch.active:hover:after {
        color: inherit;
    }
    .customer-welcome .action.switch.active:active:after {
        color: inherit;
    }
    .customer-welcome ul {
        margin: 0;
        padding: 0;
        list-style: none none;
        background: #ffffff;
        border: 1px solid #bbbbbb;
        margin-top: 4px;
        min-width: 100%;
        z-index: 101;
        box-sizing: border-box;
        display: none;
        position: absolute;
        top: 100%;
        right: 0;
        box-shadow: 0 3px 3px rgba(0, 0, 0, 0.15);
    }
    .customer-welcome ul li {
        margin: 0;
        padding: 0;
    }
    .customer-welcome ul li:hover {
        background: #e8e8e8;
        cursor: pointer;
    }
    .customer-welcome ul:before,
    .customer-welcome ul:after {
        border-bottom-style: solid;
        content: '';
        display: block;
        height: 0;
        position: absolute;
        width: 0;
    }
    .customer-welcome ul:before {
        border: 6px solid;
        border-color: transparent transparent #ffffff transparent;
        z-index: 99;
    }
    .customer-welcome ul:after {
        border: 7px solid;
        border-color: transparent transparent #bbbbbb transparent;
        z-index: 98;
    }
    .customer-welcome ul:before {
        right: 10px;
        top: -12px;
    }
    .customer-welcome ul:after {
        right: 9px;
        top: -14px;
    }
    .customer-welcome.active {
        overflow: visible;
    }
    .customer-welcome.active ul {
        display: block;
    }
    .customer-welcome li a {
        color: #333333;
        text-decoration: none;
        display: block;
        line-height: 1.4;
        padding: 8px;
    }
    .customer-welcome li a:visited {
        color: #333333;
        text-decoration: none;
    }
    .customer-welcome li a:hover {
        color: #333333;
        text-decoration: none;
    }
    .customer-welcome li a:active {
        color: #333333;
        text-decoration: none;
    }
    .customer-welcome .customer-name {
        cursor: pointer;
    }
    .customer-welcome .customer-menu {
        display: none;
    }
    .customer-welcome .action.switch {
        background-image: none;
        background: none;
        -moz-box-sizing: content-box;
        border: 0;
        box-shadow: none;
        line-height: inherit;
        margin: 0;
        padding: 0;
        text-decoration: none;
        text-shadow: none;
        font-weight: 400;
        color: #ffffff;
    }
    .customer-welcome .action.switch:focus,
    .customer-welcome .action.switch:active {
        background: none;
        border: none;
    }
    .customer-welcome .action.switch:hover {
        background: none;
        border: none;
    }
    .customer-welcome .action.switch.disabled,
    .customer-welcome .action.switch[disabled],
    fieldset[disabled] .customer-welcome .action.switch {
        pointer-events: none;
        opacity: 0.5;
    }
    .customer-welcome .header.links {
        min-width: 175px;
    }
    .customer-welcome.active .action.switch:after {
        content: '\e621';
    }
    .customer-welcome.active .customer-menu {
        display: block;
    }
    .customer-welcome .greet {
        display: none;
    }
    .header.panel > .header.links {
        margin: 0;
        padding: 0;
        list-style: none none;
        float: right;
        margin-left: auto;
    }
    .header.panel > .header.links > li {
        display: inline-block;
        vertical-align: top;
    }
    .header.panel > .header.links > li {
        margin: 0 0 0 15px;
    }
    .header.panel > .header.links > li.welcome,
    .header.panel > .header.links > li > a {
        display: inline-block;
        line-height: 1.4;
    }
    .header.panel > .header.links > li.welcome a {
        color: #ffffff;
        padding-left: 5px;
    }
    .header.panel > .header.links > .authorization-link:after {
        content: attr(data-label);
        display: inline-block;
        margin: 0 -5px 0 5px;
    }
    .header.panel > .header.links > .customer-welcome + .authorization-link {
        display: none;
    }
    .header.content {
        padding: 30px 20px 0;
    }
    .logo {
        margin: 0 auto 20px 0;
    }
    .logo img {
        max-height: inherit;
        width: auto;
    }
    .page-wrapper {
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        margin: 0;
        min-height: 100%;
        position: relative;
        transition: margin 0.3s ease-out 0s;
    }
    .page-wrapper > .breadcrumbs,
    .page-wrapper > .top-container,
    .page-wrapper > .widget {
        box-sizing: border-box;
        width: 100%;
    }
    .ie10 .page-wrapper,
    .ie11 .page-wrapper {
        height: 100%;
    }
    .page-footer {
        background: #fff;
        margin-top: auto;
        padding-bottom: 50px;
    }
    .page-footer .switcher .options ul.dropdown {
        bottom: -10px;
        left: 100%;
        margin: 0 0 0 20px;
        top: auto;
    }
    .page-footer .switcher .options ul.dropdown:before,
    .page-footer .switcher .options ul.dropdown:after {
        bottom: 13px;
        left: auto;
        right: 100%;
        top: auto;
    }
    .page-footer .switcher .options ul.dropdown:before {
        border-color: transparent #ffffff transparent transparent;
    }
    .page-footer .switcher .options ul.dropdown:after {
        border-color: transparent #bbbbbb transparent transparent;
        margin: 0 0 -1px -1px;
    }
    .footer.content {
        border-top: none;
        background: #fff;
    }
    .footer.content .block {
    }
    .footer.content ul {
        padding-right: 50px;
    }
    .footer.content .switcher.store {
        display: block;
        margin: 0;
    }
    .footer.content .links {

    }
    .footer.content .links li {
        background: transparent;
        border: none;
        margin: 0 0 8px;
        padding: 0;
        line-height: normal;
    }
    .footer.content .links a,
    .footer.content .links strong {
        display: inline;
    }
    .block.widget .products-grid .product-item {
        width: 33.33333333%;
    }
    .sidebar .block.widget .products-grid .product-item {
        margin-left: 0;
        width: 100%;
    }
    .sidebar .block.widget .products-grid .product-item .actions-secondary {
        display: block;
        padding: 10px 0;
    }
    .page-layout-1column .block.widget .products-grid .product-item {
        width: 25%;
    }
    .page-layout-3columns .block.widget .products-grid .product-item {
        width: 50%;
    }
    .sidebar .block.widget .pager .pages-item-next {
        padding: 0;
    }
    .sidebar .block.widget .pager .pages-item-next .action {
        margin: 0;
    }
    .checkout-index-index .opc-sidebar .opc-block-info .checkout-info-block .text-container {
        float: left;
    }
    .checkout-index-index .opc-sidebar .opc-block-info .checkout-info-block:after {
        clear: both;
        content: "";
        display: table;
    }
}
@media all and (min-width: 1024px), print {
    .checkout-index-index .opc-wrapper {
        width: 63%;
    }
    .opc-sidebar {
        width: 36%;
    }
    .wishlist-index-index .products-grid .product-items {
        margin: 0;
    }
    .wishlist-index-index .products-grid .product-item {
        margin-left: calc((100% - 4 * 24.439%) / 3);
        padding: 0;
        width: 24.439%;
    }
    .wishlist-index-index .products-grid .product-item:nth-child(4n + 1) {
        margin-left: 0;
    }
    .products-grid .product-item {
        width: 20%;
    }
    .page-layout-1column .products-grid .product-item {
        width: 16.66666667%;
    }
    .page-layout-3columns .products-grid .product-item {
        width: 25%;
    }
    .page-products .products-grid .product-items {
        margin: 0;
    }
    .page-products .products-grid .product-item {
        margin-left: calc((100% - 4 * 24.439%) / 3);
        padding: 0;
        width: 24.439%;
    }
    .page-products .products-grid .product-item:nth-child(4n + 1) {
    }
    .page-products.page-layout-1column .products-grid .product-item {

    }
    .page-products.page-layout-3columns .products-grid .product-item {
        margin-left: 1%;
        width: 32.667%;
    }
    .page-products.page-layout-3columns .products-grid .product-item:nth-child(3n) {
        margin-left: 1%;
    }
    .page-products.page-layout-3columns .products-grid .product-item:nth-child(3n + 1) {
        margin-left: 0;
    }
    .box-tocart .paypal:first-of-type {
        margin-top: 13px;
    }
    .checkout-index-index .modal-popup .modal-inner-wrap {
        margin-left: -400px;
        width: 800px;
        left: 50%;
    }
    .opc-wrapper .shipping-address-item {
        width: 33.33333333%;
    }
    .opc-wrapper .shipping-address-item:before {
        background: #cccccc;
        height: calc(100% - 20px);
        content: '';
        left: 0;
        position: absolute;
        top: 0;
        width: 1px;
    }
    .opc-wrapper .shipping-address-item:nth-child(3n + 1):before {
        display: none;
    }
    .opc-wrapper .shipping-address-item.selected-item:before {
        display: none;
    }
    .opc-wrapper .shipping-address-item.selected-item + .shipping-address-item:before {
        display: none;
    }
    .table-checkout-shipping-method {
        min-width: 500px;
    }
    .block.widget .products-grid .product-item {
        width: 20%;
    }
    .page-layout-1column .block.widget .products-grid .product-item {
        margin-left: calc((100% - 5 * (100%/6)) / 4);
        width: 16.66666667%;
    }
    .page-layout-1column .block.widget .products-grid .product-item:nth-child(4n + 1) {
        margin-left: calc((100% - 5 * (100%/6)) / 4);
    }
    .page-layout-1column .block.widget .products-grid .product-item:nth-child(5n + 1) {
        margin-left: 0;
    }
    .page-layout-3columns .block.widget .products-grid .product-item {
        width: 25%;
    }
    .block.widget .products-grid .product-items {
        margin: 0;
    }
    .block.widget .products-grid .product-item {
        margin-left: calc((100% - 4 * 24.439%) / 3);
        padding: 0;
        width: 24.439%;
    }
    .block.widget .products-grid .product-item:nth-child(4n + 1) {
        margin-left: 0;
    }
}
@media all and (min-width: 1440px), print {
    .sidebar .product-items .product-item-info .product-item-photo {
        float: none;
        left: 0;
        margin: 0;
        position: absolute;
        top: 0;
    }
    .sidebar .product-items .product-item-details {
        margin-left: 85px;
    }
}

/**
 * Owl Carousel v2.1.0
 * Copyright 2013-2016 David Deutsch
 * Licensed under MIT (https://github.com/OwlCarousel2/OwlCarousel2/blob/master/LICENSE)
 */
.owl-carousel,
.owl-carousel .owl-item {
    -webkit-tap-highlight-color: transparent;
    position: relative;
}
.owl-carousel {
    display: none;
    width: 100%;
    z-index: 1;
}
.owl-carousel .owl-stage {
    position: relative;
    -ms-touch-action: pan-Y;
}
.owl-carousel .owl-stage:after {
    content: ".";
    display: block;
    clear: both;
    visibility: hidden;
    line-height: 0;
    height: 0;
}
.owl-carousel .owl-stage-outer {
    position: relative;
    overflow: hidden;
    -webkit-transform: translate3d(0, 0, 0);
}
.owl-carousel .owl-item {
    min-height: 1px;
    float: left;
    -webkit-backface-visibility: hidden;
    -webkit-touch-callout: none;
}
.owl-carousel .owl-item img {
    display: block;
    width: 100%;
    -webkit-transform-style: preserve-3d;
}
.owl-carousel .owl-dots.disabled,
.owl-carousel .owl-nav.disabled {
    display: none;
}
.owl-carousel .owl-dot,
.owl-carousel .owl-nav .owl-next,
.owl-carousel .owl-nav .owl-prev {
    cursor: pointer;
    cursor: hand;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.owl-carousel.owl-loaded {
    display: block;
}
.owl-carousel.owl-loading {
    opacity: 0;
    display: block;
}
.owl-carousel.owl-hidden {
    opacity: 0;
}
.owl-carousel.owl-refresh .owl-item {
    display: none;
}
.owl-carousel.owl-drag .owl-item {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.owl-carousel.owl-grab {
    cursor: move;
    cursor: grab;
}
.owl-carousel.owl-rtl {
    direction: rtl;
}
.owl-carousel.owl-rtl .owl-item {
    float: right;
}
.no-js .owl-carousel {
    display: block;
}
.owl-carousel .animated {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}
.owl-carousel .owl-animated-in {
    z-index: 0;
}
.owl-carousel .owl-animated-out {
    z-index: 1;
}
.owl-carousel .fadeOut {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut;
}
@-webkit-keyframes fadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}
@keyframes fadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}
.owl-height {
    transition: height 0.5s ease-in-out;
}
.owl-carousel .owl-item .owl-lazy {
    opacity: 0;
    transition: opacity 0.4s ease;
}
.owl-carousel .owl-item img.owl-lazy {
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
}
.owl-carousel .owl-video-wrapper {
    position: relative;
    height: 100%;
    background: #000;
}
.owl-carousel .owl-video-play-icon {
    position: absolute;
    height: 80px;
    width: 80px;
    left: 50%;
    top: 50%;
    margin-left: -40px;
    margin-top: -40px;
    background: url(../../../../../frontend/Gfp/international/default/images/owl.video.play.png) no-repeat;
    cursor: pointer;
    z-index: 1;
    -webkit-backface-visibility: hidden;
    transition: -webkit-transform 0.1s ease;
    transition: transform 0.1s ease;
}
.owl-carousel .owl-video-play-icon:hover {
    -webkit-transform: scale(1.3, 1.3);
    -ms-transform: scale(1.3, 1.3);
    transform: scale(1.3, 1.3);
}
.owl-carousel .owl-video-playing .owl-video-play-icon,
.owl-carousel .owl-video-playing .owl-video-tn {
    display: none;
}
.owl-carousel .owl-video-tn {
    opacity: 0;
    height: 100%;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: contain;
    transition: opacity 0.4s ease;
}
.owl-carousel .owl-video-frame {
    position: relative;
    z-index: 1;
    height: 100%;
    width: 100%;
}
.owl-carousel {
    position: relative;
    padding: 0 36px;
}
.owl-carousel .owl-nav .owl-prev,
.owl-carousel .owl-nav .owl-next {
    background: transparent;
    border: none;
    position: absolute;
    top: 0;
    height: 100%;
    color: #2f3943;
    padding: 0;
    margin: 0;
    width: 50px;
}
.owl-carousel .owl-nav .owl-prev span,
.owl-carousel .owl-nav .owl-next span {
    display: none;
}
.owl-carousel .owl-nav .owl-prev:hover,
.owl-carousel .owl-nav .owl-next:hover {
    background: transparent;
    border: none;
}
.owl-carousel .owl-nav .owl-prev:before,
.owl-carousel .owl-nav .owl-next:before {
    font-family: 'luma-icons';
    border: none;
    display: inline-block;
    margin: 0;
    cursor: pointer;
    text-align: center;
    line-height: 2rem;
    color: inherit;
    font-weight: 400;
    font-size: 2rem;
    transform: rotate(0deg);
}
.owl-carousel .owl-nav .owl-prev::after,
.owl-carousel .owl-nav .owl-next::after {
    content: "";
    margin-left: 0;
}
.owl-carousel .owl-nav .owl-prev {
    left: 0;
}
.owl-carousel .owl-nav .owl-prev:before {
    right: 10px;
    content: "\e617";
}
.owl-carousel .owl-nav .owl-next {
    right: 0;
}
.owl-carousel .owl-nav .owl-next:before {
    left: 10px;
    content: "\e608";
}
.fotorama__thumb-border {
    border: 1px solid #667751;
}
.fotorama__caption {
    display: none;
}
.xgifts-container {
    text-align: center;
}
.xgifts-container .xgifts-products-list .product-items {
    flex-direction: column;
    width: 100%;
}
.xgifts-container .xgifts-products-list .product-items .product-item-info {
    width: 100%;
    padding: 20px 0;
}
.xgifts-container .xgifts-products-list .product-items .product-item-details {
    display: block;
    text-align: center;
}
.xgifts-container .xgifts-products-list .product-items .product-item-details .product-item-image {
    max-width: 100%;
}
.xgifts-container .xgifts-products-list .product-items .actions {
    text-align: center;
    width: 100%;
    margin-top: 10px;
}
.xgifts-container .xgifts-products-list .product-items .product-item {
    width: auto; display: inline-block;
}
.xgifts-container .xgifts-products-list .product-items .product-item:last-child .product-item-info {
    border-bottom: none;
}
.page-header {
    position: static;
    z-index: 100;
    max-width: 100%;
    background: white;
    width: 100%;
    height: auto;
    box-sizing: border-box;
    top: 0;
    transition: 0.4s ease all;
    padding: 0 15px;
}
.page-header .panel.wrapper {
    background-color: white;
    border-bottom: none;
    margin: 0;
}
.page-header .header.content {
    padding: 0;
}
.page-header.pageScrolled {
    box-shadow: 0 0 7px #808080;
}
.page-header.pageScrolled .header.content {
    max-width: 1570px;
    position: static;
    padding-top: 0;
}
.page-header.pageScrolled .header.content .block-search,
.page-header.pageScrolled .header.content #switcher-language,
.page-header.pageScrolled .header.content .logo-name {
    display: none;
}
.page-header.pageScrolled .header.content .logo {
    position: absolute;
    left: 25px;
    top: 10px;
    width: 100px;
    margin: 0;
}
.page-header.pageScrolled .header.content .minicart-wrapper {
    right: 20px;
    z-index: 10001;
    top: 20px;
}
.page-header.pageScrolled .nav-sections .nav-sections-items {
    margin: 17px auto;
    padding-left: 150px;
    padding-right: 75px;
    padding-bottom: 0;
    max-width: 1457px;
    transition: 0.4s ease all;
}
.checkout-index-index .page-header {
    position: static;
    box-shadow: none;
}
.checkout-index-index .page-header .header.content {
    position: static;
    padding-top: 0;
    margin: 0 auto;
}
.checkout-index-index .page-header .header.content .block-search,
.checkout-index-index .page-header .header.content #switcher-language,
.checkout-index-index .page-header .header.content .logo-name,
.checkout-index-index .page-header .header.content .minicart-wrapper {
}
.checkout-index-index .page-header .header.content .logo {

}
.page-header .switcher .options.active ul.dropdown {
    z-index: 100001;
}
.page-header .switcher .options .action.toggle::after {
    vertical-align: middle;
}
span.logo-name {
    margin-top: -70px;
    float: left;
    margin-left: 127px;
    font-weight: bold;
}
a.logo::before {
    border: none;
}
#search_mini_form .hidden {
    display: none;
}
header .block-search .action.search.disabled,
header .block-search .block-search .action.search[disabled],
header .block-search fieldset[disabled] .block-search .action.search {
    cursor: pointer;
    pointer-events: inherit;
}
header .block-search label::before {
    display: none !important;
}
header .block-search .control {
    margin-top: -10px;
}
header .block-search .action.search::before {
    color: #2D3843;
    font-size: 2rem;
    line-height: 2.7rem;
    opacity: 1;
    vertical-align: top;
    top: 0;
    right: 0;
}
header .block-search .action.search::after {
    display: none;
}
header .block-search .action.search:hover::before {
    color: #2D3843;
}
header .block-search button::after {
    display: none;
}
header .block-search .toggleSearchInput {
    display: inline-block;
    background-image: none;
    background: none;
    -moz-box-sizing: content-box;
    border: 0;
    box-shadow: none;
    line-height: inherit;
    margin: 0;
    padding: 0;
    text-decoration: none;
    text-shadow: none;
    font-weight: 400;
    position: absolute;
    right: 10px;
    top: 0;
    z-index: 1;
    cursor: pointer;
}
header .block-search .toggleSearchInput::before {
    color: #2D3843;
    font-size: 2rem;
    line-height: 2.8rem;
    opacity: 1;
    -webkit-font-smoothing: antialiased;
    content: '\e615';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
    top: 0;
}
header .block-search .toggleSearchInput:hover {
    color: #2D3843;
}
/**
Minicart
 */
.page-header .header.content .minicart-wrapper {
    position: static;
    z-index: 10001;
}
.minicart-wrapper .counterHolder {
    width: 40px;
    height: 40px;
    background: black;
    border-radius: 20px;
    display: block;
}
.minicart-wrapper .counterHolder.hasItem {
    background: #85b84b;
}
.minicart-wrapper .action.showcart,
.minicart-wrapper .action.showcart.active {
    position: relative;
}
.minicart-wrapper .action.showcart::before,
.minicart-wrapper .action.showcart.active::before {
    content: '\e908';
    color: white;
    top: 6px;
    left: 2px;
    position: absolute;
    font-size: 2.3rem;
}
.minicart-wrapper .action.showcart:hover::before,
.minicart-wrapper .action.showcart.active:hover::before {
    color: white;
}
.minicart-wrapper .action.showcart .counter.qty,
.minicart-wrapper .action.showcart.active .counter.qty {
    border-radius: 12px;
    position: absolute;
    top: -13px;
    right: -9px;
    background: black;
    min-width: 24px;
    box-sizing: border-box;
    height: 24px;
    display: block;
}
.minicart-wrapper a::before {
    border-bottom: none;
}

#switcher-language {
    float: right;
}
#switcher-language .switcher-label {
    display: none;
}
#switcher-language ul li a::before {
    border-bottom: 0;
}
/* Green bar detail page */
.product-view-navigation {
    min-height: 80px;
    background: #bae588;
    display: none;
    box-shadow: 0 3px 4px #808080;
}
.pageScrolled .product-view-navigation {
    display: block;
}
.product-view-navigation .productViewNavigationWrapper {
    max-width: 1570px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    height: 100%;
    min-height: 80px;
}
.product-view-navigation .productViewNavigationWrapper .navigation-title span {
    font-size: 2.5rem;
    font-weight: bold;
}
.product-view-navigation .productViewNavigationWrapper .navigation-title .price-box span {
    margin-left: 5px;
    font-size: 2rem;
    font-weight: normal;
}
.product-view-navigation .productViewNavigationWrapper .navigation-title .price-box .old-price span {
    font-size: 1.7rem;
    margin-left: 10px;
}
.product-view-navigation .productViewNavigationWrapper .navigation-title .price-box .save-info-block .save-info span {
    font-size: 2rem;
}
.product-view-navigation .navigation-title {
    flex: 1 0 auto;
    max-width: calc(100% - 542px);
}
.product-view-navigation .buttonWrapper {
    flex: 0 0 auto;
}
.product-view-navigation .price-box {
    display: inline-block;
}
.checkout-success {
    max-width: 1570px;
    margin: 0 auto;
}
@media (min-width: 768px) {
    .minicart-wrapper .action.showcart .text {
        right: calc(100% + 10px);
        line-height: 4.1rem;
        clip: unset;
        height: auto;
        width: auto;
        font-weight: bold;
        display: block;
    }
}
@media (min-width: 767px) and (max-width: 1200px) {
    html .sections.nav-sections {
    }
}
.footer-wrapper {

    position: relative;
}
.footer-wrapper .footer-column {
}
.footer-wrapper .footer-column.links {
    width: 25%;
}

.footer-wrapper p + p {
    margin: 0;
}
.footer-wrapper .addthis_inline_share_toolbox {
    display: block;
    position: absolute;
    top: -16px;
    text-align: center;
    margin: 0 auto;
    width: 100%;
    left: 0;
}
.footer-wrapper .addthis_inline_share_toolbox a::before {
    border-bottom: 0;
}
.footer.content {
    padding: 0;
}
.footer.content .links strong,
.footer.content .links a {
    color: inherit;
}
.footer.content .links a {
    font-size: 16px;
    margin: 0;
    padding: 0;
}
.footer.content .links a:hover {
    color: #85b84b;
    text-decoration: none;
}
.footer.content .links li {
    margin: 0 0 3px;
}
.footer.content .links p { font-size: 16px; }
.footer-headline {
    font-size: 20px;
    display: inline-block;
    font-weight: 500;
}
.copyright {
    display: none;
    background: none;
    margin: 10px 0 0;
}
.footer.bottom {
    background-color: #fff;
    text-align: center;
    padding: 18px 0;
}

.footer.bottom > .container {
    border-top: 2px solid #eee;
}

.footer.bottom ul {
    display: inline;
    padding: 0;
}
.footer.bottom ul::before {
    content: "-";
    display: inline;
}
.footer.bottom ul li {
    display: inline;
    padding-left: 10px;
}
.footer.bottom ul li a {
}
.footer.bottom ul li a:hover {
    color: #85b84b;
}
.footer.bottom span {
}
@media (min-width: 768px) {
    .nav-sections {
        background: white;
    }
    .sections.nav-sections{
        background: #677752;
        margin: 0 -15px;
    }
    .sections .navigation {
        background: #677752;
        font-size: 2rem;
        font-weight: 400;
    }
    .sections .navigation a::before {
        border-bottom: none;
    }
    .sections .navigation a.ui-state-active::before {
    }
    .sections .navigation a.ui-state-focus::before {
    }
    .sections .navigation a .ui-menu-icon {
        display: none;
    }
    .sections .navigation > ul {
        position: static;
        padding: 0;
        width: 100%;
    }
    .sections .navigation > ul > li {
        position: static;
    }
    .sections .navigation > ul > li.level-top {
        line-height: 2;
        display: inline;
    }
    .sections .navigation > ul > li.level-top.last {
    }
    .sections .navigation > ul > li + li {
        padding-left: 10px;
    }
    .sections .navigation > ul > li a {
        text-decoration: none;
    }
    .sections .navigation > ul > li:first-child {

    }
    .sections .navigation li.level0 {
        position: static;
        padding: 0 15px;
        margin: 0;
    }
    .sections .navigation li.level0 a {
        padding: 0;
    }
    .sections .navigation li.level0 > a.level-top {
        padding: 0;
    }
    .sections .navigation li.level0 > a.level-top .ui-menu-icon {
        display: none;
    }
    .sections .navigation li.level0.active > .level-top,
    .sections .navigation li.level0.has-active > .level-top {
        border: none;
        display: inline;
        text-decoration: none;
    }
    .sections .navigation li.level0.active > .level-top::before,
    .sections .navigation li.level0.has-active > .level-top::before {
    }
    .sections .navigation li.level0.active > .level-top:hover::before,
    .sections .navigation li.level0.has-active > .level-top:hover::before {
    }
    .sections .navigation li.level0 ul.level0.submenu {
        color: inherit;
        border: none;
        box-shadow: 0 5px 5px -5px rgba(0,0,0,0.1);
        /*box-shadow: @gfp-box-shadow;*/
        list-style: none;
        position: absolute;
        width: 100%;
        left: 0 !important;
        top: 40px !important;
        flex-wrap: wrap;
        font-weight: 400;
    }
    .sections .navigation li.level0 ul.level0.submenu .active > a {
        border: none;
    }
    .sections .navigation li.level0 ul.level0.submenu a.ui-state-active::before {
        border-bottom: none;
    }
    .sections .navigation li.level0 ul.level0.submenu a.ui-state-focus::before {
        border-bottom: none;
    }
    .sections .navigation li.level0 ul.level0.submenu a span:not(.ui-icon) {
        position: relative;
    }
    .sections .navigation li.level0 ul.level0.submenu a span:not(.ui-icon)::before {
        display: block;
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 93%;
    }
    .sections .navigation li.level0 ul.level0.submenu a span:not(.ui-icon):hover::before {
    }
    .sections .navigation li.level0 ul.level0.submenu li.level1 {
        max-width: 25%;
        flex: 1 0 25%;
        padding: 0;
        position: relative;
    }
    .sections .navigation li.level0 ul.level0.submenu li.level1::before {
        border-right: 1px solid #e2e2e2;
        display: block;
        content: none;
        position: absolute;
        top: 20px;
        bottom: 0;
        right: 0;
        width: 1px;
        z-index: 10;
    }
    .sections .navigation li.level0 ul.level0.submenu li.level1:nth-child(4)::before {
        border-right: none;
    }
    .sections .navigation li.level0 ul.level0.submenu li.level1 img {
        margin-top: 10px;
        width: 100%;
    }
    .sections .navigation li.level0 ul.level0.submenu li.level1 a.ui-state-active {
    }
    .sections .navigation li.level0 ul.level0.submenu li.level1:hover {
    }
    .sections .navigation li.level0 ul.level0.submenu li.level1:hover ul.submenu {
        background: transparent;
    }
    .sections .navigation li.level0 ul.level0.submenu li.level1 a {
        padding: 10px 5px 0;
    }
    .sections .navigation li.level0 ul.level0.submenu ul.level1.submenu {
        font-weight: 400;
        left: 0 !important;
        top: 100%;
        position: relative;
        border: none;
        box-shadow: none;
        list-style: none;
        margin: 0;
        line-height: 3rem;
    }
    .sections .navigation li.level0 ul.level0.submenu ul.level1.submenu li {
        margin: 0;
        padding: 0;
        width: 100%;
    }
    .sections .navigation li.level0 ul.level0.submenu ul.level1.submenu li a {
        padding: 0 10px;
    }
    .sections .navigation li.level0 ul.level0.submenu ul.level1.submenu ul.level2.submenu,
    .sections .navigation li.level0 ul.level0.submenu ul.level1.submenu ul.level2.submenu.submenu-reverse {
        top: 0 !important;
        left: 0 !important;
        padding-left: 20px;
        display: block !important;
        position: relative;
        border: none;
        box-shadow: none;
    }
    .sections .navigation li.level0 ul.level0.submenu .submenuWrapper {
        display: flex !important;
        top: auto !important;
        flex-wrap: wrap;
        padding: 0;
        width: 100%;
    }
    .sections .navigation li.level0 ul.level0.submenu > li {
        max-width: 1570px;
        margin: 0 auto;
        width: 100%;
    }
    .sections .navigation a span:hover {
    }
    .nav-sections {
        position: relative;
        margin-bottom: 0;
    }
    .nav-sections .nav-sections-items {
        max-width: 1570px;
        margin: 0 auto;
        padding-bottom: 0;
    }
    .nav-sections-item-content > .navigation {
        display: flex;
        position: static;
    }
    /**
  Page service tabs
   */
    .nav-sections-item-content .page-service-tabs {
        position: fixed;
        right: 0;
        transform: none;
        width: auto;
        display: flex;
        top: 20%;
        z-index: 1000;
    }
    .nav-sections-item-content .page-service-tabs .tabs-container {
        transform: none;
    }
    .nav-sections-item-content .page-service-tabs ul {
        margin-bottom: 0;
    }
    .nav-sections-item-content .page-service-tabs ul li {
        display: inline;
    }
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button {
        width: auto;
        display: inline-block;
        cursor: pointer;
        text-align: center;
        border: 2px solid #2D3843;
        padding: 13px 20px 13px 20px;
        margin: 5px 3px 5px 0;
        line-height: 2rem;
        text-decoration: none;
        color: white;
        background-color: #2D3843;
        font-weight: 400;
        font-size: 1.8rem;
        transition: all 0.4s;
    }
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button::after {
        font-family: 'luma-icons';
        content: "\e907";
        border: none;
        display: inline-block;
        margin-left: 5px;
    }
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button:hover,
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button:focus {
        background: #85b84b;
        border: 2px solid #85b84b;
        color: #FFFFFF;
        text-decoration: none;
    }
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button::before {
        border: none;
    }
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button.blue {
        color: white;
        background-color: #318cc5;
        border-color: #318cc5;
    }
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button.blue::after {
        content: "\e623";
    }
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button.blue:hover,
    .nav-sections-item-content .page-service-tabs ul li .servicepanel-button.blue:focus {
        opacity: 0.6;
        filter: alpha(opacity=60);
        /* For IE8 and earlier */
    }
}
.xcategory {
    margin: 0 auto;
    background: #f5f5f5;
}
.xcategory .xtags-container-wrapper {
    background: transparent;
    padding: 0;
}
.xcategory .xcategoryWrapper {
    padding: 20px;
    max-width: 1570px;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}
.xcategory .xcategoryWrapper .containerLeft,
.xcategory .xcategoryWrapper .containerRight,
.xcategory .xcategoryWrapper .containerBelow {
    display: flex;
}
.xcategory .xcategoryWrapper img {
    display: block;
}
.xcategory .xcategoryWrapper .backgroundHolder {
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.5), transparent);
    display: block;
    height: 100%;
    width: 60%;
    position: absolute;
}
.xcategory .xcategoryWrapper .containerLeft {
    flex: 0 0 65.5%;
}
.xcategory .xcategoryWrapper .containerLeft .category-item {
    flex: 0 0 100%;
}
.xcategory .xcategoryWrapper .containerLeft .category-item .xtags-container {
    position: absolute;
    bottom: 0;
    right: 20px;
}
.xcategory .xcategoryWrapper .containerLeft .category-item .xtags-container .icon {
    z-index: 10;
}
.xcategory .xcategoryWrapper .containerRight .xtags-container,
.xcategory .xcategoryWrapper .containerBelow .xtags-container {
    position: absolute;
    top: 0;
    left: 20px;
    right: auto;
}
.xcategory .xcategoryWrapper .containerRight .xtags-container .icon,
.xcategory .xcategoryWrapper .containerBelow .xtags-container .icon {
    z-index: 10;
}
.xcategory .xcategoryWrapper .containerRight {
    flex: 0 0 33%;
    flex-direction: column;
    justify-content: space-between;
}
.xcategory .xcategoryWrapper .containerRight .category-item {
    overflow: hidden;
    flex: 0 0 auto;
    max-height: 50%;
}
.xcategory .xcategoryWrapper .containerBelow {
    margin-top: 13px;
    flex: 1 0 100%;
    justify-content: space-between;
    flex-wrap: wrap;
}
.xcategory .xcategoryWrapper .containerBelow .category-item {
    flex: 0 0 32%;
}
.xcategory .xcategoryWrapper .containerBelow .category-item:nth-child(3) {
    flex: 0 0 33%;
}
.xcategory .xcategoryWrapper .category-item {
    position: relative;
}
.xcategory .xcategoryWrapper .category-item .textWrapper {
    position: absolute;
    bottom: 20px;
    left: 20px;
    word-break: break-all;
}
.xcategory .xcategoryWrapper .category-item .textWrapper h4,
.xcategory .xcategoryWrapper .category-item .textWrapper h3 {
    color: white;
}
.xcategory .xcategoryWrapper .category-item .textWrapper .sub-cat-title {
    font-size: 1.8rem;
    margin: 0;
}
.xcategory .xcategoryWrapper .category-item .textWrapper .cat-title {
    margin: 0 0 30px 0;
    font-size: 4rem;
}
.xcategory .xcategoryWrapper .category-item .categoryWrapper {
    display: inline-block;
    max-height: 100%;
    overflow: hidden;
    position: relative;
}
.xcategory .xcategoryWrapper .category-item .categoryWrapper::before {
    border-bottom: none;
}
.xcategory .xcategoryWrapper .category-item.bigImage {
    grid-column-start: 1;
    grid-column-end: 3;
    grid-row-start: 1;
    grid-row-end: 3;
}
.xcategory .xcategoryWrapper .category-item .xtags-container {
    position: absolute;
}
.header.content,
.page.messages {
    max-width: 1570px;
    margin: 0 auto;
}
.page-wrapper {
    display: block;
}
.page-main {
    padding: 0;
}
.modal-fullsize .modal-inner-wrap {
    width: 100%;
    height: 100%;
    margin: 0;
}
.panel.header {
    display: none;
}
.block-static-block.widget,
.block-cms-link.widget {
    margin: 0;
}
.block-static-block.widget .block-container,
.block-cms-link.widget .block-container {
    max-width: 1570px;
    margin: 0 auto;
}
.block-static-block.wide .block-container {
    margin: auto;
    max-width: none;
}
.block-static-block.highlight {
    padding: 50px 0;
    background: #bae588;
}
div[data-shopname='gfp-international.com'] a::before {
    border-bottom: none;
}
.columns {
    display: block;
}
.columns .column.main {
    background: #f5f5f5;
    padding: 0 15px;
}
body:not(.page-layout-custom-full-width) #maincontent .category-view-cms {
    max-width: 1570px;
    margin: 0 auto;
}
#maincontent .category-view-cms .page-title-wrapper {
    position: static;
    margin-top: 10px;
    padding: 10px 20px;
}
#maincontent .category-view-cms .category-description {
    background: transparent;
    padding: 20px;
}
@media (min-width: 992px) {
    .col-md-6 {
        width: 50%;
        float: left;
        position: relative;
        min-height: 1px;
        padding-right: 15px;
        padding-left: 15px;
    }
}
#notice-cookie-block {
    box-shadow: 0 5px 5px 5px rgba(0,0,0,0.1);
}
#notice-cookie-block .content {
    max-width: 1570px;
    margin: 0 auto;
    text-align: center;
}
#callback-form .labelProt {
    font-size: 1.3rem;
    line-height: 13px;
    padding-left: 35px;
}
div.fourcolumns {
    position: relative;
    overflow: hidden;
    padding: 1%;
}
div.fourcolumns .column-first,
div.fourcolumns .column-second,
div.fourcolumns .column-third,
div.fourcolumns .column-fourth {
    float: left;
    padding: 1%;
}
div.fourcolumns .column-first,
div.fourcolumns .column-third {
    width: 23%;
}
div.fourcolumns .column-second,
div.fourcolumns .column-fourth {
    width: 23%;
}
.top-quality-banner {
    max-width: 1570px;
    margin: 0 auto;
    display: flex;
    position: relative;
}
.top-quality-banner .portrait-col {
    position: relative;
}
.top-quality-banner .portrait-col blockquote {
    position: absolute;
    top: 62%;
    color: white;
    text-align: center;
    left: 82px;
    width: 200px;
    font-style: normal;
}
.top-quality-banner .portrait-col img {
    display: block;
}
.top-quality-banner .advantages-col {
    z-index: 1;
}
.top-quality-banner .advantages-col .textWrapper {
    padding-top: 128px;
}
.top-quality-banner .advantages-col .textWrapper h3 {
    margin: 0;
    text-align: center;
}
.top-quality-banner .advantages-col .textWrapper .small-headline {
    text-align: center;
}
.top-quality-banner .xtags-container-wrapper {
    position: absolute;
    left: 501px;
    top: 192px;
}
.top-quality-banner .xtags-container {
    position: relative;
}
.top-quality-banner .xtags-container h3 {
    margin: 0;
    text-align: center;
}
.top-quality-banner .xtags-container .xtagsItemWrapper {
    display: flex;
    text-align: center;
    justify-content: center;
    height: 150px;
    align-items: center;
}
.top-quality-banner .xtags-container .xtagsItemWrapper .xtags-tag {
    flex: 0 0 300px;
}
.top-quality-banner .xtags-container .xtagsItemWrapper .icon {
    display: inline-block;
    text-align: center;
    width: 100%;
}
.top-quality-banner .xtags-container .xtagsItemWrapper .icon::before {
    border-right: none;
    border-left: 1px solid #efefef;
    left: 0;
    top: -20px;
    height: 143px;
}
.top-quality-banner .xtags-container .xtagsItemWrapper .icon img {
    display: block;
    margin: 0 auto 10px auto;
}
.banner-container .banner-bg-image {
    background-size: cover !important;
}
.banner-inner-container {
    max-width: 1570px;
    margin: 0 auto;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.banner-inner-container .banner-tags {
    margin-right: 30px;
}
.banner-inner-container .banner-content h3 {
    font-size: 2rem;
    margin: 0;
}
.banner-inner-container .banner-content h2 {
    margin-top: 0;
}
.banner-inner-container .xtags-container-wrapper {
    background: transparent;
    padding: 0;
}
.xtags-container-wrapper {
    padding: 20px 0;
}
.xtags-container {
    max-width: 1570px;
    margin: 0 auto;
}
.xtags-container h3 {
    text-align: center;
}
.xtags-container .xtagsItemWrapper {
    display: flex;
    height: 175px;
    justify-content: center;
    align-items: center;
}
.xtags-container .xtagsItemWrapper .xtags-tag {
    flex: 1 0 auto;
    max-width: 200px;
}
.xtags-container .xtagsItemWrapper .xtags-tag:last-child .icon::before {
    border-right: none;
}
.xtags-container .xtagsItemWrapper .icon {
    position: relative;
    text-align: center;
}
.xtags-container .xtagsItemWrapper .icon::before {
    content: "";
    display: block;
    border-right: 1px solid #efefef;
    height: 175px;
    position: absolute;
    top: -33px;
    padding: 0;
    right: 0;
}
.xtags-container .xtagsItemWrapper img {
    margin: 0 auto 10px auto;
    display: block;
}
/**
Category list
 */
.category-view {
    /**
    Subcategories
   */
    /* Description */
    /* category cms */
}
.category-view .category-view-image-container {
    position: relative;
    text-align: center;
    margin-bottom: 0;
}
.category-view .category-view-image-container .category-image {
}
.category-view .category-view-image-container .page-title-wrapper {

}
.category-view .category-view-image-container .page-title-wrapper h1,
.category-view .category-view-image-container .page-title-wrapper h2 {
    margin: 0;
}
.category-view .category-view-image-container .page-title-wrapper #page-title-heading {
    order: 2;
}
.category-view .category-view-image-container .page-title-wrapper .category-sub-name {
    order: 1;
}
.category-view .xtags-container {
    position: absolute;
    left: 30px;
    top: 30px;
}
.category-view .page-title-wrapper {
    z-index: 999;
}
.category-view .category-image .image {
    width: 100%;
}
.category-view .category-description-container {
    margin-top: 30px; clear: both;
}
.category-view .category-description {
    margin-top: 20px;
}
.category-view .categories.wrapper {
    position: static;
}
.category-view .category-description {
    max-width: 1570px;
    margin: 0 auto;
    padding: 30px 0;
}
.category-view .category-description table h3 {
    margin-bottom: 0;
}
.category-view .category-cms {

}
.category-view .category-cms .xtags-container {
    position: static;
}
/* Subcategories List */
/**
  Subcategories
 */
.categories.wrapper {
    margin: 0 auto 0 auto;
    z-index: 1;
    position: relative;
    max-width: 1570px;
}
.categories.wrapper .category-overview-info {
    margin-bottom: 30px;
}
.categories.wrapper .category-overview-info .category-overview-label {
    font-size: 2.6rem;
    font-weight: bold;
    margin-right: 15px;
}
.categories.wrapper ol.categories {
    display: flex;
    flex-wrap: wrap;
    margin-left: -10px;
    margin-right: -10px;
}
.categories.wrapper ol.categories .category-item {
    flex: 0 0 auto;
    margin: 0 10px 20px;
    width: calc(25% - 20px);
    padding-bottom: 20px;
    transition: transform 0.1s;
}
.categories.wrapper ol.categories .category-item:hover {
    box-shadow: 0 0 7px #808080;
    transform: scale(1.05);
}
.categories.wrapper ol.categories .category-item .category-image {
    position: relative;
}
.categories.wrapper ol.categories .category-item.noPicture {
    padding-bottom: 0;
}
.categories.wrapper ol.categories .category-item.noPicture .category-image {
    min-height: 200px;
    background: grey;
    margin-bottom: 0;
}
.categories.wrapper ol.categories .category-item.noPicture .category-item-description {
    padding: 0;
}
.categories.wrapper ol.categories .category-item .category-item-description {
    padding: 10px;
}
.categories.wrapper ol.categories .product-item-info-wrapper {
    position: relative;
}
.categories.wrapper ol.categories .product-item-info-wrapper .category-name-actions {
}
.categories.wrapper ol.categories .product-item-info-wrapper .category-name-actions .category-item-link {
}
.categories.wrapper ol.categories .product-item-info-wrapper .category-name-actions .category-item-link::before {
    border-bottom: none;
}
.categories.wrapper ol.categories .priceDiv {
    font-size: 2.4rem;
    padding-left: 40px;
}
/* image subcontainer */
.catalog-product-view .product-item-info,
.page-products .product-item-info {
    width: 100%;
}
/* overwrite button */
#maincontent .category-view a.button {
    padding: 13px 25px 13px 31px;
    margin: 0;
}
.category-image {
    position: relative;
}
.category-image .backgroundHolder {
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.5), transparent);
    display: block;
    height: 100%;
    width: 60%;
    position: absolute;
}
/** Product list **/
.category-select-container {
    max-width: 1570px;
    margin: 0 auto 20px auto;
    text-align: center;
}
.category-select-container select {
    display: inline-block;
    border: none;
    padding-left: 15px;
}
.category-select-container h3 {
    margin: 0 20px 0 0;
    display: inline-block;
    font-size: 28px;
    font-weight: 400;
    vertical-align: middle;
}
.category-select-container .select-wrapper { vertical-align: middle; }

.page-products.page-layout-1column .products-grid,
.block.related .products-grid,
.column.main .products-grid,
.block.upsell .products-grid {
    max-width: 1570px;
    margin: 0 auto;
}
.page-products.page-layout-1column .products-grid .product-items,
.block.related .products-grid .product-items,
.column.main .products-grid .product-items,
.block.upsell .products-grid .product-items {
    margin-left: -10px;
    margin-right: -10px;
    display: flex;
    flex-wrap: wrap;
}
.page-products.page-layout-1column .products-grid .product-items .product-item,
.block.related .products-grid .product-items .product-item,
.column.main .products-grid .product-items .product-item,
.block.upsell .products-grid .product-items .product-item {
    position: relative;
    padding: 0;
    flex: 0 0 auto;
    margin: 0 10px 20px;
    width: calc(25% - 20px);
    background: #FFF;
}
.page-products.page-layout-1column .products-grid .product-items .product-item:hover,
.block.related .products-grid .product-items .product-item:hover,
.column.main .products-grid .product-items .product-item:hover,
.block.upsell .products-grid .product-items .product-item:hover {
    box-shadow: 0 0 7px #808080;
    transform: scale(1.05);
    transition: all 0.4s;
}
.page-products.page-layout-1column .products-grid .product-items .product-item a::before,
.block.related .products-grid .product-items .product-item a::before,
.column.main .products-grid .product-items .product-item a::before,
.block.upsell .products-grid .product-items .product-item a::before {
    border: none;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-info,
.block.related .products-grid .product-items .product-item .product-item-info,
.column.main .products-grid .product-items .product-item .product-item-info,
.block.upsell .products-grid .product-items .product-item .product-item-info {
    width: auto;
    height: 100%;
    display: flex;
    flex-direction: column;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-info:hover,
.block.related .products-grid .product-items .product-item .product-item-info:hover,
.column.main .products-grid .product-items .product-item .product-item-info:hover,
.block.upsell .products-grid .product-items .product-item .product-item-info:hover {
    box-shadow: none;
    margin: 0;
    border: none;
    padding: 0;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-info .product-item-image,
.block.related .products-grid .product-items .product-item .product-item-info .product-item-image,
.column.main .products-grid .product-items .product-item .product-item-info .product-item-image,
.block.upsell .products-grid .product-items .product-item .product-item-info .product-item-image,
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-info .product-item-details,
.block.related .products-grid .product-items .product-item .product-item-info .product-item-details,
.column.main .products-grid .product-items .product-item .product-item-info .product-item-details,
.block.upsell .products-grid .product-items .product-item .product-item-info .product-item-details {
    flex: 0 0 auto;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-info .product-item-price,
.block.related .products-grid .product-items .product-item .product-item-info .product-item-price,
.column.main .products-grid .product-items .product-item .product-item-info .product-item-price,
.block.upsell .products-grid .product-items .product-item .product-item-info .product-item-price {
    flex: 1 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper,
.block.related .products-grid .product-items .product-item .imageWrapper,
.column.main .products-grid .product-items .product-item .imageWrapper,
.block.upsell .products-grid .product-items .product-item .imageWrapper {
    position: relative;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-image-container,
.block.related .products-grid .product-items .product-item .imageWrapper .product-image-container,
.column.main .products-grid .product-items .product-item .imageWrapper .product-image-container,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-image-container {
    display: inline;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .backgroundHolder,
.block.related .products-grid .product-items .product-item .imageWrapper .backgroundHolder,
.column.main .products-grid .product-items .product-item .imageWrapper .backgroundHolder,
.block.upsell .products-grid .product-items .product-item .imageWrapper .backgroundHolder {
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.5), transparent);
    display: block;
    height: 100%;
    width: 90%;
    position: absolute;
    z-index: 9;
    pointer-events: none;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name {
    position: absolute;
    bottom: 0;
    z-index: 10;
    padding: 10px 20px;
    font-size: 2.4rem;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .product-item-link,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .product-item-link,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .product-item-link,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .product-item-link {
    margin: 0;
    color: white;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .product-item-link .save-info-block,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .product-item-link .save-info-block,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .product-item-link .save-info-block,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .product-item-link .save-info-block {
    display: none !important;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box {
    color: #FFF;
    margin: 0;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .save-info-block,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .save-info-block,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .save-info-block,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .save-info-block,
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .minimal-price-link,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .minimal-price-link,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .minimal-price-link,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .minimal-price-link {
    display: none !important;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .price,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .price,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .price,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .price {
    font-size: 2.4rem;
    font-weight: 400;
    color: #FFF;
    display: inline;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .price-label,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .price-label,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .price-label,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .price-label {
    color: #FFF;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price .price-label,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price .price-label,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price .price-label,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price .price-label,
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price .price-label,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price .price-label,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price .price-label,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price .price-label {
    display: none;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price,
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price,
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price {
    color: #FFF;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price .price,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price .price,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price .price,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .normal-price .price,
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price .price,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price .price,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price .price,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .special-price .price,
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price {
    display: inline;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price-final_price::before,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price-final_price::before,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price-final_price::before,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price-final_price::before {
    content: "/";
    display: inline-block;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price-label,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price-label,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price-label,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price-label {
    display: none;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price,
.block.related .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price,
.column.main .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price,
.block.upsell .products-grid .product-items .product-item .imageWrapper .product-item-name .price-box .old-price .price {
    font-size: 1.8rem;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-button,
.block.related .products-grid .product-items .product-item .product-item-button,
.column.main .products-grid .product-items .product-item .product-item-button,
.block.upsell .products-grid .product-items .product-item .product-item-button {
    text-align: center;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-price,
.block.related .products-grid .product-items .product-item .product-item-price,
.column.main .products-grid .product-items .product-item .product-item-price,
.block.upsell .products-grid .product-items .product-item .product-item-price {
    margin: 10px 10px 10px 0;
    text-align: center;
    padding: 10px;
    border-top: 1px solid #f5f5f5;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .stock-status,
.block.related .products-grid .product-items .product-item .stock-status,
.column.main .products-grid .product-items .product-item .stock-status,
.block.upsell .products-grid .product-items .product-item .stock-status {
    margin: 10px 20px;
    text-align: center;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-description,
.block.related .products-grid .product-items .product-item .product-item-description,
.column.main .products-grid .product-items .product-item .product-item-description,
.block.upsell .products-grid .product-items .product-item .product-item-description {
    margin: 20px 0 0 0;
    padding: 10px;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .xtags-container-wrapper,
.block.related .products-grid .product-items .product-item .xtags-container-wrapper,
.column.main .products-grid .product-items .product-item .xtags-container-wrapper,
.block.upsell .products-grid .product-items .product-item .xtags-container-wrapper {
    padding: 0;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .xtags-container,
.block.related .products-grid .product-items .product-item .xtags-container,
.column.main .products-grid .product-items .product-item .xtags-container,
.block.upsell .products-grid .product-items .product-item .xtags-container {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 10;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .xtags-container .xtagsItemWrapper,
.block.related .products-grid .product-items .product-item .xtags-container .xtagsItemWrapper,
.column.main .products-grid .product-items .product-item .xtags-container .xtagsItemWrapper,
.block.upsell .products-grid .product-items .product-item .xtags-container .xtagsItemWrapper {
    height: auto;
}
.page-products.page-layout-1column .products-grid .product-items .product-item .product-item-actions,
.block.related .products-grid .product-items .product-item .product-item-actions,
.column.main .products-grid .product-items .product-item .product-item-actions,
.block.upsell .products-grid .product-items .product-item .product-item-actions {
    margin: 0;
}
.page-products.page-layout-1column .products-grid .swatch-attribute,
.block.related .products-grid .swatch-attribute,
.column.main .products-grid .swatch-attribute,
.block.upsell .products-grid .swatch-attribute {
    padding: 0 15px;
}
.page-products.page-layout-1column .price-box,
.block.related .price-box,
.column.main .price-box,
.block.upsell .price-box {
    margin: 0;
}
.page-products.page-layout-1column .price-box .price,
.block.related .price-box .price,
.column.main .price-box .price,
.block.upsell .price-box .price {
    font-weight: 400;
    font-size: 1.8rem;
}
.page-products.page-layout-1column .price-box .normal-price .price-label,
.block.related .price-box .normal-price .price-label,
.column.main .price-box .normal-price .price-label,
.block.upsell .price-box .normal-price .price-label,
.page-products.page-layout-1column .price-box .special-price .price-label,
.block.related .price-box .special-price .price-label,
.column.main .price-box .special-price .price-label,
.block.upsell .price-box .special-price .price-label {
    display: none;
}
.page-products.page-layout-1column .price-box .normal-price,
.block.related .price-box .normal-price,
.column.main .price-box .normal-price,
.block.upsell .price-box .normal-price,
.page-products.page-layout-1column .price-box .special-price,
.block.related .price-box .special-price,
.column.main .price-box .special-price,
.block.upsell .price-box .special-price,
.page-products.page-layout-1column .price-box .old-price,
.block.related .price-box .old-price,
.column.main .price-box .old-price,
.block.upsell .price-box .old-price {
    display: block;
    width: 100%;
    margin: 0;
}
.page-products.page-layout-1column .price-box .special-price .price,
.block.related .price-box .special-price .price,
.column.main .price-box .special-price .price,
.block.upsell .price-box .special-price .price,
.page-products.page-layout-1column .price-box .normal-price .price,
.block.related .price-box .normal-price .price,
.column.main .price-box .normal-price .price,
.block.upsell .price-box .normal-price .price {
    font-weight: 400;
    font-size: 3.4rem;
}
.page-products.page-layout-1column .products-list .product-items,
.block.related .products-list .product-items,
.column.main .products-list .product-items,
.block.upsell .products-list .product-items {
    margin: 30px 0;
}
.page-products.page-layout-1column .products-list .product-item,
.block.related .products-list .product-item,
.column.main .products-list .product-item,
.block.upsell .products-list .product-item {
    margin: 10px 0;
    padding: 10px 0;
    border-bottom: 1px solid #f5f5f5;
}
.page-products.page-layout-1column .products-list .product-item-info,
.block.related .products-list .product-item-info,
.column.main .products-list .product-item-info,
.block.upsell .products-list .product-item-info {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-image,
.block.related .products-list .product-item-info .product-item-image,
.column.main .products-list .product-item-info .product-item-image,
.block.upsell .products-list .product-item-info .product-item-image,
.page-products.page-layout-1column .products-list .product-item-info .product-item-details,
.block.related .products-list .product-item-info .product-item-details,
.column.main .products-list .product-item-info .product-item-details,
.block.upsell .products-list .product-item-info .product-item-details,
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container,
.block.related .products-list .product-item-info .product-item-action-container,
.column.main .products-list .product-item-info .product-item-action-container,
.block.upsell .products-list .product-item-info .product-item-action-container,
.page-products.page-layout-1column .products-list .product-item-info .product-item-choice,
.block.related .products-list .product-item-info .product-item-choice,
.column.main .products-list .product-item-info .product-item-choice,
.block.upsell .products-list .product-item-info .product-item-choice {
    flex: 0 1 auto;
    text-align: left;
    box-sizing: border-box;
    width: auto;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-image,
.block.related .products-list .product-item-info .product-item-image,
.column.main .products-list .product-item-info .product-item-image,
.block.upsell .products-list .product-item-info .product-item-image {
    text-align: center;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-details,
.block.related .products-list .product-item-info .product-item-details,
.column.main .products-list .product-item-info .product-item-details,
.block.upsell .products-list .product-item-info .product-item-details {
    flex-grow: 1;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-details .product-item-name,
.block.related .products-list .product-item-info .product-item-details .product-item-name,
.column.main .products-list .product-item-info .product-item-details .product-item-name,
.block.upsell .products-list .product-item-info .product-item-details .product-item-name {
    margin: 0 0 10px 0;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-details .product-item-description > ul,
.block.related .products-list .product-item-info .product-item-details .product-item-description > ul,
.column.main .products-list .product-item-info .product-item-details .product-item-description > ul,
.block.upsell .products-list .product-item-info .product-item-details .product-item-description > ul {
    margin-left: -20px;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-details .product-list-description-content,
.block.related .products-list .product-item-info .product-item-details .product-list-description-content,
.column.main .products-list .product-item-info .product-item-details .product-list-description-content,
.block.upsell .products-list .product-item-info .product-item-details .product-list-description-content {
    margin: 15px 0;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container,
.block.related .products-list .product-item-info .product-item-action-container,
.column.main .products-list .product-item-info .product-item-action-container,
.block.upsell .products-list .product-item-info .product-item-action-container {
    text-align: right;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions {
    display: block;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary {
    display: block;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart {
    margin: -20px 0 0 0;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .fieldset,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .fieldset,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .fieldset,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .fieldset {
    display: table;
    width: 100%;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .field,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .field,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .field,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .field,
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions {
    display: table-cell;
    vertical-align: bottom;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .field,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .field,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .field,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .field {
    width: 25%;
    padding: 0 5% 0 0;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions {
    width: 70%;
}
.page-products.page-layout-1column .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions .action.tocart,
.block.related .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions .action.tocart,
.column.main .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions .action.tocart,
.block.upsell .products-list .product-item-info .product-item-action-container .product-item-actions .actions-primary .box-tocart .actions .action.tocart {
    margin: 0;
    width: 100%;
    height: 50px;
    padding: 12px 17px;
}
#maincontent .product-item-description a.button {
    margin-left: 17px;
}
#maincontent .catalog-related-product-container .action.primary.tocart {
    margin: 0;
    width: 100%;
}
.message.info {
    margin: 0 auto;
    max-width: 1570px;
}
.page-products #maincontent {
    background: #f5f5f5;
}
.page-products.page-layout-1column .products-grid {
    margin-bottom: 50px;
}
.block.related .field.choice .label {
    height: auto;
    width: auto;
}
.block.related .price-box {
    margin: 0;
}
.block.related .title strong {
    font-size: 2rem;
}
.block.related .title strong a {
    font-weight: bold;
}
.block.related .field.choice {
    position: relative;
}
.block.related .field.choice .label {
    padding-left: 35px;
    clip: auto;
}
.block.related .product-item-photo {
    padding: 0;
}
.catalog-related-product-container .block.related a.action.close {
    float: right;
}
.catalog-related-product-container .block.related a.action.close::before {
    border-bottom: none;
}
.catalog-related-product-container .block.related a.action.close::after {
    font-size: 1.4rem;
    line-height: 2rem;
    content: '\e616';
    font-family: 'luma-icons';
    margin: 0;
    vertical-align: top;
    display: inline-block;
    font-weight: normal;
    overflow: hidden;
    speak: none;
    text-align: center;
    color: red;
    padding-left: 20px;
}
.catalog-related-product-container .block.related .product-item-info.related-available .product-item-actions .actions-primary {
    display: block;
}
.catalog-related-product-container .block.related .product-item-info.related-available .tocart {
    white-space: normal;
    margin: 0;
}
.cart-container .block.crosssell .products-grid .product-items .product-item {
    width: calc(33.33333333% - 20px);
}
@media (min-width: 767px) {
    .page-products.page-layout-1column .products-list .product-item-info,
    .block.related .products-list .product-item-info,
    .column.main .products-list .product-item-info,
    .block.upsell .products-list .product-item-info {
        flex-direction: row;
    }
    .page-products.page-layout-1column .products-list .product-item-info .product-item-image,
    .block.related .products-list .product-item-info .product-item-image,
    .column.main .products-list .product-item-info .product-item-image,
    .block.upsell .products-list .product-item-info .product-item-image {
        text-align: left;
        flex-basis: 25%;
    }
    .page-products.page-layout-1column .products-list .product-item-info .product-item-details,
    .block.related .products-list .product-item-info .product-item-details,
    .column.main .products-list .product-item-info .product-item-details,
    .block.upsell .products-list .product-item-info .product-item-details {
        flex-basis: 40%;
        padding: 0 20px;
    }
    .page-products.page-layout-1column .products-list .product-item-info .product-item-action-container,
    .block.related .products-list .product-item-info .product-item-action-container,
    .column.main .products-list .product-item-info .product-item-action-container,
    .block.upsell .products-list .product-item-info .product-item-action-container {
        align-self: baseline;
        flex-basis: 25%;
    }
}
.swatch-option {
    border: 1px solid #fff;
}
.swatch-option.image:not(.disabled):hover,
.swatch-option.color:not(.disabled):hover {
    outline: 2px solid #85b84b;
    border: 1px solid #fff;
}
.swatch-option.selected {
    outline: 2px solid #2d3843;
}
.product-info-container {
    background: #f5f5f5;
}
.product-info-wrapper {
    max-width: 1570px;
    margin: 0 auto;
}
.product-tabs-wrapper {
    padding-bottom: 70px;
}
.product-category-list-container {
    padding: 100px 0;
    background: #f5f5f5;
}
.product-reviews-container {
    padding: 100px 0;
}
.product-reviews-container .product-reviews-slider-container {
    max-width: 772px;
    margin: 0 auto;
    text-align: center;
}
.page-layout-1column {
    /* Description */
}
.page-layout-1column .product.media {
    width: calc(55% - 30px);
    position: relative;
}
.page-layout-1column .product-info-main {
    width: 45%;
    margin-left: 30px;
}
.page-layout-1column .product-info-main .attribute.subname {
    font-weight: 500;
    margin-bottom: 15px;
    font-size: 16px;
}
.page-layout-1column .product-info-main .product-info-stock-sku {
    text-align: left;
    display: block;
    padding: 0;
}
.page-layout-1column .product-info-main .product-info-overview-container,
.page-layout-1column .product-info-main .product-info-cmsblock-container {

}
.page-layout-1column .product-info-main .product-info-overview-container ul,
.page-layout-1column .product-info-main .product-info-cmsblock-container ul {
    padding-left: 22px;
}
.page-layout-1column .product-info-main .product-info-price-top .old-price .price-final_price::before {
    content: "/";
    display: inline-block;
}
.page-layout-1column .product-info-main .product-info-price-container {
    margin-top: 20px;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price {
    margin: 15px 0;
    border-bottom: 0;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .price-box {
    padding-bottom: 0;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .normal-price,
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .special-price {
    display: block;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .normal-price .now-only-label,
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .special-price .now-only-label {
    font-size: 3.4rem;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .normal-price .price,
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .special-price .price {
    color: #85b84b;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .normal-price .price-label,
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .special-price .price-label {
    display: none;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .old-price .price-container {
    font-size: 1.8rem;
}
.page-layout-1column .product-info-main .product-info-price-container .product-info-price .old-price .price-container .price {
    font-weight: 400;
    font-size: 1.8rem;
}
.page-layout-1column .product-info-main .product-info-price-container .price-box .price-container .price {
    font-weight: 600;
    font-size: 40px;
}
/* Fotorama custom changes */
.fotorama__stage__frame .fotorama__img {
    width: 100%;
}
.fotorama--fullscreen {
    z-index: 10001 !important;
}
.fotorama__arr {
    background-color: transparent;
}
.fotorama__arr:hover {
    background-color: transparent;
}
#sb-nav,
#sb-info,
#sb-info-inner {
    height: 50px;
}
#sb-wrapper-inner {
    border: 2px solid #f5f5f5;
}
#sb-nav-next {
    background-image: none;
}
#sb-nav-next::before {
    border-right: #ffffff 2px solid;
    border-bottom: #ffffff 2px solid;
    -webkit-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    transform: rotate(-45deg);
    content: '';
    width: 22px;
    height: 22px;
    display: block;
    position: absolute;
    top: 10px;
}
#sb-nav-previous {
    background-image: none;
}
#sb-nav-previous::before {
    border-left: #ffffff 2px solid;
    border-bottom: #ffffff 2px solid;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    content: '';
    width: 22px;
    height: 22px;
    display: block;
    position: absolute;
    top: 10px;
}
#sb-nav-close {
    background-image: none;
}
#sb-nav-close::before {
    transform: rotate(45deg);
    border-bottom: none;
}
#sb-nav-close::after {
    transform: rotate(-45deg);
}
#sb-nav-close::before,
#sb-nav-close::after {
    content: '';
    position: absolute;
    height: 2px;
    width: 100%;
    top: 23px;
    left: 0;
    margin-top: -1px;
    background: white;
}
#sb-nav a {
    width: 55px;
    height: 65px;
}
.gallery-placeholder {
    margin-bottom: 20px;
}
#maincontent .product.media .button.action.to-faq::before,
#maincontent .product.media button.action.to-faq::before {
    content: "\e623";
}
#maincontent .product.media .button.action.to-online-catalogue::before,
#maincontent .product.media button.action.to-online-catalogue::before {
    content: "\e907";
}
#maincontent .product.media .button.action.to-assembly-hint::before,
#maincontent .product.media button.action.to-assembly-hint::before {
    content: "\e90c";
}
#maincontent .product.media .button.action.to-overview::before,
#maincontent .product.media button.action.to-overview::before {
    content: "\e90f";
}
#maincontent .product.media .button.action.to-datasheet::before,
#maincontent .product.media button.action.to-datasheet::before {
    content: "\e907";
}
#maincontent .product.media .button.action {
    display: inline-block;
}
#maincontent .product-service-container {
    margin-top: -35px;
    clear: both;
}
#maincontent .product-service-container .service-container {
    display: flex;
    max-width: 1570px;
    margin: 0 auto;
    position: relative;
}
#maincontent .product-service-container .service-container .info-col {
    position: relative;
}
#maincontent .product-service-container .service-container .info-col blockquote {
    position: absolute;
    font-style: normal;
    top: 62%;
    color: white;
    text-align: center;
    left: 115px;
    width: 200px;
    margin: 0;
}
#maincontent .product-service-container .service-container .info-col img {
    display: block;
}
#maincontent .product-service-container .service-container .serviceFormWrapper {
    position: absolute;
    left: 400px;
    top: 140px;
    display: flex;
    align-items: center;
    z-index: 10;
}
#maincontent .product-service-container .service-container .serviceFormWrapper .col {
    position: relative;
    padding: 20px;
}
#maincontent .product-service-container .service-container .serviceFormWrapper .col::before {
    border-left: 1px solid #efefef;
    left: -20px;
    top: -83px;
    height: 400px;
    display: block;
    content: "";
    position: absolute;
}
#maincontent .product-service-container .service-container .serviceFormWrapper .form-datasheet-col {
    margin-right: 20px;
}
#maincontent .product-service-container .service-container .serviceFormWrapper .form-catalog-col {
    margin-left: 20px;
}
#maincontent .product-service-container .service-container form fieldset {
    margin-bottom: 10px;
}
#maincontent .product-service-container .service-container form legend h3 {
    margin: 0;
    position: relative;
    font-size: 2.4rem;
}
#maincontent .product-service-container .service-container form legend h3::before {
    border: none;
    display: block;
    margin-right: 10px;
    text-align: center;
    line-height: 2rem;
    color: black;
    font-weight: 400;
    font-size: 2rem;
    transform: rotate(0deg);
    background-image: url('../images/datasheet-form.svg');
    background-repeat: no-repeat;
    background-size: 70px 70px;
    content: "";
    width: 70px;
    height: 70px;
    position: absolute;
    top: -80px;
    left: 0;
}
#maincontent .product-service-container .service-container form#catalog-form legend h3::before {
    background-image: url('../images/catalog-form.svg');
}
#maincontent .product-service-container .callback-container {
    border-top: 1px solid #f5f5f5;
    background: none;
}
#maincontent .product-service-container .callback-container .callback-form-container {
    width: 680px;
    margin: 0 auto;
    padding-left: 180px;
}
#maincontent .product-service-container .callback-container .callback-form-container fieldset {
    position: relative;
    margin-bottom: 0;
    padding-bottom: 35px;
}
#maincontent .product-service-container .callback-container .callback-form-container fieldset::before {
    border: none;
    display: block;
    margin-right: 10px;
    text-align: center;
    line-height: 2rem;
    color: black;
    font-weight: 400;
    font-size: 2rem;
    transform: rotate(0deg);
    background-image: url('../images/rueckrufservice.png');
    background-repeat: no-repeat;
    background-size: 150px 150px;
    content: "";
    width: 150px;
    height: 150px;
    position: absolute;
    top: 61px;
    left: -180px;
}
#maincontent .product-service-container .callback-container .callback-form-container fieldset h3 {
    margin-bottom: 0;
    font-size: 2.4rem;
}
#maincontent .product-service-container .callback-container .callback-form-container fieldset legend {
    margin: 0;
    border-bottom: 0;
}
#maincontent .product-service-container .callback-container .callback-form-container fieldset .control #phone {
    width: 280px;
    display: inline-block;
}
#maincontent .product-service-container .callback-container .callback-form-container fieldset .phoneFieldButtonWrapper {
    display: flex;
}
#maincontent .product-service-container .callback-container .callback-form-container fieldset .phoneFieldButtonWrapper button {
    margin: 0 0 0 10px;
}
#maincontent .product-service-container .callback-container .callback-form-container fieldset div.field {
    margin: 0 0 10px 0;
}
#maincontent .product-add-form {
    padding-top: 0;
}
#maincontent .product-add-form .box-tocart .fieldset {
    display: block;
    text-align: right;
}
#maincontent .product-add-form .box-tocart .fieldset .field.qty,
#maincontent .product-add-form .box-tocart .fieldset .actions {
    float: none;
    display: inline-block;
}
#maincontent .product-add-form .box-tocart .fieldset button.tocart {
    margin: 0 0 0 40px;
    width: 100%;
}
.productOrderButtonWrapper {
    max-width: 1570px;
    margin: 20px auto 20px auto;
    text-align: center;
}
.product.info.detailed {
    max-width: 1570px;
    margin: -50px auto 0 auto;
}
.product.info.detailed .product.data.items .item.title > .switch {
    border: none;
    background: #6f7d81;
    color: #fff;
}
.product.info.detailed .product.data.items .item.title > .switch::before {
    border: none;
}
.product.info.detailed .product.data.items .item.title.active > .switch {
    background-color: #636d70;
    color: #fff;
    font-weight: 600;
}
.product.info.detailed .product.data.items .item.title.active + .item.content {
    display: block;
}
.product.info.detailed .product.data.items .item.content {
    border: 0;
}
.product.info.detailed .col1,
.product.info.detailed .col2 {
    width: 50%;
    float: left;
}
.product.info.detailed .product-info-details-tab-details-description {
    clear: both;
}
.product.info.detailed .products-related a {
}
.product.info.detailed iframe {
    max-width: 100%;
}
.old-price span.price {
    text-decoration: line-through;
}
.product-info-accordion {
    display: none;
}
#maincontent .block.upsell {
    background: none;
    margin-bottom: 0;
}
#maincontent .block.upsell .products {
    margin-top: 0;
    margin-bottom: 0;
}
#maincontent .block.upsell .products .product-item-info {
    background: #f5f5f5;
}
#maincontent .block.upsell .block-title,
.review-list .block-title {
    padding-bottom: 5px;
    margin-bottom: 20px;
    width: 100%;
    border-bottom: 1px solid #f5f5f5;
}
#maincontent .block.upsell .block-title strong,
.review-list .block-title strong {
    font-weight: 700;
    font-size: 2.5rem;
    line-height: 3rem;
}
.review-list .review-items .review-item {
    border-bottom: 2px solid #FFF;
    background: #f5f5f5;
    padding: 15px;
}
.review-list .review-items .review-item .review-title {
    margin-bottom: 15px;
}
.review-list .review-toolbar {
    border-top: 1px solid #f5f5f5;
}
.catalog-related-product-container {
    padding: 20px 0;
}
#category-overview-modal .product-category-list-container {
    padding: 0;
    background: none;
}
#category-overview-modal .product-category-list-container .categories.wrapper ol.categories .category-item {
    width: calc(50% - 20px);
    padding-bottom: 0;
}
#category-overview-modal .product-category-list-container .categories.wrapper ol.categories .category-item .category-image {
    margin-bottom: 0;
}
#category-overview-modal .product-category-list-container .categories.wrapper ol.categories .category-item .category-image img {
    width: 100%;
}
#category-overview-modal .product-category-list-container .categories.wrapper ol.categories .category-item .category-item-description {
    display: none;
}
.product-options-wrapper select {
    background-image: none;
}
.addToCartSuccessPopupContainer {
    margin-top: 0;
}
.prices-tier {
    margin: 0;
}
.prices-tier .item {
    margin-bottom: 0;
}
.prices-tier .item .price-container .price {
    font-size: inherit;
}
.ui-accordion {
    position: relative;
    background: #F5F5F5;
}
.ui-accordion .ui-accordion-header {
    max-width: 1570px;
    margin: 0 auto;
    cursor: pointer;
    font-size: 2rem;
    line-height: 2.4rem;
    font-weight: 700;
    position: relative;
    border: none;
    padding: 23px 15px 23px 51px;
    text-decoration: none;
    display: flex;
}
.ui-accordion .ui-accordion-header .ui-accordion-header-icon {
    order: 2;
}
.ui-accordion .ui-accordion-header .ui-accordion-header-icon::after {
    content: "";
    display: inline-block;
    /* By using an em scale, the arrows will size with the font */
    width: 0.4em;
    height: 0.4em;
    border-right: 0.1em solid black;
    border-top: 0.1em solid black;
    right: 18px;
    transform: rotate(135deg);
    -ms-transform: rotate(135deg);
    /* IE 9 */
    -webkit-transform: rotate(135deg);
    /* Chrome, Safari, Opera */
    top: 14pt;
    margin-left: 10px;
}
.ui-accordion .ui-accordion-header.ui-state-active {
    height: auto;
    margin-bottom: 0;
}
.ui-accordion .ui-accordion-header.ui-state-active .ui-accordion-header-icon::after {
    top: 20pt;
    transform: rotate(-45deg);
}
.ui-accordion .ui-accordion-header:focus {
    outline: 0;
}
.ui-accordion .ui-accordion-header:last-child {
    margin-bottom: 0;
}
.ui-accordion .ui-accordion-header ~ .ui-accordion-header {
    border-top: 1px solid #E2E2E2;
}
.ui-accordion .ui-accordion-header::before {
    font-family: 'luma-icons';
    border: none;
    display: inline-block;
    margin-right: 10px;
    cursor: pointer;
    text-align: center;
    color: black;
    font-weight: 400;
    font-size: 2rem;
    line-height: 2rem;
    transform: rotate(0deg);
}
.ui-accordion .ui-accordion-header.product-gallery-title::before {
    content: "\e90b";
}
.ui-accordion .ui-accordion-header.product-faq-title::before {
    content: "\e623";
}
.ui-accordion .accordion-content {
    max-width: 1570px;
    margin: 0 auto;
    padding-bottom: 40px;
}
.ui-accordion .accordion-content a::before {
    border-bottom: none;
}
.ui-accordion .accordion-content .tab-content {
    border: none;
}
.owl-carousel .owl-item {
    padding-right: 10px;
}
.cart-empty {
    max-width: 900px;
    margin: 0 auto;
}
.opc-progress-bar {
    padding-top: 0;
    padding-bottom: 0;
}
.opc-progress-bar .opc-progress-bar-item {
    width: 33%;
    padding-bottom: 5px;
}
.opc-progress-bar .opc-progress-bar-item::before {
}
.opc-progress-bar .opc-progress-bar-item span {
    padding-top: 0;
}
.opc-progress-bar .opc-progress-bar-item span::before,
.opc-progress-bar .opc-progress-bar-item span::after {
}
.opc-progress-bar .opc-progress-bar-item._complete span {
    font-weight: 500;
}
.product-item-photo::before {
    border-bottom: none;
}
.product-item-photo:hover::before {
    border-bottom: none;
}
.cart.table-wrapper .col.price .price-excluding-tax,
.cart.table-wrapper .col.subtotal .price-excluding-tax {
    color: #2f3943;
    font-weight: 400;
}
.cart.table-wrapper .col.price .price-excluding-tax .price {
    font-weight: 400;
}
.cart.table-wrapper .col.subtotal .price-excluding-tax .price {
    font-weight: 400;
}
.product-item-name a {
    font-weight: 700;
}
.product-item-name a:hover {
    text-decoration: none;
    color: #85b84b;
}
.product-item-name a::before {
    border-bottom: none;
}
.checkout-cart-index .page-main {
    padding: 0;
}
.checkout-cart-index .block.crosssell {
    float: none;
    width: 100%;
    max-width: 1570px;
    margin: 0 auto;
    padding: 40px 0;
}
.checkout-cart-index .block.crosssell .title strong {
    font-size: 2rem;
    line-height: 2rem;
}
#maincontent .cart-container-wrapper .cart-container {
    margin: 0 auto;
    max-width: 1570px;
}
#maincontent .cart-container-wrapper .product-image-container {
    max-width: 100px;
}
#maincontent .cart-container-wrapper #empty_cart_button {
    display: none;
}
#maincontent .cart-container-wrapper .cart-summary {

}
#maincontent .cart-container-wrapper .cart-summary .block .title {
    font-size: 1.4rem;
    line-height: 2.6rem;
}
#maincontent .cart-container-wrapper .cart-summary .summary.title:after {
    content: "";
}
#maincontent .cart-container-wrapper .cart-summary .block.discount {
    margin-top: 10px;
    padding-top: 10px;
}
#maincontent .cart-container-wrapper .cart-summary .block.discount .label {
    display: none;
}
#maincontent .cart-container-wrapper .cart-summary .block.discount .actions-toolbar .primary {
    float: none;
    width: 100%;
}
#maincontent .cart-container-wrapper .cart-summary .block.discount .actions-toolbar .primary .action.primary {
    margin: 0;
    width: 100%;
}
#maincontent .cart-container-wrapper .cart-summary .cart-summary-info-container h4 {
    margin: 12px 0;
    font-size: 18px;
}
#maincontent .cart-container-wrapper .cart-discount {
    float: none;
}
#maincontent .cart-container-wrapper .cart-discount #block-discount {
    float: none;
}
#maincontent .cart-container-wrapper .cart-discount .block > .title strong {
    font-size: 1.8rem;
    color: #2f3943;
}
#maincontent .cart-container-wrapper .cart-discount button {
    margin: 0;
}
#maincontent .cart-container-wrapper .cart-discount #coupon_code {
    width: auto;
}
#maincontent .cart-container-wrapper .product-item-details {
    padding: 0;
}
.customer-account-create .page-title-wrapper,
.customer-account-index .page-title-wrapper,
.customer-address-form .page-title-wrapper,
.customer-account-forgotpassword .page-title-wrapper,
.customer-account-edit .page-title-wrapper,
.customer-account-create .column.main,
.customer-account-index .column.main,
.customer-address-form .column.main,
.customer-account-forgotpassword .column.main,
.customer-account-edit .column.main {
}
.customer-account-index .column.main,
.customer-address-form .column.main,
.checkout-index-index .column.main,
.checkout-cart-index .column.main,
.customer-account-edit .column.main {

}
.color {
    color: inherit;
}
.opc-block-summary {
    background: white;
}
.checkout-index-index #checkout {
    max-width: 1440px;
    margin: 0 auto;
}

.checkout-index-index .opc-sidebar button.action {
    min-height: auto;
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    text-align: right;
    position: absolute;
    right: 0;
}
.checkout-index-index .opc-sidebar button.action > span {
    display: inline-block;
    display: none;
}
.checkout-index-index .opc-sidebar button.action:hover {
    background: none;
    border: none;
    color: #85b84b;
}
.checkout-index-index .opc-sidebar button.action:hover:before {
    color: inherit;
    border: none;
}
.checkout-index-index .opc-sidebar button.action:after {
    content: "";
    margin-left: 0;
}
.checkout-index-index .opc-sidebar button.action:before {
    font-family: 'luma-icons';
    display: inline-block;
    cursor: pointer;
    text-align: center;
    color: inherit;
    border: none;
    transition: all 0.4s;
    width: auto;
    position: relative;
    bottom: auto;
    left: 0;
}
.checkout-index-index .opc-sidebar button.action.action-edit:before {
    font-size: 1.8rem;
    line-height: 2rem;
    content: '\e601';
}
.checkout-index-index .opc-sidebar .delivery-information-title > span {
    font-size: 18px;
    font-weight: 500;
}
.checkout-index-index .opc-sidebar .opc-block-shipping-information .ship-to,
.checkout-index-index .opc-sidebar .opc-block-shipping-information .ship-via,
.checkout-index-index .opc-sidebar .opc-block-delivery-information,
.checkout-index-index .opc-sidebar .opc-block-info {
    background: #FFF;
    margin: 0;
    padding: 22px 30px;
}
.checkout-index-index .opc-sidebar .opc-block-shipping-information .ship-to p + p,
.checkout-index-index .opc-sidebar .opc-block-shipping-information .ship-via p + p,
.checkout-index-index .opc-sidebar .opc-block-delivery-information p + p,
.checkout-index-index .opc-sidebar .opc-block-info p + p {
    margin: 0;
}
.checkout-index-index .billing-address-same-as-shipping-block {
    margin: 30px;
}
.checkout-index-index .checkout-shipping-method .step-title,
.checkout-index-index .checkout-shipping-method .table-checkout-shipping-method {
    display: none;
}
.checkout-payment-method .payment-option-title .action-toggle {
    color: #2f3943;
}
.checkout-payment-method .payment-option-content .actions-toolbar .primary {
    float: none;
}
#maincontent #checkout .authentication-wrapper button {
    display: none;
}
.cart-empty {
    padding-bottom: 20px;
}
.billing-info .billing-info-cms-block {
    font-size: 11px;
    font-style: italic;
    line-height: 14px;
    margin-bottom: 10px;
}
#payment #cardexpiremonth iframe,
#payment #cardexpireyear iframe {
    height: 30px;
}
.customer-account-createpassword #maincontent,
.catalogsearch-result-index #maincontent,
.cms-page-view #maincontent,
body[class*=' customer-account-'] #maincontent,
.customer-account-create #maincontent,
.htmlsitemap-index-index #maincontent,
.checkout-onepage-success #maincontent,
.customer-account-login #maincontent,
.contact-index-index #maincontent,
.cms-no-route #maincontent,
.customer-account-forgotpassword #maincontent,
.page-layout-2columns-left .page-main,
.catalog-product-compare-index .page-main
{
    max-width: 1570px;
    margin: 0 auto;
}
.customer-account-login .actions-toolbar > .secondary {
    margin-left: 20px;
}
#payolution_instalment_dob_day,
#payolution_elv_dob_day {
    padding-right: 10px;
}
form fieldset#customer-email-fieldset .field .note {
    display: none !important;
}
.checkout-agreements-block {
    margin: 20px 0;
}
.checkout-agreements-block .checkout-agreement button {
    color: #000;
}
.checkout-agreements-block .checkout-agreement button:hover {
    text-decoration: none;
    color: #000;
}
.checkout-agreements-block .checkout-agreement button::after {
    display: none;
}
.checkout-info-block,
.cart-summary-info-cms-block {
    margin: 0 0 15px;
}
.checkout-info-block .image-container,
.cart-summary-info-cms-block .image-container {
    text-align: center;
}
.checkout-info-block .image-container.image-andreas-berger,
.cart-summary-info-cms-block .image-container.image-andreas-berger {
    margin-top: -30px;
    margin-right: -20px;
    margin-bottom: -20px;
}
.checkout-info-block p + p,
.cart-summary-info-cms-block p + p {
    margin: 0;
}
.checkout-info-block h4,
.cart-summary-info-cms-block h4 {
    margin: 0 0 20px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ccc;
    font-size: 18px;
    font-weight: 500;
}
.message.global.cookie {
    background: white;
    font-size: 15px;
    z-index: 100000;
}
.message.global.cookie a {
    color: #2f3943;
}
.message.global.cookie a:hover {
    color: #85b84b;
}
.xfolder-items,
.modal-popup .xfolder-items {
    list-style: none;
    padding: 0;
}
@media (max-width: 767px) {
    .logo {
        float: none;
    }
    .logo img {
        width: 80px;
        float: left;
        padding: 10px 0;
        margin-left: 10px;
    }
    span.logo-name {
        font-size: 1.6rem;
        font-weight: 400;
        margin: 5px 0 0 -10px;
    }
    .page-header .header.content {
        padding-top: 0;
    }
    .page-header .header.content .service-wrapper-container {
        position: relative;
        height: 50px;
        background: #f5f5f5;
        z-index: 150;
    }
    .page-header .header.content .service-wrapper-container #switcher-language {
        padding: 10px;
    }
    .page-header .header.content .service-wrapper-container .block-search {
        margin-top: 0;
        background: #f5f5f5;
        width: 100%;
        padding: 0;
    }
    .page-header .header.content .service-wrapper-container .block-search form .field.search input {
        padding: 10px;
    }
    .page-header .header.content #switcher-language {
        position: relative;
        float: left;
    }
    .page-header .header.content .block.search {
        float: right;
        margin-top: 0;
    }
    .page-header .header.content .minicart-wrapper {
        right: 10px;
        top: -5px;
        z-index: inherit;
    }
    .page-header .header.content .minicart-wrapper .counterHolder {
        background: transparent;
    }
    .page-header .header.content .minicart-wrapper .action.showcart::before {
        color: #2f3943;
    }
    .page-header.pageScrolled .header.content .block-search,
    .page-header.pageScrolled .header.content #switcher-language,
    .page-header.pageScrolled .header.content .logo-name {
        display: inline-block;
    }
    .page-header.pageScrolled .header.content .logo {
        position: static;
    }
    .page-header.pageScrolled .header.content .minicart-wrapper {
        right: 10px;
        top: -5px;
        z-index: inherit;
    }
    header .block-search .toggleSearchInput {
        right: 50px;
        top: 10px;
    }
    .nav-toggle {
        right: 17px;
        left: auto;
        top: 63px;
    }
    .block-search .action.search::before {
        font-size: 1.6rem;
        line-height: 3.2rem;
        color: #8f8f8f;
        content: '\e615';
        font-family: 'luma-icons';
        margin: 0;
        vertical-align: top;
        display: inline-block;
        font-weight: normal;
        overflow: hidden;
        text-align: center;
    }
    header .block-search .action.search::before {
        color: #2D3843;
        font-size: 2rem;
        line-height: 2.7rem;
        opacity: 1;
        top: 11px;
        right: 40px;
        position: absolute;
    }
    .action.search[disabled] {
        cursor: not-allowed;
        pointer-events: none;
        opacity: 0.5;
    }
    .block-search .action.search {
        display: inline-block;
        background: none;
        -moz-box-sizing: content-box;
        border: 0;
        box-shadow: none;
        line-height: inherit;
        margin: 0;
        padding: 0;
        text-decoration: none;
        text-shadow: none;
        font-weight: 400;
        position: absolute;
        right: 10px;
        top: 0;
        z-index: 1;
    }
    .block-search .action.search > span {
        border: 0;
        clip: rect(0, 0, 0, 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }
    .block-search .control {
        border-top: none;
    }
    .minicart-wrapper .action.showcart .counter.qty,
    .minicart-wrapper .action.showcart.active .counter.qty {
        top: -7px;
    }
    .minicart-wrapper .action.showcart .counterHolder.hasItem .counter.qty,
    .minicart-wrapper .action.showcart.active .counterHolder.hasItem .counter.qty {
        background: #85b84b;
    }
}
@media (max-width: 767px) {
    .block-static-block.widget .block-container {
        padding: 0 20px;
    }
}
@media (max-width: 767px) {
    .navigation {
        background: white;
    }
    .navigation a::before {
        border-bottom: none;
    }
    .navigation ul li.all-category {
        display: none;
    }
    .navigation .submenuWrapper {
        display: block !important;
        padding: 0;
    }
    .navigation a {
        padding: 10px;
    }
    .navigation .submenu:not(:first-child) ul {
        padding-left: 0;
    }
    .navigation .submenu:not(:first-child) .active > a {
        border-color: #85b84b;
    }
    .navigation .level0.active > a:not(.ui-state-active) {
        border-color: #85b84b;
    }
    .navigation .level0.has-active > a:not(.ui-state-active) {
        border-color: #85b84b;
    }
    .nav-sections-item-title {
        display: none;
    }
    .nav-sections-item-content {
        margin-top: 0;
        margin-left: 0;
        padding: 0;
    }
    .nav-sections-item-content#store\.quicklinks {
        display: block !important;
    }
    .nav-sections-item-content#store\.quicklinks .page-service-tabs ul {
        list-style: none;
        padding-left: 0;
    }
    .nav-sections-item-content#store\.quicklinks .page-service-tabs ul li {
        padding: 8px 15px;
        margin-bottom: 0;
    }
    .nav-sections-item-content#store\.quicklinks .page-service-tabs ul li.servicePanelFolder {
        background: #318cc5;
    }
    .nav-sections-item-content#store\.quicklinks .page-service-tabs ul li.servicePanelProduct {
        background: #2D3843;
    }
    .nav-sections-item-content#store\.quicklinks .page-service-tabs ul li a {
        color: white;
        display: block;
    }
    .nav-sections-item-content#store\.quicklinks .page-service-tabs ul li a::before {
        border-bottom: none;
    }
    .nav-open .page-wrapper {
        left: auto;
    }
    .nav-toggle {
        position: absolute;
    }
    .nav-open .nav-sections {
        width: 100%;
        background: transparent;
        box-shadow: none;
    }
    .nav-open .nav-toggle::after {
        display: none;
    }
    .navigation .parent .level-top:after {
        right: 17px;
    }
    .nav-sections-items {
        padding-bottom: 20px;
    }
    .nav-open .page-header.pageScrolled .nav-sections .nav-sections-items {
        padding: 0;
    }
}
@media (max-width: 767px) {
    .xcategory .xcategoryWrapper {
        padding: 0;
        display: block;
    }
    .xcategory .xcategoryWrapper .containerLeft,
    .xcategory .xcategoryWrapper .containerRight,
    .xcategory .xcategoryWrapper .containerBelow {
        display: block;
        flex: 0 0 100%;
        width: 100%;
        max-height: 100%;
    }
    .xcategory .xcategoryWrapper .containerLeft .category-item,
    .xcategory .xcategoryWrapper .containerRight .category-item,
    .xcategory .xcategoryWrapper .containerBelow .category-item {
        width: 100%;
        max-height: 100%;
    }
    .xcategory .xcategoryWrapper .containerLeft .category-item .categoryWrapper img,
    .xcategory .xcategoryWrapper .containerRight .category-item .categoryWrapper img,
    .xcategory .xcategoryWrapper .containerBelow .category-item .categoryWrapper img {
        width: 100%;
    }
    .xcategory .xcategoryWrapper .containerRight .category-item .xtags-container,
    .xcategory .xcategoryWrapper .containerBelow .category-item .xtags-container {
        left: auto;
        right: 20px;
        top: 20px;
    }
    .xcategory .xcategoryWrapper .category-item .categoryWrapper {
        display: block;
    }
    .xcategory .xcategoryWrapper .category-item .xtags-container .xtags-tag {
        padding: 0;
        max-width: 80px;
    }
    .xcategory .xcategoryWrapper .containerBelow {
        margin-top: 0;
    }
    .xcategory .xcategoryWrapper .category-item {
        max-height: none;
    }
}
@media (max-width: 767px) {
    .top-quality-banner {
        flex-direction: column;
    }
    .top-quality-banner .portrait-col {
        flex: 1 0 100%;
        order: 2;
    }
    .top-quality-banner .portrait-col blockquote {
        left: 0;
        margin: 0;
        padding: 0 20%;
        top: 210px;
        width: 100%;
        box-sizing: border-box;
    }
    .top-quality-banner .advantages-col {
        order: 1;
    }
    .top-quality-banner .advantages-col .textWrapper {
        padding: 50px 20px 0 20px;
    }
    .top-quality-banner .advantages-col .textWrapper h3 {
        margin-bottom: 20px;
    }
    .top-quality-banner .xtags-container-wrapper {
        order: 3;
        position: relative;
        left: auto;
        top: auto;
    }
    .top-quality-banner .xtags-container h3 {
        margin-top: 10px;
    }
    .top-quality-banner .xtags-container .xtagsItemWrapper {
        flex-direction: column;
        height: auto;
    }
    .top-quality-banner .xtags-container .xtagsItemWrapper .xtags-tag {
        max-width: none;
        width: 100%;
        padding: 20px 0;
        flex: 0 0 auto;
    }
    .top-quality-banner .xtags-container .xtagsItemWrapper .xtags-tag + div {
        border-top: 1px solid #e4e4e4;
    }
    .top-quality-banner .xtags-container .xtagsItemWrapper .icon::before {
        display: none;
    }
}
@media (max-width: 767px) {
    .banner-inner-container {
        flex-direction: column;
        height: auto;
    }
    .banner-inner-container .banner-tags {
        margin-top: 50px;
        margin-right: 0;
        margin-bottom: 35px;
    }
    .banner-inner-container .banner-content {
        padding: 20px;
        text-align: center;
        margin-bottom: 50px;
    }
}
@media (max-width: 767px) {
    .xtags-container-wrapper {
        padding: 20px 0;
    }
    .xtags-container h3 {
        padding: 0 20px;
    }
    .xtags-container .xtagsItemWrapper {
        flex-direction: column;
        height: auto;
    }
    .xtags-container .xtagsItemWrapper .xtags-tag {
        max-width: none;
        width: 100%;
        padding: 20px 0;
    }
    .xtags-container .xtagsItemWrapper .xtags-tag + div {
        border-top: 1px solid #e4e4e4;
    }
    .xtags-container .xtagsItemWrapper .icon::before {
        display: none;
    }
}
@media (max-width: 767px) {
    .page-footer .footer.content {
        border-top: none;
    }
    .page-footer .footer-wrapper {
        flex-direction: column;
        padding: 50px 0 50px 0;
        background: #f5f5f5;
    }
    .page-footer .footer-wrapper .footer-column {
        padding: 30px 20px;
        text-align: center;
    }
    .page-footer .footer-wrapper .footer-column.links::after {
        display: none;
    }
    .page-footer .footer-wrapper .footer-column ul li a {
        display: inline-block;
    }
}
@media (max-width: 767px) {
    /**
    Category list
   */
    .category-view {
        /* category cms */
    }
    .category-view h2 b {
        word-break: break-word;
    }
    .category-view .category-view-image-container {
        padding: 0;
        display: block;
    }
    .category-view .category-view-image-container .category-image {
        max-width: 100%;
    }
    .category-view .category-view-image-container .page-title-wrapper {
        padding: 20px;
        text-align: center;
    }
    .category-view .category-view-image-container .xtags-container-wrapper {
        padding: 0;
    }
    .category-view .category-view-image-container .xtags-container-wrapper .xtags-container {
        position: absolute;
        top: auto;
        bottom: 0;
        left: auto;
        right: 20px;
    }
    .category-view .category-view-image-container .xtags-container-wrapper .xtags-container .xtagsItemWrapper .xtags-tag {
        max-width: 80px;
    }
    .category-view .category-view-image-container .xtags-container-wrapper .xtags-container .icon {
        z-index: 10;
    }
    .category-view .category-description-container .category-description {
        padding: 20px;
        box-sizing: border-box;
        width: 100%;
    }
    .category-view .category-description table,
    .category-view .category-description tbody,
    .category-view .category-description table tr,
    .category-view .category-description table td {
        display: block;
    }
    .category-view .category-cms {
        margin: 0 auto;
        padding: 20px 0;
    }
    .categories.wrapper {
        margin: 0;
    }
    .categories.wrapper ol.categories {
        flex-direction: column;
        margin: 0;
    }
    .categories.wrapper ol.categories .category-item {
        width: 100%;
        margin: 10px 0;
    }
    .categories.wrapper ol.categories .category-item .category-image .image {
        width: 100%;
    }
    .categories.wrapper ol.categories .category-item .product-item-info-wrapper .category-name-actions {
        width: 100%;
        text-align: center;
    }
    .categories.wrapper ol.categories .category-item:hover {
        box-shadow: none;
        transform: none;
    }
}
@media (max-width: 767px) {
    /**
  product list
   */
    .page-products.page-layout-1column .products-grid .product-items {
        margin: 0;
        flex-direction: column;
    }
    .page-products.page-layout-1column .products-grid .product-items .product-item {
        width: 100%;
        margin: 0;
    }
    .page-products.page-layout-1column .products-grid .product-items .product-item img {
        width: 100%;
    }
    .page-products.page-layout-1column .products-grid .product-items .product-item:hover {
        transition: none;
        box-shadow: none;
        transform: none;
    }
    .page-products .columns {
        padding-top: 0;
    }
    .category-select-container {
        padding: 0 20px;
    }
    .products-grid {
        margin-bottom: 0;
    }
    .products-grid .category-image {
        max-width: 100%;
    }
    .products-grid .product-item .xtags-container-wrapper {
        padding: 0;
    }
    .products-grid .product-item .xtags-container-wrapper .xtags-container {
        position: absolute;
        top: 0;
        left: auto;
        right: 20px;
    }
    .products-grid .product-item .xtags-container-wrapper .xtags-container .xtagsItemWrapper .xtags-tag {
        padding: 0;
        max-width: 80px;
    }
    .products-grid .product-item .xtags-container-wrapper .xtags-container .icon {
        z-index: 10;
    }
    .products-grid .category-description table,
    .products-grid .category-description table tr,
    .products-grid .category-description table td {
        display: block;
    }
    /** Product list **/
    .category-select-container select {
        font-size: 1.6rem;
    }
    #product.info.details.tab.related .block.related .products-list .product-item-info.related-available {
        display: block;
    }
    #product.info.details.tab.related .block.related .products-list .product-item-info.related-available div.product {
        display: block;
    }
    #product.info.details.tab.related .block.related .products-list .product-item-info.related-available div.product-item-image {
        text-align: center;
    }
    #product.info.details.tab.related .block.related .products-list .product-item-info.related-available .product-item-details {
        padding: 0;
    }
    #product.info.details.tab.related .block.related .products-list .product-item-info.related-available .product-item-choice {
        margin-bottom: 50px;
    }
}
@media (max-width: 767px) {
    .product-view-navigation,
    .pageScrolled .product-view-navigation {
        display: none;
    }
    .product-view-navigation .productViewNavigationWrapper,
    .pageScrolled .product-view-navigation .productViewNavigationWrapper {
        display: none;
    }
    .page-layout-1column .product.media {
        width: 100%;
    }
    .page-layout-1column .product.media .fotorama__dot {
        width: 10px;
        height: 10px;
    }
    .page-layout-1column .product.media .fotorama__active .fotorama__dot {
        background-color: #85b84b;
        border-color: #85b84b;
    }
    .page-layout-1column a.action,
    .page-layout-1column button.action {
        width: 100%;
    }
    .page-layout-1column .product-info-main {
        margin: 0;
        padding: 0 20px;
        width: 100%;
    }
    .page-layout-1column .product-info-main .product-info-overview-container,
    .page-layout-1column .product-info-main .product-info-cmsblock-container {
        font-size: 1.8rem;
    }
    .page-layout-1column .product-info-main .product-info-cmsblock-container table {
        display: block;
    }
    .page-layout-1column .product-info-main .product-info-cmsblock-container table tr,
    .page-layout-1column .product-info-main .product-info-cmsblock-container table td {
        display: block;
        width: 100%;
    }
    .page-layout-1column .product-info-main .product-info-price .price-box {
        padding: 0;
    }
    .page-layout-1column .product-info-main .product-info-shipping-container {
        margin-top: 15px;
    }
    .page-layout-1column .product-info-details-tab-details-description .product.attribute.description .column img {
        width: 100%;
    }
    #maincontent .product-options-bottom button.tocart {
        margin: 0;
    }
    #maincontent .product-service-container {
        margin-top: 0;
    }
    #maincontent .product-service-container .info-col .slogan-container blockquote {
        left: 0;
        margin: 0;
        padding: 0 60px;
        top: 210px;
    }
    #maincontent .product-service-container .service-container {
        flex-wrap: wrap;
    }
    #maincontent .product-service-container .service-container .info-col {
        flex: 1 0 100%;
    }
    #maincontent .product-service-container .service-container .serviceFormWrapper {
        left: auto;
        top: auto;
        flex-direction: column;
        position: relative;
    }
    #maincontent .product-service-container .service-container .serviceFormWrapper .form-catalog-col {
        margin-left: 0;
    }
    #maincontent .product-service-container .callback-container .callback-form-container {
        padding: 0 20px;
        width: 100%;
    }
    #maincontent .product-service-container .callback-container .callback-form-container fieldset .phoneFieldButtonWrapper {
        color: inherit;
        flex-wrap: wrap;
    }
    #maincontent .product-service-container .callback-container .callback-form-container fieldset .phoneFieldButtonWrapper button {
        margin: 0;
    }
    .actions-toolbar > .primary {
        text-align: left;
    }
    .product.data.items {
        margin: 0;
        width: auto;
    }
    .product.info.detailed {
        width: 100%;
    }
    .product.info.detailed .product.data.items .item.title.active {
        box-shadow: 0 7px 7px -7px #808080;
    }
    .product.info.detailed .product.data.items .item.title > .switch {
        margin-right: 0;
    }
    .product.info.detailed .col1,
    .product.info.detailed .col2 {
        float: none;
        width: 100%;
    }
    .product.info.detailed .product.data.items .item.content {
        border: 0;
        margin-top: 0;
    }
    .product.info.detailed .product.data.items .item.content table {
        display: block;
    }
    .product.info.detailed .product.data.items .item.content table tr,
    .product.info.detailed .product.data.items .item.content table td {
        display: block;
        width: 100%;
    }
    .ui-accordion .ui-accordion-header {
        padding: 22px 15px 23px 20px;
    }
    .ui-accordion .owl-carousel {
        margin-top: 50px;
    }
    .customNavigation {
        display: none;
    }
    #maincontent .block.upsell {
        margin: 0;
    }
    #maincontent .product-add-form .control .select-wrapper {
        display: block;
    }
    #maincontent .product-add-form .box-tocart {
        margin-bottom: 20px;
    }
    #maincontent .product-add-form .box-tocart .fieldset {
        display: block;
    }
    #maincontent .product-add-form .box-tocart .fieldset .field.qty {
        float: none;
        display: block;
    }
    #maincontent .product-add-form .box-tocart .fieldset .actions {
        display: block;
        float: none;
    }
    #maincontent .product-add-form .box-tocart .fieldset button.tocart {
        margin: 0;
    }
    #maincontent .block.related,
    #maincontent .block.upsell {
        padding: 0;
    }
    #maincontent .block.related .products-grid,
    #maincontent .block.upsell .products-grid {
        margin: 20px 0;
    }
    #maincontent .block.related .products-grid .product-items,
    #maincontent .block.upsell .products-grid .product-items {
        margin: 0;
    }
    #maincontent .block.related .products-grid .product-items .product-item,
    #maincontent .block.upsell .products-grid .product-items .product-item {
        margin: 0;
        width: 100%;
    }
    #maincontent .block.related .products-grid .product-items .product-item:hover,
    #maincontent .block.upsell .products-grid .product-items .product-item:hover {
        box-shadow: none;
    }
    .product-category-list-container {
        padding: 20px 0;
    }
    .product-category-list-container .category-overview-info {
        padding: 0 10px;
    }
}
@media (max-width: 767px) {
    .xgifts-container {
        text-align: center;
    }
    .xgifts-container .xgifts-products-list .product-items .product-item-details {
        text-align: center;
        flex-direction: row;
        flex-wrap: wrap;
    }
    .xgifts-container .xgifts-products-list .product-items .product-item-details .product-item-text-wrapper {
        margin-top: 20px;
        margin-bottom: 20px;
    }
}
@media (max-width: 767px) {
    .opc-progress-bar {
    }
    .payolution.instalment-plan {
        padding: 0 20px;
    }
    #maincontent .cart-container-wrapper .product-image-container {
        max-width: 100%;
    }
    #maincontent .cart-summary .cart-summary-info-container .cart-summary-info-cms-block {
        padding-left: 15px;
        padding-right: 15px;
    }
    #maincontent .cart-summary .cart-summary-info-container .cart-summary-info-cms-block .image-container {
        text-align: center;
    }
    #maincontent .cart-summary .cart-summary-info-container .cart-summary-info-cms-block .image-container.image-andreas-berger {
        margin: 0;
    }
    .cart.table-wrapper thead .col.actions {
        display: none;
    }
    .block.gift {
        padding: 0 20px;
    }
    .col.item-actions {
        padding-left: 20px;
    }
    .cart.table-wrapper .col.item-actions {
        box-sizing: border-box;
        display: block;
        float: left;
        width: 100%;
    }
    .delivery-comment {
        padding: 0 20px;
    }
    .checkout-index-index .opc-wrapper {
        width: 100%;
        box-sizing: border-box;
    }
    .checkout-index-index .opc-wrapper > li {
        box-sizing: border-box;
    }
    .checkout-index-index .opc-sidebar {
        width: 100%;
    }
    .checkout-index-index .page-header .header.content .service-wrapper-container {
        height: auto;
        padding: 0;
    }
    .checkout-index-index .minicart-wrapper .action.showcart .counterHolder.hasItem .counter.qty {
        background: black;
    }
    .checkout-index-index .page-header {
        margin-bottom: 0;
        border-bottom: none;
    }
    .opc-estimated-wrapper {
        margin: 0;
    }
    #opc-shipping_method {
        padding: 0 20px;
    }
}
.customer-account-createpassword #maincontent,
.catalogsearch-result-index #maincontent,
.cms-page-view #maincontent,
body[class*=' customer-account-'] #maincontent,
.customer-account-create #maincontent,
.htmlsitemap-index-index #maincontent,
.checkout-onepage-success #maincontent,
.customer-account-login #maincontent,
.contact-index-index #maincontent,
.customer-account-forgotpassword #maincontent {
    padding: 0;
}
.customer-account-createpassword #maincontent button.action,
.catalogsearch-result-index #maincontent button.action,
.cms-page-view #maincontent button.action,
body[class*=' customer-account-'] #maincontent button.action,
.customer-account-create #maincontent button.action,
.htmlsitemap-index-index #maincontent button.action,
.checkout-onepage-success #maincontent button.action,
.customer-account-login #maincontent button.action,
.contact-index-index #maincontent button.action,
.customer-account-forgotpassword #maincontent button.action {
    margin-right: 0;
}
@media (min-width: 768px) and (max-width: 1200px) {
    .sections.nav-sections {
        padding: 0;
    }
    .cart-empty,
    .opc-progress-bar {
        padding: 0;
    }
    .page-header .header.content {
        padding: 30px 10px 0 10px;
    }
    .xcategory .xcategoryWrapper .category-item .textWrapper .cat-title {
        margin: 0;
    }
    .top-quality-banner .advantages-col .textWrapper {
        padding-top: 40px;
    }
    .top-quality-banner .xtags-container .xtagsItemWrapper {
        flex-direction: row;
    }
    .top-quality-banner .xtags-container .xtagsItemWrapper .xtags-tag {
        flex: 1 0 145px;
    }
    .top-quality-banner .xtags-container-wrapper {
        width: auto;
        background: transparent;
        position: absolute;
        right: 126px;
        top: 192px;
        left: auto;
    }
    .xcategory .xtags-container .xtagsItemWrapper .xtags-tag {
        max-width: 50px;
    }
    .xcategory .xcategoryWrapper .containerRight .xtags-container,
    .xcategory .xcategoryWrapper .containerBelow .xtags-container {
        left: auto;
        right: 20px;
    }
    .page-layout-1column .product-info-main .product-options-bottom .box-tocart .fieldset .actions {
    }
    #maincontent .product-service-container .info-col .slogan-container blockquote {
        left: 50px;
        top: 289px;
    }
    .serviceFormWrapper .col {
        margin: 75px 0;
    }
    #maincontent .product-service-container .service-container .serviceFormWrapper .col::before {
        display: none;
    }
}
