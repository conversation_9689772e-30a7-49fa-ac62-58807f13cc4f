.mgz-flex,
.mgz-builder-navigator .mgz-navigator-element-inner,
.mgz-builder-navigator .mgz-navigator-controls ul,
.mgz-modal-elements .tab-content > .tab-pane ul li .mgz_element-container,
.mgz-modal .modal-dialog,
.mgz-modal .mgz-modal-header,
.mgz-builder .mgz-inline-editor ~ .mce-tinymce-inline .mce-container,
.mgz-builder .mgz-inline-editor ~ .mce-tinymce-inline .mce-container-body,
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-inner,
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-options,
.mgz-element-controls,
.mgz-content-helper,
.mgz-element-default,
.mgz-builder-element-icon {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.mgz-invisible,
.mgz__actions-switch .mgz__actions-switch-checkbox,
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-item-left span,
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-item-center span,
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-item-right span {
  border: 0;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}
a.mgz-btn {
  color: #333;
}
.mgz-btn:not(.primary) {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
button.mgz-btn {
  border: 0;
}
.mgz-btn,
.mgz-spectrum.sp-container button {
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  margin: 0;
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  background-image: none;
  word-wrap: break-word;
  text-decoration: none;
  position: relative;
  line-height: normal;
  padding: 10px 20px;
  color: #333;
  background-color: #e3e3e3;
  font-size: 1.4rem;
  max-width: 100%;
  height: auto;
}
.mgz-btn:hover {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  color: #5e5e5e;
  background-color: #dcdcdc;
  text-decoration: none;
}
.mgz-btn:focus {
  outline: none;
}
.mgz-btn.mgz-btn-save,
.mgz-btn.mgz-btn-cancel,
.mgz-btn.mgz-btn-replace {
  padding: 15px 20px;
  font-size: 1.6rem;
  font-weight: 500;
  min-width: 140px;
}
.mgz-btn.mgz-btn-save {
  background: #007dbd;
  color: #FFF;
}
.mgz-btn.mgz-btn-save:hover {
  background: #0073ae;
}
.mgz-btn.mgz-btn-cancel {
  color: #fff;
  background-color: #afafaf;
}
.mgz-btn.mgz-btn-cancel:hover {
  background-color: #8c8c8c;
}
.mgz-btn.mgz-btn-replace {
  float: left;
  color: #fff;
  background-color: #afafaf;
}
.mgz-btn.mgz-btn-replace:hover {
  background-color: #8c8c8c;
}
.mgz-btn.mgz-btn-delete {
  color: #FFF;
  background-color: #e22626;
}
.mgz-btn.mgz-btn-delete:hover {
  background-color: #ca1c1c;
}
.mgz-icon,
.mgz-dynamic-delete,
.mgz__field .mgz__collapsible-title:before,
.mgz__control-uiselect.ui-select-multiple:not(.ng-not-empty):before,
.mgz__control-uiselect .ui-select-toggle:before,
.mgz__control-checkbox:checked + label:before,
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-item-left,
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-item-center,
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-item-right {
  font-family: 'Magezon-Icons' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.mgz-fa,
.mgz-builder-navigator .mgz-navigator-element-inner .mgz-fa-ellipsis-v,
.mgz-builder-history-item:before {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}
.mgz-fa-s {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}
.mgz-fa-r {
  font-family: 'Font Awesome 5 Free';
  font-weight: 400;
}
.mgz-liststyle,
.mgz-modal ul,
.mgz-builder ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.mgz-builder-row-icon {
  background-image: url('../images/icons/element-icon-row.svg');
}
.mgz-builder-text-icon {
  background-image: url('../images/icons/element-icon-text-block.svg');
}
.mgz-builder-section-icon {
  background-image: url('../images/icons/element-icon-section.svg');
}
.mgz-builder-separator-icon {
  background-image: url('../images/icons/element-icon-separator.svg');
}
.mgz-builder-empty-icon {
  background-image: url('../images/icons/element-icon-empty-space.svg');
}
.mgz-builder-tabs-icon {
  background-image: url('../images/icons/element-icon-tabs.svg');
}
.mgz-builder-gmaps-icon {
  background-image: url('../images/icons/element-icon-google-maps.svg') !important;
  background-color: transparent !important;
}
.mgz-design-wrapper > .mgz__field-group-columns > .mgz__field {
  display: block;
}
.mgz-design-styling {
  width: 250px !important;
  padding-left: 30px;
  display: inline-block !important;
}
.mgz-design-styling select {
  width: 100%;
}
.mgz_field-device_type {
  margin-bottom: 20px !important;
}
.formly-field.mgz-design-layout-wrapper {
  display: block !important;
  margin-bottom: 0;
}
.formly-field.mgz-design-layout-wrapper.mgz-design-layout-all .nav.nav-tabs {
  display: none;
}
.formly-field.mgz-design-layout-wrapper > .mgz__field > .formly-field {
  margin-bottom: 0;
}
.nav-tabs-length1 > .nav-tabs {
  display: none !important;
}
.modal-tabs-style1 > .nav.nav-tabs {
  margin-bottom: 20px;
}
.modal-tabs-style1 > .nav.nav-tabs > li > a {
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  border: 0;
  margin: 0;
  padding: 0;
  font-weight: bold;
  min-width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  text-align: center;
}
.mgz-design-layout {
  position: relative;
  width: 350px !important;
  float: left;
  padding-right: 20px;
  margin-bottom: 0;
}
.mgz-design-layout .mgz-design-simply {
  position: absolute;
  right: 0;
  top: -10px;
}
.mgz-design-layout .mgz-design-simply label {
  font-weight: 600;
}
.mgz-design-layout .mgz-design-logo {
  display: block;
  height: 30px;
  width: 30px;
  color: #FFF;
  text-align: center;
  font-size: 22px;
  background: url(../images/logo.png) no-repeat center center;
  background-size: 30px;
  margin-bottom: -8px;
}
.mgz-design-layout.mgz_field-radius {
  right: 0;
  top: -35px;
  border: 1px solid #d1d1d1;
  display: block;
  position: relative;
  background: #f6f6f6;
  margin-top: 65px;
}
.mgz-design-layout.mgz_field-radius:before,
.mgz-design-layout.mgz_field-radius:after {
  content: " ";
  display: table;
  clear: both;
}
.mgz-design-layout.mgz_field-radius .mgz__field-label,
.mgz-design-layout.mgz_field-radius .mgz__field-control {
  display: inline-block;
  z-index: 1;
}
.mgz-design-layout.mgz_field-radius .mgz__field-label {
  display: none;
}
.mgz-design-layout.mgz_field-radius label {
  font-weight: bold;
}
.mgz-design-layout.mgz_field-radius input[type=text] {
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  text-align: center;
  width: 36px;
  margin: 0;
  padding: 0.4rem 2px 0.4rem;
  min-width: auto;
  font-size: 12px;
  height: 30px;
  border-color: #d1d1d1;
}
.mgz-design-layout.mgz_field-radius input[type=text]:focus {
  border-color: #007bdb;
}
.mgz-design-layout.mgz_field-radius input[type=text][disabled] {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  background-color: #f5f5f5;
  border-color: #d1d1d1;
  color: #7e7e7e;
  cursor: not-allowed;
}
.mgz-design-layout.mgz_field-radius .mgz__fieldset-wrapper-title {
  font-size: 12px;
  position: absolute;
  top: -15px;
  left: calc(50% - 75px);
  background-color: #f6f6f6;
  padding: 0 5px;
  z-index: 1;
  line-height: 30px;
}
.mgz-design-layout.mgz_field-radius .mgz__fieldset-wrapper-title strong {
  font-weight: normal;
}
.mgz-design-layout.mgz_field-radius > .mgz__fieldset-wrapper-title {
  right: 25px;
  top: -25px;
  left: auto;
  background: transparent;
}
.mgz-design-layout.mgz_field-radius .formly-field {
  position: absolute;
}
.mgz-design-layout.mgz_field-radius .formly-field.formly-field-group {
  position: relative;
  display: block;
  float: left;
}
.mgz-design-layout.mgz_field-radius .mgz-design-border {
  border: 1px solid #d1d1d1;
}
.mgz-design-layout.mgz_field-radius .mgz-design-border:before,
.mgz-design-layout.mgz_field-radius .mgz-design-border:after {
  content: " ";
  display: table;
  clear: both;
}
.mgz-design-layout.mgz_field-radius .mgz_field-padding {
  border: 1px solid #d1d1d1;
}
.mgz-design-layout.mgz_field-radius .mgz_field-padding:before,
.mgz-design-layout.mgz_field-radius .mgz_field-padding:after {
  content: " ";
  display: table;
  clear: both;
}
.mgz-design-layout.mgz_field-radius > .formly-field.formly-field-group {
  margin: 0;
}
.mgz-design-layout.mgz_field-radius .mgz_field-padding_unit {
  bottom: 3px;
  left: 3px;
}
.mgz-design-layout.mgz_field-radius .mgz__fieldset-wrapper-title {
  position: absolute;
}
.mgz-design-layout.mgz_field-radius .formly-field {
  margin: 0;
}
.mgz-design-layout.mgz_field-radius .formly-field.mgz-design-top {
  top: -15px;
  left: -15px;
}
.mgz-design-layout.mgz_field-radius .formly-field.mgz-design-right {
  top: -15px;
  right: -15px;
}
.mgz-design-layout.mgz_field-radius .formly-field.mgz-design-bottom {
  bottom: -15px;
  right: -15px;
}
.mgz-design-layout.mgz_field-radius .formly-field.mgz-design-left {
  left: -15px;
  bottom: -15px;
}
.mgz-design-layout .mgz_field-margin > .mgz__fieldset-wrapper-title {
  top: -4px;
  background: transparent;
}
.mgz-design-layout .mgz_field-margin .formly-field.formly-field-group {
  margin: 46px;
}
.mgz-design-layout .mgz_field-margin .formly-field.mgz-design-top {
  top: -15px;
  left: 50%;
  margin-left: -17px;
}
.mgz-design-layout .mgz_field-margin .formly-field.mgz-design-right {
  top: 50%;
  right: -15px;
  margin-top: -12px;
}
.mgz-design-layout .mgz_field-margin .formly-field.mgz-design-bottom {
  bottom: -15px;
  left: 50%;
  margin-left: -17px;
}
.mgz-design-layout .mgz_field-margin .formly-field.mgz-design-left {
  top: 50%;
  left: -15px;
  margin-top: -12px;
}
.mgz-design-layout .mgz_field-margin .formly-field.mgz_field-padding_unit {
  position: relative;
  margin: 55px;
  left: 0;
  bottom: 0;
}
.mgz-column-responsive i {
  font-size: 16px;
}
.mgz-column-responsive .formly > .mgz-response-xl .formly-field,
.mgz-column-responsive .formly > .mgz-response-lg .formly-field,
.mgz-column-responsive .formly > .mgz-response-md .formly-field,
.mgz-column-responsive .formly > .mgz-response-sm .formly-field,
.mgz-column-responsive .formly > .mgz-response-xs .formly-field {
  border: 1px solid #ddd;
  padding: 20px;
  text-align: center;
  vertical-align: middle;
}
.mgz-column-responsive .formly > .mgz-response-xl .formly-field:first-child,
.mgz-column-responsive .formly > .mgz-response-lg .formly-field:first-child,
.mgz-column-responsive .formly > .mgz-response-md .formly-field:first-child,
.mgz-column-responsive .formly > .mgz-response-sm .formly-field:first-child,
.mgz-column-responsive .formly > .mgz-response-xs .formly-field:first-child {
  width: 60px;
  background: #f8f8f8;
}
.mgz-column-responsive .formly > .mgz-response-xl .formly-field:nth-child(n+4),
.mgz-column-responsive .formly > .mgz-response-lg .formly-field:nth-child(n+4),
.mgz-column-responsive .formly > .mgz-response-md .formly-field:nth-child(n+4),
.mgz-column-responsive .formly > .mgz-response-sm .formly-field:nth-child(n+4),
.mgz-column-responsive .formly > .mgz-response-xs .formly-field:nth-child(n+4) {
  width: 140px;
}
.mgz-column-responsive .formly > .mgz-response-xl .formly-field + .formly-field,
.mgz-column-responsive .formly > .mgz-response-lg .formly-field + .formly-field,
.mgz-column-responsive .formly > .mgz-response-md .formly-field + .formly-field,
.mgz-column-responsive .formly > .mgz-response-sm .formly-field + .formly-field,
.mgz-column-responsive .formly > .mgz-response-xs .formly-field + .formly-field {
  border-left-color: transparent;
}
.mgz-column-responsive .formly .mgz-response-head .formly-field {
  padding: 10px;
  text-align: left;
  font-weight: 600;
}
.mgz-column-responsive .formly .mgz-response-head .formly-field:first-child {
  width: 60px;
}
.mgz-column-responsive .formly .mgz-response-head .formly-field:nth-child(n+4) {
  width: 140px;
}
.mgz-column-responsive .formly > .mgz-response-xl .formly-field,
.mgz-column-responsive .formly > .mgz-response-lg .formly-field,
.mgz-column-responsive .formly > .mgz-response-md .formly-field,
.mgz-column-responsive .formly > .mgz-response-sm .formly-field {
  border-bottom: 0;
}
.mgz-column-responsive .formly-field-group + .mgz__field,
.mgz-column-responsive .formly-field-group + .formly-field-group {
  margin: 0;
}
.mgz-column-responsive .mgz__field-group-columns .formly-field.mgz__field {
  vertical-align: bottom;
}
.mgz-imagemanager .mgz-imagemanager-preview {
  width: 90px;
  height: 90px;
  background-image: linear-gradient(45deg, #fff 25%, transparent 0, transparent 75%, #fff 0, #fff), linear-gradient(45deg, #fff 25%, transparent 0, transparent 75%, #fff 0, #fff);
  background-size: 30px 30px;
  background-position: 0 0,15px 15px;
  cursor: pointer;
  background-color: transparent;
  margin: 0 0 2px 0;
  position: relative;
  border: 1px solid #DFDFDF;
  text-align: center;
  max-width: 100%;
}
.mgz-imagemanager .mgz-imagemanager-preview:hover {
  border-color: #C8C8C8;
}
.mgz-imagemanager .mgz-imagemanager-preview .mgz-imagemanager-insert {
  position: absolute;
  width: 100%;
  height: 100%;
  background: #F5F5F5;
  left: 0;
  top: 0;
}
.mgz-imagemanager .mgz-imagemanager-preview .mgz-imagemanager-insert:hover {
  border-color: #C8C8C8;
}
.mgz-imagemanager .mgz-imagemanager-preview .mgz-imagemanager-insert span {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 32px;
  color: #007dbd;
  font-weight: 100;
}
.mgz-imagemanager img {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
  max-width: 100%;
  max-height: 100%;
}
.mgz-imagemanager .mgz-imagemanager-insert .mgz-icon-add {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
  color: #007dbd;
}
.mgz-imagemanager .mgz-imagemanager-actions {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -moz-transition-duration: 0.2s;
  -webkit-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  transition-duration: 0.2s;
  -webkit-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
  -webkit-transition-property: opacity;
  -moz-transition-property: opacity;
  -o-transition-property: opacity;
  transition-property: opacity;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.8);
}
.mgz-imagemanager .mgz-imagemanager-actions:hover {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.mgz-imagemanager .mgz-imagemanager-actions .mgz-imagemanager-action.mgz-icon.mgz-icon-add {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-opacity: 0.8;
  -moz-opacity: 0.8;
  opacity: 0.8;
  color: #FFF;
  left: 30px;
  font-weight: bold;
}
.mgz-imagemanager .mgz-imagemanager-actions .mgz-imagemanager-action.mgz-icon.mgz-icon-add:hover {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.mgz-imagemanager .mgz-imagemanager-actions .mgz-icon.mgz-icon-close {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-opacity: 0.8;
  -moz-opacity: 0.8;
  opacity: 0.8;
  color: #fa5755;
  left: 60px;
  font-weight: bold;
}
.mgz-imagemanager .mgz-imagemanager-actions .mgz-icon.mgz-icon-close:hover {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.mgz-imagemanager input {
  border: 0;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}
.mgz-dynamicrows-table-heading {
  background-color: #efefef;
  margin: 0 !important;
}
.mgz-dynamicrows-table-heading .formly-field {
  color: #303030;
  font-size: 1.4rem;
  font-weight: 600;
  vertical-align: bottom;
  border-bottom: 1px solid #fff;
  padding: 1.3rem 1rem 1.3rem 1rem;
}
.mgz-dynamicrows-table-heading .mgz__field-group-columns > .formly {
  table-layout: auto;
}
.mgz-dynamicrows {
  background-color: #efefef;
}
.mgz-dynamicrows .mgz-dynamicrows-add {
  padding: 10px;
}
.mgz-dynamicrows-position input {
  text-align: center;
}
.mgz-dynamicrows-item-index {
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  background: #007dbd;
  width: 25px;
  height: 25px;
  text-align: center;
  color: #FFF;
  position: absolute;
  left: -5px;
  top: -10px;
  font-size: 16px;
  line-height: 25px;
}
.mgz-dynamicrows-item {
  padding: 1rem 1rem 1rem 1rem;
  border-bottom: 3px solid #fff;
  position: relative;
}
.mgz-dynamicrows-item .mgz_field-delete {
  text-align: center;
  margin-bottom: 0;
}
.mgz-dynamicrows-item .mgz_element-value {
  min-width: 100px;
}
.mgz-dynamicrows-item .mgz-dynamicrows-actions {
  text-align: center;
  width: 60px !important;
}
.mgz-dynamicrows-item .mgz-dynamicrows-actions input {
  padding: 5px;
  min-width: 34px;
  text-align: center;
}
.mgz-dynamic-delete {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  background-color: transparent;
  border-color: transparent;
  padding-left: 0;
  padding-right: 0;
}
.mgz-dynamic-delete:hover {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  background-color: transparent;
  border-color: transparent;
}
.mgz-dynamic-delete:before {
  content: "\e630";
  font-size: 2rem;
}
.mgz-dynamic-delete > span {
  display: none;
}
.mgz-modal-form .mgz-magentowidget-html .admin__legend {
  margin-bottom: 1.5rem;
}
.mgz-modal-form .mgz-magentowidget-html .admin__legend + br {
  display: none;
}
.mgz-modal-form .mgz-magentowidget-html > .content-footer {
  display: none;
}
.mgz-modal-form .mgz-magentowidget-html #widget_options_form .admin__fieldset.fieldset {
  padding: 0;
  margin-left: 0;
  margin-right: 0;
  background: #FFF;
}
.mgz-modal-form .mgz-magentowidget-html #widget_options_form > .admin__fieldset.fieldset {
  margin-top: 0;
}
.mgz-modal-form .mgz-magentowidget-html #widget_options_form > .admin__fieldset.fieldset > .admin__legend {
  display: none;
}
.mgz-modal-form .mgz-magentowidget-html #widget_options_form .messages {
  display: none;
}
.mgz-modal-form .mgz-magentowidget-html .page-main-actions {
  display: none;
}
.mgz-modal-form .mgz-magentowidget-html label.error {
  color: #e22626;
}
.mgz-modal-form .mgz-magentowidget-html .admin__fieldset > .admin__field {
  margin-left: 0;
  margin-bottom: 0;
}
.mgz-modal-form .mgz-magentowidget-html .admin__fieldset > .admin__field > .admin__field-label {
  width: 100%;
  margin-left: 0;
  text-align: left;
  margin-left: 5px;
}
.mgz-modal-form .mgz-magentowidget-html .admin__fieldset > .admin__field > .admin__field-control {
  width: 100%;
  text-align: left;
  margin-left: 0;
}
.mgz-modal-form #select_widget_type-error {
  display: none !important;
}
.mgz-builder-navigator .mgz-navigator-element-inner {
  line-height: 35px;
  border-bottom: 1px solid #e6e9ec;
  cursor: pointer;
  position: relative;
}
.mgz-builder-navigator .mgz-navigator-element-inner .mgz-fa-ellipsis-v {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  position: absolute;
  left: 10px;
  font-size: 12px;
  display: none;
  cursor: move;
}
.mgz-builder-navigator .mgz-navigator-element-inner:hover .mgz-fa-ellipsis-v {
  display: block;
}
.mgz-builder-navigator .mgz-navigator-controls {
  position: absolute;
  right: 0;
  height: 35px;
  line-height: 35px;
  top: 0;
}
.mgz-builder-navigator .mgz-navigator-controls ul li {
  text-align: center;
  cursor: pointer;
}
.mgz-builder-navigator .mgz-navigator-controls ul li:hover {
  background: #e6e6e6;
}
.mgz-builder-navigator .mgz-navigator-controls ul li span {
  display: none;
}
.mgz-builder-navigator .mgz-navigator-controls ul li a {
  width: 30px;
  color: #333;
  display: block;
}
.mgz-builder-navigator .mgz-navigator-element-visible {
  width: 30px;
  text-align: center;
}
.mgz-builder-navigator .mgz-navigator-element {
  position: relative;
}
.mgz-builder-navigator .magezon-builder-directive-control_row_layout,
.mgz-builder-navigator .magezon-builder-directive-control_navigator {
  display: none;
}
.mgz-builder-navigator .mgz-navigator-element-list-toggle {
  -webkit-opacity: 0.8;
  -moz-opacity: 0.8;
  opacity: 0.8;
  margin-right: 8px;
}
.mgz-builder-navigator .mgz-navigator-element-list-toggle:hover {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.mgz-builder-navigator .mgz-navigator-element-title {
  width: 100%;
}
.mgz-builder-navigator .mgz-navigator-element-title .mgz-fa-eye-slash {
  display: none;
}
.mgz-builder-navigator .mgz-element-actived > .mgz-element-inner > .mgz-navigator-element-inner-wrapper > .mgz-navigator-element-inner,
.mgz-builder-navigator .mgz-element-actived > .mgz-element-inner > .mgz-navigator-element-inner {
  background: #f2f2f2;
}
.mgz-builder-navigator .mgz-element-disabled .mgz-fa-eye-slash,
.mgz-builder-navigator .mgz-element-hide-default .mgz-fa-eye-slash {
  display: none;
}
.mgz-builder-navigator .mgz-element-disabled .mgz-fa-eye-slash,
.mgz-builder-navigator .mgz-element-hide-default .mgz-fa-eye-slash {
  display: inline;
}
.mgz-modal-navigator .magezon-builder-directive-modal_navigator_toggle i {
  cursor: pointer;
  color: #FFF;
  position: relative;
  margin-right: 12px;
  font-size: 18px;
}
.mgz-modal-navigator .magezon-builder-directive-modal_navigator_toggle i:before {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
}
.mgz-modal-elements * {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.mgz-modal-elements .mgz-modal-search {
  width: 265px;
  position: relative;
  margin-right: 15px;
}
.mgz-modal-elements .mgz-modal-search label {
  -webkit-transition: color 0.2s ease-in-out;
  -moz-transition: color 0.2s ease-in-out;
  -ms-transition: color 0.2s ease-in-out;
  -o-transition: color 0.2s ease-in-out;
  height: 24px;
  width: 24px;
  content: '';
  display: block;
  position: absolute;
  left: 6px;
  top: 0;
  margin: auto;
  cursor: pointer;
  color: #bfc2c8;
  font-size: 22px;
}
.mgz-modal-elements .mgz-modal-search input {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  display: inline-block;
  margin: 0;
  padding: 8px 12px 8px 32px;
  line-height: normal;
  font-size: 12px;
  color: #333;
  border: none;
  width: 100%;
  position: relative;
  left: 0;
}
.mgz-modal-elements .mgz-modal-search input:focus {
  outline: none;
}
.mgz-modal-elements .mgz-modal-search input:focus + label {
  cursor: default;
  color: #7f8591;
}
.mgz-modal-elements .mgz-modal-search input[type='search'] {
  -webkit-appearance: searchfield;
  appearance: searchfield;
}
.mgz-modal-elements .mgz-modal-search input[type='search']::-webkit-search-cancel-button,
.mgz-modal-elements .mgz-modal-search input[type='search']::-webkit-search-decoration {
  -webkit-appearance: value;
  appearance: value;
}
.mgz-modal-elements .mgz-modal-search input::-webkit-input-placeholder {
  font-style: italic;
}
.mgz-modal-elements .mgz-modal-search input:-moz-placeholder {
  font-style: italic;
}
.mgz-modal-elements .mgz-modal-search input::-moz-placeholder {
  font-style: italic;
}
.mgz-modal-elements .mgz-modal-search input:-ms-input-placeholder {
  font-style: italic;
}
.mgz-modal-elements .mgz-modal-search input:focus + label {
  color: #7f8591;
}
.mgz-modal-elements .mgz-modal-search .mgz-icon-close {
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  position: absolute;
  background: #c8c8c8;
  font-size: 5px;
  padding: 4px;
  cursor: pointer;
  color: #fff;
  right: 5px;
  top: 9px;
  font-weight: bold;
}
.mgz-modal-elements .mgz-modal-search .mgz-icon-close:hover {
  background: #b3b3b3;
}
.mgz-modal-elements .tab-content > .tab-pane a {
  text-decoration: none;
}
.mgz-modal-elements .tab-content > .tab-pane ul li {
  -webkit-transition: border-color 0.2s;
  -moz-transition: border-color 0.2s;
  -ms-transition: border-color 0.2s;
  -o-transition: border-color 0.2s;
  background: none repeat scroll 0 0 #f5f5f5;
  border: 1px solid #faf9f9;
  padding: 0;
  margin: 0;
  overflow: hidden;
  position: relative;
  float: left;
  height: 70px;
  list-style: none;
  cursor: pointer;
  width: 16.5%;
}
.mgz-modal-elements .tab-content > .tab-pane ul li a {
  color: #333;
}
.mgz-modal-elements .tab-content > .tab-pane ul li:hover {
  border-color: #00aef0;
}
.mgz-modal-elements .tab-content > .tab-pane ul li:hover a {
  color: #00a0d2;
}
.mgz-modal-elements .tab-content > .tab-pane ul li .mgz_element-container {
  height: 100%;
}
.mgz-modal-elements .tab-content > .tab-pane ul li .mgz_element-container .mgz-builder-element-icon,
.mgz-modal-elements .tab-content > .tab-pane ul li .mgz_element-container .mgz_element-info {
  -webkit-flex: 1 1 auto;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  -webkit-align-self: center;
  -ms-align-self: center;
  align-self: center;
  text-align: left;
  padding: 5px;
}
.mgz-modal-elements .tab-content > .tab-pane ul li .mgz_element-container .mgz_element-info {
  width: 100%;
  line-height: 18px;
}
.mgz-modal-elements .tab-content > .tab-pane ul li .mgz_element-container .mgz_element-info .mgz_element-name {
  font-weight: 600;
  line-height: 14px;
  font-size: 14px;
}
.mgz-modal-elements .tab-content > .tab-pane ul li .mgz_element-container .mgz_element-info .mgz_element-description {
  font-size: 11px;
  line-height: 14px;
  color: #999;
  margin-right: 5px;
  margin-top: 3px;
}
.mgz-modal-elements .mgz-modal-tab.mgz-modal-searching li.active > a {
  color: #fff !important;
  background: #007dbd !important;
}
.mgz-modal-elements .mgz-modal-tab.mgz-modal-searching li.active:hover > a {
  background-color: rgba(0, 0, 0, 0.1) !important;
  cursor: pointer;
}
.mgz-modal-element .mgz-modal-tab > .tab-content {
  max-height: 300px;
}
.mgz-modal-element .mgz_field-hide_element .mgz__field-control a {
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  border: 0;
  margin: 0;
  padding: 0;
  font-weight: bold;
  min-width: 35px;
  height: 35px;
  line-height: 35px;
  font-size: 14px;
  text-align: center;
  display: inline-block;
  background: #e7e7e7;
  cursor: pointer;
  color: #333;
  float: left;
  margin-right: 2px;
  margin-bottom: 2px;
}
.mgz-modal-element .mgz_field-hide_element .mgz__field-control a.selected,
.mgz-modal-element .mgz_field-hide_element .mgz__field-control a:hover {
  background: #007dbd;
  color: #FFF;
}
.mgz-modal-templates.mgz-modal-form .modal-dialog {
  max-width: 1000px;
}
.mgz-modal-templates .mgz-mytemplates-list {
  margin-top: -2px;
}
.mgz-modal-templates .mgz-mytemplates-list .mgz-mytemplates-item {
  border-top: 1px solid #e2e2e2;
  border-bottom: 1px solid #e2e2e2;
  position: relative;
}
.mgz-modal-templates .mgz_field-tab_general {
  padding: 20px;
}
.mgz-modal-templates .mgz-mytemplates-item {
  position: relative;
}
.mgz-modal-templates .mgz-mytemplates-item:before,
.mgz-modal-templates .mgz-mytemplates-item:after {
  content: " ";
  display: table;
  clear: both;
}
.mgz-modal-templates .mgz-mytemplates-item + .mgz-mytemplates-item {
  border-top: 0;
}
.mgz-modal-templates .mgz-mytemplates-item:hover .mgz-mytemplates-item-toolbar {
  background-color: #f9f9f9;
}
.mgz-modal-templates .mgz-mytemplates-item:hover .mgz-mytemplates-item-name {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.mgz-modal-templates ul.mgz-mytemplates-item-actions {
  float: right;
  margin-right: 20px;
}
.mgz-modal-templates ul.mgz-mytemplates-item-actions li {
  display: inline-block;
  padding: 0 5px;
  position: relative;
}
.mgz-modal-templates ul.mgz-mytemplates-item-actions li a {
  -webkit-opacity: 0.7;
  -moz-opacity: 0.7;
  opacity: 0.7;
  color: inherit;
  cursor: pointer;
}
.mgz-modal-templates ul.mgz-mytemplates-item-actions li a:hover {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.mgz-modal-templates ul.mgz-mytemplates-item-actions li .mgz-icon-arrow_drop_down,
.mgz-modal-templates ul.mgz-mytemplates-item-actions li .mgz-icon-arrow_drop_up {
  font-size: 20px;
}
.mgz-modal-templates .mgz-mytemplates-item-name {
  -webkit-opacity: 0.8;
  -moz-opacity: 0.8;
  opacity: 0.8;
  display: inline-block;
  padding: 0 20px;
  font-weight: 600;
  cursor: pointer;
}
.mgz-modal-templates .mgz-mytemplates-item-toolbar {
  line-height: 50px;
  height: 50px;
  border-bottom: 1px solid transparent;
}
.mgz-modal-templates .mgz-active .mgz-mytemplates-item-toolbar {
  border-bottom-color: #e2e2e2;
}
.mgz-modal-templates .mgz-mytemplates-item-content {
  padding: 20px;
  position: relative;
  overflow: hidden;
}
.mgz-modal-templates .mgz-mytemplates-item-content:before,
.mgz-modal-templates .mgz-mytemplates-item-content:after {
  content: " ";
  display: table;
  clear: both;
}
.mgz-modal-templates .mgz-mytemplates-item-content .mgz-mytemplates-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  bottom: 0;
  z-index: 9999;
  left: 0;
}
.mgz-modal-templates .mgz-mytemplates-name .mgz__field-control {
  position: relative;
  display: table;
  border-collapse: separate;
  width: 100%;
}
.mgz-modal-templates .mgz-mytemplates-name .mgz__field-control input {
  width: 99%;
  display: table-cell;
  position: relative;
  z-index: 2;
  float: left;
  margin-bottom: 0;
}
.mgz-modal-templates .mgz-mytemplates-name .mgz__field-control span {
  position: relative;
  font-size: 0;
  white-space: nowrap;
  width: 1%;
  vertical-align: middle;
  display: table-cell;
}
.mgz-modal-templates .mgz-mytemplates-name .mgz__field-control button {
  height: 100%;
  padding: 7px;
  font-size: 1.4rem;
}
.mgz-modal-templates .mgz__field-control > .mgz-spinner {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  top: 50%;
  position: absolute;
  top: 45%;
  left: 50%;
  z-index: 0;
}
.mgz-modal-templates .mgz-mytemplates-empty {
  padding: 10px 0;
}
.mgz-modal-templates .mgz-tabs-nav-container .mgz-tabs-nav:last-child {
  display: none;
}
.mgz-modal-templates .mgz-modal-tab > .tab-content {
  padding: 0 !important;
}
.mgz-builder-history-item {
  line-height: 35px;
  border-bottom: 1px solid #e6e9ec;
  padding-left: 20px;
  padding-right: 1.6rem;
  cursor: pointer;
  position: relative;
}
.mgz-builder-history-item:before {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  position: absolute;
  left: 10px;
  font-size: 10px;
  display: none;
}
.mgz-builder-history-item:hover:before {
  display: block;
  content: "\f1da";
}
.mgz-builder-history-item:hover .mgz-builder-history-item__apply {
  display: block;
}
.mgz-builder-history-item:hover,
.mgz-builder-history-item.mgz-builder-history-item_selected {
  background: #e6e6e6;
}
.mgz-builder-history-item.mgz-builder-history-item_selected:before {
  display: block;
  content: "\f00c";
}
.mgz-builder-history-item.mgz-builder-history-item__action__editing_started .mgz-builder-history-item__action {
  text-decoration: none;
}
.mgz-builder-history-item .mgz-builder-history-item__date {
  -webkit-opacity: 0.9;
  -moz-opacity: 0.9;
  opacity: 0.9;
  float: right;
}
.mgz-builder-history-item .mgz-builder-history-item__apply {
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  margin: 0;
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  background-image: none;
  word-wrap: break-word;
  text-decoration: none;
  position: relative;
  line-height: normal;
  padding: 10px 20px;
  color: #333;
  background-color: #e3e3e3;
  font-size: 1.4rem;
  max-width: 100%;
  height: auto;
  background: #007dbd;
  color: #FFF;
  float: right;
  padding: 3px 5px;
  font-size: 12px;
  margin: 5px 10px 0 0;
  display: none;
}
.mgz-builder-history-item .mgz-builder-history-item__apply:hover {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  color: #5e5e5e;
  background-color: #dcdcdc;
  text-decoration: none;
}
.mgz-builder-history-item .mgz-builder-history-item__apply:focus {
  outline: none;
}
.mgz-builder-history-item .mgz-builder-history-item__apply.mgz-btn-save,
.mgz-builder-history-item .mgz-builder-history-item__apply.mgz-btn-cancel,
.mgz-builder-history-item .mgz-builder-history-item__apply.mgz-btn-replace {
  padding: 15px 20px;
  font-size: 1.6rem;
  font-weight: 500;
  min-width: 140px;
}
.mgz-builder-history-item .mgz-builder-history-item__apply.mgz-btn-save {
  background: #007dbd;
  color: #FFF;
}
.mgz-builder-history-item .mgz-builder-history-item__apply.mgz-btn-save:hover {
  background: #0073ae;
}
.mgz-builder-history-item .mgz-builder-history-item__apply.mgz-btn-cancel {
  color: #fff;
  background-color: #afafaf;
}
.mgz-builder-history-item .mgz-builder-history-item__apply.mgz-btn-cancel:hover {
  background-color: #8c8c8c;
}
.mgz-builder-history-item .mgz-builder-history-item__apply.mgz-btn-replace {
  float: left;
  color: #fff;
  background-color: #afafaf;
}
.mgz-builder-history-item .mgz-builder-history-item__apply.mgz-btn-replace:hover {
  background-color: #8c8c8c;
}
.mgz-builder-history-item .mgz-builder-history-item__apply.mgz-btn-delete {
  color: #FFF;
  background-color: #e22626;
}
.mgz-builder-history-item .mgz-builder-history-item__apply.mgz-btn-delete:hover {
  background-color: #ca1c1c;
}
.mgz-builder-history-item .mgz-builder-history-item__apply:hover {
  background: #0073ae;
  color: #FFF;
}
.mgz-builder-history-item .mgz-builder-history-item__title {
  font-weight: 700;
}
.mgz-builder-history-item .mgz-builder-history-item__action {
  text-decoration: underline;
}
.mgz-modal-clear_layout .modal-content {
  padding: 20px;
}
.formly > .formly-field:last-child {
  margin-bottom: 0;
}
.mgz-modal {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  -webkit-box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
  box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
  left: 0;
  right: 0;
  bottom: auto;
  background: #FFF;
  position: fixed !important;
  top: 5%;
}
.mgz-modal .modal-dialog {
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: stretch;
  -ms-align-items: stretch;
  align-items: stretch;
  -webkit-align-content: stretch;
  -ms-align-content: stretch;
  align-content: stretch;
  width: 100%;
  margin: 0;
  height: 100%;
}
.mgz-modal .mgz-modal-header {
  padding: 20px;
  background: #007dbd;
}
.mgz-modal .mgz-modal-header .mgz-modal-title {
  -webkit-flex-grow: 1;
  -ms-flex-grow: 1;
  flex-grow: 1;
  font-size: 2.2rem;
  font-weight: 300;
  margin: 0;
  min-height: 1em;
  color: #FFF;
  line-height: 1.2;
}
.mgz-modal .mgz-modal-header .mgz-modal-title .mgz-icon-edit {
  cursor: pointer;
}
.mgz-modal .mgz-modal-header .mgz-modal-title .mgz-modal-icon {
  margin-right: 5px;
}
.mgz-modal .mgz-modal-header .mgz-modal-title .mgz-contenteditable-element {
  padding-right: 0;
}
.mgz-modal .mgz-modal-header .mgz-modal-title span {
  display: inline-block;
}
.mgz-modal .mgz-modal-header .mgz-modal-title span.modal-subtitle {
  font-size: 1.4rem;
  font-style: italic;
}
.mgz-modal .mgz__control-select {
  min-width: 100%;
  max-width: 100%;
}
.mgz-modal .mgz-modal-tab > .tab-content {
  padding: 20px;
  overflow: scroll;
  display: block;
  width: 100%;
}
.mgz-modal .mgz-modal-content {
  -webkit-flex: 1 1 auto;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  min-height: 150px;
  position: relative;
  overflow-x: hidden;
  overflow-y: scroll;
  max-height: 100%;
}
.mgz-modal .mgz-modal-content > .mgz-spinner {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  position: absolute;
  z-index: 0;
}
.mgz-modal .nav-tabs {
  display: block;
  text-align: center;
  border: 0;
  background: transparent;
  margin-bottom: 15px;
}
.mgz-modal .nav-tabs li {
  float: none;
  display: inline-block;
  margin-right: 3px;
}
.mgz-modal .nav-tabs li > a {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  border: 0;
  padding: 2px 10px;
  background: #e7e7e7;
  color: #303030;
}
.mgz-modal .nav-tabs li.active > a,
.mgz-modal .nav-tabs li:hover > a {
  background: #007dbd;
  color: #FFF;
  border: 0;
}
.mgz-modal .mgz-modal-content-inner {
  height: 100%;
}
.mgz-modal .modal-content {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex: initial;
  -ms-flex: initial;
  flex: initial;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: stretch;
  -ms-align-items: stretch;
  align-items: stretch;
  -webkit-align-content: stretch;
  -ms-align-content: stretch;
  align-content: stretch;
  -webkit-flex-basis: 100%;
  -ms-flex-basis: 100%;
  flex-basis: 100%;
  background: #fff;
  position: relative;
  border: 0;
  overflow: scroll;
}
.mgz-modal .modal-content > form > .formly {
  min-height: 150px;
}
.mgz-modal .modal-content > form > .formly > .formly-field {
  margin: -1px 0 0 0;
}
.mgz-modal .mgz-modal-action-close {
  -webkit-opacity: 0.8;
  -moz-opacity: 0.8;
  opacity: 0.8;
  background: none;
  border: 0;
  color: #FFF;
  padding: 0;
}
.mgz-modal .mgz-modal-action-close:hover {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.mgz-modal .mgz-modal-action-close i {
  -webkit-transition: color 0.1s linear;
  -moz-transition: color 0.1s linear;
  -ms-transition: color 0.1s linear;
  -o-transition: color 0.1s linear;
  font-size: 2rem;
}
.mgz-modal .mgz-modal-footer {
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  padding: 18px;
  background: #f6f6f6;
  width: 100%;
  bottom: 0;
  z-index: 1;
}
.mgz-modal .mgz-modal-footer:before,
.mgz-modal .mgz-modal-footer:after {
  content: " ";
  display: table;
  clear: both;
}
.mgz-modal .magezon-builder-directive-modal_title {
  width: 100%;
}
.mgz-modal .mgz-modal-tab > .nav.nav-tabs {
  overflow: hidden;
  margin-bottom: 0;
  background: #007dbd;
  padding: 0 20px;
  border: 0;
  text-align: left;
  position: relative;
}
.mgz-modal .mgz-modal-tab > .nav.nav-tabs > li {
  margin: 0;
}
.mgz-modal .mgz-modal-tab > .nav.nav-tabs > li > a {
  -webkit-border-radius: 5px 5px 0 0;
  -moz-border-radius: 5px 5px 0 0;
  border-radius: 5px 5px 0 0;
  -webkit-transition: 'color,background,border' 0.2s ease-in-out;
  -moz-transition: 'color,background,border' 0.2s ease-in-out;
  -ms-transition: 'color,background,border' 0.2s ease-in-out;
  -o-transition: 'color,background,border' 0.2s ease-in-out;
  color: #FFF;
  border: 0;
  padding: 14px 13px;
  background: #007dbd;
}
.mgz-modal .mgz-modal-tab > .nav.nav-tabs > li.active > a {
  color: #555;
  background: #FFF;
}
.mgz-modal .mgz-modal-tab > .nav.nav-tabs > li:hover:not(.active) > a {
  background-color: rgba(0, 0, 0, 0.1);
}
.mgz-modal .magezon-builder-directive-modal_save,
.mgz-modal .magezon-builder-directive-modal_yes {
  margin-left: 8px;
}
.mgz-modal .magezon-builder-directive-modal_minimize {
  margin-right: 5px;
}
.mgz-modal .magezon-builder-directive-modal_minimize button {
  padding: 0;
  margin: 0;
  background: none;
  border: 0;
  cursor: pointer;
}
.mgz-modal .magezon-builder-directive-modal_minimize button:hover i {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.mgz-modal .magezon-builder-directive-modal_minimize i {
  -webkit-opacity: 0.8;
  -moz-opacity: 0.8;
  opacity: 0.8;
  font-size: 26px;
  color: #FFF;
}
.mgz-modal .magezon-builder-directive-modal_minimize button {
  -moz-transition-duration: 0.2s;
  -webkit-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  transition-duration: 0.2s;
  -webkit-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}
.mgz-modal .magezon-builder-directive-modal_minimize button.mgz-minimized {
  -webkit-transform: scaleY(-1);
  -moz-transform: scaleY(-1);
  -ms-transform: scaleY(-1);
  -o-transform: scaleY(-1);
}
.mgz-modal.mgz-minimized .ui-resizable-n,
.mgz-modal.mgz-minimized .ui-resizable-s,
.mgz-modal.mgz-minimized .ui-resizable-ne,
.mgz-modal.mgz-minimized .ui-resizable-nw {
  display: none;
}
.mgz-modal .mgz-inner-widthauto .mgz__control-select {
  width: auto;
  min-width: auto;
}
.mgz-modal-open .modal-backdrop.in {
  -webkit-opacity: 0.7;
  -moz-opacity: 0.7;
  opacity: 0.7;
  z-index: 799 !important;
  background: #000;
}
.mce-fullscreen .mgz-modal {
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  max-height: 100% !important;
}
@media (min-width: 768px) {
  .mgz-modal.mgz-modal-resizable {
    width: 80%;
  }
}
@media (max-width: 767px) {
  .mgz-modal {
    left: 10px !important;
    right: 10px !important;
    width: unset !important;
    height: 90% !important;
  }
}
.mgz-abs-form-control-pattern,
.mgz__control-text,
.mgz__control-textarea,
.mgz__control-select,
.mgz__control-uiselect,
.mgz__control-uiselect input {
  -webkit-transition: border-color 0.1s linear;
  -moz-transition: border-color 0.1s linear;
  -ms-transition: border-color 0.1s linear;
  -o-transition: border-color 0.1s linear;
  -webkit-border-radius: 1px;
  -moz-border-radius: 1px;
  border-radius: 1px;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  -webkit-appearance: none;
  background-color: #ffffff;
  border: 1px solid #adadad;
  color: #303030;
  font-size: 14px;
  font-weight: 400;
  height: auto;
  line-height: 1.36;
  padding: 0.6rem 1rem 0.6rem;
  vertical-align: baseline;
  width: auto;
}
.mgz-abs-form-control-pattern:hover,
.mgz__control-text:hover,
.mgz__control-textarea:hover,
.mgz__control-select:hover,
.mgz__control-uiselect:hover,
.mgz__control-uiselect input:hover {
  border-color: #878787;
}
.mgz-abs-form-control-pattern:focus,
.mgz__control-text:focus,
.mgz__control-textarea:focus,
.mgz__control-select:focus,
.mgz__control-uiselect:focus,
.mgz__control-uiselect input:focus {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  border-color: #007bdb;
  outline: 0;
}
.mgz-abs-form-control-pattern[disabled],
.mgz__control-text[disabled],
.mgz__control-textarea[disabled],
.mgz__control-select[disabled],
.mgz__control-uiselect[disabled],
.mgz__control-uiselect input[disabled] {
  background-color: #e9e9e9;
  border-color: #adadad;
  color: #303030;
  cursor: not-allowed;
  opacity: 0.5;
}
.mgz__control-text {
  min-width: 4rem;
}
.mgz__control-textarea {
  resize: vertical;
}
.mgz__control-multiselect option,
.mgz__control-multiselect optgroup {
  padding: 0.5rem 1rem;
}
.mgz-control-delete {
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  position: absolute;
  font-size: 8px;
  padding: 4px;
  right: -8px;
  top: -8px;
  cursor: pointer;
  color: #e22626;
  border: 1px solid #e22626;
  background: #FFF;
  font-weight: bold;
  z-index: 99;
}
.mgz-control-delete:hover {
  background: #e22626;
  color: #FFF;
}
.mgz-builder-fieldset > .formly > .formly-field.formly-field-group > label {
  font-size: 1.5rem;
}
.mgz__fieldset {
  border: 0;
  margin: 0;
  min-width: 0;
  padding: 0;
}
.mgz__field {
  display: block;
}
.mgz__field:before,
.mgz__field:after {
  content: " ";
  display: table;
  clear: both;
}
.mgz__field .mgz__field-label {
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 1.4rem;
  font-weight: 600;
  line-height: 3.2rem;
  padding: 0;
  white-space: nowrap;
  display: block;
  cursor: pointer;
  position: relative;
  text-align: left;
}
.mgz__field .mgz__field-label span {
  display: inline-block;
  line-height: 1.4;
  vertical-align: middle;
  white-space: normal;
  cursor: pointer;
}
.mgz__field .mgz__field-label .mgz-spinner i {
  width: 20px;
  height: 20px;
  border-width: 2px;
}
.mgz__field .mgz__collapsible-title {
  padding: 0 10px 0 25px;
}
.mgz__field .mgz__collapsible-title:before {
  content: '\e616';
  font-size: 1.8rem;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
  left: 10px;
}
.mgz__field .mgz__collapsible-title._show:before {
  content: '\e615';
}
.mgz__field .mgz__collapsible-content {
  border-bottom: 1px solid #cccccc;
  padding: 0 10px 10px 10px;
}
.mgz__tooltip {
  display: inline-block;
  margin: 0 0 0 3px;
  position: relative;
}
.mgz__tooltip:hover .mgz__tooltip-content {
  display: block;
}
.mgz__tooltip .mgz__tooltip-help {
  margin: 0;
  position: relative;
  width: auto;
}
.mgz__tooltip .mgz__tooltip-help span {
  padding-right: 10px;
}
.mgz__tooltip .mgz__tooltip-help span:before {
  -webkit-font-smoothing: antialiased;
  font-family: 'Admin Icons';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  speak: none;
  color: #41362f;
  content: '\e633';
  font-size: 1.7rem;
}
.mgz__tooltip .mgz__tooltip-content {
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  background: #31302b;
  bottom: 100%;
  line-height: 1.5;
  padding: 18px;
  right: 0;
  width: 300px;
  color: #FFF;
  display: none;
  position: absolute;
  text-shadow: none;
  z-index: 20;
  white-space: initial;
  font-weight: 400;
  left: -15px;
}
.mgz__tooltip .mgz__tooltip-content:before {
  border-bottom: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #31302b;
  content: '';
  height: 0;
  position: absolute;
  top: auto;
  width: 0;
  bottom: -5px;
}
.mgz__tooltip.tooltip-top-left .mgz__tooltip-content:before,
.mgz__tooltip.tooltip-top-right .mgz__tooltip-content:before {
  border-bottom: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #31302b;
  content: '';
  height: 0;
  position: absolute;
  top: auto;
  width: 0;
  bottom: -5px;
}
.mgz__tooltip.tooltip-top-left .mgz__tooltip-content {
  left: auto;
}
.mgz__tooltip.tooltip-top-left .mgz__tooltip-content:before {
  right: 15px;
}
.mgz__tooltip.tooltip-top-right .mgz__tooltip-content {
  right: -255px;
}
.mgz__tooltip.tooltip-bottom-left .mgz__tooltip-content,
.mgz__tooltip.tooltip-bottom-right .mgz__tooltip-content {
  margin: 0;
  top: 30px;
  bottom: auto;
}
.mgz__tooltip.tooltip-bottom-left .mgz__tooltip-content:before,
.mgz__tooltip.tooltip-bottom-right .mgz__tooltip-content:before {
  border-top: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid #31302b;
  content: '';
  height: 0;
  position: absolute;
  top: -5px;
  width: 0;
}
.mgz__tooltip.tooltip-bottom-left .mgz__tooltip-content {
  left: auto;
}
.mgz__tooltip.tooltip-bottom-left .mgz__tooltip-content:before {
  left: auto;
  right: 15px;
}
.mgz__tooltip.tooltip-bottom-right .mgz__tooltip-content {
  right: -255px;
}
div.formly-field {
  margin-bottom: 1.5rem;
}
.mgz__field-note {
  font-size: 1.2rem;
  margin: 10px 0 0;
  padding: 0;
  color: #4d4d4d;
  display: block;
  line-height: 20px;
  margin-top: 8px;
  clear: both;
}
.mgz__field-group-columns {
  table-layout: fixed;
  display: table;
  width: 100%;
}
.mgz__field-group-columns > .mgz__field {
  display: table-cell;
  vertical-align: top;
  width: 100%;
}
.mgz__field-group-columns > .mgz__field:nth-child(n+2) {
  padding-left: 20px;
}
.mgz__field-control {
  position: relative;
}
.mgz__field-control:after {
  content: " ";
  display: table;
  clear: both;
}
.mgz__field-control .mgz__control-text,
.mgz__field-control .mgz__control-textarea {
  width: 100%;
}
.mgz__field-control .ng-touched.ng-invalid {
  border-color: #e22626;
}
.mgz__field-control-inline > .mgz__field-label {
  width: calc(100% * 0.25 - 30px);
  float: left;
}
.mgz__field-control-inline > .mgz__field-control {
  width: calc(100% * 0.75 - 30px);
  float: left;
}
.mgz__field-control-auto-width > .mgz__field-label {
  width: auto;
  float: left;
  margin-right: 15px;
}
.mgz__field-control-auto-width > .mgz__field-control {
  width: auto;
  float: left;
}
.required > .formly-field > .mgz__field-label > span:after,
._required > .formly-field > .mgz__field-label > span:after,
.required > .mgz__field-label > span:after,
._required > .mgz__field-label > span:after {
  color: #e22626;
  content: '*';
  display: inline-block;
  font-size: 1.6rem;
  font-weight: 500;
  line-height: 1;
  margin-left: 5px;
  margin-top: 0.2rem;
  z-index: 1;
}
.mgz__control-select {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  background-image: url(../images/arrows-bg.svg), linear-gradient(#e3e3e3, #e3e3e3), linear-gradient(#adadad, #adadad);
  background-position: calc(100% - 12px) -34px, 100%, calc(100% - 3.2rem) 0;
  background-size: auto, 3.2rem 100%, 1px 100%;
  background-repeat: no-repeat;
  max-width: 100%;
  padding-bottom: 0.5rem;
  padding-right: 4rem;
  padding-top: 0.5rem;
  transition: border-color 0.1s linear;
}
.mgz__control-select:hover {
  cursor: pointer;
}
.mgz__control-select:focus {
  background-image: url(../images/arrows-bg.svg), linear-gradient(#e3e3e3, #e3e3e3), linear-gradient(#007bdb, #007bdb);
  background-position: calc(100% - 12px) 13px, 100%, calc(100% - 3.2rem) 0;
  border-color: #007bdb;
}
.mgz__control-select::-ms-expand {
  display: none;
}
.mgz__control-uiselect {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  min-width: 4rem;
  padding: 0;
  width: 100%;
  position: relative;
  z-index: 11;
}
.mgz__control-uiselect .ui-select-placeholder {
  -webkit-opacity: 0.8;
  -moz-opacity: 0.8;
  opacity: 0.8;
}
.mgz__control-uiselect:not(.ui-select-multiple) {
  border: 0;
}
.mgz__control-uiselect input {
  width: 100%;
}
.mgz__control-uiselect .ui-select-choices {
  -webkit-border-radius: 1px;
  -moz-border-radius: 1px;
  border-radius: 1px;
  -webkit-box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.5);
  -moz-box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.5);
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.5);
  background-color: #FFF;
  border: 1px solid #007bdb;
  color: #41362f;
  font-weight: 400;
  left: 0;
  list-style: none;
  margin: 2px 0 0;
  min-width: 0;
  padding: 0;
  right: 0;
  top: 100%;
}
.mgz__control-uiselect.ui-select-bootstrap .ui-select-choices-row.active > span {
  background-color: #e0f6fe;
  color: #41362f;
}
.mgz__control-uiselect.ui-select-bootstrap .ui-select-choices-row > span {
  padding: 10px;
}
.mgz__control-uiselect .form-control {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  width: 100% !important;
}
.mgz__control-uiselect .btn-default:hover,
.mgz__control-uiselect .btn-default:focus {
  background-color: transparent;
  border-color: #adadad;
}
.mgz__control-uiselect:focus {
  outline: none;
}
.mgz__control-uiselect .ui-select-choices-group > .ui-select-choices-row > .ui-select-choices-row-inner {
  padding-left: 20px;
}
.mgz__control-uiselect .ui-select-choices-group-label {
  color: #41362f;
  font-size: 14px;
  font-weight: bold;
  padding: 10px;
}
.mgz__control-uiselect .dropdown-menu .divider {
  margin: 0;
}
.mgz__control-uiselect .ui-select-match-item {
  font-size: 16px;
  margin-right: 5px;
}
.mgz__control-uiselect.ui-select-multiple {
  min-height: 33px;
  padding: 0;
  width: 100%;
  position: relative;
}
.mgz__control-uiselect.ui-select-multiple:not(.ng-not-empty) {
  position: relative;
}
.mgz__control-uiselect.ui-select-multiple.open:before {
  display: none;
}
.mgz__control-uiselect.ui-select-multiple .ui-select-match > span {
  -webkit-border-radius: 1px;
  -moz-border-radius: 1px;
  border-radius: 1px;
  background-color: #f5f5f5;
  border: 1px solid #a79d95;
  display: inline-block;
  font-size: 1.2rem;
  margin: 0.3rem 0 0.3rem 0.3rem;
  padding: 0.3rem 2.2rem 0.4rem 1rem;
  position: relative;
  transition: border-color 0.1s linear;
  cursor: pointer;
  width: auto;
}
.mgz__control-uiselect.ui-select-multiple .ui-select-match > span:hover {
  border-color: #908379;
}
.mgz__control-uiselect.ui-select-multiple .ui-select-match .ui-select-match-item {
  position: static;
  font-size: 1.2rem;
}
.mgz__control-uiselect.ui-select-multiple .ui-select-match .close {
  cursor: pointer;
  float: right;
  color: #736963;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;
  bottom: 0;
  font-size: 16px;
  width: 2rem;
  line-height: 23px;
}
.mgz__control-uiselect.ui-select-multiple .ui-select-match .close:hover {
  color: #060504;
}
.mgz__control-uiselect.ui-select-multiple:not(.ng-not-empty),
.mgz__control-uiselect .ui-select-toggle {
  cursor: pointer;
}
.mgz__control-uiselect.ui-select-multiple:not(.ng-not-empty):before,
.mgz__control-uiselect .ui-select-toggle:before {
  position: absolute;
  content: "\e5c5";
  top: 0;
  right: 0;
  font-size: 30px;
  color: #524a42;
}
.mgz__control-uiselect.ui-select-multiple:not(.ng-not-empty):hover:before,
.mgz__control-uiselect .ui-select-toggle:hover:before {
  color: #333;
}
.mgz__control-uiselect .ui-select-match-close:hover {
  color: #e22626;
}
.mgz__control-radio,
.mgz__control-checkbox {
  cursor: pointer;
  opacity: 0.01;
  overflow: hidden;
  position: absolute;
  vertical-align: top;
}
.mgz__control-radio:after,
.mgz__control-checkbox:after {
  display: none;
}
.mgz__control-radio + label,
.mgz__control-checkbox + label {
  cursor: pointer;
  display: inline-block;
}
.mgz__control-radio + label:before,
.mgz__control-checkbox + label:before {
  background-color: #FFF;
  border: 1px solid #adadad;
  color: transparent;
  float: left;
  height: 1.6rem;
  text-align: center;
  vertical-align: top;
  width: 1.6rem;
}
.mgz__control-radio + .mgz__field-label,
.mgz__control-checkbox + .mgz__field-label {
  padding-left: 2.6rem;
}
.mgz__control-radio + .mgz__field-label:before,
.mgz__control-checkbox + .mgz__field-label:before {
  margin: 1px 1rem 0 -2.6rem;
}
.mgz__control-radio:checked + label:before,
.mgz__control-checkbox:checked + label:before {
  color: #514943;
}
.mgz__control-radio.disabled + label,
.mgz__control-checkbox.disabled + label,
.mgz__control-radio[disabled] + label,
.mgz__control-checkbox[disabled] + label {
  color: #303030;
  cursor: default;
  opacity: 0.5;
}
.mgz__control-radio.disabled + label:before,
.mgz__control-checkbox.disabled + label:before,
.mgz__control-radio[disabled] + label:before,
.mgz__control-checkbox[disabled] + label:before {
  background-color: #e9e9e9;
  border-color: #adadad;
  cursor: default;
}
._keyfocus .mgz__control-radio:not([disabled]):focus + label:before,
._keyfocus .mgz__control-checkbox:not([disabled]):focus + label:before,
._keyfocus .mgz__control-radio:not(.disabled):focus + label:before,
._keyfocus .mgz__control-checkbox:not(.disabled):focus + label:before {
  border-color: #007bdb;
}
.mgz__control-radio:not([disabled]):hover + label:before,
.mgz__control-checkbox:not([disabled]):hover + label:before,
.mgz__control-radio:not(.disabled):hover + label:before,
.mgz__control-checkbox:not(.disabled):hover + label:before {
  border-color: #878787;
}
.mgz__control-radio + label:before {
  border-radius: 1.6rem;
  content: '';
  transition: border-color 0.1s linear, color 0.1s ease-in;
}
.mgz__control-radio.mgz__control-radio + label:before {
  line-height: 140%;
}
.mgz__control-radio:checked + label {
  position: relative;
}
.mgz__control-radio:checked + label:after {
  background-color: #514943;
  border-radius: 50%;
  content: '';
  height: 10px;
  left: 3px;
  position: absolute;
  top: 4px;
  width: 10px;
}
.mgz__control-radio:checked:not([disabled]):hover,
.mgz__control-radio:checked:not(.disabled):hover {
  cursor: default;
}
.mgz__control-radio:checked:not([disabled]):hover + label,
.mgz__control-radio:checked:not(.disabled):hover + label {
  cursor: default;
}
.mgz__control-radio:checked:not([disabled]):hover + label:before,
.mgz__control-radio:checked:not(.disabled):hover + label:before {
  border-color: #adadad;
}
.mgz__control-checkbox + label:before {
  border-radius: 1px;
  content: '';
  font-size: 0;
  transition: font-size 0.1s ease-out, color 0.1s ease-out, border-color 0.1s linear;
}
.mgz__control-checkbox:checked + label:before {
  content: '\e62d';
  font-size: 1.1rem;
  line-height: 125%;
}
.mgz__control-checkbox.mgz__control-checkbox,
.mgz__control-radio.mgz__control-checkbox {
  margin: 0;
  position: absolute;
}
.mgz-radio,
.mgz-checkbox {
  position: relative;
  display: block;
  margin-top: 10px;
  margin-bottom: 10px;
}
.mgz-radio label,
.mgz-checkbox label {
  min-height: 20px;
  padding-left: 20px;
  margin-bottom: 0;
  font-weight: normal;
  cursor: pointer;
}
.mgz-radio-inline,
.mgz-checkbox-inline {
  position: relative;
  display: inline-block;
  margin-bottom: 0;
  font-weight: normal;
  vertical-align: middle;
  cursor: pointer;
}
.mgz-radio-inline input[type="radio"],
.mgz-checkbox-inline input[type="radio"],
.mgz-radio-inline input[type="checkbox"],
.mgz-checkbox-inline input[type="checkbox"] {
  position: absolute;
  margin-top: 4px \9;
  margin-left: -20px;
}
.mgz-radio-inline > .mgz__field-label,
.mgz-checkbox-inline > .mgz__field-label {
  line-height: inherit;
}
.mgz-radio-inline + .mgz-radio-inline,
.mgz-checkbox-inline + .mgz-checkbox-inline {
  margin-top: 0;
  margin-left: 10px;
}
.mgz__actions-switch .mgz__actions-switch-text:before {
  content: attr(data-text-off);
  padding-left: 47px;
  white-space: nowrap;
  font-weight: 400;
}
.mgz__actions-switch .mgz__actions-switch-checkbox:checked + .mgz__actions-switch-label:before {
  left: 15px;
}
.mgz__actions-switch .mgz__actions-switch-checkbox:checked + .mgz__actions-switch-label:after {
  background: #79a22e;
}
.mgz__actions-switch .mgz__actions-switch-checkbox:checked + .mgz__actions-switch-label .mgz__actions-switch-text:before {
  content: attr(data-text-on);
}
.mgz__actions-switch .mgz__actions-switch-checkbox:focus + .mgz__actions-switch-label:before,
.mgz__actions-switch .mgz__actions-switch-checkbox:focus + .mgz__actions-switch-label:after {
  border-color: #007bdb;
}
.mgz__actions-switch .mgz__actions-switch-checkbox[disabled] + .mgz__actions-switch-label {
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}
.mgz__actions-switch .mgz__actions-switch-label {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  cursor: pointer;
  display: inline-block;
  height: 22px;
  line-height: 22px;
  position: relative;
  user-select: none;
  vertical-align: middle;
}
.mgz__actions-switch .mgz__actions-switch-label:before,
.mgz__actions-switch .mgz__actions-switch-label:after {
  left: 0;
  position: absolute;
  right: auto;
  top: 0;
}
.mgz__actions-switch .mgz__actions-switch-label:before {
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
  -webkit-transition: left 0.2s ease-in 0s;
  -moz-transition: left 0.2s ease-in 0s;
  -ms-transition: left 0.2s ease-in 0s;
  -o-transition: left 0.2s ease-in 0s;
  background: #ffffff;
  border: 1px solid #aaa6a0;
  content: '';
  display: block;
  height: 22px;
  width: 22px;
  z-index: 1;
}
.mgz__actions-switch .mgz__actions-switch-label:after {
  -webkit-transition: background 0.2s ease-in 0s;
  -moz-transition: background 0.2s ease-in 0s;
  -ms-transition: background 0.2s ease-in 0s;
  -o-transition: background 0.2s ease-in 0s;
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  border-radius: 12px;
  background: #e3e3e3;
  border: 1px solid #aaa6a0;
  content: '';
  display: block;
  height: 22px;
  width: 37px;
  z-index: 0;
}
._disabled .mgz__actions-switch-checkbox + .mgz__actions-switch-label,
.mgz__actions-switch-checkbox.disabled + .mgz__actions-switch-label {
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}
.mgz_field-type-icon {
  vertical-align: top;
  min-width: 250px;
  position: relative;
}
.mgz_field-type-icon .mgz__control-uiselect {
  border: 0;
}
.mgz_field-type-icon .mgz-builder-icon-left {
  margin-right: 10px;
}
.mgz_field-type-icon .mgz-builder-icon-left,
.mgz_field-type-icon .mgz-builder-icon-right {
  float: left;
}
.mgz_field-type-icon .mgz-builder-icon-right select {
  height: 34px;
}
.mgz_field-type-icon .mgz__field-control {
  position: relative;
}
.mgz_field-type-icon .mgz-icon-list {
  -webkit-box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.5);
  -moz-box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.5);
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.5);
  position: absolute;
  z-index: 9999;
  background: #FFF;
  width: 100%;
  border: 1px solid #007bdb;
  padding: 10px;
  min-width: 610px;
}
.mgz_field-type-icon .mgz-icon-list:before,
.mgz_field-type-icon .mgz-icon-list:after {
  content: " ";
  display: table;
  clear: both;
}
.mgz_field-type-icon .mgz-icon-list .mgz__control-uiselect .ui-select-choices-row {
  -webkit-transition: background-color 0.1s linear;
  -moz-transition: background-color 0.1s linear;
  -ms-transition: background-color 0.1s linear;
  -o-transition: background-color 0.1s linear;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  border-radius: 10px;
  float: left;
  list-style: none;
  width: 65px;
  height: 65px;
  text-align: center;
  background: #e3e3e3;
  margin-bottom: 8px;
  margin-right: 8px;
  cursor: pointer;
  font-size: 32px;
  border: 3px solid transparent;
  position: relative;
  overflow: hidden;
  top: 0 !important;
}
.mgz_field-type-icon .mgz-icon-list .mgz__control-uiselect .ui-select-choices-row i {
  padding: 0;
  margin: 0;
  transition: unset;
  background: 0 0;
  font-size: 32px;
  width: 65px;
  height: 65px;
}
.mgz_field-type-icon .mgz-icon-list .mgz__control-uiselect .ui-select-choices-row i:before {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
}
.mgz_field-type-icon .mgz-icon-list .mgz__control-uiselect .ui-select-choices-row > span {
  padding: 0;
}
.mgz_field-type-icon .mgz-icon-list .mgz__control-uiselect .ui-select-choices-row > span:hover {
  background: transparent;
}
.mgz_field-type-icon .mgz-icon-list .mgz__control-uiselect .ui-select-choices-row.active,
.mgz_field-type-icon .mgz-icon-list .mgz__control-uiselect .ui-select-choices-row:hover {
  border-color: #007dbd;
  background: #fff;
}
.mgz_field-type-icon .mgz-icon-list .mgz__control-uiselect .ui-select-choices-row.active > span {
  background: transparent;
}
.mgz_field-type-icon .mgz-icon-list .mgz__control-uiselect .ui-select-choices-content {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  border: 0;
  position: static;
}
.mgz_field-type-icon .ui-select-choices-group {
  padding: 8px 0;
}
.mgz_field-type-icon .ui-select-bootstrap > .ui-select-choices,
.mgz_field-type-icon .ui-select-bootstrap > .ui-select-no-choice {
  max-height: 35rem;
}
.mgz_field-type-icon .mgz-icon-toolbar {
  display: inline-block;
  border: 1px solid #adadad;
  background: #FFF;
  width: 102px;
}
.mgz_field-type-icon .mgz-icon-toolbar:before,
.mgz_field-type-icon .mgz-icon-toolbar:after {
  content: " ";
  display: table;
  clear: both;
}
.mgz_field-type-icon .mgz-selected-icon {
  display: block;
  width: 60px;
  float: left;
  text-align: center;
  line-height: 32px;
  font-size: 24px;
  position: relative;
  z-index: 1;
}
.mgz_field-type-icon .mgz-selector-button {
  float: right;
  line-height: 32px;
  width: 39px;
  height: 100%;
  display: block;
  text-align: center;
  cursor: pointer;
  color: #999;
  background: #f4f4f4;
  border-left: 1px solid #cccccc;
}
.mgz_field-type-icon .mgz-selector-button:hover {
  background-color: #f1f1f1;
}
.linkbuilder-label {
  font-weight: 700;
  margin-left: 10px;
  display: inline-block;
}
.linkbuilder-input {
  border: 0;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}
.mgz_field-type-color .sp-replacer {
  float: left;
  margin-right: 2px;
}
.mgz_field-type-color .mgz-colorpicker-input {
  width: calc(100% - 35px) !important;
}
.mgz-spectrum .sp-choose {
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  margin: 0;
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  background-image: none;
  word-wrap: break-word;
  text-decoration: none;
  position: relative;
  line-height: normal;
  padding: 10px 20px;
  color: #333;
  background-color: #e3e3e3;
  font-size: 1.4rem;
  max-width: 100%;
  height: auto;
  -webkit-transition: none;
  -moz-transition: none;
  -ms-transition: none;
  -o-transition: none;
}
.mgz-spectrum .sp-choose:hover {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  color: #5e5e5e;
  background-color: #dcdcdc;
  text-decoration: none;
}
.mgz-spectrum .sp-choose:focus {
  outline: none;
}
.mgz-spectrum .sp-choose.mgz-btn-save,
.mgz-spectrum .sp-choose.mgz-btn-cancel,
.mgz-spectrum .sp-choose.mgz-btn-replace {
  padding: 15px 20px;
  font-size: 1.6rem;
  font-weight: 500;
  min-width: 140px;
}
.mgz-spectrum .sp-choose.mgz-btn-save {
  background: #007dbd;
  color: #FFF;
}
.mgz-spectrum .sp-choose.mgz-btn-save:hover {
  background: #0073ae;
}
.mgz-spectrum .sp-choose.mgz-btn-cancel {
  color: #fff;
  background-color: #afafaf;
}
.mgz-spectrum .sp-choose.mgz-btn-cancel:hover {
  background-color: #8c8c8c;
}
.mgz-spectrum .sp-choose.mgz-btn-replace {
  float: left;
  color: #fff;
  background-color: #afafaf;
}
.mgz-spectrum .sp-choose.mgz-btn-replace:hover {
  background-color: #8c8c8c;
}
.mgz-spectrum .sp-choose.mgz-btn-delete {
  color: #FFF;
  background-color: #e22626;
}
.mgz-spectrum .sp-choose.mgz-btn-delete:hover {
  background-color: #ca1c1c;
}
.mgz-spectrum .sp-choose:hover {
  color: #FFF;
  background: #007dbd !important;
  border-color: #007dbd !important;
}
.mgz-spectrum .sp-cancel {
  color: #007dbd !important;
}
.mgz-builder-editor .mce-branding-powered-by {
  display: none;
}
.mgz-editor-simple .mce-flow-layout {
  height: 30px;
  overflow: hidden;
}
.mgz-builder .mgz-navbar {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  background-color: #007dbd;
  min-height: 55px;
  margin: 0;
  border: 0;
}
.mgz-builder .mgz-navbar .mgz-spinner {
  background: #007dbd;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.mgz-builder .mgz-navbar .mgz-spinner i {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
  border-color: #FFF;
  border-top-color: transparent;
  top: 30%;
  left: 30%;
}
.mgz-builder .mgz-navbar a {
  text-decoration: none;
}
.mgz-builder .mgz-navbar .mgz-navbar-nav {
  display: block;
  margin: 0;
}
.mgz-builder .mgz-navbar .mgz-navbar-nav > li {
  float: left;
  margin: 0;
  height: 55px;
  position: relative;
}
.mgz-builder .mgz-navbar .mgz-navbar-nav > li .mgz-navbar-icon,
.mgz-builder .mgz-navbar .mgz-navbar-nav > li .mgz-builder-icon {
  font-size: 22px;
  line-height: 55px;
}
.mgz-builder .mgz-navbar .mgz-navbar-nav > li .mgz-icon-code {
  font-weight: bold;
}
.mgz-builder .mgz-navbar li:hover > a,
.mgz-builder .mgz-navbar li:hover > div > a {
  background-color: #0073ae;
}
.mgz-builder .mgz-navbar .mgz-navbar-btn {
  display: block;
  line-height: 55px;
  height: 55px;
  width: 60px;
  color: #FFF;
  text-align: center;
  font-size: 22px;
}
.mgz-builder .mgz-navbar .mgz-navbar-btn[disabled] {
  -webkit-opacity: 0.2;
  -moz-opacity: 0.2;
  opacity: 0.2;
}
.mgz-builder .mgz-navbar .mgz-icon-cms {
  font-size: 28px;
  line-height: 55px;
}
.mgz-view-mode.mgz-dropdown:hover .mgz-icon {
  -webkit-opacity: 0.8;
  -moz-opacity: 0.8;
  opacity: 0.8;
}
.mgz-view-mode.mgz-dropdown .mgz-icon {
  -webkit-opacity: 0.4;
  -moz-opacity: 0.4;
  opacity: 0.4;
  margin-left: -8px;
}
.mgz-view-mode.mgz-dropdown .mgz-view-mode-wrapper > a i.mgz-icon {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.mgz-view-mode.mgz-dropdown .mgz-view-mode-wrapper .mgz-icon-arrow_drop_down {
  margin-right: -15px;
}
.mgz-view-mode li.active a i,
.mgz-view-mode li:hover a i {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.mgz-view-mode li:hover > a {
  background-color: #005780 !important;
  color: #FFF;
}
.dndDraggingSource {
  display: none !important;
}
.mgz-placeholder {
  display: block;
  background: transparent url(../images/pattern.gif);
  min-height: 36px;
}
.mgz-builder .mgz-element {
  min-height: 36px;
}
.mgz-builder .mgz-element.mgz-element-actived {
  outline: 2px solid #95ca24;
}
.mgz-builder .mgz-element-profile {
  float: left;
  width: 100%;
}
.mgz-dndDragover {
  outline: 1px dashed rgba(125, 125, 125, 0.4) !important;
}
.mgz-draging .mgz-element-collection:hover {
  outline: none;
}
.mgz-draging .mgz-element-controls {
  display: none !important;
}
.mgz-builder .mgz-inline-editor {
  line-height: inherit;
}
.mgz-builder .mgz-inline-editor .magento-placeholder-error img,
.mgz-builder .mgz-inline-editor .magento-placeholder img {
  vertical-align: middle;
  max-height: 20px;
  margin-right: 5px;
}
.mgz-builder .mgz-inline-editor .magento-placeholder {
  -webkit-box-sizing: initial;
  -moz-box-sizing: initial;
  box-sizing: initial;
  display: inline-block;
  margin: 5px;
  background: #dff7ff;
  outline: 2px solid #c0dffa;
  padding: 2px 4px;
  vertical-align: bottom;
  cursor: pointer !important;
  font-weight: normal;
  font-style: normal;
  font-size: 14px;
  color: #000;
  word-break: break-all;
}
.mgz-builder .mgz-inline-editor .magento-placeholder[data-mce-selected] {
  outline: 2px solid #2d8ac7 !important;
}
.mgz-builder .mgz-inline-editor .magento-placeholder-error {
  -webkit-box-sizing: initial;
  -moz-box-sizing: initial;
  box-sizing: initial;
  display: inline-block;
  margin: 5px;
  background: #dff7ff;
  outline: 2px solid #c0dffa;
  padding: 2px 4px;
  vertical-align: bottom;
  cursor: pointer !important;
  font-weight: normal;
  font-style: normal;
  font-size: 14px;
  color: #000;
  word-break: break-all;
}
.mgz-builder .mgz-inline-editor .magento-placeholder-error img {
  vertical-align: middle;
  max-height: 20px;
  margin-right: 5px;
}
.mgz-builder .mgz-inline-editor .magento-placeholder-error[data-mce-selected] {
  outline: 2px solid #2d8ac7 !important;
}
.mgz-builder .mgz-inline-editor .magento-placeholder-error:before {
  content: '';
  display: inline-block;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABWUlEQVR42qVTS07DMBD1pggqcQAQEofINbrNvh977Au04gJtxZYiVZwLJJasAhJJ3LBrI4Xwxm3SKErCz9Koqec9e/xmnhBYdjK5tlo/R8ZsI63XIdGFqK1QSg+5MNY6scbMXobDU5eIpbyKiaZIpLExOX4/cdg6IOoXZP6OlbpDPjtgUks0y4l6An8esbl1iUPghgyA5ZPvnzAo0XphC/IRkyZKKVEnl8GVEK04+LsRY8yb4HJbAVpnLprJmVVqIcLx+DIieqiX2Bl8ITQB93wv0mDQb3pnGxkXLqsiu8WCufd2H5KBfM/Yepv/d0Du+2c/fQJjIilvyyfwhLEgvxHR7itZORFjHs82clcbuRNazwXm/uOvg4RKAoFSbkofVMjJN6MMzG5DNBI5XAVDTeG0XaE0a9JpJviADejM5LrgeT1UMsJmAOC8nLCqnbG3QQ6Y13ciWbTyC+DUOUqNY1A2AAAAAElFTkSuQmCC);
  background-repeat: no-repeat;
  background-position-x: 1px;
  background-position-y: 1px;
  width: 20px;
  height: inherit;
  margin-right: 4px;
  vertical-align: middle;
}
.mgz-builder .mgz-inline-editor .magento-placeholder-error img {
  display: none;
}
.mgz-builder .mgz-inline-editor .magento-placeholder-error[data-mce-selected] {
  outline: 2px solid #E12525 !important;
}
.mgz-builder .mgz-inline-editor ~ .mce-tinymce-inline {
  -webkit-transition: transform 200ms, opacity 200ms, visibility 200ms;
  -moz-transition: transform 200ms, opacity 200ms, visibility 200ms;
  -ms-transition: transform 200ms, opacity 200ms, visibility 200ms;
  -o-transition: transform 200ms, opacity 200ms, visibility 200ms;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  display: block !important;
  height: auto !important;
  pointer-events: none;
  position: absolute;
  visibility: hidden;
  max-width: 100%;
  z-index: 99;
}
.mgz-builder .mgz-inline-editor ~ .mce-tinymce-inline .mce-container,
.mgz-builder .mgz-inline-editor ~ .mce-tinymce-inline .mce-container-body {
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  height: auto !important;
  width: auto !important;
}
.mgz-builder .mgz-inline-editor ~ .mce-tinymce-inline .mce-abs-layout-item {
  position: static;
}
.mgz-builder .mgz-inline-editor.mce-edit-focus {
  -webkit-box-shadow: 0 0 0 1px #999;
  -moz-box-shadow: 0 0 0 1px #999;
  box-shadow: 0 0 0 1px #999;
  outline: none;
}
.mgz-builder .mgz-inline-editor.mce-edit-focus ~ .mce-tinymce-inline {
  -webkit-transform: translateY(-100%);
  -moz-transform: translateY(-100%);
  -ms-transform: translateY(-100%);
  -o-transform: translateY(-100%);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  pointer-events: all;
  visibility: visible !important;
}
.mgz-builder .mgz-inline-editor._right-aligned-toolbar ~ div.mce-tinymce-inline {
  left: auto !important;
}
.mgz-builder .mce-panel {
  background-color: #f0f0f0;
  border-width: 0px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.2);
  border-image: initial;
}
.mgz-builder .mce-btn {
  background-color: #f0f0f0;
}
.mgz-builder .mce-btn.mce-active,
.mgz-builder .mce-btn.mce-active:hover {
  background-color: #dbdbdb !important;
  border-color: #ccc !important;
}
.mgz-builder .mce-btn.mce-active button,
.mgz-builder .mce-btn.mce-active:hover button,
.mgz-builder .mce-btn.mce-active i,
.mgz-builder .mce-btn.mce-active:hover i {
  color: #333;
}
.mgz-builder .mgz-element-toolbar {
  -webkit-transition: transform 200ms, opacity 200ms, visibility 200ms;
  -moz-transition: transform 200ms, opacity 200ms, visibility 200ms;
  -ms-transition: transform 200ms, opacity 200ms, visibility 200ms;
  -o-transition: transform 200ms, opacity 200ms, visibility 200ms;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  background-color: #f0f0f0;
  border: 1px solid #bfbfbf;
  left: -2px;
  position: absolute;
  visibility: hidden;
  width: calc(100% + 4px);
  z-index: 100;
}
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-inner {
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-options {
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-options > a {
  text-transform: capitalize;
  border: 1px solid transparent;
  color: #373330;
  display: block;
  height: 32px;
  line-height: 32px;
  text-align: center;
  text-decoration: none;
  width: 34px;
  font-size: 15px;
  font-weight: 600;
  margin: 2px;
}
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-options > a:hover,
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-options > a.active {
  background-color: #ddd;
  border: 1px solid #ccc;
}
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-options + .mgz-element-toolbar-options:before {
  background-color: #ddd;
  content: '';
  height: 80%;
  left: 0;
  max-height: 32px;
  position: relative;
  top: 10%;
  width: 1px;
}
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-options > input {
  width: 50px;
  text-align: center;
}
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-options > input:focus {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  outline: none;
}
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-item-left:before {
  content: "\e965";
}
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-item-center:before {
  content: "\e964";
}
.mgz-builder .mgz-element-toolbar .mgz-element-toolbar-item-right:before {
  content: "\e966";
}
.mgz-builder .mgz-element-toolbar .sp-replacer {
  border: 0;
  padding-top: 5px;
}
.mgz-builder .mgz-element-editing {
  -webkit-box-shadow: 0 0 0 1px #999;
  -moz-box-shadow: 0 0 0 1px #999;
  box-shadow: 0 0 0 1px #999;
}
.mgz-builder .mgz-element-editing .mgz-element-toolbar {
  -webkit-transform: translateY(-100%);
  -moz-transform: translateY(-100%);
  -ms-transform: translateY(-100%);
  -o-transform: translateY(-100%);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  pointer-events: all;
  visibility: visible !important;
}
.mgz-builder .mgz-element-disabled,
.mgz-builder .mgz-element-hide-default {
  -webkit-opacity: 0.6;
  -moz-opacity: 0.6;
  opacity: 0.6;
}
.mgz-builder .mgz-element-disabled .mgz-element-controls,
.mgz-builder .mgz-element-hide-default .mgz-element-controls {
  background-color: #ccc !important;
  border-color: #ccc !important;
}
.mgz-builder .mgz-element-disabled .mgz-element-controls .mgz-control-btn:hover,
.mgz-builder .mgz-element-hide-default .mgz-element-controls .mgz-control-btn:hover {
  background-color: #b3b3b3 !important;
  border-color: #b3b3b3 !important;
}
.mgz-builder .mgz-element .mgz-spectrum {
  width: 378px;
}
.mgz-builder .mgz-element .mgz-contenteditable-element:focus {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.mgz-builder .mgz-element-empty .mgz-element-inner {
  min-height: 100px;
  height: 100%;
}
.mgz-builder .mgz-element-empty > .mgz-icon {
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  font-size: 20px;
  color: #FFF;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  height: 38px;
  width: 38px;
  line-height: 38px;
  text-align: center;
  position: absolute;
  background-color: #c9c9c9;
  cursor: pointer;
  z-index: 1;
}
.mgz-builder .mgz-element-empty > .mgz-icon:hover {
  background-color: #e4e4e4;
}
.mgz-element-controls {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  position: absolute;
  line-height: 36px;
  top: -36px;
  z-index: 99;
  white-space: nowrap;
  background: #2b4b80;
  color: #FFF;
}
.mgz-element-controls li {
  cursor: pointer;
  position: relative;
}
.mgz-element-controls li[disabled] > a {
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  cursor: not-allowed !important;
}
.mgz-element-controls a {
  color: #FFF;
  padding: 0 4px;
}
.mgz-element-controls a:hover {
  text-decoration: none;
}
.mgz-element-controls i {
  color: #FFF;
  font-size: 12px;
}
.mgz-element-controls i.mgz-icon-dragndrop {
  padding: 0 2px;
}
.mgz-element-controls i.mgz-icon-separator {
  width: 10px;
}
.mgz-element-controls .mgz-icon-separator {
  -webkit-border-radius: 0.6;
  -moz-border-radius: 0.6;
  border-radius: 0.6;
}
.mgz-element-controls .mgz-element-control {
  position: relative;
  float: left;
}
.mgz-element-controls .mgz-element-control:hover:first-child {
  -webkit-border-radius: 5px 0 0 0;
  -moz-border-radius: 5px 0 0 0;
  border-radius: 5px 0 0 0;
}
.mgz-element-controls .mgz-element-control:hover > a {
  background: #95ca24;
}
.mgz-element-controls .mgz-element-control:hover > .mgz-element-control-dropdown {
  display: block;
}
.mgz-element-controls .mgz-element-control:hover .mgz-element-control-top {
  -webkit-border-radius: 5px 5px 0 0;
  -moz-border-radius: 5px 5px 0 0;
  border-radius: 5px 5px 0 0;
}
.mgz-element-controls .mgz-element-control:first-child {
  -webkit-border-radius: 5px 0 0 5px;
  -moz-border-radius: 5px 0 0 5px;
  border-radius: 5px 0 0 5px;
}
.mgz-element-controls .mgz-element-control:last-child {
  -webkit-border-radius: 0 5px 5px 0;
  -moz-border-radius: 0 5px 5px 0;
  border-radius: 0 5px 5px 0;
  overflow: hidden;
}
.mgz-element-controls .mgz-element-control > a {
  display: inline-block;
  position: relative;
}
.mgz-element-controls .mgz-element-control.mgz-element-row-control:hover > a {
  background: #007dbd;
}
.mgz-element-controls .mgz-element-control.mgz-element-row-control .mgz-element-control-dropdown {
  background: #007dbd;
}
.mgz-element-controls .mgz-element-control.mgz-element-row-control .mgz-element-control-dropdown ul li:hover {
  background: #0073ae;
}
.mgz-element-controls .mgz-element-control.mgz-element-column-control:hover > a {
  background: #f3af1c;
}
.mgz-element-controls .mgz-element-control.mgz-element-column-control .mgz-element-control-dropdown {
  background: #f3af1c;
}
.mgz-element-controls .mgz-element-control.mgz-element-column-control .mgz-element-control-dropdown ul li:hover {
  background: #e9a30c;
}
.mgz-element-controls .mgz-element-control .mgz-element-control-dropdown {
  -webkit-border-radius: 0 0 5px 5px;
  -moz-border-radius: 0 0 5px 5px;
  border-radius: 0 0 5px 5px;
  display: none;
  position: absolute;
  background: #95ca24;
  width: 150px;
  left: 0;
}
.mgz-element-controls .mgz-element-control .mgz-element-control-dropdown ul li a {
  text-align: left;
  padding-left: 8px;
  padding-right: 16px;
  display: block;
}
.mgz-element-controls .mgz-element-control .mgz-element-control-dropdown ul li:hover {
  background: #85b420;
}
.mgz-element-controls .mgz-element-control .mgz-element-control-dropdown ul li:last-child {
  -webkit-border-radius: 0 0 5px 5px;
  -moz-border-radius: 0 0 5px 5px;
  border-radius: 0 0 5px 5px;
}
.mgz-element-controls .mgz-element-control .mgz-element-control-dropdown ul li i {
  margin-right: 10px;
}
.mgz-element-controls .mgz-element-control .mgz-element-control-dropdown ul li[disabled] {
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  cursor: not-allowed;
}
.mgz-element-controls .mgz-element-control .mgz-element-control-dropdown .mgz-control-inner .mgz-dropdown-list {
  position: absolute;
  display: none;
  left: 100%;
  top: 0;
  width: 290px;
  background: #007dbd;
}
.mgz-element-controls .mgz-element-control .mgz-element-control-dropdown .mgz-control-inner:hover .mgz-dropdown-list {
  display: block;
}
.mgz-element-controls .mgz-element-control .mgz-element-control-dropdown .mgz-control-layout > i {
  font-size: 10px;
  margin-right: 3px;
}
.mgz-element-controls .mgz-element-control.mgz-element-control-green > a {
  padding: 0 8px;
}
.mgz-element-controls .mgz-element-control .mgz-icon-code {
  font-weight: bold;
}
.mgz-element-controls .mgz-element-control-top {
  -webkit-flex: initial;
  -ms-flex: initial;
  flex: initial;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  line-height: 36px;
  top: -36px;
  z-index: 99;
  white-space: nowrap;
  background: #95ca24;
  left: 0;
  position: absolute;
}
.mgz-element-controls .mgz-element-control-top > li {
  display: inline-block;
}
.mgz-element-controls .mgz-element-control-top > li ul {
  border-right: 1px solid #85b420;
}
.mgz-element-controls .mgz-element-control-top > li:last-child ul {
  border-right: 0;
}
.mgz-element-controls .mgz-element-control-top li {
  display: inline-block;
}
.mgz-element-controls .mgz-element-control-top li:hover > a,
.mgz-element-controls .mgz-element-control-top li.actived > a {
  background: #85b420;
}
.mgz-element-controls .mgz-element-control-top a {
  display: block;
  padding: 0 10px;
}
.mgz-element-add-control {
  -webkit-transform: translate(-50%, 0);
  -moz-transform: translate(-50%, 0);
  -ms-transform: translate(-50%, 0);
  -o-transform: translate(-50%, 0);
  left: 50%;
  position: absolute;
  bottom: -20px;
  z-index: 1;
}
.mgz-element-add-control .mgz-element-add {
  color: #FFF;
}
.mgz-element-add-control .mgz-element-add:hover:before {
  border-bottom-color: #85b420;
}
.mgz-element-add-control .mgz-element-add:hover .mgz-btn-content {
  background: #85b420;
}
.mgz-element-add-control .mgz-element-add:before {
  position: relative;
  left: 7px;
  content: "";
  display: block;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid #95ca24;
}
.mgz-element-add-control .mgz-element-add .mgz-btn-content {
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  background: #95ca24;
  padding: 3px 4px;
}
.mgz-content-helper {
  -webkit-align-items: center;
  -ms-align-items: center;
  align-items: center;
  -webkit-justify-content: center;
  -ms-justify-content: center;
  justify-content: center;
  position: relative;
  height: 100%;
  border: 1px dashed rgba(125, 125, 125, 0.4);
  float: left;
  width: 100%;
}
.mgz-content-helper i {
  -webkit-transition: opacity 0.2s ease-in-out;
  -moz-transition: opacity 0.2s ease-in-out;
  -ms-transition: opacity 0.2s ease-in-out;
  -o-transition: opacity 0.2s ease-in-out;
  -webkit-opacity: 0.25;
  -moz-opacity: 0.25;
  opacity: 0.25;
  font-size: 26px;
  color: #000000;
  cursor: pointer;
  margin: 35px 0;
  flex: auto;
  max-width: 100px;
  text-align: center;
}
.mgz-content-helper i:hover {
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
}
.mgz-resize:before,
.mgz-resize:after {
  content: " ";
  display: table;
  clear: both;
}
.mgz-resizing > .mgz-column-resize {
  z-index: 10;
  display: block;
}
.mgz-resizing > .mgz-resize-wrapper {
  z-index: 11;
  border-color: #007dbd;
}
.mgz-resizing > .mgz-column-container > .mgz-column-controls {
  display: none !important;
}
.mgz-resizing > .mgz-resize-wrapper,
.mgz-element-collection:hover > .mgz-resize-wrapper {
  display: block;
}
.mgz-column-resize {
  display: none;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  font-size: 28px;
  font-weight: 500;
  text-align: center;
  background: rgba(224, 246, 254, 0.5);
  color: #007dbd;
}
.mgz-column-resize:before {
  content: "";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
.mgz-resize {
  display: block;
  position: absolute;
  cursor: col-resize;
  z-index: 9;
  width: 20px;
  right: -10px;
  top: 0;
  bottom: 0;
}
.mgz-resize:hover > span {
  border-color: #007dbd;
  background: #007dbd;
}
.mgz-resize span {
  -webkit-transform: translate(-50%, 0);
  -moz-transform: translate(-50%, 0);
  -ms-transform: translate(-50%, 0);
  -o-transform: translate(-50%, 0);
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  position: absolute;
  display: block;
  border-width: 0 1px;
  height: 100%;
  top: 0;
  margin: 0;
  right: -1px;
  width: 3px;
  left: 50%;
}
/***
Spectrum Colorpicker v1.8.0
https://github.com/bgrins/spectrum
Author: Brian Grinstead
License: MIT
***/
.colorpicker-spectrum {
  display: none;
}
.colorpicker-input {
  vertical-align: middle;
  width: 50% !important;
}
.mgz-spectrum.sp-container {
  position: absolute;
  margin-top: 2px;
  top: 0;
  left: 0;
  display: inline-block;
  *display: inline;
  *zoom: 1;
  /* https://github.com/bgrins/spectrum/issues/40 */
  z-index: 9999994;
  overflow: hidden;
}
.mgz-spectrum.sp-container.sp-flat {
  position: relative;
}
/* Fix for * { box-sizing: border-box; } */
.mgz-spectrum.sp-container,
.mgz-spectrum.sp-container * {
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}
/* http://ansciath.tumblr.com/post/**********/css-aspect-ratio */
.sp-top {
  position: relative;
  width: 100%;
  display: inline-block;
}
.sp-top-inner {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}
.sp-color {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 20%;
}
.sp-hue {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 84%;
  height: 100%;
}
.sp-clear-enabled .sp-hue {
  top: 33px;
  height: 77.5%;
}
.sp-fill {
  padding-top: 80%;
}
.sp-sat,
.sp-val {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.sp-alpha-enabled .sp-top {
  margin-bottom: 18px;
}
.sp-alpha-enabled .sp-alpha {
  display: block;
}
.sp-alpha-handle {
  position: absolute;
  top: -4px;
  bottom: -4px;
  width: 6px;
  left: 50%;
  cursor: pointer;
  border: 1px solid black;
  background: white;
  opacity: 0.8;
}
.sp-alpha {
  display: none;
  position: absolute;
  bottom: -20px;
  right: 0;
  left: 0;
  height: 14px;
}
.sp-alpha-inner {
  border: solid 1px #333;
}
.sp-clear {
  display: none;
}
.sp-clear.sp-clear-display {
  background-position: center;
}
.sp-clear-enabled .sp-clear {
  display: block;
  position: absolute;
  top: 0px;
  right: 0;
  bottom: 0;
  left: 84%;
  height: 28px;
}
/* Don't allow text selection */
.mgz-spectrum.sp-container,
.sp-replacer,
.sp-preview,
.sp-dragger,
.sp-slider,
.sp-alpha,
.sp-clear,
.sp-alpha-handle,
.mgz-spectrum.sp-container.sp-dragging .sp-input,
.mgz-spectrum.sp-container button {
  -webkit-user-select: none;
  -moz-user-select: -moz-none;
  -o-user-select: none;
  user-select: none;
}
.mgz-spectrum.sp-container.sp-input-disabled .sp-input-container {
  display: none;
}
.mgz-spectrum.sp-container.sp-buttons-disabled .sp-button-container {
  display: none;
}
.mgz-spectrum.sp-container.sp-palette-buttons-disabled .sp-palette-button-container {
  display: none;
}
.sp-palette-only .sp-picker-container {
  display: none;
}
.sp-palette-disabled .sp-palette-container {
  display: none;
}
.sp-initial-disabled .sp-initial {
  display: none;
}
/* Gradients for hue, saturation and value instead of images.  Not pretty... but it works */
.sp-sat {
  background-image: -webkit-gradient(linear, 0 0, 100% 0, from(#FFF), to(rgba(204, 154, 129, 0)));
  background-image: -webkit-linear-gradient(left, #FFF, rgba(204, 154, 129, 0));
  background-image: -moz-linear-gradient(left, #fff, rgba(204, 154, 129, 0));
  background-image: -o-linear-gradient(left, #fff, rgba(204, 154, 129, 0));
  background-image: -ms-linear-gradient(left, #fff, rgba(204, 154, 129, 0));
  background-image: linear-gradient(to right, #fff, rgba(204, 154, 129, 0));
  -ms-filter: "progid:DXImageTransform.Microsoft.gradient(GradientType = 1, startColorstr=#FFFFFFFF, endColorstr=#00CC9A81)";
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=1, startColorstr='#FFFFFFFF', endColorstr='#00CC9A81');
}
.sp-val {
  background-image: -webkit-gradient(linear, 0 100%, 0 0, from(#000000), to(rgba(204, 154, 129, 0)));
  background-image: -webkit-linear-gradient(bottom, #000000, rgba(204, 154, 129, 0));
  background-image: -moz-linear-gradient(bottom, #000, rgba(204, 154, 129, 0));
  background-image: -o-linear-gradient(bottom, #000, rgba(204, 154, 129, 0));
  background-image: -ms-linear-gradient(bottom, #000, rgba(204, 154, 129, 0));
  background-image: linear-gradient(to top, #000, rgba(204, 154, 129, 0));
  -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#00CC9A81, endColorstr=#FF000000)";
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00CC9A81', endColorstr='#FF000000');
}
.sp-hue {
  background: -moz-linear-gradient(top, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
  background: -ms-linear-gradient(top, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
  background: -o-linear-gradient(top, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(#ff0000), color-stop(0.17, #ffff00), color-stop(0.33, #00ff00), color-stop(0.5, #00ffff), color-stop(0.67, #0000ff), color-stop(0.83, #ff00ff), to(#ff0000));
  background: -webkit-linear-gradient(top, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
  background: linear-gradient(to bottom, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
}
/* IE filters do not support multiple color stops.
   Generate 6 divs, line them up, and do two color gradients for each.
   Yes, really.
 */
.sp-1 {
  height: 17%;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff0000', endColorstr='#ffff00');
}
.sp-2 {
  height: 16%;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffff00', endColorstr='#00ff00');
}
.sp-3 {
  height: 17%;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00ff00', endColorstr='#00ffff');
}
.sp-4 {
  height: 17%;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00ffff', endColorstr='#0000ff');
}
.sp-5 {
  height: 16%;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0000ff', endColorstr='#ff00ff');
}
.sp-6 {
  height: 17%;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff00ff', endColorstr='#ff0000');
}
.sp-hidden {
  display: none !important;
}
/* Clearfix hack */
.sp-cf:before,
.sp-cf:after {
  content: "";
  display: table;
}
.sp-cf:after {
  clear: both;
}
.sp-cf {
  *zoom: 1;
}
/* Mobile devices, make hue slider bigger so it is easier to slide */
@media (max-device-width: 480px) {
  .sp-color {
    right: 40%;
  }
  .sp-hue {
    left: 63%;
  }
  .sp-fill {
    padding-top: 60%;
  }
}
.sp-dragger {
  border-radius: 5px;
  height: 5px;
  width: 5px;
  border: 1px solid #fff;
  background: #000;
  cursor: pointer;
  position: absolute;
  top: 0;
  left: 0;
}
.sp-slider {
  position: absolute;
  top: 0;
  cursor: pointer;
  height: 3px;
  left: -1px;
  right: -1px;
  border: 1px solid #000;
  background: white;
  opacity: 0.8;
}
/*
Theme authors:
Here are the basic themeable display options (colors, fonts, global widths).
See http://bgrins.github.io/spectrum/themes/ for instructions.
*/
.mgz-spectrum.sp-container {
  border-radius: 0;
  background-color: #ffffff;
  border: solid 1px #1979c3;
  padding: 0;
}
.mgz-spectrum.sp-container,
.mgz-spectrum.sp-container button,
.mgz-spectrum.sp-container input,
.sp-color,
.sp-hue,
.sp-clear {
  font: normal 12px "Lucida Grande", "Lucida Sans Unicode", "Lucida Sans", Geneva, Verdana, sans-serif;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}
.sp-top {
  margin-bottom: 3px;
}
.sp-color,
.sp-hue,
.sp-clear {
  border: solid 1px #666;
}
/* Input */
.sp-input-container {
  float: right;
  width: 100px;
  margin-bottom: 4px;
}
.sp-initial-disabled .sp-input-container {
  width: 100%;
}
.sp-input {
  font-size: 12px !important;
  border: 1px solid #adadad;
  padding: 4px 5px;
  margin: 5px 0px 0px 0px;
  width: 100%;
  background: transparent;
  border-radius: 3px;
  color: #222;
}
.sp-input:focus {
  border: 1px solid #1979c3;
}
.sp-input.sp-validation-error {
  border: 1px solid red;
  background: #fdd;
}
.sp-picker-container,
.sp-palette-container {
  float: left;
  position: relative;
  padding: 10px;
  padding-bottom: 300px;
  margin-bottom: -290px;
}
.sp-picker-container {
  width: 172px;
  border-left: solid 1px #fff;
}
/* Palettes */
.sp-palette-container {
  border-right: solid 1px #ccc;
}
.sp-palette-only .sp-palette-container {
  border: 0;
}
.sp-palette .sp-thumb-el {
  display: block;
  position: relative;
  float: left;
  width: 24px;
  height: 15px;
  margin: 3px;
  cursor: pointer;
  border: solid 2px transparent;
}
.sp-palette .sp-thumb-el:hover,
.sp-palette .sp-thumb-el.sp-thumb-active {
  border-color: #007bdb;
}
.sp-thumb-el {
  position: relative;
}
/* Initial */
.sp-initial {
  float: left;
  border: solid 1px #333;
}
.sp-initial span {
  width: 30px;
  height: 25px;
  border: none;
  display: block;
  float: left;
  margin: 0;
}
.sp-initial .sp-clear-display {
  background-position: center;
}
/* Buttons */
.sp-palette-button-container,
.sp-button-container {
  float: right;
  margin-top: 4px;
}
/* Replacer (the little preview div that shows up instead of the <input>) */
.sp-replacer {
  margin: 0;
  overflow: hidden;
  cursor: pointer;
  padding: 3px;
  display: inline-block;
  *zoom: 1;
  *display: inline;
  border: solid 1px #adadad;
  background: #eaeaea;
  color: #333;
  vertical-align: middle;
  box-sizing: border-box;
}
.sp-replacer:hover,
.sp-replacer.sp-active {
  border-color: #1979c3;
  color: #111;
}
.sp-replacer.sp-disabled {
  cursor: default;
  border-color: silver;
  color: silver;
}
.sp-dd {
  height: 16px;
  line-height: 16px;
  float: left;
  font-size: 10px;
  display: none;
}
.sp-preview {
  position: relative;
  width: 25px;
  height: 25px;
  border: solid 1px #adadad;
  float: left;
  z-index: 0;
}
.sp-palette {
  *width: 220px;
  max-width: 220px;
}
.sp-palette .sp-thumb-el {
  width: 16px;
  height: 16px;
  margin: 2px 1px;
  border: solid 1px #d0d0d0;
}
.mgz-spectrum.sp-container {
  padding-bottom: 0;
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.5);
  margin-top: 4px;
}
/* Buttons: http://hellohappy.org/css3-buttons/ */
.sp-cancel {
  font-size: 14px;
  color: #1979c3 !important;
  margin: 0;
  padding: 2px;
  margin-right: 15px;
  vertical-align: middle;
  text-decoration: none;
}
.sp-cancel:hover {
  text-decoration: underline;
}
.sp-palette span:hover,
.sp-palette span.sp-thumb-active {
  border-color: #000;
}
.sp-preview,
.sp-alpha,
.sp-thumb-el {
  position: relative;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==);
}
.sp-preview-inner,
.sp-alpha-inner,
.sp-thumb-inner {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}
.sp-palette .sp-thumb-inner {
  background-position: 50% 50%;
  background-repeat: no-repeat;
}
.sp-palette .sp-thumb-light.sp-thumb-active .sp-thumb-inner {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAIVJREFUeNpiYBhsgJFMffxAXABlN5JruT4Q3wfi/0DsT64h8UD8HmpIPCWG/KemIfOJCUB+Aoacx6EGBZyHBqI+WsDCwuQ9mhxeg2A210Ntfo8klk9sOMijaURm7yc1UP2RNCMbKE9ODK1HM6iegYLkfx8pligC9lCD7KmRof0ZhjQACDAAceovrtpVBRkAAAAASUVORK5CYII=);
}
.sp-palette .sp-thumb-dark.sp-thumb-active .sp-thumb-inner {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjEwMPRyoQAAAMdJREFUOE+tkgsNwzAMRMugEAahEAahEAZhEAqlEAZhEAohEAYh81X2dIm8fKpEspLGvudPOsUYpxE2BIJCroJmEW9qJ+MKaBFhEMNabSy9oIcIPwrB+afvAUFoK4H0tMaQ3XtlrggDhOVVMuT4E5MMG0FBbCEYzjYT7OxLEvIHQLY2zWwQ3D+9luyOQTfKDiFD3iUIfPk8VqrKjgAiSfGFPecrg6HN6m/iBcwiDAo7WiBeawa+Kwh7tZoSCGLMqwlSAzVDhoK+6vH4G0P5wdkAAAAASUVORK5CYII=);
}
.sp-clear-display {
  background-repeat: no-repeat;
  background-position: center;
  background-color: #fff;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNSIgaGVpZ2h0PSIyNSIgc3R5bGU9ImJhY2tncm91bmQ6I2ZmZiIgdmlld0JveD0iMCAwIDI1IDI1Ij4KICA8cGF0aCBmaWxsPSJub25lIiBzdHJva2U9IiNGMDAiIHN0cm9rZS1saW5lY2FwPSJzcXVhcmUiIGQ9Ik0wLjUsMC41IEwyNS41LDI0LjUiLz4KPC9zdmc+Cg==);
}
.sp-palette-row-selection {
  padding-top: 10px;
}
.sp-palette-row-selection:before {
  content: "Favorite Colors";
}
.mgz-element-default {
  height: 100%;
}
.mgz-element-default .mgz-builder-element-icon,
.mgz-element-default .mgz-element-info {
  -webkit-flex: 1 1 auto;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  -webkit-align-self: center;
  -ms-align-self: center;
  align-self: center;
  padding: 5px;
}
.mgz-element-default .mgz-element-info {
  width: 100%;
  line-height: 18px;
}
.mgz-element-default .mgz-element-info .mgz-element-name {
  font-weight: 600;
  line-height: 14px;
  font-size: 14px;
}
.mgz-element-default .mgz-element-info .mgz-element-description {
  font-size: 11px;
  font-style: italic;
  color: #737373;
  margin-right: 5px;
  margin-top: 3px;
}
.mgz-element-default p > .magento-placeholder {
  margin-left: 0;
  margin-top: 8px;
}
.mgz-builder .mgz-element-row.mgz-element-actived {
  outline: 2px solid #007dbd;
}
.mgz-builder .mgz-element-row.mgz-row-equal-height > .mgz-element-inner > .inner-content > .mgz-element-column > .mgz-resize-wrapper {
  display: none;
}
.mgz-builder .mgz-element-column {
  margin-top: 3px;
  margin-bottom: 3px;
}
.mgz-builder .mgz-element-column:hover {
  outline: 1px dashed rgba(125, 125, 125, 0.4);
}
.mgz-builder .mgz-element-column.mgz-element-actived {
  outline: 2px solid #f3af1c;
}
.mgz-builder .mgz-tabs .mgz-tabs-tab-content {
  display: block;
}
.mgz-builder .mgz-tabs .mgz-tabs-nav {
  outline: none !important;
}
.mgz-builder .mgz-tabs .mgz-tabs-nav .mgz-placeholder {
  -webkit-border-radius: 5px 5px 0 0;
  -moz-border-radius: 5px 5px 0 0;
  border-radius: 5px 5px 0 0;
  width: 100px;
  height: 50px;
  margin: 0;
  display: inline-block;
}
.mgz-builder .mgz-tabs .mgz-tabs-nav .mgz-placeholder a {
  visibility: hidden;
  padding: 14px 20px;
  display: block;
}
.mgz-builder .mgz-tabs .mgz-tabs-tab-add a {
  margin: 0 !important;
  background: #ebebeb !important;
  color: #666666 !important;
  border: 1px solid #e3e3e3 !important;
}
.mgz-builder .mgz-tabs .mgz-tabs-tab-add a:hover {
  background: #dcdcdc !important;
}
.mgz-builder .mgz-element-tab {
  outline: none !important;
}
.mgz-builder .mgz-element-heading .mgz-element-heading-text {
  margin: 0;
}
.mgz-element .mgz-spinner {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 9;
}
.mgz-builder-mode .admin__control-wysiwig {
  display: none;
}
.mgz-profile-spinner {
  width: 100%;
  height: 200px;
  position: relative;
}
.mgz-profile-spinner i {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
}
.mgz-width200,
.mgz-inner-width200 > .mgz__field-control > .mgz__control-select {
  width: 200% !important;
}
.mgz-fixed-width100,
.mgz-fixed-inner-width100 > .mgz__field-control > .mgz__control-select {
  width: 100px !important;
}
.mgz-width120 {
  width: 120% !important;
}
.mgz-width100,
.mgz-inner-width100 > .mgz__field-control > .mgz__control-select {
  width: 100% !important;
}
.mgz-width95 {
  width: 95% !important;
}
.mgz-width90 {
  width: 90% !important;
}
.mgz-width85 {
  width: 85% !important;
}
.mgz-width80 {
  width: 80% !important;
}
.mgz-inner-width80 {
  width: 80% !important;
}
.mgz-width75 {
  width: 75% !important;
}
.mgz-width70 {
  width: 70% !important;
}
.mgz-width65 {
  width: 65% !important;
}
.mgz-width60 {
  width: 60% !important;
}
.mgz-width55 {
  width: 55% !important;
}
.mgz-width50 {
  width: 50% !important;
}
.mgz-width45 {
  width: 45% !important;
}
.mgz-width45 {
  width: 45% !important;
}
.mgz-width40 {
  width: 40% !important;
}
.mgz-width35 {
  width: 35% !important;
}
.mgz-width30 {
  width: 30% !important;
}
.mgz-width25 {
  width: 25% !important;
}
.mgz-width20 {
  width: 20% !important;
}
.mgz-width15 {
  width: 15% !important;
}
.mgz-width10 {
  width: 10% !important;
}
/*!
 * ui-select
 * http://github.com/angular-ui/ui-select
 * Version: 0.19.7 - 2017-04-15T14:28:36.790Z
 * License: MIT
 */
.ui-select-highlight {
  font-weight: 700;
}
.ui-select-offscreen {
  clip: rect(0 0 0 0) !important;
  width: 1px!important;
  height: 1px!important;
  border: 0!important;
  margin: 0!important;
  padding: 0!important;
  overflow: hidden!important;
  position: absolute!important;
  outline: 0!important;
  left: 0!important;
  top: 0 !important;
}
.selectize-control.single > .selectize-input > input,
.selectize-control > .selectize-dropdown {
  width: 100%;
}
.ui-select-choices-row:hover {
  background-color: #f5f5f5;
}
.ng-dirty.ng-invalid > a.select2-choice {
  border-color: #D44950;
}
.select2-result-single {
  padding-left: 0;
}
.select-locked > .ui-select-match-close,
.select2-locked > .select2-search-choice-close {
  display: none;
}
body > .select2-container.open {
  z-index: 9999;
}
.ui-select-container.select2.direction-up .ui-select-match,
.ui-select-container[theme=select2].direction-up .ui-select-match {
  border-radius: 0 0 4px 4px;
}
.ui-select-container.select2.direction-up .ui-select-dropdown,
.ui-select-container[theme=select2].direction-up .ui-select-dropdown {
  border-radius: 4px 4px 0 0;
  border-top-width: 1px;
  border-top-style: solid;
  box-shadow: 0 -4px 8px rgba(0, 0, 0, 0.25);
  margin-top: -4px;
}
.ui-select-container.select2.direction-up .ui-select-dropdown .select2-search,
.ui-select-container[theme=select2].direction-up .ui-select-dropdown .select2-search {
  margin-top: 4px;
}
.ui-select-container.select2.direction-up.select2-dropdown-open .ui-select-match,
.ui-select-container[theme=select2].direction-up.select2-dropdown-open .ui-select-match {
  border-bottom-color: #5897fb;
}
.ui-select-container[theme=select2] .ui-select-dropdown .ui-select-search-hidden,
.ui-select-container[theme=select2] .ui-select-dropdown .ui-select-search-hidden input {
  opacity: 0;
  height: 0;
  min-height: 0;
  padding: 0;
  margin: 0;
  border: 0;
}
.selectize-input.selectize-focus {
  border-color: #007FBB !important;
}
.selectize-control.multi > .selectize-input > input {
  margin: 0 !important;
}
.ng-dirty.ng-invalid > div.selectize-input {
  border-color: #D44950;
}
.ui-select-container[theme=selectize].direction-up .ui-select-dropdown {
  box-shadow: 0 -4px 8px rgba(0, 0, 0, 0.25);
  margin-top: -2px;
}
.ui-select-container[theme=selectize] input.ui-select-search-hidden {
  opacity: 0;
  height: 0;
  min-height: 0;
  padding: 0;
  margin: 0;
  border: 0;
  width: 0;
}
.ui-select-bootstrap .ui-select-toggle {
  position: relative;
}
.ui-select-bootstrap .ui-select-toggle > .caret {
  position: absolute;
  height: 10px;
  top: 50%;
  right: 10px;
  margin-top: -2px;
}
.input-group > .ui-select-bootstrap.dropdown {
  position: static;
}
.input-group > .ui-select-bootstrap > input.ui-select-search.form-control {
  border-radius: 4px 0 0 4px;
}
.input-group > .ui-select-bootstrap > input.ui-select-search.form-control.direction-up {
  border-radius: 4px 0 0 4px !important;
}
.ui-select-bootstrap .ui-select-search-hidden {
  opacity: 0;
  height: 0;
  min-height: 0;
  padding: 0;
  margin: 0;
  border: 0;
}
.ui-select-bootstrap > .ui-select-match > .btn {
  text-align: left!important;
  border-color: #878787;
  color: #303030;
}
.ui-select-bootstrap > .ui-select-match > .caret {
  position: absolute;
  top: 45%;
  right: 15px;
}
.ui-select-bootstrap > .ui-select-choices,
.ui-select-bootstrap > .ui-select-no-choice {
  width: 100%;
  height: auto;
  max-height: 300px;
  overflow-x: hidden;
}
body > .ui-select-bootstrap.open {
  z-index: 1000;
}
.ui-select-multiple.ui-select-bootstrap {
  height: auto;
  padding: 3px 3px 0;
}
.ui-select-multiple.ui-select-bootstrap input.ui-select-search {
  background-color: transparent!important;
  border: none;
  outline: 0;
}
.ui-select-multiple.ui-select-bootstrap .ui-select-match .close {
  font-size: 1.6em;
}
.ui-select-multiple.ui-select-bootstrap .ui-select-match-item {
  outline: 0;
  margin: 0 3px 3px 0;
}
.ui-select-multiple .ui-select-match-item {
  position: relative;
}
.ui-select-multiple .ui-select-match-item.dropping .ui-select-match-close {
  pointer-events: none;
}
.ui-select-multiple:hover .ui-select-match-item.dropping-before:before {
  content: "";
  position: absolute;
  top: 0;
  right: 100%;
  height: 100%;
  margin-right: 2px;
  border-left: 1px solid #428bca;
}
.ui-select-multiple:hover .ui-select-match-item.dropping-after:after {
  content: "";
  position: absolute;
  top: 0;
  left: 100%;
  height: 100%;
  margin-left: 2px;
  border-right: 1px solid #428bca;
}
.ui-select-bootstrap .ui-select-choices-row > span {
  cursor: pointer;
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: 400;
  line-height: 1.42857143;
  color: #333;
  white-space: nowrap;
}
.ui-select-bootstrap .ui-select-choices-row > span:focus,
.ui-select-bootstrap .ui-select-choices-row > span:hover {
  text-decoration: none;
  color: #262626;
  background-color: #f5f5f5;
}
.ui-select-bootstrap .ui-select-choices-row.active > span {
  color: #fff;
  text-decoration: none;
  outline: 0;
  background-color: #428bca;
}
.ui-select-bootstrap .ui-select-choices-row.active.disabled > span,
.ui-select-bootstrap .ui-select-choices-row.disabled > span {
  color: #777;
  cursor: not-allowed;
  background-color: #fff;
}
.ui-select-match.ng-hide-add,
.ui-select-search.ng-hide-add {
  display: none !important;
}
.ui-select-bootstrap.ng-dirty.ng-invalid > button.btn.ui-select-match {
  border-color: #D44950;
}
.ui-select-container[theme=bootstrap].direction-up .ui-select-dropdown {
  box-shadow: 0 -4px 8px rgba(0, 0, 0, 0.25);
}
.ui-select-bootstrap .ui-select-match-text {
  width: 100%;
  padding-right: 1em;
}
.ui-select-bootstrap .ui-select-match-text span {
  display: inline-block;
  width: 100%;
  overflow: hidden;
}
.ui-select-bootstrap .ui-select-toggle > a.btn {
  position: absolute;
  height: 10px;
  right: 10px;
  margin-top: -2px;
}
.ui-select-refreshing.glyphicon {
  position: absolute;
  right: 0;
  padding: 8px 27px;
}
@-webkit-keyframes ui-select-spin {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes ui-select-spin {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
.ui-select-spin {
  -webkit-animation: ui-select-spin 2s infinite linear;
  animation: ui-select-spin 2s infinite linear;
}
.ui-select-refreshing.ng-animate {
  -webkit-animation: none 0s;
}
@keyframes mgzspin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.mgz-spinner {
  display: inline-block;
  position: relative;
}
.mgz-spinner i {
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: block;
  border: 3px solid #007dbd;
  border-top-color: transparent;
  animation: mgzspin 1.1s infinite linear;
}
.mgz-spinner i span {
  display: none;
}
.mgz-tooltip {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  position: absolute;
  z-index: 1030;
  display: block;
  visibility: visible;
  padding: 5px;
  line-height: 28px;
  font-size: 13px;
}
.mgz-tooltip.in {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
}
.mgz-tooltip.top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  border-width: 5px 5px 0;
  border-top-color: #000000;
}
.mgz-tooltip.right .tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: #000000;
}
.mgz-tooltip.bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000000;
}
.mgz-tooltip.left .tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: #000000;
}
.mgz-tooltip .tooltip-inner {
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  max-width: 200px;
  padding: 3px 8px;
  color: #ffffff;
  text-align: center;
  text-decoration: none;
  background: #191E23;
}
.mgz-tooltip .tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.mgz-builder * {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.mgz-builder a {
  cursor: pointer;
}
.mgz-builder .mgz-builder-content {
  -webkit-transition: width 0.2s ease-in-out;
  -moz-transition: width 0.2s ease-in-out;
  -ms-transition: width 0.2s ease-in-out;
  -o-transition: width 0.2s ease-in-out;
  margin: 0 auto;
  background: #FFF;
}
.mgz-builder .mgz-builder-content:before,
.mgz-builder .mgz-builder-content:after {
  content: " ";
  display: table;
  clear: both;
}
.mgz-builder .mgz-builder-content.mgz-builder-profile-empty .mgz-builder-content-inner {
  margin: 0;
}
.mgz-builder .mgz-dropdown {
  position: relative;
}
.mgz-builder .mgz-dropdown:hover .mgz-dropdown-list {
  display: block;
}
.mgz-builder .mgz-dropdown .mgz-dropdown-list {
  display: none;
  position: absolute;
  z-index: 999;
  background: #0073ae;
  width: 100%;
  left: 0;
}
.mgz-builder .mgz-dropdown .mgz-dropdown-list > li.active > a,
.mgz-builder .mgz-dropdown .mgz-dropdown-list > li:hover > a {
  background: #005780;
}
.mgz-builder .magezon-logo {
  background: url('../images/logo.svg') no-repeat center center;
  background-size: 35px;
}
.mgz-fullscreen {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  overflow: scroll;
  max-width: 100%;
  z-index: 799;
  background: #282828;
}
.mgz-fullscreen .mgz-navbar {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  width: 100%;
  z-index: 9;
}
.mgz-fullscreen.mgz-builder-mode-xl .mgz-builder-content,
.mgz-fullscreen.mgz-builder-mode-lg .mgz-builder-content,
.mgz-fullscreen.mgz-builder-mode-md .mgz-builder-content,
.mgz-fullscreen.mgz-builder-mode-sm .mgz-builder-content,
.mgz-fullscreen.mgz-builder-mode-xs .mgz-builder-content {
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  min-height: 100%;
  margin-top: 55px;
}
.mgz-fullscreen.mgz-builder-mode-xl .mgz-builder-content {
  width: 1200px;
}
.mgz-fullscreen.mgz-builder-mode-lg .mgz-builder-content {
  width: 992px;
}
.mgz-fullscreen.mgz-builder-mode-md .mgz-builder-content {
  width: 800px;
}
.mgz-fullscreen.mgz-builder-mode-sm .mgz-builder-content {
  width: 640px;
}
.mgz-fullscreen.mgz-builder-mode-xs .mgz-builder-content {
  width: 480px;
}
.magezon-builder-active-wrapper {
  display: block !important;
}
.magezon-builder-active-wrapper > .magezon-builder-active > .admin__field-control {
  width: 100% !important;
}
.mgz-builder-grouped-wrapper > .admin__field-control,
.admin__field.mgz-builder-wrapper > .admin__field-control {
  width: calc(100% * 0.72 - 30px);
}
.catalog-product-edit .mgz-builder-grouped-wrapper > .admin__field-control,
.catalog-product-edit .admin__field.mgz-builder-wrapper > .admin__field-control {
  width: 100%;
}
.mgz-builder-grouped-wrapper .admin__control-wysiwig,
.admin__field.mgz-builder-wrapper .admin__control-wysiwig {
  display: none;
}
.admin__fieldset > .admin__field > .admin__field-control.mgz-builder-grouped-wrapper {
  width: calc(100% * 0.72 - 30px);
}
.mgz-builder-action-btn,
.mgz-builder-action-btn:focus {
  padding: 5px 10px 5px 35px;
  background-color: #007dbd;
  border-color: #0073ae;
  color: #FFF;
  font-weight: normal;
  margin-bottom: 5px;
  font-size: 15px;
  position: relative;
  display: none;
}
.mgz-builder-action-btn > i,
.mgz-builder-action-btn:focus > i {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  position: absolute;
  left: 15px;
  font-size: 25px;
}
.mgz-builder-action-btn:hover,
.mgz-builder-action-btn:focus:hover {
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  color: #FFF;
  background-color: #0073ae;
  border-color: #0073ae;
}
.mgz-builder-wrapper.admin__field {
  display: block;
}
.mgz-builder-wrapper.admin__field .admin__control-textarea {
  height: 200px;
}
.admin__scope-old .form-inline .mgz-deactive-builder .label ~ .control {
  width: 80%;
}
.mgz-builder-wysiwyg-btn {
  display: none;
}
.mgz-deactive-builder .admin__field-control > textarea,
.mgz-deactive-builder .admin__control-textarea,
.mgz-deactive-builder .mgz-builder-wysiwyg-btn {
  display: none;
}
.mgz-deactive-builder .mgz-builder-deactive-btn,
.mgz-deactive-builder .mgz-builder {
  display: block;
}
.mgz-active-builder .admin__control-textarea,
.mgz-active-builder .mgz-builder-active-btn {
  display: inline-block !important;
}
.mgz-active-builder .mgz-builder {
  display: none;
}
.mgz-active-builder .mgz-builder-wysiwyg-btn {
  display: inline-block;
  margin-left: 5px;
  padding-left: 10px;
  background: #e3e3e3;
  border-color: #adadad;
  color: #514943;
}
.dndPlaceholder.f-right,
.dndPlaceholder.f-left {
  width: 125px;
}
.mgz-builder-element-icon {
  -webkit-align-self: center;
  -ms-align-self: center;
  align-self: center;
  padding: 5px;
}
.mgz-builder-element-icon i {
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  display: inline-block;
  width: 32px;
  height: 32px;
  font-size: 20px;
  color: #FFF;
  position: relative;
}
.mgz-builder-element-icon i:before {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
}
.mgz-contenteditable-element {
  cursor: text;
}
.mgz-contenteditable-element.placeholder-text:before,
.mgz-contenteditable-element.ng-empty:before {
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  content: attr(data-placeholder);
  color: #41362f;
}
.mgz-contenteditable-element.placeholder-text:focus:before,
.mgz-contenteditable-element.ng-empty:focus:before {
  content: '';
}
.mgz-draggable-handle {
  cursor: move;
}
.mgz-builder-box-message {
  display: block;
  float: left;
  width: 100%;
  margin: 30px 0;
}
.mgz-builder-box-message .mgz-btn {
  padding: 5px 10px 5px 10px;
  background-color: #007dbd;
  border-color: #0073ae;
  color: #FFF;
  font-weight: normal;
  position: relative;
  border-radius: 3px;
}
.mgz-builder-box-message .mgz-btn:hover {
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  color: #FFF;
  background-color: #0073ae;
  border-color: #0073ae;
}
