<?php
$helper            = $this->helper('Magezon\Builder\Helper\Data');
$element           = $this->getElement();
$height            = $element->getData('image_height');
$width             = $element->getData('image_width');
$onclick           = $element->getData('onclick');
$title             = $element->getData('title');
$description       = $element->getData('description');
$zoomEffect        = $element->getData('zoom_effect');
$alt               = $element->getData('alt_tag');
$contentPosition   = $element->getData('content_position');
$overlayColor      = $element->getData('overlay_color');
$hoverOverlayColor = $element->getData('hover_overlay_color');
$imageHoverEffect  = $element->getData('image_hover_effect');
$imgId             = $element->getData('img_id');
$lazyLoading       = $element->getData('load_lazy') ?? "true";
$src               = $this->getSrc();
$link              = $this->getLink();
$srcset            = $this->getSrcset();
$wrapperClasses    = $this->getImgWrapperClasses();
$linkClasses       = $this->getLinkClasses();
$hoverImage        = $helper->getImageUrl($element->getData('hover_image'));
?>
<?php if($src) { ?>
	<?php $alt = $alt ? $alt : $this->getFilename($src); ?>
	<div class="<?= $wrapperClasses ?>">
		<div class="mgz-single-image-inner <?= $contentPosition ? 'mgz-flex-position-' . $contentPosition : '' ?> <?= $imageHoverEffect ? 'hover-type-' . $imageHoverEffect : '' ?> <?= $link['url'] ? 'mgz-image-link' : '' ?>">
		<?php if ($link['url']) { ?>
			<a class="<?= $linkClasses ?>" 
			href="<?= $link['url'] ?>" 
			data-type="<?= $onclick == 'video_map' ? 'iframe' : 'image' ?>"
			data-title="<?= $escaper->escapeHtml($title) ?>"
			data-zoom="<?= $zoomEffect ? '1' : '0' ?>" 
			<?= $link['blank'] ? 'target="_blank"' : '' ?> <?= $link['nofollow'] ? 'rel="nofollow"' : '' ?>
			>
		<?php } ?>
			<img <?= $lazyLoading ? 'loading="lazy"':'' ?> style="object-fit: cover; min-width: 1px; min-height: 1px;" <?= $imgId ? 'id="' . $imgId . '"' : '' ?> class="mgz-hover-main" src="<?= $src ?>" alt="<?= $alt ?>" <?= $width ? 'width="' . $width . '"' : '' ?> <?= $height ? 'height="' . $height . '"' : '' ?> <?= $srcset ? 'srcset="' . $srcset . '"' : '' ?> title="<?= $escaper->escapeHtml($title) ?>" <?php if ($hoverImage) { ?>data-hover="<?= $hoverImage ?>"<?php } ?>/>
		<?php if ($link['url']) { ?>
			</a>
		<?php } ?>
			<?php if (($title || $description) && $contentPosition && ($contentPosition != 'never')) { ?>
				<div class="image-content">
					<?php if ($title) { ?>
					<div class="image-title"><?= $title ?></div>
					<?php } ?>
					<?php if ($description) { ?>
					<div class="image-description"><?= $description ?></div>
					<?php } ?>
				</div>
			<?php } ?>
		</div>
		<?php if ($overlayColor || $hoverOverlayColor) { ?>
			<div class="mgz-overlay"></div>
		<?php } ?>
	</div>
<?php } ?>