<?php
$coreHelper      = $this->helper('\Magezon\Core\Helper\Data');
$element         = $this->getElement();
$title           = $coreHelper->filter($element->getData('title'));
$titleAlign      = $element->getData('title_align');
$titleTag        = $element->getData('title_tag') ? $element->getData('title_tag') : 'h2';
$description     = $coreHelper->filter($element->getData('description'));
$showLine        = $element->getData('show_line');
$linePosition    = $element->getData('line_position');
$items           = $this->toObjectArray($element->getItems());
$htmlId          = $element->getHtmlId();
$carouselOptions = $this->getOwlCarouselOptions();
$classes         = $this->getOwlCarouselClasses();
?>
<?php if (count($items)) { ?>
<div class="mgz-block">
	<?php if ($title || $description) { ?>
	<div class="mgz-block-heading mgz-block-heading-align-<?= $titleAlign ?><?= $showLine ? ' mgz-block-heading-line' : '' ?> mgz-block-heading-line-position-<?= $linePosition ?>">
		<?php if ($title) { ?>
			<<?= $titleTag ?> class="title"><?= $title ?></<?= $titleTag ?>>
		<?php } ?>
		<?php if ($description) { ?>
			<div class="info"><?= $description ?></div>
		<?php } ?>
	</div>
	<?php } ?>
	<div class="mgz-block-content">
		<?php if ($items && count($items)) { ?>
			<div class="mgz-carousel owl-carousel <?= implode(' ', $classes) ?>" data-mage-init='{"Magezon_Builder/js/carousel":<?= $coreHelper->serialize($carouselOptions) ?>}'>
				<?php foreach ($items as $item) { ?>
					<div class="mgz-content-carouse-slide">
						<?= $coreHelper->filter($item['content']) ?>						
					</div>
				<?php } ?>
			</div>
		<?php } ?>
	</div>
</div>
<?php } ?>