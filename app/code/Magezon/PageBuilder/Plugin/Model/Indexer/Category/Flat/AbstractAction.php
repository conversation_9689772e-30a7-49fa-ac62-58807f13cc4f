<?php
/**
 * Magezon
 *
 * This source file is subject to the Magezon Software License, which is available at https://www.magezon.com/license
 * Do not edit or add to this file if you wish to upgrade the to newer versions in the future.
 * If you wish to customize this module for your needs.
 * Please refer to https://www.magezon.com for more information.
 *
 * @category  Magezon
 * @package   Magezon_PageBuilder
 * @copyright Copyright (C) 2019 Magezon (https://www.magezon.com)
 */

namespace Magezon\PageBuilder\Plugin\Model\Indexer\Category\Flat;

class AbstractAction
{
    public function afterGetColumns(
        $subject,
        $result
    ) {
        $attributes = ['description'];
        foreach ($attributes as $attr) {
            if (isset($result[$attr]['type'][1]) && $result[$attr]['type'][1] == '64k') {
                $result[$attr]['type'][1] = '64M';
            }
        }
        return $result;
    }
}
