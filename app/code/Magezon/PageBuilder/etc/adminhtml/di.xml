<?xml version="1.0"?>
<!--
/**
 * Magezon
 *
 * This source file is subject to the Magezon Software License, which is available at https://www.magezon.com/license.
 * Do not edit or add to this file if you wish to upgrade the to newer versions in the future.
 * If you wish to customize this module for your needs.
 * Please refer to https://www.magezon.com for more information.
 *
 * @category  Magezon
 * @package   Magezon_PageBuilder
 * @copyright Copyright (C) 2019 Magezon (https://www.magezon.com)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<preference for="Magento\Ui\Component\Form\Element\Wysiwyg" type="Magezon\PageBuilder\Ui\Component\Form\Element\Builder" />
	<preference for="Magento\Catalog\Ui\Component\Category\Form\Element\Wysiwyg" type="Magezon\PageBuilder\Ui\Component\Form\Element\Builder" />
	<virtualType name="Magento\Catalog\Ui\DataProvider\Product\Form\Modifier\Pool" type="Magento\Ui\DataProvider\Modifier\Pool">
        <arguments>
            <argument name="modifiers" xsi:type="array">
                <item name="pagebuilder_general" xsi:type="array">
                    <item name="class" xsi:type="string">Magezon\PageBuilder\Ui\DataProvider\Product\Form\Modifier\General</item>
                    <item name="sortOrder" xsi:type="number">55</item>
                </item>
            </argument>
        </arguments>
    </virtualType>
</config>