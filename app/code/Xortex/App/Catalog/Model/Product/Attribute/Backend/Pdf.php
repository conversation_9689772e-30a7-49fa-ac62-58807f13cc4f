<?php
namespace Xortex\App\Catalog\Model\Product\Attribute\Backend;

use Magento\Eav\Model\Entity\Attribute\Backend\AbstractBackend;
use Magento\Framework\App\Filesystem\DirectoryList;

/**
 * Product image attribute backend
 *
 * <AUTHOR> Core Team <<EMAIL>>
 */
class Pdf extends AbstractBackend
{

    /**
     * @var string
     */
    const PDF_BASEPATH = "/catalog/product/pdf";

    /**
     * Filesystem facade
     *
     * @var \Magento\Framework\Filesystem
     */
    protected $_filesystem;

    /**
     * File Uploader factory
     *
     * @var \Magento\MediaStorage\Model\File\UploaderFactory
     */
    protected $_fileUploaderFactory;

    /**
     * @param \Magento\Framework\Filesystem $filesystem
     * @param \Magento\MediaStorage\Model\File\UploaderFactory $fileUploaderFactory
     */
    public function __construct(
        \Magento\Framework\Filesystem $filesystem,
        \Magento\MediaStorage\Model\File\UploaderFactory $fileUploaderFactory
    )
    {
        $this->_filesystem = $filesystem;
        $this->_fileUploaderFactory = $fileUploaderFactory;
    }

    /**
     * After save
     *
     * @param \Magento\Framework\DataObject $object
     * @return $this|void
     * @throws \Exception
     */
    public function afterSave($object)
    {
        $value = $object->getData($this->getAttribute()->getName());

        $delete = $object->offsetGet($this->getAttribute()->getName()."_delete");
        if ($value && $delete) {
            $object->setData($this->getAttribute()->getName(), '');
            $this->getAttribute()->getEntity()->saveAttribute($object, $this->getAttribute()->getName());
            $this->_filesystem->getDirectoryWrite(DirectoryList::MEDIA)->delete($value);
            return;
        }

        try {
            /** @var $uploader \Magento\MediaStorage\Model\File\Uploader */
            $uploader = $this->_fileUploaderFactory->create(['fileId' => "product[" . $this->getAttribute()->getName() . "]"]);
            $uploader->setAllowedExtensions(['pdf']);
            $uploader->setAllowRenameFiles(true);
            $uploader->setFilesDispersion(true);
        } catch (\Exception $e) {
            return $this;
        }
        $path = $this->_filesystem->getDirectoryRead(
            DirectoryList::MEDIA
        )->getAbsolutePath(
            self::PDF_BASEPATH
        );
        $uploader->save($path);

        // Falls eine Datei ausgetauscht wurde sollte alte gelöscht werden
        if($value) {
            $this->_filesystem->getDirectoryWrite(DirectoryList::MEDIA)->delete($value);
        }

        $fileName = $uploader->getUploadedFileName();
        if ($fileName) {
            $object->setData($this->getAttribute()->getName(), self::PDF_BASEPATH . $fileName);
            $this->getAttribute()->getEntity()->saveAttribute($object, $this->getAttribute()->getName());
        }
        return $this;
    }
}