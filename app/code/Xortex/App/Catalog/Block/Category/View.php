<?php

namespace Xortex\App\Catalog\Block\Category;

class View extends \Magento\Catalog\Block\Category\View
{

    /**
     * Bildgrößen
     */
    const CATEGORY_LIST_IMAGE_WIDTH = 300;
    const CATEGORY_LIST_IMAGE_HEIGHT = 220;

    /**
     * @param $categoryId
     * @return \Magento\Catalog\Model\ResourceModel\Category\Collection|\Magento\Framework\Data\Tree\Node\Collection
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function getChildCategoryListCollection($categoryId)
    {
        $childCategoryCollection = $this->getCurrentCategory()->getCategories($categoryId, 1, false, true, false);
        $childCategoryCollection->addAttributeToSelect('*')->setOrder(
            'position',
            \Magento\Framework\DB\Select::SQL_ASC
        );
        $childCategoryCollection->addAttributeToFilter("top", 1);
        $childCategoryCollection->load();
        return $childCategoryCollection;
    }

    /**
     * @return \Magento\Catalog\Model\ResourceModel\Category\Collection|\Magento\Framework\Data\Tree\Node\Collection
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getChildCategoryListCollectionFromCurrentCategory()
    {
        return $this->getChildCategoryListCollection($this->getCurrentCategory()->getId());
    }

    /**
     * @return \Magento\Catalog\Model\ResourceModel\Category\Collection|\Magento\Framework\Data\Tree\Node\Collection
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getChildCategoryListCollectionFromParentCategory()
    {
        return $this->getChildCategoryListCollection($this->getCurrentCategory()->getParentId());
    }

    /**
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getCategoriesSelectHtml()
    {
        $categories = $this->getChildCategoryListCollectionFromParentCategory();
        if ($categories->count() > 0) {
            /** @var \Magento\Framework\View\Element\Html\Select $selectHtmlBlock */
            $selectHtmlBlock = $this->getLayout()->createBlock("Magento\Framework\View\Element\Html\Select");
            $selectHtmlBlock->setName("category-select");

            /** @var \Magento\Catalog\Model\Category $category */
            foreach ($categories as $category) {
                $params = [];
                if ($this->getCurrentCategory()->getId() == $category->getId()) {
                    $params["selected"] = "selected";
                }
                $selectHtmlBlock->addOption($category->getUrl(), $category->getName(), $params);
            }
            $selectHtmlBlock->setExtraParams("onchange='window.location.href = this.value; return false;'");
            return $selectHtmlBlock->toHtml();
        }
        return "";
    }

    /**
     * Check if category display mode is "CMS"
     * @return bool
     */
    public function isCmsMode()
    {
        return $this->getCurrentCategory()->getDisplayMode() == \Xortex\App\Catalog\Model\Category\Attribute\Source\Mode::DM_CMS;
    }
}