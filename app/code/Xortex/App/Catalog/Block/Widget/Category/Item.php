<?php

namespace Xortex\App\Catalog\Block\Widget\Category;
class Item extends \Magento\Framework\View\Element\Template {

  protected $_template = 'Xortex_App::catalog/widget/category/item.phtml';

  /**
   * @var \Magento\Catalog\Model\Category
   */
  protected $category;
  protected $width;
  protected $height;

  /**
   * @return \Magento\Catalog\Model\Category
   */
  public function getCategory() {
    return $this->category;
  }

  /**
   * @return mixed
   */
  public function getWidth() {
    return $this->width;
  }

  /**
   * @param mixed $width
   */
  public function setWidth($width) {
    $this->width = $width;
  }

  /**
   * @return mixed
   */
  public function getHeight() {
    return $this->height;
  }

  /**
   * @param mixed $height
   */
  public function setHeight($height) {
    $this->height = $height;
  }

  /**
   * @param \Magento\Catalog\Model\Category $category
   */
  public function setCategory($category) {
    $this->category = $category;
  }
}