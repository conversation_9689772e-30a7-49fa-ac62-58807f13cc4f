<?php

namespace Xortex\App\Checkout\Plugin;

/**
 * Class LayoutProcessor
 */
class LayoutProcessor
{

    /**
     * @param \Magento\Checkout\Block\Checkout\LayoutProcessor $subject
     * @param \Closure $proceed
     * @param array $jsLayout
     * @return array
     */
    public function afterProcess(
        \Magento\Checkout\Block\Checkout\LayoutProcessor $subject,
        $jsLayoutResult,
        array $jsLayout
    )
    {


        foreach ($jsLayoutResult['components']['checkout']['children']['steps']['children']['billing-step']['children']
                 ['payment']['children']['payments-list']['children'] as $child)
        {
            if (isset($child['children']['form-fields'])) {
                $child['children']['form-fields']['children']['postcode'] = array_merge(
                    $child['children']['form-fields']['children']['postcode'],
                    ['sortOrder' => 75]
                );
            }
        }
        $jsLayoutResult['components']['checkout']['children']['steps']['children']['billing-step']
        ['children']['payment']['children']['payments-list']['children']['checkmo-form']['children']['form-fields']['children']['company']['sortOrder'] = 110;

        // Set Tooltip to E-Mail
        $jsLayoutResult['components']['checkout']['children']['steps']['children']['shipping-step']
        ['children']['shippingAddress']['children']['customer-email']['tooltip']["description"] = __('To send your order confirmation, we need your correct email address. If you do not have an email address, please call us - we will be happy to wrap up your order by phone!');

        // Set Tooltip to Telephone
        $jsLayoutResult['components']['checkout']['children']['steps']['children']['shipping-step']
        ['children']['shippingAddress']['children']['shipping-address-fieldset']['children']['telephone']['config']['tooltip']["description"] = __('In order to be able to arrange a delivery date for the delivery, we need your telephone number.');

        // Set Tooltip to Adressdetails (Company) - Shipping Address
        $jsLayoutResult['components']['checkout']['children']['steps']['children']['shipping-step']
        ['children']['shippingAddress']['children']['shipping-address-fieldset']['children']['company']['config']['tooltip']["description"] = __('Bitte geben Sie uns eventuelle für die Zustellung notwendige Details an.
                                        z.B. "z.Hd. Schmidt" oder z.B. bei Zustellung an Ihren Arbeitsplatz den Firmennamen etc.');

        // Set Tooltip to Alternative Telefonnummer (Fax) - Shipping Address
        $jsLayoutResult['components']['checkout']['children']['steps']['children']['shipping-step']
        ['children']['shippingAddress']['children']['shipping-address-fieldset']['children']['fax']['config']['tooltip']["description"] = __('Bitte geben Sie nach Möglichkeit eine zweite Telefonnummer an, unter der wir uns mit Ihnen in Verbindung setzen können. 
        z.B. zweite Mobilnummer, Tel.Nr. Ehegatte etc.');

        // Set Tooltip to Adressdetails (Company) - Billing Address
        $jsLayoutResult['components']['checkout']['children']['steps']['children']['shipping-step']
        ['children']['shippingAddress']['children']['billing-address']["children"]["form-fields"]["children"]['company']['config']['tooltip']["description"] = __('Bitte geben Sie uns eventuelle für die Zustellung notwendige Details an.
                                        z.B. "z.Hd. Schmidt" oder z.B. bei Zustellung an Ihren Arbeitsplatz den Firmennamen etc.');

        // Set Tooltip to Alternative Telefonnummer (Fax) - Billing Address
        $jsLayoutResult['components']['checkout']['children']['steps']['children']['shipping-step']
        ['children']['shippingAddress']['children']['billing-address']["children"]["form-fields"]["children"]['fax']['config']['tooltip']["description"] = __('Bitte geben Sie uns eventuelle für die Zustellung notwendige Details an.
                                        z.B. "z.Hd. Schmidt" oder z.B. bei Zustellung an Ihren Arbeitsplatz den Firmennamen etc.');

        return $jsLayoutResult;
    }

    /**
     * @param \Magento\Checkout\Block\Checkout\LayoutProcessor $subject
     * @param \Closure $proceed
     * @param string $paymentCode
     * @param array $elements
     * @return array
     */
    public function afterGetBillingAddressComponent(
        \Magento\Checkout\Block\Checkout\LayoutProcessor $subject,
        $paymentCode,
        $elements,
        \Closure $proceed
    )
    {
        return [
            'component' => 'Magento_Checkout/js/view/billing-address',
            'displayArea' => 'billing-address-form-' . $paymentCode,
            'provider' => 'checkoutProvider',
            'deps' => 'checkoutProvider',
            'dataScopePrefix' => 'billingAddress' . $paymentCode,
            'sortOrder' => 1,
            'children' => [
                'form-fields' => [
                    'component' => 'uiComponent',
                    'displayArea' => 'additional-fieldsets',
                    'children' => $this->merger->merge(
                        $elements,
                        'checkoutProvider',
                        'billingAddress' . $paymentCode,
                        [
                            'country_id' => [
                                'sortOrder' => 115,
                            ],
                            'region' => [
                                'visible' => false,
                            ],
                            'region_id' => [
                                'component' => 'Magento_Ui/js/form/element/region',
                                'config' => [
                                    'template' => 'ui/form/field',
                                    'elementTmpl' => 'ui/form/element/select',
                                    'customEntry' => 'billingAddress' . $paymentCode . '.region',
                                ],
                                'validation' => [
                                    'required-entry' => true,
                                ],
                                'filterBy' => [
                                    'target' => '${ $.provider }:${ $.parentScope }.country_id',
                                    'field' => 'country_id',
                                ],
                            ],
                            'postcode' => [
                                'component' => 'Magento_Ui/js/form/element/post-code',
                                'validation' => [
                                    'required-entry' => true,
                                ],
                            ],
                            'company' => [
                                'validation' => [
                                    'min_text_length' => 0,
                                ],
                                'config' => [
                                    'tooltip' => [
                                        'description' => __('test billing'),
                                    ],
                                ],
                            ],
                            'fax' => [
                                'validation' => [
                                    'min_text_length' => 0,
                                ],
                            ],
                            'telephone' => [
                                'config' => [
                                    'tooltip' => [
                                        'description' => __('For delivery questions.'),
                                    ],
                                ],
                            ],
                        ]
                    ),
                ],
            ],
        ];
    }


}