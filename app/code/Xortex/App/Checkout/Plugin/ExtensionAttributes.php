<?php

namespace Xortex\App\Checkout\Plugin;

/**
 * Class LayoutProcessor
 */
class ExtensionAttributes
{

    /**
     * @param $block
     * @param $address
     * @return array
     */
    public function beforeGetFormattedAddress($block, $address)
    {
        $attributes = $address->getExtensionAttributes();
        if ($attributes && count($attributes->__toArray())) {
            $address->setTelephoneMobile($attributes->getTelephoneMobile());
        }
        return array($address);
    }

    /**
     * @param $helper
     * @param $dataObject
     * @param array $data
     * @param $interfaceName
     * @return array
     */
    public function beforePopulateWithArray($helper, $dataObject, array $data, $interfaceName)
    {
        if(isset($data['extension_attributes'])) {
            switch ($interfaceName) {
                case '\Magento\Sales\Api\Data\OrderAddressInterface':
                    if ($data['extension_attributes'] instanceof \Magento\Quote\Api\Data\AddressExtensionInterface) {
                        $data['extension_attributes'] = $data['extension_attributes']->__toArray();
                    }
                    break;
                case '\Magento\Customer\Api\Data\AddressInterface':
                    if ($data['extension_attributes'] instanceof \Magento\Quote\Api\Data\AddressExtensionInterface) {
                        $data['extension_attributes'] = $data['extension_attributes']->__toArray();
                        if (isset($data['extension_attributes']['telephone_mobile'])) {
                            $data['telephone_mobile'] = $data['extension_attributes']['telephone_mobile'];
                        }
                    }
                    break;
                case '\Magento\Quote\Api\Data\TotalsInterface':
                    unset($data['extension_attributes']);
                    break;
            }
        }
        return array($dataObject, $data, $interfaceName);
    }

}