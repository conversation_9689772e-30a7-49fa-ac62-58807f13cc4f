<?php
/** @var Xortex\App\Catalog\Block\Widget\Category $block */
$block = $this;
/** @var \Magento\Catalog\Model\ResourceModel\Category\Collection $categories */
$categories = $this->getCategoryCollection();
/** @var Xortex\App\Catalog\Block\Widget\Category\Item $itemRenderer */
$itemRenderer = $block->getLayout()->createBlock("Xortex\App\Catalog\Block\Widget\Category\Item");
if($categories && $categories->count() > 0) {
?>
  <div class="block-content catalog-category xcategory">
    <div class="xcategoryWrapper">
    <?php
    $index = 0;

    $container1 = array();
    $container2 = array();
    $container3 = array();
    foreach($categories as $category) {
      if($index == 0) {
        $container1[] = $category;
      } else if($index < 3) {
        $container2[] = $category;
      } else {
        $container3[] = $category;
      }
      $index++;
    } ?>

    <div class="containerLeft"><?php
    foreach($container1 as $category) {
      $itemRenderer->setCategory($category);
      $itemRenderer->setWidth(1020);
      $itemRenderer->setHeight(740);
      echo $itemRenderer->toHtml();

    }?>
    </div>

    <div class="containerRight"><?php
      foreach($container2 as $category) {
        $itemRenderer->setCategory($category);
        $itemRenderer->setWidth(520);
        $itemRenderer->setHeight(360);
        echo $itemRenderer->toHtml();

      }?>
    </div>

    <div class="containerBelow"><?php
      $j = 1;
      foreach($container3 as $category) {
        $itemRenderer->setCategory($category);
        $itemRenderer->setWidth(500);
        $itemRenderer->setHeight(360);

        if($j%3 == 0){
          $itemRenderer->setWidth(520);
          $itemRenderer->setHeight(360);
        }
        echo $itemRenderer->toHtml();
        $j++;
      }?>
    </div>
  </div>
  </div>
<?php
// end if category count
}
?>


<script>
  require(['jquery'], function(jQuery){
    // window load (important that graphics are loaded)
    jQuery(window).load(function(){
      resizeCatalogCategory();
    });


    // resize
    jQuery(window).resize(function(){
      resizeCatalogCategory();
    });

    // catalog category image height
    function resizeCatalogCategory(){
      if(document.body.parentElement.clientWidth > 768){
        // Image height minus padding
        var calculatedPadding = parseInt(jQuery('.xcategoryWrapper').outerWidth() * 0.015);

        var imgHeight = jQuery('.containerLeft .categoryWrapper img').outerHeight();
        // max height for images in right column
        var imgMaxHeight = (imgHeight - calculatedPadding) / 2;

        // Set max height for containers
        if(imgMaxHeight !== 0) {
          jQuery.each(jQuery('.containerRight .category-item'), function (key, val) {
            jQuery(val).css('max-height', imgMaxHeight + 'px');
          });
        }

        if(imgHeight !== 0){
          jQuery('.containerRight').css('max-height', imgHeight+'px');
        }

        // 8px is the value of the oversized box at the top
        var marginTop = calculatedPadding - 8;
        jQuery('.containerBelow').css('margin-top', marginTop+'px');
      }
    }
  });
</script>
