<?php
/**
 * Category view template
 * @var $block \Xortex\App\Catalog\Block\Category\View
 */
// Beschreibung nur anzeigen falls man nicht im Produkt-Listenansicht Modus ist
if ($block->isContentMode() || $block->isMixedMode()) { ?>
  <div class="category-view-image-container container">
    <?php echo $block->getChildHtml(); ?>
  </div><?php
} else if($block->isCmsMode()) {
  echo $block->getChildHtml();
}