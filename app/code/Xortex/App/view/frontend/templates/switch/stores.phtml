<?php
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php
/**
 * Store switcher template
 *
 * @see \Magento\Store\Block\Store\Switcher
 */
/** @var \Xortex\App\Block\Store\Switcher $block */
$block = $this;
?>
<?php if ($block->isVisible() && count($block->getGroups())>1): ?>
<div class="switcher store switcher-store" id="switcher-store">
    <strong class="label switcher-label"><span><?php /* @escapeNotVerified */ echo __('Select Store') ?></span></strong>
    <div class="actions dropdown options switcher-options">
        <?php foreach ($block->getGroups() as $_group): ?>
        <?php if ($_group->getId() == $block->getCurrentGroupId()): ?>
            <div class="action toggle switcher-trigger"
                 role="button"
                 tabindex="0"
                 data-mage-init='{"dropdown":{}}'
                 data-toggle="dropdown"
                 data-trigger-keypress-button="true"
                 id="switcher-store-trigger">
                <strong>
                    <span><?php echo $block->escapeHtml($_group->getName()) ?></span>
                </strong>
            </div>
        <?php endif; ?>
        <?php endforeach; ?>
        <ul class="dropdown switcher-dropdown" data-target="dropdown">
            <?php foreach ($block->getGroups() as $_group): ?>
            <?php if (!($_group->getId() == $block->getCurrentGroupId())): ?>
                <li class="switcher-option">
                    <a href="#"
                       data-post='<?php /* @escapeNotVerified */ echo $block->getTargetStorePostData($_group->getDefaultStore()); ?>'>
                        <?php echo $block->escapeHtml($_group->getName()) ?>
                    </a>
                </li>
            <?php endif; ?>
            <?php endforeach; ?>
        </ul>
    </div>
</div>
<?php endif; ?>
