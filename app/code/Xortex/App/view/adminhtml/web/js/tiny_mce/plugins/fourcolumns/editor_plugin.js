tinyMCE.addI18n({
  en: {
    fourcolumns:
      {
        insert_column: "Insert four columns"
      }
  }
});

tinyMCE.addI18n({
  de: {
    fourcolumns: {
      insert_column: "Vier Spalten einfügen"
    }
  }
});

(function () {
  tinymce.create('tinymce.plugins.FourColumnsPlugin', {

    /**
     * @param {tinymce.Editor} ed Editor instance that the plugin is initialized in.
     * @param {string} url Absolute URL to where the plugin is located.
     */
    init: function (ed, url) {
      var t = this;
      t.editor = ed;
      //ed.contentCSS = ['test.css'];
      //ed.contentCSS = [ed.settings.magentoPluginsOptions._object.fourcolumns.css];
      ed.addCommand('mceFourColumns', t._insertColumn, t);
      ed.addButton('fourcolumns', {
        title: 'fourcolumns.insert_column',
        cmd: 'mceFourColumns',
        image: url + '/img/icon.gif'
      });
    },

    _insertColumn: function () {
      var ed = this.editor;
      ed.execCommand('mceInsertContent', false, '<div class="fourcolumns row">' +
        '<div class="column-first col-md-3 col-xs-12">Erste Spalte ...</div>' +
        '<div class="column-second col-md-3 col-xs-12">Zweite Spalte ...</div>' +
        '<div class="column-third col-md-3 col-xs-12">Dritte Spalte ...</div>' +
        '<div class="column-fourth col-md-3 col-xs-12">Vierte Spalte ...</div>' +
        '</div>');
    }
  });

  // Register plugin
  tinymce.PluginManager.add('fourcolumns', tinymce.plugins.FourColumnsPlugin);
})();
