<?php
namespace Xortex\App\Block\Store;

use Magento\Directory\Helper\Data;

class Switcher extends \Magento\Store\Block\Switcher {
  /**
   * FR Store codes
   */
  const WEBSITE_FR_CODE = "gfp_fr";

  /**
   * @return mixed
   */
  private function getNotVisibleStores() {
    if (!$this->hasData('not_visible_stores')) {
      $website = $this->_storeManager->getWebsite(self::WEBSITE_FR_CODE);
      $stores = $website->getStores();
      $storeBlacklist = array();
      if ($stores && count($stores) > 0) {
        foreach ($stores as $store) {
          $storeBlacklist[] = $store->getCode();
        }
      }
      $this->setData('not_visible_stores', $storeBlacklist);
    }
    return $this->getData('not_visible_stores');
  }

  /**
   * Sprachenwähler sollte im FR Store nicht sichtbar sein
   * @return bool
   */
  public function isVisible() {
    $storeBlacklist = $this->getNotVisibleStores();
    if (in_array($this->getCurrentStoreCode(), $storeBlacklist)) {
      return false;
    }
    return true;
  }

  /**
   * Liefert alle Bilder
   * @return array
   */
  public function getRawAllStores() {
    if (!$this->hasData('raw_all_stores')) {
      $websites = $this->_storeManager->getWebsites();
      $stores = [];
      foreach ($websites as $website) {
        $websiteStores = $website->getStores();
        foreach ($websiteStores as $store) {
          /* @var $store \Magento\Store\Model\Store */
          if (!$store->isActive()) {
            continue;
          }
          $localeCode = $this->_scopeConfig->getValue(
            Data::XML_PATH_DEFAULT_LOCALE,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $store
          );
          $store->setLocaleCode($localeCode);
          $params = ['_query' => []];
          if (!$this->isStoreInUrl()) {
            $params['_query']['___store'] = $store->getCode();
          }
          $baseUrl = $store->getUrl('', $params);

          $store->setHomeUrl($baseUrl);
          $stores[$store->getGroupId()][$store->getId()] = $store;
        }
      }
      $this->setData('raw_all_stores', $stores);
    }
    return $this->getData('raw_all_stores');
  }

  /**
   * @return \Magento\Store\Model\Store[]
   */
  public function getAllStores() {
    if (!$this->getData('all_stores')) {
      $rawStores = $this->getRawAllStores();
      $allStores = array();
      if(is_array($rawStores)) {
        foreach($rawStores as $groupId => $stores) {
          $allStores = array_merge($allStores, $stores);
        }
      }
      $this->setData('all_stores', $allStores);
    }
    return $this->getData('all_stores');
  }
}