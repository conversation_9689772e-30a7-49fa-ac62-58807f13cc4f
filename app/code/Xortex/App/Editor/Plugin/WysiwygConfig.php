<?php

namespace Xortex\App\Editor\Plugin;

use Magento\Cms\Model\Wysiwyg\Config as Subject;
use Magento\Framework\DataObject;
use Xortex\App\Editor\Model\Column;
use Xortex\App\Editor\Model\FourColumns;

class WysiwygConfig {

  /**
   * @var Column
   */
  private $column;

  /**
   * @var FourColumns
   */
  private $fourcolumns;

  /**
   * @param Column $column
   */
  public function __construct(Column $column, FourColumns $fourColumns) {
    $this->column = $column;
    $this->fourcolumns = $fourColumns;
  }

  public function afterGetConfig($subject, \Magento\Framework\DataObject $config)
  {
    $config->addData([
        'add_directives' => true,
    ]);

    $styleArray = [
        'Button' => 'button',
        'Button - blue' => 'button blue',
        'Button - green' => 'button green',
        'Button - light' => 'button light',
        'Price' => 'priceDiv',
        'Footer - Headline' => 'footer-headline',
        'No padding' => 'no-padding',
        'Gfp-Table' => 'gfptable'
    ];

    $styles = array_map(function($title, $class) {
      return "{$title}={$class} ";
    }, array_keys($styleArray), array_values($styleArray));

    $config->addData(["settings" => ["theme_advanced_styles" => implode("; ", $styles)]]);

    // Spalten
    $textWithBoxPluginSettings = $this->column->getPluginSettings($config);
    $config->addData($textWithBoxPluginSettings);

    $textWithColumnBoxPluginSettings = $this->fourcolumns->getPluginSettings($config);
    $config->addData($textWithColumnBoxPluginSettings);

    return $config;
  }
}