<?php

namespace Xortex\App\Editor\Model;

use Magento\Framework\DataObject;

class FourColumns {

  /**
   * @var string
   */
  const PLUGIN_NAME = 'fourcolumns';

  /**
   * @var \Magento\Framework\View\Asset\Repository
   */
  protected $assetRepo;

  /**
   * @param \Magento\Framework\View\Asset\Repository $assetRepo
   */
  public function __construct(\Magento\Framework\View\Asset\Repository $assetRepo) {
    $this->assetRepo = $assetRepo;
  }

  /**
   * @param \Magento\Framework\DataObject $config
   * @return array
   */
  public function getPluginSettings(DataObject $config): array {
    $plugins   = $config->getData('plugins');
    $plugins[] = [
      'name' => self::PLUGIN_NAME,
      'src' => $this->getPluginJsSrc(),
      'options' => [
        'title' => __('Column 25%'),
        'class' => 'add-four-column plugin',
        'css' => $this->getPluginCssSrc()
      ],
    ];

    return ['plugins' => $plugins];
  }

  /**
   * @return string
   */
  private function getPluginJsSrc(): string {
    return $this->assetRepo->getUrl(
      sprintf('Xortex_App::js/tiny_mce/plugins/%s/editor_plugin.js', self::PLUGIN_NAME)
    );
  }

  /**
   * @return string
   */
  private function getPluginCssSrc(): string {
    return $this->assetRepo->getUrl(
      sprintf('Xortex_App::css/tiny_mce/plugins/%s/content.css', self::PLUGIN_NAME)
    );
  }
}
