<?php

namespace Xortex\App\Setup;

use Magento\Framework\DB\Ddl\Table;
use Magento\Catalog\Api\Data\ProductAttributeInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Eav\Setup\EavSetup;
use Magento\Cms\Model\BlockFactory;
use Magento\Customer\Setup\CustomerSetupFactory;
use Magento\Quote\Setup\QuoteSetupFactory;
use Magento\Sales\Setup\SalesSetupFactory;
use Magento\Customer\Api\AddressMetadataInterface;

class UpgradeData implements UpgradeDataInterface
{
    /**
     * @var EavSetupFactory
     */
    protected $eavSetupFactory;

    /**
     * @var BlockFactory
     */
    protected $blockFactory;

    /**
     * @var CustomerSetupFactory
     */
    protected $customerSetupFactory;

    /**
     * @var QuoteSetupFactory
     */
    protected $quoteSetupFactory;

    /**
     * @var SalesSetupFactory
     */
    protected $salesSetupFactory;


    /**
     * UpgradeData constructor
     *
     * @param EavSetupFactory $eavSetupFactory
     * @param BlockFactory $blockFactory
     */
    public function __construct(
        EavSetupFactory $eavSetupFactory,
        BlockFactory $blockFactory,
        CustomerSetupFactory $customerSetupFactory,
        QuoteSetupFactory $quoteSetupFactory,
        SalesSetupFactory $salesSetupFactory
    )
    {
        $this->eavSetupFactory = $eavSetupFactory;
        $this->blockFactory = $blockFactory;
        $this->customerSetupFactory = $customerSetupFactory;
        $this->quoteSetupFactory = $quoteSetupFactory;
        $this->salesSetupFactory = $salesSetupFactory;
    }

    /**
     * {@inheritdoc}
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();


        if (version_compare($context->getVersion(), '1.0.4', '<')) {
            /** @var \Magento\Eav\Setup\EavSetup $eavSetup */
            $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

            $eavSetup->addAttribute(
                \Magento\Catalog\Model\Category::ENTITY,
                'top', [
                'type' => 'int',
                'label' => 'Topcategory',
                'input' => 'boolean',
                'source' => 'Magento\Eav\Model\Entity\Attribute\Source\Boolean',
                'visible' => true,
                'default' => 0,
                'required' => false,
                'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                'group' => 'General Information',
                'sort_order' => 3,
            ]);
        }

        if (version_compare($context->getVersion(), '1.0.5', '<')) {
            $cmsBlockData = [
                'title' => 'Bestellung Info Text',
                'identifier' => 'xcheckout_billing_info',
                'content' => "Mit einer erfolgreich abgeschlossener Bestellung im Online-Shop erkläre ich mich damit einverstanden, dass die von mir angegebenen personenbezogenen Daten im Checkout ausschließlich durch die GFP Handels GesmbH. zu Zwecken der an mich gerichteten Werbung bzw. für Gewinnspiele per E-Mail genutzt werden dürfen. Ich kann die Nutzung meiner Daten durch die GFP Handels GesmbH. jederzeit mittels Abmeldeformular widersprechen.",
                'is_active' => 1,
                'stores' => [0],
                'sort_order' => 0
            ];

            $this->blockFactory->create()->setData($cmsBlockData)->save();
        }

        if (version_compare($context->getVersion(), '1.0.6', '<')) {
            /** @var EavSetup $eavSetup */
            $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

            $eavSetup->addAttribute(
                \Magento\Catalog\Model\Category::ENTITY,
                'short_description',
                [
                    'type' => 'text',
                    'label' => 'Short Description',
                    'input' => 'textarea',
                    'visible' => true,
                    'default' => null,
                    'required' => false,
                    'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                    'wysiwyg_enabled' => true,
                    'is_html_allowed_on_front' => true,
                    'group' => 'General Information',
                    'sort_order' => 5,
                ]
            );
        }

        if (version_compare($context->getVersion(), '1.0.7', '<')) {
            /** @var EavSetup $eavSetup */
            $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

            // Felder die nicht mehr benötigt werden
            $eavSetup->removeAttribute(
                \Magento\Catalog\Model\Product::ENTITY,
                'top_quality'
            );
            $eavSetup->removeAttribute(
                \Magento\Catalog\Model\Product::ENTITY,
                'top_offer'
            );
            $eavSetup->removeAttribute(
                \Magento\Catalog\Model\Product::ENTITY,
                'made_in_at'
            );
            $eavSetup->removeAttribute(
                \Magento\Catalog\Model\Product::ENTITY,
                'delivery_time'
            );
            $eavSetup->removeAttribute(
                \Magento\Catalog\Model\Product::ENTITY,
                'short_headline'
            );
            $eavSetup->removeAttribute(
                \Magento\Catalog\Model\Product::ENTITY,
                'online_catalog'
            );

            // Datasheet PDF Feld updaten
            $eavSetup->updateAttribute(
                \Magento\Catalog\Model\Product::ENTITY,
                "datasheet_pdf",
                "frontend_input",
                "file"
            );
            $eavSetup->updateAttribute(
                \Magento\Catalog\Model\Product::ENTITY,
                "datasheet_pdf",
                "backend_model",
                "Xortex\App\Catalog\Model\Product\Attribute\Backend\Pdf"
            );
        }

        if (version_compare($context->getVersion(), '1.0.8', '<')) {
            /** @var EavSetup $eavSetup */
            $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

            $eavSetup->addAttribute(
                \Magento\Catalog\Model\Category::ENTITY,
                'header_image',
                [
                    'type' => 'varchar',
                    'label' => 'Header Image',
                    'input' => 'image',
                    'backend' => 'Magento\Catalog\Model\Category\Attribute\Backend\Image',
                    'visible' => true,
                    'default' => null,
                    'required' => false,
                    'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                    'is_html_allowed_on_front' => false,
                    'group' => 'Content',
                    'sort_order' => 4,
                ]
            );
        }

        if (version_compare($context->getVersion(), '1.0.9', '<')) {
            /** @var EavSetup $eavSetup */
            $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

            $eavSetup->addAttribute(
                \Magento\Catalog\Model\Product::ENTITY,
                'subname',
                [
                    'type' => 'text',
                    'label' => 'Subheadline',
                    'input' => 'text',
                    'visible' => true,
                    'default' => null,
                    'required' => false,
                    'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                    'wysiwyg_enabled' => true,
                    'is_html_allowed_on_front' => true,
                    'is_visible_on_front' => true,
                    'group' => 'General',
                    'sort_order' => 5,
                ]
            );
        }

        if (version_compare($context->getVersion(), '1.1.0', '<')) {
            /** @var \Magento\Eav\Setup\EavSetup $eavSetup */
            $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

            $eavSetup->addAttribute(
                \Magento\Catalog\Model\Category::ENTITY,
                'showonproductdetailpage', [
                'type' => 'int',
                'label' => 'Show on product page',
                'input' => 'boolean',
                'source' => 'Magento\Eav\Model\Entity\Attribute\Source\Boolean',
                'visible' => true,
                'default' => 0,
                'required' => false,
                'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                'group' => 'General Information',
                'sort_order' => 4,
            ]);
        }

        if (version_compare($context->getVersion(), '1.1.1', '<')) {
            /** @var \Magento\Eav\Setup\EavSetup $eavSetup */
            $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

            $eavSetup->addAttribute(
                \Magento\Catalog\Model\Product::ENTITY,
                'cms_block_id',
                [
                    'type' => 'int',
                    'label' => 'CMS - Block',
                    'input' => 'select',
                    'source' => 'Magento\Catalog\Model\Category\Attribute\Source\Page',
                    'backend' => '',
                    'visible' => true,
                    'default' => null,
                    'required' => false,
                    'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                    'searchable' => false,
                    'filterable' => false,
                    'comparable' => false,
                    'visible_on_front' => false,
                    'used_in_product_listing' => true,
                    'unique' => false,
                    'group' => 'Content',
                    'comment' => 'CMS Block is shown on detailpage under shortdescription'
                ]
            );
        }

        if (version_compare($context->getVersion(), '1.1.2', '<')) {
            $cmsBlockData = [
                'title' => 'Infotext Zubehörprodukte nach Kauf eines Produktes',
                'identifier' => 'product-related-info',
                'content' => "Folgendes kompatibles Zubehör könnte für Ihr Produkt interessant sein:",
                'is_active' => 1,
                'stores' => [0],
                'sort_order' => 0
            ];

            $this->blockFactory->create()->setData($cmsBlockData)->save();
        }

        if (version_compare($context->getVersion(), '1.1.3', '<')) {
            /** @var \Magento\Eav\Setup\EavSetup $eavSetup */
            $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

            $eavSetup->addAttribute(
                \Magento\Catalog\Model\Product::ENTITY,
                'detaildescription_intro',
                [
                    'type' => 'text',
                    'label' => 'Detailbeschreibung Einleitung',
                    'input' => 'textarea',
                    'visible' => true,
                    'default' => null,
                    'required' => false,
                    'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                    'wysiwyg_enabled' => true,
                    'is_html_allowed_on_front' => true,
                    'is_visible_on_front' => true,
                    'group' => 'Content',
                    'comment' => 'CMS Block is shown on detailpage in tab details',
                    'sort_order' => 5,
                ]
            );
        }

        // Attribut Telefonnummer für die Adressdaten im Checkout hinzufügen
        if (version_compare($context->getVersion(), '1.1.4', '<')) {
            $this->quoteSetupFactory->create()->addAttribute('quote_address', 'telephone_mobile', ['type' => Table::TYPE_TEXT]);
            $this->salesSetupFactory->create()->addAttribute('order_address', 'telephone_mobile', ['type' => Table::TYPE_TEXT]);
        }

        // Upgrade of Version 1.1.5
        if (version_compare($context->getVersion(), '1.1.5', '<')) {
            $attributesInfo = [
                'telephone_mobile' => [
                    'label' => 'Telephone Mobile',
                    'type' => 'varchar',
                    'input' => 'text',
                    'position' => 500,
                    'visible' => true,
                    'required' => false,
                    'user_defined' => true,
                    'system' => 0
                ]
            ];
            $customerSetup = $this->customerSetupFactory->create(['setup' => $setup]);
            $customerAddressEntity = $customerSetup->getEavConfig()->getEntityType('customer_address');

            $attributeSetIdAddress = $customerAddressEntity->getDefaultAttributeSetId();
            foreach ($attributesInfo as $attributeCode => $attributeParams) {
                $customerSetup->addAttribute(AddressMetadataInterface::ENTITY_TYPE_ADDRESS, $attributeCode, $attributeParams);
                $attribute = $customerSetup->getEavConfig()->getAttribute(AddressMetadataInterface::ENTITY_TYPE_ADDRESS, $attributeCode);

                $attribute->addData([
                    'attribute_set_id' => $attributeSetIdAddress,
                    'used_in_forms', ['adminhtml_customer_address', 'customer_address_edit', 'customer_register_address'],
                ]);
                $attribute->save();
                $setup->getConnection()->query("INSERT INTO `eav_entity_attribute` (`entity_attribute_id`, `entity_type_id`, `attribute_set_id`, `attribute_group_id`, `attribute_id`, `sort_order`) VALUES (NULL, '2', '2', '2', '{$attribute->getId()}', '500')");
            }
        }
        $setup->endSetup();
    }
}
