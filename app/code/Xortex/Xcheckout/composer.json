{"name": "xortex/xcheckout", "description": "A Magento 2 module for customer can choose shipping delivery date from frontend order", "require": {"php": "~5.5.0|~5.6.0|~7.0.0", "magento/module-config": "100.0.*", "magento/module-store": "100.0.*", "magento/module-backend": "100.0.*", "magento/framework": "100.0.*"}, "type": "magento2-module", "version": "100.0.0", "license": ["OSL-3.0", "AFL-3.0"], "autoload": {"files": ["registration.php"], "psr-4": {"Xortex\\Xcheckout\\": ""}}}