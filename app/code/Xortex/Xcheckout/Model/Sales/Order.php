<?php

namespace Xortex\Xcheckout\Model\Sales;

/**
 * Order model
 */
class Order extends \Magento\Sales\Model\Order {

    const DELIVERY_DATE = 'delivery_date';
    const DELIVERY_COMMENT = 'delivery_comment';


    /**
     * @return string
     */
    public function getDeliveryDate() {
        return $this->getData(self::DELIVERY_DATE);
    }

    /**
     * @return string
     */
    public function getDeliveryComment() {
        return $this->getData(self::DELIVERY_COMMENT);
    }

    /**
     * @return string
     */
    public function getDeliveryDateWeekNo() {
        if($deliveryDate = $this->getData(self::DELIVERY_DATE)) {
            return date("W", strtotime($deliveryDate));
        }
        return "";
    }
}