var config = {
    "map": {
        "*": {
            'Magento_Checkout/js/model/shipping-save-processor/default': 'Xortex_Xcheckout/js/model/shipping-save-processor/default',
            "Magento_Checkout/js/proceed-to-checkout" : "Xortex_Xcheckout/js/proceed-to-checkout"
        }
    },
    "config": {
      "mixins": {
        'Magento_Checkout/js/model/step-navigator': {
          'Xortex_Xcheckout/js/model/step-navigator-mixin': true
        },
          'Magento_Checkout/js/model/error-processor': {
              'Xortex_Xcheckout/js/model/error-processor-mixin': true
          },
      }
    }
};