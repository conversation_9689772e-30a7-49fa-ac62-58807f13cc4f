define([
    'jquery',
    'ko',
    'uiComponent',
    'moment',
    'mageUtils',
    'Xortex_Xcheckout/js/model/cart-save-processor'
], function (
    $,
    ko,
    Component,
    moment,
    utils,
    cartSaveProcessor
) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Xortex_Xcheckout/delivery-date-block'
        },
        initialize: function () {
            this._super();
            var disabled = window.checkoutConfig.shipping.delivery_date.disabled;
            var noday = window.checkoutConfig.shipping.delivery_date.noday;
            var offsetMinDate = parseInt(window.checkoutConfig.shipping.delivery_date.offsetStart);
            var offsetMaxDate = parseInt(window.checkoutConfig.shipping.delivery_date.offsetEnd);
            var format = window.checkoutConfig.shipping.delivery_date.format;
            var defaultDate = window.checkoutConfig.shipping.delivery_date.defaultDate;

            if(!format) {
                format = 'yy-mm-dd';
            }
            if(!offsetMinDate) {
                offsetMinDate = 0;
            }
            if(!offsetMaxDate) {
                offsetMaxDate = 0;
            }
            var disabledDay = disabled.split(",").map(function(item) {
                return parseInt(item, 10);
            });

            var instance = this;

            ko.bindingHandlers.datepicker = {
                init: function (element, valueAccessor, allBindingsAccessor) {
                    var $el = $(element);
                    //initialize datepicker
                    if(noday) {
                        var options = {
                            minDate: offsetMinDate,
                            maxDate: offsetMaxDate,
                            dateFormat:format,
                            defaultDate: defaultDate,
                            onSelect: function (date) {
                              if (jQuery(".delivery-date-info strong").length > 0) {
                                  jQuery(".delivery-date-info strong").html(instance.getWeekNumberFromDate(date));
                                  cartSaveProcessor.saveCartInformation();
                              }
                            }
                        };
                    } else {
                        var options = {
                            minDate: offsetMinDate,
                            maxDate: offsetMaxDate,
                            dateFormat:format,
                            defaultDate: defaultDate,
                            beforeShowDay: function(date) {
                                var day = date.getDay();
                                if(disabledDay.indexOf(day) > -1) {
                                    return [false];
                                } else {
                                    return [true];
                                }
                            },
                            onSelect: function(date) {
                              if (jQuery(".delivery-date-info strong").length > 0) {
                                jQuery(".delivery-date-info strong").html(instance.getWeekNumberFromDate(date));
                                cartSaveProcessor.saveCartInformation();
                              }
                            }
                        };
                    }

                    $el.datepicker(options);

                    var writable = valueAccessor();
                    if (!ko.isObservable(writable)) {
                        var propWriters = allBindingsAccessor()._ko_property_writers;
                        if (propWriters && propWriters.datepicker) {
                            writable = propWriters.datepicker;
                        } else {
                            return;
                        }
                    }
                    writable($(element).datepicker("getDate"));
                },
                update: function (element, valueAccessor) {
                    var widget = $(element).data("DatePicker");
                    //when the view model is updated, update the widget
                    if (widget) {
                        var date = ko.utils.unwrapObservable(valueAccessor());
                        widget.date(date);
                    }
                }
            };

            return this;
        },
        getWeekNumberFromDate: function(dateString) {
            var m = moment(
                dateString,
                "DDMMYYYY"
            );
            return m.week();
        }
    });
});
