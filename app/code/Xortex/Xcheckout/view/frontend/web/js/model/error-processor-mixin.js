define(
    [
        'j<PERSON>y',
        'mage/utils/wrapper',
        'Magento_Ui/js/model/messageList',
        'Magento_Checkout/js/model/step-navigator'
    ],
    function ($, wrapper,globalMessageList, stepNavigator) {
        'use strict';

        return function (targetModule) {
            if (window.checkoutConfig) {
                targetModule.process = wrapper.wrap(targetModule.process, function (originalAction, response, messageContainer) {
                    var origReturn = originalAction(response, messageContainer);
                    if (response.status == 400 && response.responseJSON.message.includes('%fieldName')) {
                       stepNavigator.navigateTo("shipping");
                    }
                    return origReturn;
                });
            }
            return targetModule;
        };
    }
);
