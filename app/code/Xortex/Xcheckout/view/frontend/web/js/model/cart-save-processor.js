/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

define([
    'jquery',
    'ko',
    'Magento_Checkout/js/model/quote',
    'mage/storage',
    'Magento_Checkout/js/model/error-processor',
    'Magento_Checkout/js/model/url-builder',
    'Xortex_Xcheckout/js/model/cart/full-screen-loader'
], function (
    $,
    ko,
    quote,
    storage,
    errorProcessor,
    urlBuilder,
    fullScreenLoader
) {
    'use strict';

    return {
        /**
         * @return {jQuery.Deferred}
         */
        saveCartInformation: function () {

            var payload;
            payload = {
                'cartInformation': {
                    'delivery_date': $('[name="delivery_date"]').val(),
                    'delivery_comment': $('[name="delivery_comment"]').val()
                }
            };

            var params = {
                cartId: quote.getQuoteId()
            };
            fullScreenLoader.startLoader();

            return storage.post(
                urlBuilder.createUrl("/guest-carts/:cartId/cart-information", params),
                JSON.stringify(payload)
            ).done(
                function (response) {
                    fullScreenLoader.stopLoader();
                }
            ).fail(
                function (response) {
                    errorProcessor.process(response);
                    fullScreenLoader.stopLoader();
                }
            );
        }
    };
});
