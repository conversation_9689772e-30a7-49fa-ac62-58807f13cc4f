ul, li {
  list-style: none;
  padding: 0; }

.grid {
  -webkit-column-count: 3;
  -moz-column-count: 3;
  column-count: 3;
  column-gap: 15px;
  list-style: none;
  margin: 0;
  padding: 0 50px; }
  .grid li {
    margin: 0;
    padding: 0;
    margin-bottom: 10px;
    transition: all .5s ease; }
    .grid li:hover {
      cursor: pointer;
      transform: scale(1.025); }
    .grid li img {
      width: 100%; }

:root {
  --btnColor: #000;
  --selector_bottom: 0px;
  --selector_right: 0px;
  --pos_arrows: 10px; }

.WS-lightbox {
  font-family: 'Montserrat', sans-serif;
  position: fixed;
  display: grid;
  justify-items: center;
  align-items: center;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: white;
  background-color: rgba(0, 0, 0, 0.9); }
  .WS-lightbox .WS-lightbox--bg {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    filter: blur(10px);
    z-index: -1;
    background-size: cover;
    background-position: center; }
  .WS-lightbox [class^="WS-lb-ctrl-"] {
    z-index: 1; }
  .WS-lightbox [class^="WS-lb-ctrl-"], .WS-lightbox [class^="WS-lb-arrow-"] {
    position: absolute;
    display: grid;
    justify-content: center;
    align-content: center;
    height: 30px;
    width: 30px;
    background-color: var(--btnColor);
    box-sizing: border-box;
    color: white;
    opacity: .8;
    transition: all .5s ease; }
    .WS-lightbox [class^="WS-lb-ctrl-"]:hover, .WS-lightbox [class^="WS-lb-arrow-"]:hover {
      cursor: pointer;
      opacity: 1; }
    .WS-lightbox [class^="WS-lb-ctrl-"][class*="-close"], .WS-lightbox [class^="WS-lb-arrow-"][class*="-close"] {
      position: initial;
      top: 0;
      position: absolute;
      right: 0; }
  .WS-lightbox .WS-lb-arrows {
    position: absolute;
    width: 100%;
    bottom: 50%; }
    .WS-lightbox .WS-lb-arrows.outside {
      z-index: 1; }
    .WS-lightbox .WS-lb-arrows [class^="WS-lb-arrow-"] {
      top: calc(50% - 15px);
      z-index: 1; }
      .WS-lightbox .WS-lb-arrows [class^="WS-lb-arrow-"][class*="-left"] {
        left: var(--pos_arrows); }
        .WS-lightbox .WS-lb-arrows [class^="WS-lb-arrow-"][class*="-left"][class*="attached-"] {
          left: calc( (30px + var(--pos_arrows)) * -1); }
      .WS-lightbox .WS-lb-arrows [class^="WS-lb-arrow-"][class*="-right"] {
        right: var(--pos_arrows); }
        .WS-lightbox .WS-lb-arrows [class^="WS-lb-arrow-"][class*="-right"][class*="attached-"] {
          right: calc( (30px + var(--pos_arrows)) * -1); }
      .WS-lightbox .WS-lb-arrows [class^="WS-lb-arrow-"].outside {
        top: calc(50% - 15px - (var(--selector_bottom) * .5)); }
  .WS-lightbox .WS-lightbox--container {
    position: relative;
    line-height: 0;
    transition: all .8s ease; }
    .WS-lightbox .WS-lightbox--container img {
      border: 2px solid white;
      box-sizing: border-box; }
  .WS-lightbox .WS-lightbox--subcontainer {
    position: relative;
    display: grid;
    justify-content: center;
    align-content: center;
    background-color: lightgrey; }
    .WS-lightbox .WS-lightbox--subcontainer[data-zoom-open="1"] {
      overflow: hidden; }
      .WS-lightbox .WS-lightbox--subcontainer[data-zoom-open="1"] img {
        position: absolute; }

[class^="WS-lb-arrow"].shrink-hover:hover {
  transform: scale(0.8); }

[class^="WS-lb-arrow"].zoom-hover:hover {
  transform: scale(1.2); }

[class^="WS-lb-arrow"][class*="-top"].translate-hover:hover {
  transform: translateY(-10px); }

[class^="WS-lb-arrow"][class*="-bottom"].translate-hover:hover {
  transform: translateX(10px); }

[class^="WS-lb-arrow"][class*="-left"] {
  left: var(--pos_arrows); }
  [class^="WS-lb-arrow"][class*="-left"][class*="attached-"] {
    left: calc( (30px + var(--pos_arrows)) * -1); }
  [class^="WS-lb-arrow"][class*="-left"].translate-hover:hover {
    transform: translateX(-10px); }

[class^="WS-lb-arrow"][class*="-right"] {
  right: var(--pos_arrows); }
  [class^="WS-lb-arrow"][class*="-right"][class*="attached-"] {
    right: calc( (30px + var(--pos_arrows)) * -1); }
  [class^="WS-lb-arrow"][class*="-right"].translate-hover:hover {
    transform: translateX(10px); }

.WS-lightbox {
  display: none; }
