<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse - initial contents
 */
namespace Payolution\Payments\Test\Integration\Model;

/**
 * Class PayolutionElvTest
 * @package Payolution\Payments\Test\Integration\Model
 */
class PayolutionElvTestCase extends PayolutionAbstractApiTestCase
{
    /**
     * @var string
     */
    protected $_preCheckApiType = 'Payolution\Payments\Model\Api\PreCheck\Elv';

    /**
     * @var String
     */
    protected $_preAuthApiType = 'Payolution\Payments\Model\Api\PreAuth\Elv';

    /**
     * @var string
     */
    protected $_captureApiType  = 'Payolution\Payments\Model\Api\Capture\Elv';

    /**
     * @var string
     */
    protected $_methodName = \Payolution\Payments\Helper\Data::ELV_PAYMENT_CODE;

    /**
     * {@inheritdoc}
     */
    public function setUp()
    {
        parent::setUp();
        $this->_formDataPreCheck = array(
            'dob'           => '1978-10-01',
            'email'         => '<EMAIL>',
        );

        $this->_formDataPreAuth = array(
            'dob'           => '1978-10-01',
            'email'         => '<EMAIL>',
            'iban'          => '**********************',
            'account_holder' => 'test',
        );
    }
}
