<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse - initial contents
 */
namespace Payolution\Payments\Test\Integration\Model;

/**
 * Class PayolutionElvReversalTest
 * @package Payolution\Payments\Test\Integration\Model
 */
class PayolutionElvReversalTest extends PayolutionElvTestCase
{
    /**
     * @var string
     */
    protected $_cancelApiType   = 'Payolution\Payments\Model\Api\Cancel\Elv';

    /**
     * Test perform a full Order without Capture and Cancel it again
     */
    public function testReversal()
    {
        $this->performPreAuth();
        foreach ($this->_preAuthIds[$this->_methodName] as $preAuthId) {
            $this->_preAuthOrder->setPayolutionUniqueId($preAuthId);
            /** @var \Payolution\Payments\Model\Api\Cancel $cancelApi */
            $cancelApi = $this->_apiFactory->create(
                $this->_cancelApiType,
                [
                    'order' => $this->_preAuthOrder,
                ]
            )->run();

            $this->assertNotEmpty($cancelApi->isSuccess(), 'Elv reversal request did not return "ACK".');
        }
    }
}
