<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * <PERSON> <<EMAIL>> - initial contents
 */
namespace Payolution\Payments\Model\Logger;

use Monolog\Logger;
use Monolog\Formatter\LineFormatter;
use Magento\Framework\Filesystem\DriverInterface;

class Handler extends \Magento\Framework\Logger\Handler\Base
{
    /**
     * Log level
     *
     * @var int
     */
    protected $loggerType = Logger::DEBUG;

    /**
     * Logfile
     *
     * @var string
     */
    protected $payolutionLog = '/var/log/payolution/api.log';

    /**
     * @param DriverInterface $filesystem
     * @param null $filePath
     */
    public function __construct(DriverInterface $filesystem, $filePath = null)
    {
        $this->fileName = $this->_getPayolutionLogFileName();
        parent::__construct($filesystem, $filePath);
        $this->setFormatter(new LineFormatter(null, null, true, true));
    }

    /**
     * Retrieve dynamic payolution log file name
     *
     * @return mixed
     */
    protected function _getPayolutionLogFileName()
    {
        return str_replace(
            'api',
            'api_' . date('Ymd', time()),
            $this->payolutionLog
        );
    }
}
