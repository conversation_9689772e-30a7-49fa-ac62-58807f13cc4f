<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * <PERSON> <<EMAIL>> - initial contents
 */
namespace Payolution\Payments\Model\Config\Source;

use \Payolution\Payments\Helper\ConfigHelper as PayolutionHelper;

/**
 * Class TransactionMode
 *
 * @package Payolution\Payments\Model\Config\Source
 */
class TransactionMode implements \Magento\Framework\Option\ArrayInterface
{
    /**
     * Options getter
     *
     * @return array
     */
    public function toOptionArray()
    {
        return array(
            array(
                'value' => PayolutionHelper::CONNECTOR_TEST_MODE,
                'label' => __('Test'),
            ),
            array(
                'value' => PayolutionHelper::CONNECTOR_LIVE_MODE,
                'label' => __('Live'),
            ),
        );
    }

    /**
     * Get options in "key-value" format
     *
     * @return array
     */
    public function toArray()
    {
        return array(
            PayolutionHelper::CONNECTOR_TEST_MODE => __('Test'),
            PayolutionHelper::CONNECTOR_LIVE_MODE => __('Live'),
        );
    }
}
