<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse - initial contents
 */
namespace Payolution\Payments\Model\Api;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Sales\Model\Order;
use Payolution\Payments\Helper\ConfigHelper;
use Payolution\Payments\Helper\Data;
use Payolution\Payments\Model\Logger;

/**
 * Class Refund
 * @package Payolution\Payments\Model\Api
 */
class Refund extends AbstractApi
{
    /**
     * @var string
     */
    protected $_code = 'VA.RF';

    /**
     * @var string
     */
    protected $_responseType = 'Payolution\Payments\Model\Api\Response\Refund';

    /**
     * @var CustomerRepositoryInterface
     */
    protected $_customerRepository;

    /**
     * @var float
     */
    protected $_refundAmount = 0;

    /**
     * @param \Magento\Framework\ObjectManagerInterface $objectManager
     * @param \Magento\Framework\HTTP\LaminasClientFactory $httpClientFactory
     * @param \Magento\Framework\App\Helper\Context $context
     * @param Order $order
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param Logger $payolutionLogger
     * @param Data $helper
     * @param ConfigHelper $configHelper
     * @param CustomerRepositoryInterface $customerRepository
     * @param float $amount
     */
    public function __construct(
        \Magento\Framework\ObjectManagerInterface $objectManager,
        \Magento\Framework\HTTP\LaminasClientFactory $httpClientFactory,
        \Magento\Framework\App\Helper\Context $context,
        Order $order,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        Logger $payolutionLogger,
        Data $helper,
        ConfigHelper $configHelper,
        CustomerRepositoryInterface $customerRepository,
        $amount
    ) {
        $this->_transferObject      = $order;
        $this->_customerRepository  = $customerRepository;
        $this->_refundAmount        = $amount;

        return parent::__construct(
            $objectManager,
            $httpClientFactory,
            $context,
            $storeManager,
            $payolutionLogger,
            $helper,
            $configHelper
        );
    }

    /**
     * Retrieve channel id
     *
     * @return string
     */
    protected function _getChannel()
    {
        if ($this->_getCustomer() && $this->_helper->isCustomerB2B($this->_getCustomer())) {
            $this->_channel = 'invoice_b2b';
        }
        return parent::_getChannel();
    }

    /**
     * @return \Magento\Customer\Model\Data\Customer|null
     */
    protected function _getCustomer()
    {
        $customerId = $this->_getTransferObject()->getCustomerId();
        return $customerId
            ? $this->_customerRepository->getById($customerId)
            : null;
    }

    /**
     * Check if valid order is provided
     *
     * @return $this
     */
    protected function _validateRequestParams()
    {
        parent::_validateRequestParams();
        if (!$this->_transferObject instanceof Order) {
            throw new \InvalidArgumentException(
                __('Valid order missing for refund request!')
            );
        }
        return $this;
    }

    /**
     * Set request credentials
     *
     * @return $this
     */
    protected function _setRequestGeneralData()
    {
        parent::_setRequestGeneralData();
        $xml = $this->_getXMLTemplate();
        $xml->Transaction->Identification->ReferenceID      = $this->_getTransferObject()->getPayolutionUniqueId();
        $xml->Transaction->Identification->TransactionID    = $this->_getTransferObject()->getIncrementId();

        // set refund amount
        $xml->Transaction->Payment->Presentation->Amount    = sprintf('%01.2f', $this->_refundAmount);
        return $this;
    }

    /**
     * Set Criterion request data
     *
     * @return $this
     */
    protected function _setRequestCriterionData()
    {
        $xml = $this->_getXMLTemplate();
        $criterion = $xml->Transaction->Analysis->addChild('Criterion', 'Magento');
        $criterion->addAttribute('name', 'PAYOLUTION_REQUEST_SYSTEM_VENDOR');
        $criterion = $xml->Transaction->Analysis->addChild('Criterion', 'CustomSystemVersion');
        $criterion->addAttribute('name', 'PAYOLUTION_REQUEST_SYSTEM_VERSION');
        $criterion = $xml->Transaction->Analysis->addChild('Criterion', 'Webshop');
        $criterion->addAttribute('name', 'PAYOLUTION_REQUEST_SYSTEM_TYPE');
        $criterion = $xml->Transaction->Analysis->addChild('Criterion', 'Payolution_Payments');
        $criterion->addAttribute('name', 'PAYOLUTION_MODULE_NAME');
        $criterion = $xml->Transaction->Analysis->addChild('Criterion', $this->_configHelper->getModuleVersion());
        $criterion->addAttribute('name', 'PAYOLUTION_MODULE_VERSION');

        // fetch tax amount
//        $totals = $this->_getTransferObject()->getTotals();
//        $taxAmount = !empty($totals['tax']) ? $totals['tax']->getValue() : 0;
        //FIXME how retrieve tax amount for partial refunds when amount is combination of products / shipping with different tax rates
        $taxAmount = 0;
        $criterion = $xml->Transaction->Analysis->addChild('Criterion', $taxAmount);
        $criterion->addAttribute('name', 'PAYOLUTION_TAX_AMOUNT');

        return $this;
    }

    /**
     * Xml for capture
     *
     * @return \SimpleXmlElement
     */
    protected function _getXMLTemplate()
    {
        if (!$this->_xmlTemplate) {
            $xml = <<<XML
<?xml version="1.0" encoding="UTF-8"?>
<Request version="1.0">
  <Header>
    <Security sender="" />
  </Header>
  <Transaction channel="" mode="">
    <User pwd="" login="" />
    <Identification>
      <TransactionID></TransactionID>
      <ReferenceID></ReferenceID>
    </Identification>
    <Payment code="VA.RF">
      <Presentation>
        <Amount></Amount>
        <Usage></Usage>
        <Currency></Currency>
      </Presentation>
    </Payment>
    <Analysis></Analysis>
  </Transaction>
</Request>
XML;
            $this->_xmlTemplate = new \SimpleXMLElement($xml);
        }
        return $this->_xmlTemplate;
    }
}
