<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse <<EMAIL>> - initial contents
 */
namespace Payolution\Payments\Model\Api\PreCheck;

/**
 * Class Elv
 * @package Payolution\Payments\Model\Api\PreCheck
 */
class Elv extends \Payolution\Payments\Model\Api\PreCheck
{
    /**
     * @var string
     */
    protected $_brand = 'PAYOLUTION_ELV';

    /**
     * @var string
     */
    protected $_channel = 'elv';

    /**
     * Set account data
     *
     * @return $this
     */
    protected function _setRequestCriterionData()
    {
        parent::_setRequestCriterionData();
        $xml = $this->_getXMLTemplate();
        if ($accountHolder = $this->_getFormData('account_holder')) {
            $criterion = $xml->Transaction->Analysis->addChild('Criterion', $accountHolder);
            $criterion->addAttribute('name', 'PAYOLUTION_ACCOUNT_HOLDER');
        }
        if ($iban = $this->_getFormData('iban')) {
            $criterion = $xml->Transaction->Analysis->addChild('Criterion', $iban);
            $criterion->addAttribute('name', 'PAYOLUTION_ACCOUNT_IBAN');
        }

        return $this;
    }
}
