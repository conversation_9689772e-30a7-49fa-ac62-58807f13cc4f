<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse <<EMAIL>> - initial contents
 */
namespace Payolution\Payments\Model\Api\PreAuth;

/**
 * Class Instalment
 * @package Payolution\Payments\Model\Api\PreAuth
 */
class Instalment extends \Payolution\Payments\Model\Api\PreAuth
{
    /**
     * @var string
     */
    protected $_brand = 'PAYOLUTION_INS';

    /**
     * @var string
     */
    protected $_channel = 'instalment';

    /**
     * Set calculation / account data
     *
     * @return $this
     */
    protected function _setRequestCriterionData()
    {
        parent::_setRequestCriterionData();
        $xml = $this->_getXMLTemplate();

        $criterion = $xml->Transaction->Analysis->addChild('Criterion', $this->_getFormData('calculation_id'));
        $criterion->addAttribute('name', 'PAYOLUTION_CALCULATION_ID');
        $criterion = $xml->Transaction->Analysis->addChild('Criterion', $this->_getFormData('duration'));
        $criterion->addAttribute('name', 'PAYOLUTION_DURATION');

        if ($accountHolder = $this->_getFormData('account_holder')) {
            $criterion = $xml->Transaction->Analysis->addChild('Criterion', $accountHolder);
            $criterion->addAttribute('name', 'PAYOLUTION_ACCOUNT_HOLDER');
        }
        if ($iban = $this->_getFormData('iban')) {
            $criterion = $xml->Transaction->Analysis->addChild('Criterion', $iban);
            $criterion->addAttribute('name', 'PAYOLUTION_ACCOUNT_IBAN');
        }

        return $this;
    }

    /**
     * Before converting to xml mask sensitive data: iban / bic
     *
     * @return mixed
     */
    public function __toString()
    {
        $xml = $this->_getXMLTemplate();

        $fieldsToConceal = [
            'PAYOLUTION_ACCOUNT_IBAN',
        ];

        $key = 0;
        foreach ($xml->Transaction->Analysis->Criterion as $criterion) {
            if (in_array($criterion['name'], $fieldsToConceal)) {
                $value = (string)$criterion;
                $count = (int) (strlen($value) - strlen($value) / 3);
                $pad = str_pad('', $count, 'x');
                $newValue = preg_replace('/.{' . $count . '}$/', $pad, $value);
                $xml->Transaction->Analysis->Criterion[$key] = $newValue;
            }
            $key++;
        }
        return $this->_getXMLTemplate()->asXML();
    }
}
