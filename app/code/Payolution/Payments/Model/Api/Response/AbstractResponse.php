<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse - initial contents
 */
namespace Payolution\Payments\Model\Api\Response;

use Magento\Framework\Simplexml\Element;

/**
 * Class AbstractResponse
 * @package Payolution\Payments\Model\Api\Response
 */
abstract class AbstractResponse
{
    const ACK = 'ACK';

    /**
     * @var \Payolution\Payments\Model\Api\AbstractApi
     */
    protected $_request;

    /**
     * @var Element
     */
    protected $_response;

    /**
     * @var \Laminas\Http\Response
     */
    protected $_originalResponse;

    /**
     * Set response from api request
     *
     * @param $response
     * @throws \Exception
     */
    public function setResponse($response)
    {
        /* @var $response \Laminas\Http\Response */
        if (!$response || !$response instanceof \Laminas\Http\Response ) {
            return;
        }

        if (!$response->isSuccess()) {
            throw (
                new \Exception('Request ('. $this->_getRequestClassName() .') returns: ' . $response->getStatus())
            );
        }

        try {
            $this->_originalResponse = $response;
            $this->_response = new Element($response->getBody());
        } catch (\Exception $e) {
            // TODO handle invalid xml response
        }

        if (!$this->isSuccess()) {
            throw new \Laminas\Http\Client\Exception\RuntimeException($this->getErrorMessage());
        }
    }

    /**
     * Set request model
     *
     * @param \Payolution\Payments\Model\Api\AbstractApi $request
     * @return $this
     */
    public function setRequest(\Payolution\Payments\Model\Api\AbstractApi $request)
    {
        $this->_request = $request;
        return $this;
    }

    /**
     * Retrieve request name
     *
     * @return string
     */
    protected function _getRequestClassName()
    {
        return $this->_request ? get_class($this->_request) : '';
    }

    /**
     * Retrieve xml response
     *
     * @return mixed
     */
    public function getResponseXml()
    {
        return $this->_response->asXML();
    }

    /**
     * Check if request was successful
     *
     * @return bool
     */
    public function isSuccess()
    {
        return self::ACK == (string)$this->_response->descend('Transaction/Processing/Result');
    }

    /**
     * Retrieve unique id from response
     *
     * @return null|string
     */
    public function getUniqueId()
    {
        // FIMXE use descend method
        return $this->_getXpathValue("//Transaction//Identification//UniqueID");
    }

    /**
     * Retrieve payment reference
     *
     * @return null|string
     */
    public function getPaymentReference()
    {
        return (string)$this->_response->descend("Transaction/Processing/ConnectorDetails/Result@name=PaymentReference");
    }

    /**
     * Retrieve status codes from response
     *
     * @return \Magento\Framework\DataObject
     */
    public function getError()
    {
        try {
            $errorCode = $this->_response->descend('Transaction/Processing/Return');
            $status = [
                'status'    => (string)$this->_response->descend('Transaction/Processing/Status'),
                'reason'    => (string)$this->_response->descend('Transaction/Processing/Reason'),
                'return'    => (string)$this->_response->descend('Transaction/Processing/Return'),
                'error_code' => (string)($errorCode ? $errorCode->getAttribute('code') : ''),
            ];
        } catch (\Exception $e) {
            $status = ['error_code' => $e->getMessage()];
        }
        return new \Magento\Framework\DataObject($status);
    }

    /**
     * Fetch error message
     *
     * @return string
     */
    public function getErrorMessage()
    {
        $error = $this->getError();
        $errorMessage = [];
        if ($error->getData('error_code')) {
            $errorMessage[] = 'code: ' . $error->getData('error_code');
        }
        if ($error->getData('status')) {
            $errorMessage[] = 'status: ' . $error->getData('status');
        }
        if ($error->getData('return')) {
            $errorMessage[] = 'return: ' . $error->getData('return');
        }
        if ($error->getData('reason')) {
            $errorMessage[] = 'reason: ' . $error->getData('reason');
        }
        return nl2br(implode("\n", $errorMessage));
    }

    /**
     * Retrieve xpath value
     *
     * @param $xpath
     * @return array|null|string
     */
    protected function _getXpathValue($xpath)
    {
        $value = null;
        try {
            $tmp = $this->_response->xpath($xpath);
            if (is_array($tmp) && (1 == count($tmp))) {
                $value = (string)current($tmp);
            } elseif (is_array($tmp) && (1 < count($tmp))) {
                $value = $tmp;
            }
        } catch (\Exception $e) {
            // log..
        }
        return $value;
    }

    /**
     * Convert to xml - for logging purposes
     *
     * @return mixed
     */
    public function __toString()
    {
        return $this->getResponseXML();
    }
}
