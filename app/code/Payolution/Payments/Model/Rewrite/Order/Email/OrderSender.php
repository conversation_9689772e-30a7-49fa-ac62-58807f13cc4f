<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse - initial contents
 */
namespace Payolution\Payments\Model\Rewrite\Order\Email;

use Magento\Payment\Helper\Data as PaymentHelper;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\Order\Email\Container\OrderIdentity;
use Magento\Sales\Model\Order\Email\Container\Template;
use Magento\Sales\Model\ResourceModel\Order as OrderResource;
use Magento\Sales\Model\Order\Address\Renderer;
use Magento\Framework\Event\ManagerInterface;
use Magento\Sales\Model\Order\Email\SenderBuilder;
use Payolution\Payments\Helper\Data as PayolutionHelper;
use Payolution\Payments\Helper\ConfigHelper;

class OrderSender extends \Magento\Sales\Model\Order\Email\Sender\OrderSender
{
    /**
     * @var ConfigHelper
     */
    protected $_configHelper;

    /**
     * @var PayolutionHelper
     */
    protected $_payolutionHelper;

    /**
     * @param Template $templateContainer
     * @param OrderIdentity $identityContainer
     * @param Order\Email\SenderBuilderFactory $senderBuilderFactory
     * @param \Psr\Log\LoggerInterface $logger
     * @param PaymentHelper $paymentHelper
     * @param OrderResource $orderResource
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $globalConfig
     * @param Renderer $addressRenderer
     * @param ManagerInterface $eventManager
     * @param ConfigHelper $configHelper
     * @param PayolutionHelper $payolutionHelper
     */
    public function __construct(
        Template $templateContainer,
        OrderIdentity $identityContainer,
        \Magento\Sales\Model\Order\Email\SenderBuilderFactory $senderBuilderFactory,
        \Psr\Log\LoggerInterface $logger,
        Renderer $addressRenderer,
        PaymentHelper $paymentHelper,
        OrderResource $orderResource,
        \Magento\Framework\App\Config\ScopeConfigInterface $globalConfig,
        ManagerInterface $eventManager,
        ConfigHelper $configHelper,
        PayolutionHelper $payolutionHelper
    ) {
        parent::__construct(
            $templateContainer,
            $identityContainer,
            $senderBuilderFactory,
            $logger,
            $addressRenderer,
            $paymentHelper,
            $orderResource,
            $globalConfig,
            $eventManager
        );

        $this->_configHelper        = $configHelper;
        $this->_payolutionHelper    = $payolutionHelper;
    }

    /**
     * @param Order $order
     * @return bool
     */
    protected function checkAndSend(Order $order)
    {
        $this->identityContainer->setStore($order->getStore());
        if (!$this->identityContainer->isEnabled()) {
            return false;
        }
        $this->prepareTemplate($order);
        // $this->_addPayolutionCopyTo($order);

        /** @var SenderBuilder $sender */
        $sender = $this->getSender();

        try {
            $sender->send();
            $sender->sendCopyTo();
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }

        return true;
    }

    /**
     * Send mail to invoices-[merchantId]@payolution.com if payolution payment method was used
     */
    protected function _addPayolutionCopyTo(Order $order)
    {
        if ($this->_isPayolutionPaymentUsed($order)) {
            // send email to invoice-[merchant]@payolution.com
            $this->identityContainer->addPayolutionEmailCopyTo($this->_configHelper->getOrderEmailBcc());
        }
    }


    /**
     * Check if payolution payment method was used for given order
     *
     * @param Order $order
     * @return bool
     */
    protected function _isPayolutionPaymentUsed(Order $order)
    {
        foreach ($order->getAllPayments() as $paymentMethod) {
            if (in_array($paymentMethod->getMethod(), $this->_payolutionHelper->getAvailablePaymentMethods())) {
                return true;
            }
        }
        return false;
    }
}
