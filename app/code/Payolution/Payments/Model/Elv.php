<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse - initial contents
 */
namespace Payolution\Payments\Model;

use Payolution\Payments\Helper\Data;
use Payolution\Payments\Helper\PreCheck as PrecheckHelper;
use Magento\Framework\App\ObjectManager;

/**
 * Class Elv
 * Payolution Elv Payment
 * @package Payolution\Payments\Model
 */
class Elv extends AbstractMethod
{
    /**
     * @var String
     */
    protected $_preAuthApiType = 'Payolution\Payments\Model\Api\PreAuth\Elv';

    /**
     * @var string
     */
    protected $_captureApiType  = 'Payolution\Payments\Model\Api\Capture\Elv';

    /**
     * @var string
     */
    protected $_cancelApiType   = 'Payolution\Payments\Model\Api\Cancel\Elv';

    /**
     * @var string
     */
    protected $_refundApiType   = 'Payolution\Payments\Model\Api\Refund\Elv';

    /**
     * PreCheck checkout session key
     *
     * @var string
     */
    protected $_precheckSessionKey = PrecheckHelper::PRECHECK_ELV;

    /**
     * @var array
     */
    protected $_payolutionAdditionalData = [
        'account_holder',
        'iban',
        'dob',
        'email',
    ];

    /**
     * Payment method code
     *
     * @var string
     */
    protected $_code = Data::ELV_PAYMENT_CODE;

    /**
     * @var string
     */
    protected $_formBlockType = 'Payolution\Payments\Block\Form\Elv';

    /**
     * @var string
     */
    protected $_infoBlockType = 'Payolution\Payments\Block\Info\Elv';

    /**
     * Availability option
     *
     * @var bool
     */
    protected $_isOffline = true;

    /**
     * Elv only available for B2C customer
     *
     * @param \Magento\Quote\Api\Data\CartInterface $quote
     * @return bool
     */
    public function isAvailable(\Magento\Quote\Api\Data\CartInterface $quote = null)
    {
        $isAvailable = parent::isAvailable($quote);
        if ($isAvailable) {
            $helper = ObjectManager::getInstance()->create('Payolution\\Payments\\Helper\\Data');
            $isAvailable = !$helper->isCustomerB2B($quote->getCustomer());
        }
        return $isAvailable;
    }

    /**
     * Retrieve form data for preauth request
     *
     * @return array
     */
    protected function getPaymentFormData()
    {
        return [
            'email'         => $this->getCustomerEmail(),
            'dob'           => $this->getCustomerDob(),
            'iban'          => $this->getIban(),
            'account_holder' => $this->getAccountHolder(),
        ];
    }

    /**
     * Encrypt account holder value
     *
     * @param $value
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function setAccountHolder($value)
    {
        return $this->getInfoInstance()->encrypt($value);
    }

    /**
     * Retrieve account holder
     *
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function getAccountHolder()
    {
        $value = '';
        $data = $this->getInfoInstance()->getAdditionalData();
        if (!empty($data['account_holder'])) {
            $value = (string)$this->getInfoInstance()->decrypt($data['account_holder']);
        }
        return $value;
    }

    /**
     * Encrypt iban value
     *
     * @param $value
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function setIban($value)
    {
        return $this->getInfoInstance()->encrypt($value);
    }

    /**
     * Retrieve iban
     *
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function getIban()
    {
        $value = '';
        $data = $this->getInfoInstance()->getAdditionalData();
        if (!empty($data['iban'])) {
            $value = (string)$this->getInfoInstance()->decrypt($data['iban']);
        }
        return $value;
    }
}
