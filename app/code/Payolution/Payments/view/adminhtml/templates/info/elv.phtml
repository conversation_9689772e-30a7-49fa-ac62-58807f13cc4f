<?php
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/**
 * @var $block \Payolution\Payments\Block\Info\Elv
 */
$specificInfo = $block->getSpecificInformation();
?>
<?php echo $block->escapeHtml($block->getMethod()->getTitle()); ?>

<?php if ($specificInfo): ?>
    <table class="data-table admin__table-secondary">
        <?php foreach ($specificInfo as $label => $value):?>
            <tr>
                <th><?php echo $block->escapeHtml($label)?>:</th>
                <td>
                    <?php /* @noEscape */ echo nl2br($block->escapeHtml(implode("\n", $block->getValueAsArray($value, true))));?>
                </td>
            </tr>
        <?php endforeach; ?>
    </table>
<?php endif;?>

<?php echo $block->getChildHtml()?>
