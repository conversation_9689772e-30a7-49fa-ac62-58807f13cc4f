<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse - initial contents
 */
// @codingStandardsIgnoreFile

/**
 * @var $block \Payolution\Payments\Block\Info\Elv
 */
$specificInfo = $block->getSpecificInformation();
$title = $block->escapeHtml($block->getMethod()->getTitle());
?>
    <dl class="payment-method">
        <dt class="title"><?php /* @noEscape */ echo $title; ?></dt>
        <?php if ($specificInfo):?>
            <dd class="content">
                <dl class="payolution-details">
                    <dt></dt>
                    <?php foreach ($specificInfo as $label => $value):?>
                        <dd>
                            <b><?php echo $block->escapeHtml($label)?></b><br/>
                            <?php /* @noEscape */ echo nl2br($block->escapeHtml(implode("\n", $block->getValueAsArray($value, true))));?>
                        </dd>
                    <?php endforeach; ?>
                </dl>
            </dd>
        <?php endif;?>
    </dl>
<?php echo $block->getChildHtml()?>