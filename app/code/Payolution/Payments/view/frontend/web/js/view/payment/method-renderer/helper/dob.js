/*browser:true*/
/*global define*/
define(
    [
        'mage/translate'
    ],
    function ($t) {
        'use strict';

        return {
            /**
             * Fetch customer dob from customerData
             *
             * @returns {Boolean|Object}
             */
            getCustomerDob: function () {
                var customerData = window.customerData || {}, dob;
                try {
                    dob = customerData.hasOwnProperty('dob') ? customerData.dob : '';
                    var match = dob.match(/(\d{4})-(\d{2})-(\d{2})/);
                    if (match.length == 4) {
                        return {
                            day: match[3],
                            month: match[2],
                            year: match[1]
                        }
                    }
                } catch (e) {
                }
                return false;
            },

            /**
             * Check if customer age is above 18
             *
             * @param year
             * @param month
             * @param day
             * @returns {boolean}
             */
            hasMinAge: function(year, month, day) {
                var dob = this.isValidDate(year, (month - 1), day);
                if (!dob) {
                    return false;
                }
                var today = new Date(),
                    age = today.getFullYear() - dob.getFullYear(),
                    m = today.getMonth() - dob.getMonth();
                if ((m < 0) || ((m == 0) && today.getDate() < dob.getDate())) {
                    age--;
                }
                return age >= 18;
            },

            /**
             * Check if provided date is valid
             *
             * @param year
             * @param month
             * @param day
             * @returns {boolean|Date}
             */
            isValidDate: function(year, month, day) {
                var date = new Date(year, month, day);
                if ((date.getMonth() == month)
                    && (date.getFullYear() == year)
                    && (date.getDate() == day)
                ) {
                    return date;
                }
                return false;
            },

            /**
             * Fetch possible days
             *
             * @returns {Array}
             */
            getDobDayValues: function () {
                var numberOfDays = 31, days = [];
                for (var i = 0; i < numberOfDays; i++) {
                    days.push({ value: (i + 1) });
                }
                return days;
            },

            /**
             * Fetch months
             *
             * @returns {Array}
             */
            getDobMonthValues: function () {
                return [
                    { value: 1, label: $t('JANUARY') },
                    { value: 2, label: $t('FEBRUARY') },
                    { value: 3, label: $t('MARCH') },
                    { value: 4, label: $t('APRIL') },
                    { value: 5, label: $t('MAY') },
                    { value: 6, label: $t('JUNE') },
                    { value: 7, label: $t('JULY') },
                    { value: 8, label: $t('AUGUST') },
                    { value: 9, label: $t('SEPTEMBER') },
                    { value: 10, label: $t('OCTOBER') },
                    { value: 11, label: $t('NOVEMBER') },
                    { value: 12, label: $t('DECEMBER')}
                ];
            },

            /**
             * Fetch possible years of birth
             *
             * @returns {Array}
             */
            getDobYearValues: function () {
                var now = new Date(), years = [], startYear = now.getFullYear() - 18;
                for (var i = 0; i < 100; i++) {
                    years.push({ value: (startYear - i) });
                }
                return years;
            }
        }
    }
);