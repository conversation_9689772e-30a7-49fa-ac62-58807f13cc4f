/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 */
/*browser:true*/
/*global define*/
define(
    [
        'Payolution_Payments/js/view/payment/method-renderer/default',
        'ko',
        'underscore',
        'jquery',
        'payolution/dobHelper',
        'mage/translate',
        'validate/iban',
        'mage/template',
        'Magento_Catalog/js/price-utils',
        'Magento_Checkout/js/model/quote'
    ],
    function (Component, ko, _, $, helper, $t, ibanValidator, mageTemplate, utils, quote) {
        'use strict';

        var payolutionConfig = window.checkoutConfig.payolution || {};

        return Component.extend({
            defaults: {
                template: 'Payolution_Payments/payment/instalment',
                accountHolder: '',
                duration: '',
                iban: '',
                calculationId: ''
            },
            accountDetailsForCountryCodes: [],
            precheckUrl: payolutionConfig.instalmentPrecheckUrl || '',
            selectedRate: null,
            maxVisibleRates: 2,
            quoteGrandTotal: null,
            instalmentDetails: [],
            instalmentButtonTemplate:
                '<% _.each(data.buttons, function(button, index) { %>' +
                    '<li class="">' +
                        '<button class="instalment-rate action-primary" type="button" data-val="<%= index %>">' +
                            '<span><%= button.text %></span>' +
                        '</button>' +
                    '</li>' +
                '<% }); %>',
            ratePlanTemplate:
                '<% _.each(data.items, function(item) { %>' +
                    '<li class="rate<%= item.cssClass %>">' +
                        '<%= item.text %>' +
                    '</li>' +
                '<% }); %>',

            /**
             * Add validation rules for iban
             * @returns {Object}
             */
            initialize: function() {
                this._super();
                $.validator.addMethod(
                    'validate-iban', function (value) {
                        return false !== ibanValidator.isValid(value);
                    }, $t('IBAN_ERROR_MESSAGE')
                );

                return this;
            },

            /**
             * Set observables
             */
            initObservable: function () {
                // set quote grand total
                var grandTotal = quote.totals()['grand_total'];
                this.quoteGrandTotal = this.getLocalizedAmount(grandTotal);

                var accountDetailsForCountryCodes = payolutionConfig.accountDetailsForCountryCodes || '';
                if (/,/.test(accountDetailsForCountryCodes)) {
                    this.accountDetailsForCountryCodes = payolutionConfig.accountDetailsForCountryCodes.split(',');
                }

                this._super()
                    .observe([
                        'showAllInstalments',
                        'instalmentButtons',
                        'selectedRate',
                        'totalAmount',
                        'interestRate',
                        'effectiveRate',
                        'quoteGrandTotal',
                        'instalmentPlan',
                        'accountHolder',
                        'iban',
                        'calculationId'
                    ]);

                this.getButtonList = ko.computed(function() {
                    return this.getInstalmentButtons();
                }, this);

                this.getDraftDownloadUrl = ko.computed(function() {
                    return this.getDownloadUrl();
                }, this);

                this.showAccountDetails = ko.computed(function() {
                    return this.canUsePayolutionCheckout()
                        && this.isAccountDetailsRequired();
                }, this);

                this.isPayolutionAllowed = ko.computed(function() {
                    return this.canUsePayolutionCheckout()
                        && this.enablePrecheck()
                        && this.isPlaceOrderActionAllowed()
                        && this.selectedRate() != null;

                }, this);

                return this;
            },

            /**
             * Allow checkout only with selected rate
             *
             * @returns {*}
             */
            canUsePayolutionMethod: function() {
                return this._super()
                    && this.selectedRate() != null;
            },

            /**
             * Account details only required for customers from DE/AT
             *
             * @returns {boolean}
             */
            isAccountDetailsRequired: function() {
                var billingAddress = quote.billingAddress() || {},
                    countryId = billingAddress.countryId;
                return _.indexOf(this.accountDetailsForCountryCodes, countryId) != -1;
            },

            /**
             * Toggle instalments > 3.
             */
            toggleInstalments: function() {
                var toggleLink = $('#instalment_toggler');
                if (this.showAllInstalments()) {
                    $('.ins-plan').find('li:gt('+ this.maxVisibleRates +')').addClass('hidden');
                    toggleLink.text($t('SHOW_ALL_RATES')).attr('title', $t('SHOW_ALL_RATES'));
                    this.showAllInstalments(0);
                } else {
                    $('.ins-plan').find('li').removeClass('hidden');
                    toggleLink.text($t('HIDE_RATES')).attr('title', $t('HIDE_RATES'));
                    this.showAllInstalments(1);
                }
            },

            /**
             * Create url to prepare download of drafted instalment contract
             *
             * @returns {*|string}
             */
            getDownloadUrl: function() {
                var url = payolutionConfig.draftAgreementUrl || '', data, hash;
                try {
                    if (url.length > 0 && this.selectedRate() != null) {
                        data = this.instalmentDetails[this.selectedRate()] || {};
                        hash = data.draftUrl || '';
                        url += '?pdf=' + hash;
                    }
                } catch (e) {}
                return url;
            },

            getUnavailableHint: function() {
                return $t('HINT_UNAVAILABLE');
            },

            getVariantAddressHint: function() {
                return $t('HINT_UNAVAILABLE_VARIANT_ADDRESS');
            },

            /**
             * Trim values for iban after value has changed by user
             *
             * @returns {Object}
             */
            trimValue: function() {
                var iban = this.iban();
                try {
                    if (iban) {
                        this.iban(iban.replace(/[^a-zA-Z0-9]/g, ''));
                    }
                } catch (e) {}
                return this;
            },

            /**
             * Handle response data
             *
             * @param response
             * @return {*}
             */
            handlePrecheckResponse: function(response) {
                var self = this,
                    details = response.PaymentDetails || [],
                    calculationId = response.CalculationId || '',
                    buttons = [],
                    buttonTxt = '';

                try {
                    if (response.Status == 'SUCCESS') {
                        self.isPrecheckPositive(true);
                        self.showPayolutionFailed(false);

                        _.each(details, function (d) {
                            self.instalmentDetails.push(
                                {
                                    totalAmount: self.getLocalizedAmount(d.TotalAmount),
                                    interestRate: self.getLocalizedDecimal(d.InterestRate) + ' %',
                                    effectiveRate: self.getLocalizedDecimal(d.EffectiveInterestRate) + ' %',
                                    draftUrl: d.StandardCreditInformationUrl,
                                    instalmentRates: d.Installment,
                                    duration: d.Duration
                                }
                            );
                            buttonTxt = d.Duration + ' ' + $t('MONTHLY_RATES_AT') +
                            ' ' + self.getLocalizedAmount(d.Installment[0].Amount);
                            buttons.push({text: buttonTxt});
                        });
                        this.instalmentButtons(buttons);
                        $('.instalment-rate').bind("click", function () {
                            var button = $(this), index = button.data('val');
                            self.selectedRate(index);
                            $('.instalment-rate').removeClass('ins-active-button');
                            button.addClass('ins-active-button');
                            self.setInstalmentAmounts(index);
                        });
                        self.calculationId(calculationId);
                        self.canUsePayolutionCheckout(true);
                    } else {
                        self.showPayolutionFailed(true);
                    }
                } catch (e) {
                    console.log('Error while fetching data from precheck response!');
                    self.isPrecheckPositive(false);
                    self.showPayolutionFailed(true);
                }
                return this;
            },

            /**
             * Create instalment plan
             *
             * @param index
             */
            setInstalmentAmounts: function(index) {
                var self = this,
                    data = this.instalmentDetails[index] || {},
                    instalmentRates = data.instalmentRates || [], // TODO probably different property name
                    instalmentRateItems = [],
                    tmpl;

                this.totalAmount(data.totalAmount);
                this.interestRate(data.interestRate);
                this.effectiveRate(data.effectiveRate);

                _.each(instalmentRates, function(rate, index) {
                    var date = self.getFormattedDate(rate.Due),
                        cssClass = (index > self.maxVisibleRates && !self.showAllInstalments()) ? ' hidden' : '',
                        rateTxt;
                    rateTxt =
                        (index + 1) + '. ' +
                        $t('SINGLE_RATE') + ' (' +
                        $t('RATE_IS_DUE_ON') + ' ' +
                        date.day +
                        date.month +
                        date.year + ") &nbsp;" +
                        self.getLocalizedAmount(rate.Amount)
                    ;
                    instalmentRateItems.push({ text: rateTxt, cssClass: cssClass });
                });
                tmpl = mageTemplate(this.ratePlanTemplate, {
                    data: {
                        items: instalmentRateItems
                    }
                });
                this.instalmentPlan(tmpl);
            },

            /**
             * Fetch rendered instalment buttons
             *
             * @returns {String}
             */
            getInstalmentButtons: function() {
                return mageTemplate(this.instalmentButtonTemplate, {
                    data: {
                        buttons: this.instalmentButtons()
                    }
                });
            },

            /**
             * Retrieve amount with currency symbol used in current store
             *
             * @param amount
             * @returns {*}
             */
            getLocalizedAmount: function(amount) {
                var priceFormat = window.checkoutConfig.priceFormat || {};
                return utils.formatPrice(amount, priceFormat);
            },

            /**
             * Format float value with localized decimal separator
             *
             * @param number
             * @returns {string}
             */
            getLocalizedDecimal: function(number) {
                var format = window.checkoutConfig.priceFormat || {},
                    decimalSymbol = format.decimalSymbol === undefined ? ',' : format.decimalSymbol,
                    precision = Math.abs(number - parseInt(number, 10)).toFixed(2).slice(2);
                return parseInt(number, 10) + decimalSymbol + precision;
            },

            /**
             * Format date
             *
             * @param date
             * @returns {{day: string, month: string, year: number}}
             */
            getFormattedDate: function(date) {
                var formattedDate = new Date(date),
                    month = "" + (formattedDate.getMonth() + 1),
                    day = "" + formattedDate.getDate(),
                    pad = "00";
                return {
                    day: (pad.substring(0, pad.length - day.length) + day) + ".",
                    month: (pad.substring(0, pad.length - month.length) + month) + ".",
                    year: formattedDate.getFullYear()
                };
            },

            /**
             * Get payolution instalment data when placing order
             *
             * @return {Object}
             */
            getPlaceOrderPayolutionData: function() {
                return {
                    dob: this.getCustomerDob(),
                    email: this.getCustomerEmail(),
                    ssn: this.ssn(),
                    telephone: this.telephone(),
                    duration: this.getInstalmentDuration(),
                    account_holder: this.accountHolder(),
                    iban: this.iban(),
                    calculation_id: this.calculationId()
                };
            },

            /**
             * Retrieve selected duration
             *
             * @returns {string}
             */
            getInstalmentDuration: function() {
                var selectedDuration = '';
                try {
                    if (typeof this.selectedRate() !== 'undefined' && this.instalmentDetails[this.selectedRate()]) {
                        selectedDuration = this.instalmentDetails[this.selectedRate()]['duration'];
                    }
                } catch (e) {}

                return selectedDuration;
            }
        });
    }
);