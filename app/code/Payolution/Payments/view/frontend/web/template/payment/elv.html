<!--
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 -->
<div class="payment-method payolution" data-bind="css: {'_active': (getCode() == isChecked())}">
    <div class="payment-method-title field choice">
        <input type="radio"
               name="payment[method]"
               class="radio"
               data-bind="attr: {'id': getCode()}, value: getCode(), checked: isChecked, click: selectPaymentMethod, visible: isRadioButtonVisible()" />
        <label data-bind="attr: {'for': getCode()}" class="label"><span data-bind="text: getTitle()"></span></label>
    </div>

    <div class="payment-method-content">
        <div class="payment-method-billing-address" data-bind="attr: {'id': 'change_billing_address_' + getCode()}">
            <!-- ko foreach: $parent.getRegion(getBillingAddressFormName()) -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!--/ko-->
        </div>

        <form id="payolution_elv-form" class="form form-payolution" data-role="payolution_elv-form">
            <div class="input-box account-details fieldset" data-bind="visible:showTelephoneNumber()">
                <div class="field required">
                    <label for="" class="label"
                           data-bind="attr: {'for': getCode() + '_telephone'}"><!-- ko i18n: 'TELEPHONE_NUMBER' --><!-- /ko --></label>
                    <input type="text"
                           class="validate-phoneLax"
                           name="payment[' + getCode() + '_telephone]"
                           data-validate="{'required-entry':true}"
                           data-bind="attr: {id: getCode() + '_telephone'},
                                value: telephone,
                                valueUpdate: 'keyup'"/>
                </div>
            </div>

            <div class="input-box account-details fieldset" data-bind="visible:showSSN()">
                <div class="field required">
                    <label for="" class="label"
                           data-bind="attr: {'for': getCode() + '_ssn'}"><!-- ko i18n: 'SSN_NUMBER' --><!-- /ko --></label>
                    <input type="text"
                           class="validate-ssn-4"
                           name="payment[' + getCode() + '_ssn]"
                           data-validate="{'required-entry':true}"
                           data-bind="attr: {id: getCode() + '_ssn', 'placeholder': $t('SSN_PLACEHOLDER')},
                                value: ssn,
                                valueUpdate: 'keyup'"/>
                </div>
            </div>

            <div class="fieldset">
                <div class="input-box customer-dob field">
                    <label for="" class="label"><!-- ko i18n: 'DATE_OF_BIRTH' --><!-- /ko --></label>
                    <div class="dob">
                        <select id="" name=""
                                data-bind="attr: {'id': getCode() + '_dob_day', 'title': $t('DAY'), name: 'payment[' + getCode() + '_dob_day]'},
                               options: dobDayValues,
                               optionsCaption: $t('DAY'),
                               optionsValue: 'value',
                               optionsText: 'value',
                               value: customerDobDay"
                                value="" title=""
                                class="validate-select dob-day">
                        </select>
                        <select id="" name=""
                                data-bind="attr: {'id': getCode() + '_dob_month', 'title': $t('MONTH'), name: 'payment[' + getCode() + '_dob_month]'},
                                options: dobMonthValues,
                                optionsCaption: $t('MONTH'),
                                optionsValue: 'value',
                                optionsText: 'label',
                                value: customerDobMonth"
                                value="" title=""
                                class="validate-select dob-month">

                        </select>
                        <select id="" name=""
                                data-bind="attr: {'id': getCode() + '_dob_year', 'title': $t('YEAR'), name: 'payment[' + getCode() + '_dob_year]'},
                                options: dobYearValues,
                                optionsCaption: $t('YEAR'),
                                optionsValue: 'value',
                                optionsText: 'value',
                                value: customerDobYear"
                                value="" title=""
                                class="validate-select dob-year">
                        </select>
                    </div>
                    <p class="min-age"><!-- ko i18n: 'HINT_MINIMUM_AGE_INSTALMENT' --><!-- /ko --></p>
                </div>
            </div>
            <div class="input-box account-details fieldset">
                <div class="field required" data-bind="attr: {'data-hasrequired': $t('* Required Fields')}">
                    <label for="" class="label"
                           data-bind="attr: {'for': getCode() + '_account_holder'}"><!-- ko i18n: 'ACCOUNT_HOLDER' --><!-- /ko --></label>
                    <input type="text"
                           name="payment[' + getCode() + '_account_holder]"
                           data-validate="{'required': true}"
                           data-bind="attr: {id: getCode() + '_account_holder'},
                                value: accountHolder"/>
                </div>
                <div class="field required">
                    <label for="" class="label"
                           data-bind="attr: {'for': getCode() + '_iban'}"><!-- ko i18n: 'IBAN' --><!-- /ko --></label>
                    <input type="text"
                           name="payment[' + getCode() + '_iban]"
                           data-validate="{'required': true, 'validate-iban': true}"
                           data-bind="attr: {id: getCode() + '_iban'},
                                value: iban, event: { change: trimValue }"/>
                </div>
            </div>

            <div class="agreements">
                <input type="checkbox" id="" name="" value="1"
                       data-bind="checked: hasAcceptedSepa,
                       enable: enablePrecheck(),
                       css: {disabled: !enablePrecheck()},
                       attr: {'id': getCode() + '_sepa_mandate', name: 'payment[' + getCode() + '_sepa_mandate]'}"/>
                <label for="" class="required"
                       data-bind="attr: {'for': getCode() + '_sepa_mandate'}, html:getSepaLink()"></label>
                <br/>
                <input type="checkbox" id="" name="" value="1"
                       data-bind="click: doPrecheck,
                       enable: enablePrecheck(),
                       css: {disabled: !enablePrecheck()},
                       attr: {'id': getCode() + '_solvency_check', name: 'payment[' + getCode() + '_solvency_check]'}"/>
                <label for="" class="required"
                       data-bind="attr: {'for': getCode() + '_solvency_check'}"><!-- ko i18n: 'SOLVENCY_AGREEMENT_INVOICE_LABEL' --><!-- /ko --></label>
                <p data-bind="html:getPayolutionAgreementLink()"><!-- ko i18n: 'SOLVENCY_AGREEMENT_LINK' --><!-- /ko --></p>
            </div>
        </form>

        <div class="checkout-agreements-block">
            <!-- ko foreach: $parent.getRegion('before-place-order') -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!--/ko-->
        </div>

        <div class="actions-toolbar">
            <div class="primary">
                <button class="action primary checkout"
                        type="submit"
                        data-bind="
                        click: placeOrder,
                        attr: {'title': $t('Place Order')},
                        enable: (getCode() == isChecked()),
                        css: {disabled: !isPayolutionAllowed()}
                        "
                        disabled>
                    <span data-bind="i18n: 'Place Order'"></span>
                </button>
            </div>
        </div>
        <div class="method-unavailable" data-bind="visible: showPayolutionFailed(), text: getUnavailableHint()"></div>
        <div class="method-unavailable" data-bind="visible: showAddressFailed(), text: getVariantAddressHint()"></div>
    </div>
</div>
