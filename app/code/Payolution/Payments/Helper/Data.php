<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse - initial contents
 */
namespace Payolution\Payments\Helper;

use Magento\Framework\DataObject;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\LocalizedException;

/**
 * Class Data
 *
 * Data Helper
 *
 * @package Payolution\Payments\Helper
 */
class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    const MODULE_VERSION                        = '2.2.3';

    const MEDIA_PAYOLUTION_DIR                  = 'payolution';
    const MEDIA_PAYOLUTION_JS_DIR               = 'js';
    const MEDIA_PAYOLUTION_JS_FILE_NAME         = 'instalment-calculator.js';

    const INSTALMENT_PAYMENT_CODE               = 'payolution_instalment';
    const INVOICE_PAYMENT_CODE                  = 'payolution_invoice';
    const ELV_PAYMENT_CODE                      = 'payolution_elv';

    /**
     * @var \Magento\Customer\Api\GroupRepositoryInterface
     */
    protected $_groupRepository;

    /**
     * Holds ISO639 locale format
     *
     * TODO: make implementation for more languages easier
     *
     * @var array
     */
    protected $_countryLocalizationIso = [
        'de_DE' => 'de',
        'en_CA' => 'en',
        'en_GB' => 'uk',
        'en_US' => 'en',
        'nl_NL' => 'nl',
        'fr_CA' => 'fr',
        'fr_FR' => 'fr',
    ];

    /**
     * Available payolution payment methods
     *
     * @var array
     */
    protected $_payolutionPaymentMethods = [
        self::ELV_PAYMENT_CODE,
        self::INVOICE_PAYMENT_CODE,
        self::INSTALMENT_PAYMENT_CODE,
    ];

    /**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    /**
     * Checkout session
     *
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession;

    /**
     * @var ConfigHelper
     */
    protected $_configHelper;

    /**
     * Helper constructor.
     *
     * @param \Magento\Framework\App\Helper\Context $context
     * @param \Magento\Customer\Api\GroupRepositoryInterface $groupRepository
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Checkout\Model\Session $checkoutSession
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Customer\Api\GroupRepositoryInterface $groupRepository,
        \Magento\Framework\Registry $registry,
        \Magento\Checkout\Model\Session $checkoutSession,
        ConfigHelper $configHelper
    ) {
        $this->_groupRepository     = $groupRepository;
        $this->_coreRegistry        = $registry;
        $this->_checkoutSession     = $checkoutSession;
        $this->_configHelper        = $configHelper;
        parent::__construct($context);
    }

    /**
     * Retrieve payment method ids
     *
     * @return array
     */
    public function getAvailablePaymentMethods()
    {
        return $this->_payolutionPaymentMethods;
    }

    /**
     * Returns current order
     *
     * @return array|null
     * @deprecated
     */
    public function getOrder()
    {
        return $this->_coreRegistry->registry('current_order');
    }

    /**
     * Get ISO639 localization codes
     *
     * @param string $localeCode
     * @return mixed
     */
    public function getCountryLocalizationIso($localeCode = null)
    {
        if ($localeCode) {
            return isset($this->_countryLocalizationIso[$localeCode])
                ? $this->_countryLocalizationIso[$localeCode]
                : '';
        }
        return $this->_countryLocalizationIso;
    }

    /**
     * Get dir where instalment js is saved
     *
     * @return string
     */
    public function getInstalmentJsDir()
    {
        return self::MEDIA_PAYOLUTION_DIR . '/' . self::MEDIA_PAYOLUTION_JS_DIR . '/';
    }

    /**
     * Check if current customer is b2b customer
     *
     * @param $customer \Magento\Customer\Model\Customer|\Magento\Customer\Api\Data\CustomerInterface
     * @return mixed
     */
    public function isCustomerB2B($customer)
    {
        $checkResult = new DataObject();
        $group = $this->_getGroup($customer->getGroupId());
        $quote = $this->_checkoutSession->getQuote();
        //if (($group && $group->getCode() == 'Retailer') || $quote->getShippingAddress()->getCompany()) {
            //$checkResult->setData('is_b2b_customer', true);
        //} else {
            $checkResult->setData('is_b2b_customer', false);
        //}

        // for future use in observers
        $this->_eventManager->dispatch(
            'payolution_is_customer_b2b',
            [
                'result'    => $checkResult,
                'customer'  => $customer,
            ]
        );

        //FIXME when customer is no longer / becomes b2b customer - existing orders no longer can become captured / cancelled / refunded
        return $checkResult->getData('is_b2b_customer');
    }

    /**
     * Retrieve customer group by id
     *
     * @param $groupId
     * @return \Magento\Customer\Api\Data\GroupInterface|null
     */
    protected function _getGroup($groupId)
    {
        try {
            $group = $this->_groupRepository->getById($groupId);
        } catch (LocalizedException $e) {
            $group = null;
        } catch (NoSuchEntityException $e) {
            $group = null;
        }
        return $group;
    }

    /**
     * Retrieve session_id for fraud protection
     *
     * @return string
     */
    public function getFraudProtectionSessionId()
    {
        $sessionId = '';
        if ($this->_configHelper->getFraudProtectionEnabled()) {
            $methodName = 'get'
                . str_replace(
                    ' ',
                    '',
                    ucwords(str_replace('_', ' ', ConfigHelper::FRAUD_SESSION_NAME))
                );

            $sessionId = $this->_checkoutSession->$methodName();
        }
        return $sessionId;
    }
}
