<?php
/**
 * Copyright (c) 2008-2016 dotSource GmbH.
 * All rights reserved.
 * http://www.dotsource.de
 *
 * Contributors:
 * Sebastian Ninse - initial contents
 */
namespace Payolution\Payments\Controller\Instalment;

use Payolution\Payments\Model\Instalment;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Context;

/**
 * Class Draft
 * @package Payolution\Payments\Controller\Instalment
 */
class Draft extends \Magento\Framework\App\Action\Action
{
    /**
     * @var \Magento\Framework\Controller\Result\RawFactory
     */
    protected $_resultRawFactory;

    /**
     * @var \Magento\Framework\Controller\Result\ForwardFactory
     */
    protected $_resultForwardFactory;

    /**
     * @var Instalment\Draft
     */
    protected $_draftClient;

    /**
     * {@inheritDoc}
     */
    public function __construct(
        Context $context,
        \Payolution\Payments\Model\Instalment\Draft $draftClient,
        \Magento\Framework\Controller\Result\RawFactory $resultRawFactory,
        \Magento\Framework\Controller\Result\ForwardFactory $resultForwardFactory
    ) {
        parent::__construct($context);
        $this->_resultRawFactory = $resultRawFactory;
        $this->_resultForwardFactory = $resultForwardFactory;
        $this->_draftClient = $draftClient;
    }

    /**
     * Fetch instalment draft pdf
     *
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\Result\Raw
     */
    public function execute()
    {
        $urlHash = $this->getRequest()->getParam('pdf');
        try {
            $resultRaw = $this->_resultRawFactory->create();
            $pdfStream = $this->_draftClient->getInstalmentDraft($urlHash);
            if ($pdfStream) {
                $resultRaw->setHeader('Content-type', 'application/pdf')
                    ->setContents($pdfStream);
                return $resultRaw;
            }
        } catch (\Exception $e) {
            $this->_objectManager->get('Psr\Log\LoggerInterface')->error($e);
        }
        $this->messageManager->addError(__('ERROR_WHILE_FETCHING_INSTALMENT_DRAFT'));
        $resultForward = $this->_resultForwardFactory->create();
        $resultForward->forward('noroute');
        return $resultForward;
    }
}
