<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace GFP\Theme\Model\Config\Source\Page;

class LazyLoad implements \Magento\Framework\Data\OptionSourceInterface
{

    const ENABLED = 1;
    const DISABLED = 0;
    const PRODUCTION = 2;
    const ALPINE = 3;

    public function toOptionArray()
    {
        return [
            ['value' => self::DISABLED, 'label' => __('Disabled')],
            ['value' => self::ENABLED, 'label' => __('Enabled')],
            ['value' => self::PRODUCTION, 'label' => __('Only in Production Mode')],
            ['value' => self::ALPINE, 'label' => __('Hide Content and load intersected')]
        ];
    }
}
