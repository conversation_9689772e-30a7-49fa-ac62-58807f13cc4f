<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace GFP\Seo\Model\StoreSwitcher;

use Magento\Store\Api\Data\StoreInterface;
use Magento\Store\Api\StoreResolverInterface;
use Magento\Store\Model\StoreSwitcherInterface;
use Magento\Framework\Url\Helper\Data as UrlHelper;

/**
 * Remove SID, from_store, store from target url.
 *
 * Used in store-switching process in HTML frontend.
 */
class Clean implements StoreSwitcherInterface
{

    /**
     * Generate target URL to switch stores through other mechanism then via URL params.
     *
     * @param StoreInterface $fromStore store where we came from
     * @param StoreInterface $targetStore store where to go to
     * @param string $redirectUrl original url requested for redirect after switching
     * @return string redirect url
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function switch(StoreInterface $fromStore, StoreInterface $targetStore, string $redirectUrl): string
    {
        if(substr($redirectUrl, -4) === "/de/" ){
            return substr($redirectUrl,0,-3);
        }
        return $redirectUrl;
    }
}
