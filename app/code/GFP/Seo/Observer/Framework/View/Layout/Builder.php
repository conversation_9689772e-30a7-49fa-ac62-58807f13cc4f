<?php

namespace GFP\Seo\Observer\Framework\View\Layout;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Page\Config;


class Builder implements ObserverInterface
{

    /** @var Config $pageConfig */
    private $pageConfig;
    private UrlInterface $url;

    /**
     * constructor
     *
     * @param Config  $pageConfig
     */
    public function __construct(
        Config $pageConfig,
        UrlInterface $url
    ) {
        $this->pageConfig  = $pageConfig;

        $this->url = $url;
    }

    /**
     * Execute
     *
     * @param Observer $observer
     * @return $this
     */
    public function execute(Observer $observer)
    {
        $currentUrl = $this->url->getCurrentUrl();
        $baseUrl = $this->url->getBaseUrl();
        $rootBaseUrl = substr($baseUrl,0,strpos($baseUrl,"/",8));
        if( $currentUrl === $rootBaseUrl."/" || $currentUrl === $rootBaseUrl."/de/" | $currentUrl === $rootBaseUrl."/de"){
            $this->pageConfig->addRemotePageAsset($rootBaseUrl, 'link_rel', ['attributes' => ['rel'=> 'canonical']]);
        }
        return $this;
    }
}
