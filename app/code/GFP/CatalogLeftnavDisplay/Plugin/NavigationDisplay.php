<?php

namespace GFP\CatalogLeftnavDisplay\Plugin;

use Magento\Framework\Exception\LocalizedException;
use Smile\ElasticsuiteCatalog\Block\Navigation;

class NavigationDisplay
{
    /**
     * @param Navigation $subject
     * @param bool $result
     * @return bool
     * @throws LocalizedException
     */
    public function afterCanShowBlock(Navigation $subject, bool $result): bool
    {
        $handles = $subject->getLayout()->getUpdate()->getHandles();
        return in_array("hyva_catalog_category_view_selectable_0_elasticsearch", $handles);
    }
}
