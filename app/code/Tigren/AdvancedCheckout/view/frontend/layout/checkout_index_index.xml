<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <link src="Tigren_AdvancedCheckout::js/form/element/accordion.js"/>
    </head>
    <body>
        <referenceBlock name="checkout.root">
            <arguments>
                <argument name="jsLayout" xsi:type="array">
                    <item name="components" xsi:type="array">
                        <item name="checkout" xsi:type="array">
                            <item name="children" xsi:type="array">
                                <item name="steps" xsi:type="array">
                                    <item name="children" xsi:type="array">
                                        <item name="shipping-step" xsi:type="array">
                                            <item name="children" xsi:type="array">
                                                <item name="shippingAddress" xsi:type="array">
                                                    <item name="children" xsi:type="array">
                                                        <item name="billingAddress" xsi:type="array">
                                                            <item name="config" xsi:type="array">
                                                                <item name="deps" xsi:type="array">
                                                                    <item name="0" xsi:type="string">checkout.steps.shipping-step.step-config</item>
                                                                    <item name="1" xsi:type="string">checkoutProvider</item>
                                                                </item>
                                                                <item name="popUpForm" xsi:type="array">
                                                                    <item name="element" xsi:type="string">#opc-new-billing-address</item>
                                                                    <item name="options" xsi:type="array">
                                                                        <item name="type" xsi:type="string">popup</item>
                                                                        <item name="responsive" xsi:type="boolean">true</item>
                                                                        <item name="innerScroll" xsi:type="boolean">true</item>
                                                                        <item name="title" xsi:type="string" translate="true">Billing Address</item>
                                                                        <item name="trigger" xsi:type="string">opc-new-billing-address</item>
                                                                        <item name="buttons" xsi:type="array">
                                                                            <item name="save" xsi:type="array">
                                                                                <item name="text" xsi:type="string" translate="true">Save Address</item>
                                                                                <item name="class" xsi:type="string">action primary action-save-address</item>
                                                                            </item>
                                                                            <item name="cancel" xsi:type="array">
                                                                                <item name="text" xsi:type="string" translate="true">Cancel</item>
                                                                                <item name="class" xsi:type="string">action secondary action-hide-popup</item>
                                                                            </item>
                                                                        </item>
                                                                    </item>
                                                                </item>
                                                            </item>
                                                            <item name="component" xsi:type="string">Tigren_AdvancedCheckout/js/view/billing</item>
                                                            <item name="displayArea" xsi:type="string">billing-address</item>
                                                            <item name="provider" xsi:type="string">checkoutProvider</item>
                                                            <item name="sortOrder" xsi:type="string">1</item>
                                                            <item name="children" xsi:type="array">
                                                                <item name="before-form" xsi:type="array">
                                                                    <item name="component" xsi:type="string">uiComponent</item>
                                                                    <item name="displayArea" xsi:type="string">billing-before-form</item>
                                                                    <item name="children" xsi:type="array">
                                                                        <!-- before form fields -->
                                                                    </item>
                                                                </item>
                                                                <item name="before-fields" xsi:type="array">
                                                                    <item name="component" xsi:type="string">uiComponent</item>
                                                                    <item name="displayArea" xsi:type="string">billing-before-fields</item>
                                                                    <item name="children" xsi:type="array">
                                                                        <!-- before fields -->
                                                                    </item>
                                                                </item>
                                                                <item name="address-list" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Tigren_AdvancedCheckout/js/view/billing-address/list</item>
                                                                    <item name="config" xsi:type="array">
                                                                        <item name="template" xsi:type="string">Tigren_AdvancedCheckout/billing-address/custom-list</item>
                                                                    </item>
                                                                    <item name="displayArea" xsi:type="string">billing-address-list</item>
                                                                </item>
                                                            </item>
                                                        </item>
                                                    </item>
                                                </item>
                                            </item>
                                        </item>
                                    </item>
                                </item>
                            </item>
                        </item>
                    </item>
                </argument>
            </arguments>
        </referenceBlock>
    </body>
</page>