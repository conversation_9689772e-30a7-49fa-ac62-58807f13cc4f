<?php

use Hyva\Theme\Model\ViewModelRegistry;

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Framework\View\Element\Template $block */

/** @var ViewModelRegistry $viewModels */

/** @var \CopeX\Swiper\ViewModel\Swiper $swiper */

$swiper = $block->getViewModel();

$items = $block->getItems();

$lightboxId = $block->getLightboxId() ?: (time() . uniqid());
?>

<div class="swiper-lightbox lightbox p-0 fixed flex flex-col h-screen w-screen justify-center items-center inset-0 bg-black bg-opacity-90 z-50 transition" x-transition x-cloak x-show="lightboxOpen !== false" style="display: none;">
    <div class="mx-auto relative max-width w-full swiper ">
        <div class="swiper-lightbox-container swiper-container flex flex-col" x-ref="container_<?= $lightboxId ?>_lightbox">
            <div class="swiper-wrapper items-center">
                <?php foreach ($items as $id => $item) :
                    ?>
                    <div class="swiper-slide">
                        <div class="flex flex-col text-center overflow-hidden">
                            <div class="flex-shrink-1">
                                <?= $item ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
                <div class="swiper-lazy-preloader swiper-lazy-preloader-white"></div>
            </div>
            <div class="flex flex-row flex-nowrap mx-auto justify-center pt-4 w-full md:w-1/2 relative">
                <div class="flex flex-row flex-nowrap w-5/6 md:w-full justify-between<?= count($items) < 2 ? " hidden": ""?>">
                    <div class="flex items-center">
                        <button @click="lightboxPrev" aria-label="prev"
                                class="bg-white -ml-2 lg:-ml-4 flex justify-center items-center w-10 h-10 focus:outline-none">
                            <svg viewBox="0 0 20 20" fill="currentColor" class="chevron-left w-6 h-6">
                                <use href="#chevron-left">
                            </svg>
                        </button>
                    </div>
                    <div class="swiper-pagination" style="bottom: 0;"></div>
                    <div class="flex items-center">
                        <button @click="lightboxNext" aria-label="next"
                                class="bg-white -mr-2 lg:-mr-4 flex justify-center items-center w-10 h-10 focus:outline-none">
                            <svg viewBox="0 0 20 20" fill="currentColor" class="chevron-right w-6 h-6">
                                <use href="#chevron-right">
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="absolute -right-20">
                    <button @click="closeLightbox" aria-label="close" class="float-left pt-2 pr-2 outline-none focus:outline-none">
                        <svg class="fill-current text-white " xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 18 18">
                            <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z">
                            </path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    function initLightbox<?= $lightboxId ?>() {
        return Object.assign( {
            lightbox: null,
            lightboxOpen: false,
            baseConfig: {
                loop: true,
                spaceBetween:  5,
                freeMode: false,
                lazy: {loadPrevNext: true, checkInView: true, loadOnTransitionStart: true}
            },
            init() {
                swiperInit.init( () =>{
                    this.lightbox = new Swiper(this.$refs.container_<?= $lightboxId ?>_lightbox, Object.assign({},this.baseConfig,
                        {
                            slidesPerView: 1,
                            init: false,
                            pagination: { el:  '.swiper-lightbox .swiper-pagination', dynamicBullets: true, dynamicMainBullets: 10},
                            enabled: <?= count($items) > 1 ? "true" : "false";?>,
                        }
                    ));
                });
            },
            lightboxNext(){
                this.lightbox.slideNext();
            },
            lightboxPrev(){
                this.lightbox.slidePrev();
            },
            openLightbox(){
                this.lightboxOpen = true;
                if (this.lightbox) {
                    this.lightbox.init();
                }
            },
            closeLightbox(){
                this.lightboxOpen = false;
            },
            eventListeners: {
                ['@keydown.window.escape']() {
                    this.closeLightbox();
                }
            }
        }, <?= $block->getInitScript() ?: "{}"?>);
    }
</script>