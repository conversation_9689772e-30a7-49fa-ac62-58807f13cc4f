<?php

use Hyva\Theme\Model\ViewModelRegistry;

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Framework\View\Element\Template $block */

/** @var ViewModelRegistry $viewModels */

$pagination = $block->getPagination() ?? false;
$headline = $block->getHeadline() ?? $block->getTitle();
$lightbox = $block->getLightbox() ?? true;
$items = $block->getItems() ?? [];
if (is_object($items) && $items instanceof Iterator) {
    $items = iterator_to_array($items);
}
if (!$itemCount = count($items)) {
    return '';
}
$width = $block->getWidth() ?? "w-full md:w-10/12";
$swiperWrapperClass = $block->getSwiperWrapperClass() ?? "swiper-wrapper items-center";
$swiperSlideClass = $block->getSwiperSlideClass() ?? "swiper-slide";
$swiperActiveClass = $block->getSwiperActiveClass() ?? "swiper-slide-visible swiper-slide-active";
$sliderId = time() . uniqid();
$mainArrowColor = $block->getMainArrowColor() ?? 'currentColor';
$sliderItemRenderer = $block->getChildBlock('slider.item.template');
$slidesPerView = $block->getSlidesPerView() ?: 1;
?>
<div class="min-w-0">
    <?php if ($headline): ?>
        <h2><?= $headline ?></h2>
    <?php endif; ?>
    <div x-data="initSwiper<?= $sliderId ?>()"
             x-intersect.once.margin.-100px="init()"
             x-bind="eventListeners"
            class="bg-white">
            <div class="swiper-main relative <?= $width ?> mx-auto swiper">
                <div class="mx-6 my-2 overflow-hidden">
                    <?php if($itemCount > $slidesPerView): ?>
                    <div class="absolute inset-y-0 -left-4 z-10 flex items-center">
                        <button @click="mainPrev" aria-label="prev"
                                class="flex justify-center items-center ml-4 w-10 h-10 focus:outline-none">
                            <svg viewBox="0 0 20 20" fill="<?= $mainArrowColor ?>" class="chevron-left w-20 h-20">
                                <use href="#chevron-left"></use>
                            </svg>
                        </button>
                    </div>
                    <?php endif; ?>
                    <div class="swiper-main-container swiper-container" x-ref="container_<?= $sliderId ?>">
                        <div class="<?= $swiperWrapperClass ?>">
                            <?php $itemCounter = 0; ?>
                            <?php foreach ($items as $id => $item) : ?>
                                <div class="<?= $swiperSlideClass ?> <?= $itemCounter++ < $slidesPerView ? $swiperActiveClass : ""?>" @click="openLightbox">
                                    <?= $sliderItemRenderer->setItem($item)->toHtml() ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <?php if($pagination) : ?>
                        <div class="swiper-pagination -mb-4 md:mb-0"></div>
                        <?php endif; ?>
                        <?php if($block->getAfterMain()) : ?>
                            <?= $block->getAfterMain(); ?>
                        <?php endif; ?>
                    </div>
                    <?php if($itemCount > $slidesPerView): ?>
                    <div class="absolute inset-y-0 -right-8 mr-4 z-10 flex items-center">
                        <button @click="mainNext" aria-label="next"
                                class="flex justify-center items-center mr-4 w-10 h-10 focus:outline-none">
                            <svg viewBox="0 0 20 20" fill="<?= $mainArrowColor ?>" class="chevron-right w-20 h-20">
                                <use href="#chevron-right"></use>
                            </svg>
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php if ($lightbox): ?>
            <div class="swiper-lightbox lightbox p-0 fixed flex flex-col h-screen w-screen justify-center items-center inset-0 bg-black bg-opacity-90 z-50 transition" x-transition x-cloak x-show="lightboxOpen !== false" style="display: none;">
                <div class="mx-auto relative max-width w-full swiper ">
                    <div class="swiper-lightbox-container swiper-container flex flex-col text-white" x-ref="container_<?= $sliderId ?>_lightbox">
                        <div class="swiper-wrapper items-center">
                            <?php foreach ($items as $id => $item) : ?>
                                <div class="<?= $swiperSlideClass ?> <?= $itemCounter++ < $slidesPerView ? $swiperActiveClass : ""?>">
                                    <?= $sliderItemRenderer->setLightBox(true)->setItem($item)->toHtml() ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="flex flex-row flex-nowrap mx-auto justify-center pt-4 w-full md:w-1/2 relative">
                            <div class="flex flex-row flex-nowrap w-5/6 md:w-full items-center justify-between<?= count($items) < 2 ? " hidden": ""?>">
                                <div class="flex items-center">
                                    <button @click="lightboxPrev" aria-label="prev"
                                            class="-ml-2 lg:-ml-4 flex justify-center items-center w-10 h-10 focus:outline-none">
                                        <svg viewBox="0 0 20 20" fill="white" class="chevron-left w-10 h-10">
                                            <use href="#chevron-left"></use>
                                        </svg>
                                    </button>
                                </div>
                                <div class="swiper-pagination"></div>
                                <div class="flex items-center">
                                    <button @click="lightboxNext" aria-label="next"
                                            class="-mr-2 lg:-mr-4 flex justify-center items-center w-10 h-10 focus:outline-none">
                                        <svg viewBox="0 0 20 20" fill="white" class="chevron-right w-10 h-10">
                                            <use href="#chevron-right"></use>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="fixed top-2 right-2 z-10">
                                <button @click="closeLightbox" aria-label="close" class="float-left outline-none focus:outline-none">
                                    <svg class="fill-current text-white w-8 h-8" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18">
                                        <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z">
                                        </path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        </div>
    <script>
        function initSwiper<?= $sliderId ?>() {
            return Object.assign( {
                swiper: null,
                thumb: null,
                lightbox: null,
                lightboxOpen: false,
                initialized: false,
                baseConfig: {
                    loop: <?= $block->getLoop() ?: "true" ?>,
                    spaceBetween:  5,
                    freeMode: false,
                    lazy: {loadPrevNext: true, checkInView: true, loadOnTransitionStart: true}
                },
                init() {
                    swiperInit.init( () => {
                        this.swiper = new Swiper(this.$refs.container_<?= $sliderId ?>, Object.assign({},this.baseConfig,
                            {
                                slidesPerView:  <?= $slidesPerView ?: "1" ?>,
                                pagination: { el:  '.swiper-main .swiper-pagination', dynamicBullets: true, dynamicMainBullets: 10},
                                enabled: <?= $itemCount > $slidesPerView ? "true" : "false";?>,
                            },
                            <?= $block->getExtraConfig() ?: "{}"?>
                        ));
                        <?php if($lightbox): ?>
                        this.lightbox = new Swiper(this.$refs.container_<?= $sliderId ?>_lightbox, Object.assign({},this.baseConfig,
                            {
                                slidesPerView: <?= $slidesPerView ?: "1" ?>,
                                init: false,
                                pagination: { el:  '.swiper-lightbox .swiper-pagination', dynamicBullets: true, dynamicMainBullets: 10},
                                enabled: <?= $itemCount > $slidesPerView ? "true" : "false";?>,
                            },
                            <?= $block->getLightboxExtraConfig() ?: "{}"?>
                        ));
                        <?php endif; ?>
                    });
                },
                mainNext(){
                    this.swiper.slideNext();
                },
                mainPrev(){
                    this.swiper.slidePrev();
                },
                lightboxNext(){
                    this.lightbox.slideNext();
                },
                lightboxPrev(){
                    this.lightbox.slidePrev();
                },
                openLightbox(){
                    this.lightboxOpen = true;
                    if (this.lightbox) {
                        this.lightbox.init();
                        this.lightbox.slideTo(this.swiper.activeIndex);
                    }
                },
                closeLightbox(){
                    this.lightboxOpen = false;
                },
                eventListeners: {
                    ['@keydown.window.escape']() {
                        this.closeLightbox();
                    }
                }
            }, <?= $block->getInitScript() ?: "{}"?>);
        }
    </script>
</div>
