{"version": 3, "file": "swiper-bundle.min.js", "names": ["isObject", "obj", "constructor", "Object", "extend", "target", "src", "keys", "for<PERSON>ach", "key", "length", "ssrDocument", "body", "addEventListener", "removeEventListener", "activeElement", "blur", "nodeName", "querySelector", "querySelectorAll", "getElementById", "createEvent", "initEvent", "createElement", "children", "childNodes", "style", "setAttribute", "getElementsByTagName", "createElementNS", "importNode", "location", "hash", "host", "hostname", "href", "origin", "pathname", "protocol", "search", "getDocument", "doc", "document", "ssrWindow", "navigator", "userAgent", "history", "replaceState", "pushState", "go", "back", "CustomEvent", "this", "getComputedStyle", "getPropertyValue", "Image", "Date", "screen", "setTimeout", "clearTimeout", "matchMedia", "requestAnimationFrame", "callback", "cancelAnimationFrame", "id", "getWindow", "win", "window", "nextTick", "delay", "now", "getTranslate", "el", "axis", "matrix", "curTransform", "transformMatrix", "curStyle", "currentStyle", "WebKitCSSMatrix", "transform", "webkitTransform", "split", "map", "a", "replace", "join", "MozTransform", "OTransform", "MsTransform", "msTransform", "toString", "m41", "parseFloat", "m42", "o", "prototype", "call", "slice", "isNode", "node", "HTMLElement", "nodeType", "to", "arguments", "undefined", "noExtend", "i", "nextSource", "keysArray", "filter", "indexOf", "nextIndex", "len", "<PERSON><PERSON><PERSON>", "desc", "getOwnPropertyDescriptor", "enumerable", "__swiper__", "setCSSProperty", "varName", "varValue", "setProperty", "animateCSSModeScroll", "_ref", "swiper", "targetPosition", "side", "startPosition", "translate", "time", "startTime", "duration", "params", "speed", "wrapperEl", "scrollSnapType", "cssModeFrameID", "dir", "isOutOfBound", "current", "animate", "getTime", "progress", "Math", "max", "min", "easeProgress", "cos", "PI", "currentPosition", "scrollTo", "overflow", "elementChildren", "element", "selector", "matches", "tag", "classes", "classList", "add", "Array", "isArray", "elementOffset", "box", "getBoundingClientRect", "clientTop", "clientLeft", "scrollTop", "scrollY", "scrollLeft", "scrollX", "top", "left", "elementStyle", "prop", "elementIndex", "child", "previousSibling", "elementParents", "parents", "parent", "parentElement", "push", "elementTransitionEnd", "fireCallBack", "e", "elementOuterSize", "size", "<PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "support", "deviceCached", "browser", "getSupport", "smoothScroll", "documentElement", "touch", "DocumentTouch", "calcSupport", "getDevice", "overrides", "_temp", "platform", "ua", "device", "ios", "android", "screenWidth", "width", "screenHeight", "height", "match", "ipad", "ipod", "iphone", "windows", "macos", "os", "calcDevice", "<PERSON><PERSON><PERSON><PERSON>", "needPerspectiveFix", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "String", "includes", "major", "minor", "num", "Number", "isWebView", "test", "calcB<PERSON>er", "eventsEmitter", "on", "events", "handler", "priority", "self", "eventsListeners", "destroyed", "method", "event", "once", "once<PERSON><PERSON><PERSON>", "off", "__emitterProxy", "_len", "args", "_key", "apply", "onAny", "eventsAnyListeners", "offAny", "index", "splice", "<PERSON><PERSON><PERSON><PERSON>", "emit", "data", "context", "_len2", "_key2", "unshift", "processLazyPreloader", "imageEl", "slideEl", "closest", "isElement", "slideClass", "lazyEl", "lazyPreloaderClass", "remove", "unlazy", "slides", "removeAttribute", "preload", "amount", "lazyPreloadPrevNext", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerViewDynamic", "ceil", "activeIndex", "slideIndexLastInView", "rewind", "realIndex", "update", "updateSize", "clientWidth", "clientHeight", "isHorizontal", "isVertical", "parseInt", "isNaN", "assign", "updateSlides", "getDirectionLabel", "property", "marginRight", "getDirectionPropertyValue", "label", "slidesEl", "swiperSize", "rtlTranslate", "rtl", "wrongRTL", "isVirtual", "virtual", "enabled", "previousSlidesLength", "<PERSON><PERSON><PERSON><PERSON>", "snapGrid", "slidesGrid", "slidesSizesGrid", "offsetBefore", "slidesOffsetBefore", "offsetAfter", "slidesOffsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "spaceBetween", "slidePosition", "prevSlideSize", "virtualSize", "marginLeft", "marginBottom", "marginTop", "centeredSlides", "cssMode", "gridEnabled", "grid", "rows", "slideSize", "initSlides", "shouldResetSlideSize", "breakpoints", "slide", "updateSlide", "slideStyles", "currentTransform", "currentWebKitTransform", "roundLengths", "paddingLeft", "paddingRight", "boxSizing", "floor", "swiperSlideSize", "abs", "slidesPerGroup", "slidesPerGroupSkip", "effect", "setWrapperSize", "updateWrapperSize", "newSlidesGrid", "slidesGridItem", "loop", "groups", "slidesBefore", "slidesAfter", "groupSize", "_", "slideIndex", "centeredSlidesBounds", "allSlidesSize", "slideSizeValue", "maxSnap", "snap", "centerInsufficientSlides", "allSlidesOffset", "snapIndex", "addToSnapGrid", "addToSlidesGrid", "v", "watchOverflow", "checkOverflow", "watchSlidesProgress", "updateSlidesOffset", "backFaceHiddenClass", "containerModifierClass", "hasClassBackfaceClassAdded", "contains", "maxBackfaceHiddenSlides", "updateAutoHeight", "activeSlides", "newHeight", "setTransition", "getSlideByIndex", "getSlideIndexByData", "visibleSlides", "offsetHeight", "minusOffset", "offsetLeft", "offsetTop", "swiperSlideOffset", "cssOverflowAdjustment", "updateSlidesProgress", "offsetCenter", "slideVisibleClass", "visibleSlidesIndexes", "slideOffset", "slideProgress", "minTranslate", "originalSlideProgress", "slideBefore", "slideAfter", "originalProgress", "updateProgress", "multiplier", "translatesDiff", "maxTranslate", "isBeginning", "isEnd", "progressLoop", "wasBeginning", "wasEnd", "isBeginningRounded", "isEndRounded", "firstSlideIndex", "lastSlideIndex", "firstSlideTranslate", "lastSlideTranslate", "translateMax", "translateAbs", "autoHeight", "updateSlidesClasses", "getFilteredSlide", "activeSlide", "slideActiveClass", "slideNextClass", "slidePrevClass", "nextSlide", "nextEls", "nextElement<PERSON><PERSON>ling", "next", "elementNextAll", "prevSlide", "prevEls", "previousElementSibling", "prev", "elementPrevAll", "emitSlidesClasses", "updateActiveIndex", "newActiveIndex", "previousIndex", "previousRealIndex", "previousSnapIndex", "getVirtualRealIndex", "aIndex", "normalizeSlideIndex", "getActiveIndexByTranslate", "skip", "getAttribute", "initialized", "runCallbacksOnInit", "updateClickedSlide", "slideFound", "clickedSlide", "clickedIndex", "slideToClickedSlide", "virtualTranslate", "currentTranslate", "setTranslate", "byController", "newProgress", "x", "y", "previousTranslate", "translateTo", "runCallbacks", "translateBounds", "internal", "animating", "preventInteractionOnTransition", "newTranslate", "isH", "behavior", "onTranslateToWrapperTransitionEnd", "transitionEmit", "direction", "step", "slideTo", "initial", "normalizedTranslate", "normalizedGrid", "normalizedGridNext", "allowSlideNext", "allowSlidePrev", "transitionStart", "transitionEnd", "t", "_immediateVirtual", "_cssModeVirtualInitialSet", "initialSlide", "onSlideToWrapperTransitionEnd", "slideToLoop", "newIndex", "slideNext", "perGroup", "slidesPerGroupAuto", "increment", "loopPreventsSliding", "loopFix", "_clientLeft", "slidePrev", "normalize", "val", "normalizedSnapGrid", "prevSnap", "prevSnapIndex", "prevIndex", "lastIndex", "slideReset", "slideToClosest", "threshold", "currentSnap", "slideToIndex", "slideSelector", "loopedSlides", "getSlideIndex", "loopCreate", "slideRealIndex", "activeSlideIndex", "byMousewheel", "prependSlidesIndexes", "appendSlidesIndexes", "isNext", "isPrev", "slidesPrepended", "slidesAppended", "prepend", "append", "recalcSlides", "currentSlideTranslate", "diff", "touches", "controller", "control", "loopParams", "c", "loop<PERSON><PERSON><PERSON>", "newSlidesOrder", "swiperSlideIndex", "onTouchStart", "touchEventsData", "ev<PERSON><PERSON>", "simulate<PERSON>ouch", "pointerType", "originalEvent", "targetEl", "touchEventsTarget", "which", "button", "isTouched", "isMoved", "swipingClassHasValue", "noSwipingClass", "eventPath", "<PERSON><PERSON><PERSON>", "path", "shadowRoot", "noSwipingSelector", "isTargetShadow", "noSwiping", "base", "__closestFrom", "assignedSlot", "found", "getRootNode", "closestElement", "allowClick", "swi<PERSON><PERSON><PERSON><PERSON>", "currentX", "pageX", "currentY", "pageY", "startX", "startY", "edgeSwipeDetection", "iOSEdgeSwipeDetection", "edgeSwipeThreshold", "iOSEdgeSwipeThreshold", "innerWidth", "preventDefault", "allowTouchCallbacks", "isScrolling", "startMoving", "touchStartTime", "swipeDirection", "allowThresholdMove", "focusableElements", "shouldPreventDefault", "allowTouchMove", "touchStartPreventDefault", "touchStartForcePreventDefault", "isContentEditable", "freeMode", "onTouchMove", "pointerIndex", "findIndex", "cachedEv", "pointerId", "targetTouch", "preventedByNestedSwiper", "prevX", "prevY", "touchReleaseOnEdges", "targetTouches", "diffX", "diffY", "sqrt", "touchAngle", "atan2", "zoom", "cancelable", "touchMoveStopPropagation", "nested", "stopPropagation", "touchesDiff", "previousX", "previousY", "oneWayMovement", "touchRatio", "prevTouchesDirection", "touchesDirection", "isLoop", "startTranslate", "evt", "bubbles", "dispatchEvent", "allowMomentumBounce", "grabCursor", "setGrabCursor", "loopFixed", "disableParentSwiper", "resistanceRatio", "resistance", "follow<PERSON><PERSON>", "onTouchEnd", "type", "touchEndTime", "timeDiff", "pathTree", "lastClickTime", "currentPos", "stopIndex", "rewindFirstIndex", "rewindLastIndex", "ratio", "longSwipesMs", "longSwipes", "longSwipesRatio", "shortSwipes", "navigation", "nextEl", "prevEl", "onResize", "setBreakpoint", "isVirtualLoop", "autoplay", "running", "paused", "resizeTimeout", "resume", "onClick", "preventClicks", "preventClicksPropagation", "stopImmediatePropagation", "onScroll", "onLoad", "dummy<PERSON><PERSON><PERSON>ttached", "dummyEventListener", "capture", "dom<PERSON>ethod", "swiperMethod", "passive", "updateOnWindowResize", "isGridEnabled", "defaults", "init", "resizeObserver", "createElements", "url", "breakpointsBase", "uniqueNavElements", "passiveListeners", "wrapperClass", "_emitClasses", "moduleExtendParams", "allModulesParams", "moduleParamName", "moduleParams", "auto", "prototypes", "transition", "transitionDuration", "moving", "isLocked", "__preventObserver__", "cursor", "unsetGrabCursor", "attachEvents", "bind", "detachEvents", "breakpoint", "getBreakpoint", "currentBreakpoint", "breakpointP<PERSON>ms", "originalParams", "wasMultiRow", "isMultiRow", "wasEnabled", "emitContainerClasses", "fill", "wasModuleEnabled", "isModuleEnabled", "disable", "enable", "directionChanged", "needsReLoop", "changeDirection", "isEnabled", "containerEl", "currentHeight", "innerHeight", "points", "point", "minRatio", "substr", "value", "sort", "b", "wasLocked", "lastSlideRightEdge", "addClasses", "classNames", "suffixes", "entries", "prefix", "resultClasses", "item", "prepareClasses", "autoheight", "centered", "removeClasses", "extendedDefaults", "Swiper", "swipers", "newParams", "modules", "__modules__", "mod", "extendParams", "swiperParams", "passedParams", "eventName", "velocity", "trunc", "clickTimeout", "velocities", "imagesToLoad", "imagesLoaded", "setProgress", "cls", "className", "getSlideClasses", "updates", "view", "exact", "spv", "breakLoop", "translateValue", "translated", "complete", "newDirection", "needUpdate", "currentDirection", "changeLanguageDirection", "mount", "mounted", "shadowEl", "getWrapperSelector", "trim", "getWrapper", "destroy", "deleteInstance", "cleanStyles", "object", "deleteProps", "static", "newDefaults", "module", "m", "installModule", "createElementIfNotDefined", "checkProps", "classesToSelector", "appendSlide", "appendElement", "tempDOM", "innerHTML", "observer", "prependSlide", "prependElement", "addSlide", "activeIndexBuffer", "baseLength", "slidesBuffer", "currentSlide", "removeSlide", "slidesIndexes", "indexToRemove", "removeAllSlides", "prototypeGroup", "protoMethod", "use", "animationFrame", "resize<PERSON><PERSON>ler", "orientationChangeHandler", "ResizeObserver", "newWidth", "_ref2", "contentBoxSize", "contentRect", "inlineSize", "blockSize", "observe", "unobserve", "observers", "attach", "options", "MutationObserver", "WebkitMutationObserver", "mutations", "observerUpdate", "attributes", "childList", "characterData", "observeParents", "observeSlideChildren", "containerParents", "disconnect", "cssModeTimeout", "cache", "renderSlide", "renderExternal", "renderExternalUpdate", "addSlidesBefore", "addSlidesAfter", "from", "offset", "force", "previousFrom", "previousTo", "previousSlidesGrid", "previousOffset", "offsetProp", "onRendered", "slidesToRender", "prependIndexes", "appendIndexes", "loopFrom", "loopTo", "domSlidesAssigned", "numberOfNewSlides", "newCache", "cachedIndex", "cachedEl", "cachedElIndex", "handle", "kc", "keyCode", "charCode", "pageUpDown", "keyboard", "isPageUp", "isPageDown", "isArrowLeft", "isArrowRight", "isArrowUp", "isArrowDown", "shift<PERSON>ey", "altKey", "ctrl<PERSON>ey", "metaKey", "onlyInViewport", "inView", "swiper<PERSON><PERSON><PERSON>", "swiperHeight", "windowWidth", "windowHeight", "swiperOffset", "swiperCoord", "returnValue", "timeout", "mousewheel", "releaseOnEdges", "invert", "forceToAxis", "sensitivity", "eventsTarget", "thresholdDel<PERSON>", "thresholdTime", "lastEventBeforeSnap", "lastScrollTime", "recentWheelEvents", "handleMouseEnter", "mouseEntered", "handleMouseLeave", "animateSlider", "newEvent", "delta", "raw", "targetElContainsTarget", "rtlFactor", "sX", "sY", "pX", "pY", "detail", "wheelDelta", "wheelDeltaY", "wheelDeltaX", "HORIZONTAL_AXIS", "deltaY", "deltaX", "deltaMode", "spinX", "spinY", "pixelX", "pixelY", "positions", "sign", "ignoreWheelEvents", "position", "sticky", "shift", "prevEvent", "firstEvent", "snapToThreshold", "autoplayDisableOnInteraction", "stop", "releaseScroll", "hideOnClick", "disabledClass", "hiddenClass", "lockClass", "navigationDisabledClass", "makeElementsArray", "getEl", "res", "toggleEl", "disabled", "subEl", "tagName", "onPrevClick", "onNextClick", "initButton", "destroyButton", "_s", "pagination", "clickable", "isHidden", "toggle", "pfx", "bulletSize", "bulletElement", "renderBullet", "renderProgressbar", "renderFraction", "renderCustom", "progressbarOpposite", "dynamicBullets", "dynamicMainBullets", "formatFractionCurrent", "number", "formatFractionTotal", "bulletClass", "bulletActiveClass", "modifierClass", "currentClass", "totalClass", "progressbarFillClass", "progressbarOppositeClass", "clickableClass", "horizontalClass", "verticalClass", "paginationDisabledClass", "bullets", "dynamicBulletIndex", "isPaginationDisabled", "setSideBullets", "bulletEl", "onBulletClick", "newSlideIndex", "currentSlideIndex", "total", "firstIndex", "midIndex", "classesToRemove", "suffix", "s", "flat", "bullet", "bulletIndex", "first<PERSON><PERSON>played<PERSON><PERSON>et", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dynamicBulletsLength", "bulletsOffset", "subElIndex", "fractionEl", "textContent", "totalEl", "progressbarDirection", "scale", "scaleX", "scaleY", "progressEl", "render", "paginationHTML", "numberOfBullets", "dragStartPos", "dragSize", "trackSize", "divider", "dragTimeout", "scrollbar", "dragEl", "newSize", "newPos", "hide", "opacity", "display", "getPointerPosition", "clientX", "clientY", "setDragPosition", "positionRatio", "onDragStart", "onDragMove", "onDragEnd", "snapOnRelease", "activeListener", "passiveListener", "eventMethod", "swiperEl", "dragClass", "draggable", "scrollbarDisabledClass", "raf", "timeLeft", "waitForTransition", "disableOnInteraction", "stopOnLastSlide", "reverseDirection", "pauseOnMouseEnter", "autoplayTimeLeft", "wasPaused", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touchStartTimeout", "slideChanged", "pausedByInteraction", "autoplayDelayTotal", "autoplayDelayCurrent", "autoplayStartTime", "onTransitionEnd", "calcTimeLeft", "run", "delayForce", "currentSlideDelay", "activeSlideEl", "getSlideDelay", "proceed", "start", "pause", "reset", "onVisibilityChange", "visibilityState", "onPointerEnter", "onPointerLeave", "thumbs", "multipleActiveThumbs", "autoScrollOffset", "slideThumbActiveClass", "thumbsContainerClass", "swiperCreated", "onThumbClick", "thumbsSwiper", "thumbsParams", "SwiperClass", "thumbsSwiperParams", "thumbsToActivate", "thumbActiveClass", "useOffset", "currentThumbsIndex", "newThumbsIndex", "newThumbsSlide", "getThumbsElementAndInit", "thumbsElement", "onThumbsSwiper", "watchForThumbsToAppear", "momentum", "momentumRatio", "momentumBounce", "momentumBounceRatio", "momentumVelocityRatio", "minimumVelocity", "lastMoveEvent", "pop", "velocityEvent", "distance", "momentumDuration", "momentumDistance", "newPosition", "afterBouncePosition", "doBounce", "bounceAmount", "needsLoopFix", "j", "moveDistance", "currentSlideSize"], "sources": ["../node_modules/ssr-window/ssr-window.esm.js", "../src/shared/utils.js", "../src/shared/get-support.js", "../src/shared/get-device.js", "../src/shared/get-browser.js", "../src/core/events-emitter.js", "../src/shared/process-lazy-preloader.js", "../src/core/update/index.js", "../src/core/update/updateSize.js", "../src/core/update/updateSlides.js", "../src/core/update/updateAutoHeight.js", "../src/core/update/updateSlidesOffset.js", "../src/core/update/updateSlidesProgress.js", "../src/core/update/updateProgress.js", "../src/core/update/updateSlidesClasses.js", "../src/core/update/updateActiveIndex.js", "../src/core/update/updateClickedSlide.js", "../src/core/translate/index.js", "../src/core/translate/getTranslate.js", "../src/core/translate/setTranslate.js", "../src/core/translate/minTranslate.js", "../src/core/translate/maxTranslate.js", "../src/core/translate/translateTo.js", "../src/core/transition/transitionEmit.js", "../src/core/slide/index.js", "../src/core/slide/slideTo.js", "../src/core/slide/slideToLoop.js", "../src/core/slide/slideNext.js", "../src/core/slide/slidePrev.js", "../src/core/slide/slideReset.js", "../src/core/slide/slideToClosest.js", "../src/core/slide/slideToClickedSlide.js", "../src/core/loop/index.js", "../src/core/loop/loopCreate.js", "../src/core/loop/loopFix.js", "../src/core/loop/loopDestroy.js", "../src/core/events/onTouchStart.js", "../src/core/events/onTouchMove.js", "../src/core/events/onTouchEnd.js", "../src/core/events/onResize.js", "../src/core/events/onClick.js", "../src/core/events/onScroll.js", "../src/core/events/onLoad.js", "../src/core/events/index.js", "../src/core/breakpoints/setBreakpoint.js", "../src/core/check-overflow/index.js", "../src/core/defaults.js", "../src/core/moduleExtendParams.js", "../src/core/core.js", "../src/core/transition/index.js", "../src/core/transition/setTransition.js", "../src/core/transition/transitionStart.js", "../src/core/transition/transitionEnd.js", "../src/core/grab-cursor/index.js", "../src/core/grab-cursor/setGrabCursor.js", "../src/core/grab-cursor/unsetGrabCursor.js", "../src/core/breakpoints/index.js", "../src/core/breakpoints/getBreakpoint.js", "../src/core/classes/index.js", "../src/core/classes/addClasses.js", "../src/core/classes/removeClasses.js", "../src/shared/create-element-if-not-defined.js", "../src/shared/classes-to-selector.js", "../src/modules/manipulation/methods/appendSlide.js", "../src/modules/manipulation/methods/prependSlide.js", "../src/modules/manipulation/methods/addSlide.js", "../src/modules/manipulation/methods/removeSlide.js", "../src/modules/manipulation/methods/removeAllSlides.js", "../src/core/modules/resize/resize.js", "../src/core/modules/observer/observer.js", "../src/swiper.js", "../src/modules/virtual/virtual.js", "../src/modules/keyboard/keyboard.js", "../src/modules/mousewheel/mousewheel.js", "../src/modules/navigation/navigation.js", "../src/modules/pagination/pagination.js", "../src/modules/scrollbar/scrollbar.js", "../src/modules/autoplay/autoplay.js", "../src/modules/thumbs/thumbs.js", "../src/modules/free-mode/free-mode.js", "../src/modules/manipulation/manipulation.js"], "sourcesContent": ["/**\n * SSR Window 4.0.2\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2021, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: December 13, 2021\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n    return (obj !== null &&\n        typeof obj === 'object' &&\n        'constructor' in obj &&\n        obj.constructor === Object);\n}\nfunction extend(target = {}, src = {}) {\n    Object.keys(src).forEach((key) => {\n        if (typeof target[key] === 'undefined')\n            target[key] = src[key];\n        else if (isObject(src[key]) &&\n            isObject(target[key]) &&\n            Object.keys(src[key]).length > 0) {\n            extend(target[key], src[key]);\n        }\n    });\n}\n\nconst ssrDocument = {\n    body: {},\n    addEventListener() { },\n    removeEventListener() { },\n    activeElement: {\n        blur() { },\n        nodeName: '',\n    },\n    querySelector() {\n        return null;\n    },\n    querySelectorAll() {\n        return [];\n    },\n    getElementById() {\n        return null;\n    },\n    createEvent() {\n        return {\n            initEvent() { },\n        };\n    },\n    createElement() {\n        return {\n            children: [],\n            childNodes: [],\n            style: {},\n            setAttribute() { },\n            getElementsByTagName() {\n                return [];\n            },\n        };\n    },\n    createElementNS() {\n        return {};\n    },\n    importNode() {\n        return null;\n    },\n    location: {\n        hash: '',\n        host: '',\n        hostname: '',\n        href: '',\n        origin: '',\n        pathname: '',\n        protocol: '',\n        search: '',\n    },\n};\nfunction getDocument() {\n    const doc = typeof document !== 'undefined' ? document : {};\n    extend(doc, ssrDocument);\n    return doc;\n}\n\nconst ssrWindow = {\n    document: ssrDocument,\n    navigator: {\n        userAgent: '',\n    },\n    location: {\n        hash: '',\n        host: '',\n        hostname: '',\n        href: '',\n        origin: '',\n        pathname: '',\n        protocol: '',\n        search: '',\n    },\n    history: {\n        replaceState() { },\n        pushState() { },\n        go() { },\n        back() { },\n    },\n    CustomEvent: function CustomEvent() {\n        return this;\n    },\n    addEventListener() { },\n    removeEventListener() { },\n    getComputedStyle() {\n        return {\n            getPropertyValue() {\n                return '';\n            },\n        };\n    },\n    Image() { },\n    Date() { },\n    screen: {},\n    setTimeout() { },\n    clearTimeout() { },\n    matchMedia() {\n        return {};\n    },\n    requestAnimationFrame(callback) {\n        if (typeof setTimeout === 'undefined') {\n            callback();\n            return null;\n        }\n        return setTimeout(callback, 0);\n    },\n    cancelAnimationFrame(id) {\n        if (typeof setTimeout === 'undefined') {\n            return;\n        }\n        clearTimeout(id);\n    },\n};\nfunction getWindow() {\n    const win = typeof window !== 'undefined' ? window : {};\n    extend(win, ssrWindow);\n    return win;\n}\n\nexport { extend, getDocument, getWindow, ssrDocument, ssrWindow };\n", "import { getWindow, getDocument } from 'ssr-window';\n\nfunction deleteProps(obj) {\n  const object = obj;\n  Object.keys(object).forEach((key) => {\n    try {\n      object[key] = null;\n    } catch (e) {\n      // no getter for object\n    }\n    try {\n      delete object[key];\n    } catch (e) {\n      // something got wrong\n    }\n  });\n}\nfunction nextTick(callback, delay = 0) {\n  return setTimeout(callback, delay);\n}\nfunction now() {\n  return Date.now();\n}\nfunction getComputedStyle(el) {\n  const window = getWindow();\n  let style;\n  if (window.getComputedStyle) {\n    style = window.getComputedStyle(el, null);\n  }\n  if (!style && el.currentStyle) {\n    style = el.currentStyle;\n  }\n  if (!style) {\n    style = el.style;\n  }\n\n  return style;\n}\nfunction getTranslate(el, axis = 'x') {\n  const window = getWindow();\n  let matrix;\n  let curTransform;\n  let transformMatrix;\n\n  const curStyle = getComputedStyle(el, null);\n\n  if (window.WebKitCSSMatrix) {\n    curTransform = curStyle.transform || curStyle.webkitTransform;\n    if (curTransform.split(',').length > 6) {\n      curTransform = curTransform\n        .split(', ')\n        .map((a) => a.replace(',', '.'))\n        .join(', ');\n    }\n    // Some old versions of Webkit choke when 'none' is passed; pass\n    // empty string instead in this case\n    transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n  } else {\n    transformMatrix =\n      curStyle.MozTransform ||\n      curStyle.OTransform ||\n      curStyle.MsTransform ||\n      curStyle.msTransform ||\n      curStyle.transform ||\n      curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n    matrix = transformMatrix.toString().split(',');\n  }\n\n  if (axis === 'x') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m41;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[12]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[4]);\n  }\n  if (axis === 'y') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m42;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[13]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[5]);\n  }\n  return curTransform || 0;\n}\nfunction isObject(o) {\n  return (\n    typeof o === 'object' &&\n    o !== null &&\n    o.constructor &&\n    Object.prototype.toString.call(o).slice(8, -1) === 'Object'\n  );\n}\nfunction isNode(node) {\n  // eslint-disable-next-line\n  if (typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined') {\n    return node instanceof HTMLElement;\n  }\n  return node && (node.nodeType === 1 || node.nodeType === 11);\n}\nfunction extend(...args) {\n  const to = Object(args[0]);\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  for (let i = 1; i < args.length; i += 1) {\n    const nextSource = args[i];\n    if (nextSource !== undefined && nextSource !== null && !isNode(nextSource)) {\n      const keysArray = Object.keys(Object(nextSource)).filter((key) => noExtend.indexOf(key) < 0);\n      for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n        const nextKey = keysArray[nextIndex];\n        const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n        if (desc !== undefined && desc.enumerable) {\n          if (isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else if (!isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            to[nextKey] = {};\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else {\n            to[nextKey] = nextSource[nextKey];\n          }\n        }\n      }\n    }\n  }\n  return to;\n}\n\nfunction setCSSProperty(el, varName, varValue) {\n  el.style.setProperty(varName, varValue);\n}\n\nfunction animateCSSModeScroll({ swiper, targetPosition, side }) {\n  const window = getWindow();\n  const startPosition = -swiper.translate;\n  let startTime = null;\n  let time;\n  const duration = swiper.params.speed;\n\n  swiper.wrapperEl.style.scrollSnapType = 'none';\n  window.cancelAnimationFrame(swiper.cssModeFrameID);\n\n  const dir = targetPosition > startPosition ? 'next' : 'prev';\n\n  const isOutOfBound = (current, target) => {\n    return (dir === 'next' && current >= target) || (dir === 'prev' && current <= target);\n  };\n\n  const animate = () => {\n    time = new Date().getTime();\n    if (startTime === null) {\n      startTime = time;\n    }\n\n    const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);\n    const easeProgress = 0.5 - Math.cos(progress * Math.PI) / 2;\n    let currentPosition = startPosition + easeProgress * (targetPosition - startPosition);\n\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      currentPosition = targetPosition;\n    }\n    swiper.wrapperEl.scrollTo({\n      [side]: currentPosition,\n    });\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.scrollSnapType = '';\n      setTimeout(() => {\n        swiper.wrapperEl.style.overflow = '';\n        swiper.wrapperEl.scrollTo({\n          [side]: currentPosition,\n        });\n      });\n      window.cancelAnimationFrame(swiper.cssModeFrameID);\n      return;\n    }\n    swiper.cssModeFrameID = window.requestAnimationFrame(animate);\n  };\n  animate();\n}\n\nfunction getSlideTransformEl(slideEl) {\n  return (\n    slideEl.querySelector('.swiper-slide-transform') ||\n    (slideEl.shadowEl && slideEl.shadowEl.querySelector('.swiper-slide-transform')) ||\n    slideEl\n  );\n}\n\nfunction findElementsInElements(elements = [], selector = '') {\n  const found = [];\n  elements.forEach((el) => {\n    found.push(...el.querySelectorAll(selector));\n  });\n  return found;\n}\nfunction elementChildren(element, selector = '') {\n  return [...element.children].filter((el) => el.matches(selector));\n}\n\nfunction createElement(tag, classes = []) {\n  const el = document.createElement(tag);\n  el.classList.add(...(Array.isArray(classes) ? classes : [classes]));\n  return el;\n}\nfunction elementOffset(el) {\n  const window = getWindow();\n  const document = getDocument();\n  const box = el.getBoundingClientRect();\n  const body = document.body;\n  const clientTop = el.clientTop || body.clientTop || 0;\n  const clientLeft = el.clientLeft || body.clientLeft || 0;\n  const scrollTop = el === window ? window.scrollY : el.scrollTop;\n  const scrollLeft = el === window ? window.scrollX : el.scrollLeft;\n  return {\n    top: box.top + scrollTop - clientTop,\n    left: box.left + scrollLeft - clientLeft,\n  };\n}\nfunction elementPrevAll(el, selector) {\n  const prevEls = [];\n  while (el.previousElementSibling) {\n    const prev = el.previousElementSibling; // eslint-disable-line\n    if (selector) {\n      if (prev.matches(selector)) prevEls.push(prev);\n    } else prevEls.push(prev);\n    el = prev;\n  }\n  return prevEls;\n}\nfunction elementNextAll(el, selector) {\n  const nextEls = [];\n  while (el.nextElementSibling) {\n    const next = el.nextElementSibling; // eslint-disable-line\n    if (selector) {\n      if (next.matches(selector)) nextEls.push(next);\n    } else nextEls.push(next);\n    el = next;\n  }\n  return nextEls;\n}\nfunction elementStyle(el, prop) {\n  const window = getWindow();\n  return window.getComputedStyle(el, null).getPropertyValue(prop);\n}\nfunction elementIndex(el) {\n  let child = el;\n  let i;\n  if (child) {\n    i = 0;\n    // eslint-disable-next-line\n    while ((child = child.previousSibling) !== null) {\n      if (child.nodeType === 1) i += 1;\n    }\n    return i;\n  }\n  return undefined;\n}\n\nfunction elementParents(el, selector) {\n  const parents = []; // eslint-disable-line\n  let parent = el.parentElement; // eslint-disable-line\n  while (parent) {\n    if (selector) {\n      if (parent.matches(selector)) parents.push(parent);\n    } else {\n      parents.push(parent);\n    }\n    parent = parent.parentElement;\n  }\n  return parents;\n}\n\nfunction elementTransitionEnd(el, callback) {\n  function fireCallBack(e) {\n    if (e.target !== el) return;\n    callback.call(el, e);\n    el.removeEventListener('transitionend', fireCallBack);\n  }\n  if (callback) {\n    el.addEventListener('transitionend', fireCallBack);\n  }\n}\n\nfunction elementOuterSize(el, size, includeMargins) {\n  const window = getWindow();\n  if (includeMargins) {\n    return (\n      el[size === 'width' ? 'offsetWidth' : 'offsetHeight'] +\n      parseFloat(\n        window\n          .getComputedStyle(el, null)\n          .getPropertyValue(size === 'width' ? 'margin-right' : 'margin-top'),\n      ) +\n      parseFloat(\n        window\n          .getComputedStyle(el, null)\n          .getPropertyValue(size === 'width' ? 'margin-left' : 'margin-bottom'),\n      )\n    );\n  }\n  return el.offsetWidth;\n}\n\nexport {\n  animateCSSModeScroll,\n  deleteProps,\n  nextTick,\n  now,\n  getTranslate,\n  isObject,\n  extend,\n  getComputedStyle,\n  setCSSProperty,\n  getSlideTransformEl,\n  // dom\n  findElementsInElements,\n  createElement,\n  elementChildren,\n  elementOffset,\n  elementPrevAll,\n  elementNextAll,\n  elementStyle,\n  elementIndex,\n  elementParents,\n  elementTransitionEnd,\n  elementOuterSize,\n};\n", "import { getWindow, getDocument } from 'ssr-window';\n\nlet support;\n\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n\n  return {\n    smoothScroll: document.documentElement && 'scrollBehavior' in document.documentElement.style,\n\n    touch: !!(\n      'ontouchstart' in window ||\n      (window.DocumentTouch && document instanceof window.DocumentTouch)\n    ),\n  };\n}\n\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\n\nexport { getSupport };\n", "import { getWindow } from 'ssr-window';\nimport { getSupport } from './get-support.js';\n\nlet deviceCached;\n\nfunction calcDevice({ userAgent } = {}) {\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n\n  const device = {\n    ios: false,\n    android: false,\n  };\n\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = [\n    '1024x1366',\n    '1366x1024',\n    '834x1194',\n    '1194x834',\n    '834x1112',\n    '1112x834',\n    '768x1024',\n    '1024x768',\n    '820x1180',\n    '1180x820',\n    '810x1080',\n    '1080x810',\n  ];\n  if (\n    !ipad &&\n    macos &&\n    support.touch &&\n    iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0\n  ) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\n\nfunction getDevice(overrides = {}) {\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\n\nexport { getDevice };\n", "import { getWindow } from 'ssr-window';\n\nlet browser;\n\nfunction calcBrowser() {\n  const window = getWindow();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua\n        .split('Version/')[1]\n        .split(' ')[0]\n        .split('.')\n        .map((num) => Number(num));\n      needPerspectiveFix = major < 16 || (major === 16 && minor < 2);\n    }\n  }\n  return {\n    isSafari: needPerspectiveFix || isSafari(),\n    needPerspectiveFix,\n    isWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent),\n  };\n}\n\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\n\nexport { getBrowser };\n", "/* eslint-disable no-underscore-dangle */\n\nexport default {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach((event) => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler(...args) {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach((event) => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (\n            eventHandler === handler ||\n            (eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler)\n          ) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n\n  emit(...args) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n\n    eventsArray.forEach((event) => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach((eventHandler) => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler) => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  },\n};\n", "export const processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => (swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`);\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    const lazyEl = slideEl.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n    if (lazyEl) lazyEl.remove();\n  }\n};\n\nconst unlazy = (swiper, index) => {\n  if (!swiper.slides[index]) return;\n  const imageEl = swiper.slides[index].querySelector('[loading=\"lazy\"]');\n  if (imageEl) imageEl.removeAttribute('loading');\n};\n\nexport const preload = (swiper) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  let amount = swiper.params.lazyPreloadPrevNext;\n  const len = swiper.slides.length;\n  if (!len || !amount || amount < 0) return;\n  amount = Math.min(amount, len);\n  const slidesPerView =\n    swiper.params.slidesPerView === 'auto'\n      ? swiper.slidesPerViewDynamic()\n      : Math.ceil(swiper.params.slidesPerView);\n  const activeIndex = swiper.activeIndex;\n  const slideIndexLastInView = activeIndex + slidesPerView - 1;\n  if (swiper.params.rewind) {\n    for (let i = activeIndex - amount; i <= slideIndexLastInView + amount; i += 1) {\n      const realIndex = ((i % len) + len) % len;\n      if (realIndex !== activeIndex && realIndex > slideIndexLastInView) unlazy(swiper, realIndex);\n    }\n  } else {\n    for (\n      let i = Math.max(slideIndexLastInView - amount, 0);\n      i <= Math.min(slideIndexLastInView + amount, len - 1);\n      i += 1\n    ) {\n      if (i !== activeIndex && i > slideIndexLastInView) unlazy(swiper, i);\n    }\n  }\n};\n", "import updateSize from './updateSize.js';\nimport updateSlides from './updateSlides.js';\nimport updateAutoHeight from './updateAutoHeight.js';\nimport updateSlidesOffset from './updateSlidesOffset.js';\nimport updateSlidesProgress from './updateSlidesProgress.js';\nimport updateProgress from './updateProgress.js';\nimport updateSlidesClasses from './updateSlidesClasses.js';\nimport updateActiveIndex from './updateActiveIndex.js';\nimport updateClickedSlide from './updateClickedSlide.js';\n\nexport default {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide,\n};\n", "import { elementStyle } from '../../shared/utils.js';\n\nexport default function updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if ((width === 0 && swiper.isHorizontal()) || (height === 0 && swiper.isVertical())) {\n    return;\n  }\n\n  // Subtract paddings\n  width =\n    width -\n    parseInt(elementStyle(el, 'padding-left') || 0, 10) -\n    parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height =\n    height -\n    parseInt(elementStyle(el, 'padding-top') || 0, 10) -\n    parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height,\n  });\n}\n", "import {\n  elementChildren,\n  elementOuterSize,\n  elementStyle,\n  setCSSProperty,\n} from '../../shared/utils.js';\n\nexport default function updateSlides() {\n  const swiper = this;\n  function getDirectionLabel(property) {\n    if (swiper.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom',\n    }[property];\n  }\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(getDirectionLabel(label)) || 0);\n  }\n\n  const params = swiper.params;\n\n  const { wrapperEl, slidesEl, size: swiperSize, rtlTranslate: rtl, wrongRTL } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, `.${swiper.params.slideClass}, swiper-slide`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = (parseFloat(spaceBetween.replace('%', '')) / 100) * swiperSize;\n  }\n\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach((slideEl) => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slidesLength);\n  }\n\n  // Calc slides\n  let slideSize;\n\n  const shouldResetSlideSize =\n    params.slidesPerView === 'auto' &&\n    params.breakpoints &&\n    Object.keys(params.breakpoints).filter((key) => {\n      return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n    }).length > 0;\n\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slidesLength, getDirectionLabel);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[getDirectionLabel('width')] = ``;\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal()\n          ? elementOuterSize(slide, 'width', true)\n          : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const { clientWidth, offsetWidth } = slide;\n          slideSize =\n            width +\n            paddingLeft +\n            paddingRight +\n            marginLeft +\n            marginRight +\n            (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n\n      if (slides[i]) {\n        slides[i].style[getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0)\n        slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (\n        (index - Math.min(swiper.params.slidesPerGroupSkip, index)) %\n          swiper.params.slidesPerGroup ===\n        0\n      )\n        snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n\n    swiper.virtualSize += slideSize + spaceBetween;\n\n    prevSlideSize = slideSize;\n\n    index += 1;\n  }\n\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = `${swiper.virtualSize + params.spaceBetween}px`;\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[getDirectionLabel('width')] = `${swiper.virtualSize + params.spaceBetween}px`;\n  }\n\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid, getDirectionLabel);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n\n    if (\n      Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) >\n      1\n    ) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil(\n        (swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup,\n      );\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n\n  if (params.spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : getDirectionLabel('marginRight');\n    slides\n      .filter((_, slideIndex) => {\n        if (!params.cssMode || params.loop) return true;\n        if (slideIndex === slides.length - 1) {\n          return false;\n        }\n        return true;\n      })\n      .forEach((slideEl) => {\n        slideEl.style[key] = `${spaceBetween}px`;\n      });\n  }\n\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach((slideSizeValue) => {\n      allSlidesSize += slideSizeValue + (params.spaceBetween ? params.spaceBetween : 0);\n    });\n    allSlidesSize -= params.spaceBetween;\n    const maxSnap = allSlidesSize - swiperSize;\n    snapGrid = snapGrid.map((snap) => {\n      if (snap < 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach((slideSizeValue) => {\n      allSlidesSize += slideSizeValue + (params.spaceBetween ? params.spaceBetween : 0);\n    });\n    allSlidesSize -= params.spaceBetween;\n    if (allSlidesSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid,\n  });\n\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(\n      wrapperEl,\n      '--swiper-centered-offset-after',\n      `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`,\n    );\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map((v) => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map((v) => v + addToSlidesGrid);\n  }\n\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}\n", "export default function updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n\n  const getSlideByIndex = (index) => {\n    if (isVirtual) {\n      return swiper.slides[swiper.getSlideIndexByData(index)];\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach((slide) => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = `${newHeight}px`;\n}\n", "export default function updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement\n    ? swiper.isHorizontal()\n      ? swiper.wrapperEl.offsetLeft\n      : swiper.wrapperEl.offsetTop\n    : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset =\n      (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) -\n      minusOffset -\n      swiper.cssOverflowAdjustment();\n  }\n}\n", "export default function updateSlidesProgress(translate = (this && this.translate) || 0) {\n  const swiper = this;\n  const params = swiper.params;\n\n  const { slides, rtlTranslate: rtl, snapGrid } = swiper;\n\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n\n  // Visible Slides\n  slides.forEach((slideEl) => {\n    slideEl.classList.remove(params.slideVisibleClass);\n  });\n\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n\n    const slideProgress =\n      (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) /\n      (slide.swiperSlideSize + params.spaceBetween);\n    const originalSlideProgress =\n      (offsetCenter -\n        snapGrid[0] +\n        (params.centeredSlides ? swiper.minTranslate() : 0) -\n        slideOffset) /\n      (slide.swiperSlideSize + params.spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isVisible =\n      (slideBefore >= 0 && slideBefore < swiper.size - 1) ||\n      (slideAfter > 1 && slideAfter <= swiper.size) ||\n      (slideBefore <= 0 && slideAfter >= swiper.size);\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n      slides[i].classList.add(params.slideVisibleClass);\n    }\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}\n", "export default function updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = (swiper && swiper.translate && swiper.translate * multiplier) || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let { progress, isBeginning, isEnd, progressLoop } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndexByData(0);\n    const lastSlideIndex = swiper.getSlideIndexByData(swiper.slides.length - 1);\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd,\n  });\n\n  if (params.watchSlidesProgress || (params.centeredSlides && params.autoHeight))\n    swiper.updateSlidesProgress(translate);\n\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if ((wasBeginning && !isBeginning) || (wasEnd && !isEnd)) {\n    swiper.emit('fromEdge');\n  }\n\n  swiper.emit('progress', progress);\n}\n", "import { elementChildren, elementNextAll, elementPrevAll } from '../../shared/utils.js';\n\nexport default function updateSlidesClasses() {\n  const swiper = this;\n\n  const { slides, params, slidesEl, activeIndex } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n\n  const getFilteredSlide = (selector) => {\n    return elementChildren(\n      slidesEl,\n      `.${params.slideClass}${selector}, swiper-slide${selector}`,\n    )[0];\n  };\n  slides.forEach((slideEl) => {\n    slideEl.classList.remove(params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n  });\n\n  let activeSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${slideIndex}\"]`);\n    } else {\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${activeIndex}\"]`);\n    }\n  } else {\n    activeSlide = slides[activeIndex];\n  }\n\n  if (activeSlide) {\n    // Active classes\n    activeSlide.classList.add(params.slideActiveClass);\n\n    // Next Slide\n    let nextSlide = elementNextAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n    if (params.loop && !nextSlide) {\n      nextSlide = slides[0];\n    }\n    if (nextSlide) {\n      nextSlide.classList.add(params.slideNextClass);\n    }\n    // Prev Slide\n    let prevSlide = elementPrevAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n    if (params.loop && !prevSlide === 0) {\n      prevSlide = slides[slides.length - 1];\n    }\n    if (prevSlide) {\n      prevSlide.classList.add(params.slidePrevClass);\n    }\n  }\n\n  swiper.emitSlidesClasses();\n}\n", "import { preload } from '../../shared/process-lazy-preloader.js';\n\nexport function getActiveIndexByTranslate(swiper) {\n  const { slidesGrid, params } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (\n        translate >= slidesGrid[i] &&\n        translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2\n      ) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nexport default function updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex,\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n\n  const getVirtualRealIndex = (aIndex) => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.realIndex = getVirtualRealIndex(activeIndex);\n    }\n    return;\n  }\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (swiper.slides[activeIndex]) {\n    realIndex = parseInt(\n      swiper.slides[activeIndex].getAttribute('data-swiper-slide-index') || activeIndex,\n      10,\n    );\n  } else {\n    realIndex = activeIndex;\n  }\n\n  Object.assign(swiper, {\n    previousSnapIndex,\n    snapIndex,\n    previousRealIndex,\n    realIndex,\n    previousIndex,\n    activeIndex,\n  });\n\n  if (swiper.initialized) {\n    preload(swiper);\n  }\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (previousRealIndex !== realIndex) {\n    swiper.emit('realIndexChange');\n  }\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    swiper.emit('slideChange');\n  }\n}\n", "export default function updateClickedSlide(e) {\n  const swiper = this;\n  const params = swiper.params;\n  const slide = e.closest(`.${params.slideClass}, swiper-slide`);\n  let slideFound = false;\n  let slideIndex;\n\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (\n    params.slideToClickedSlide &&\n    swiper.clickedIndex !== undefined &&\n    swiper.clickedIndex !== swiper.activeIndex\n  ) {\n    swiper.slideToClickedSlide();\n  }\n}\n", "import getTranslate from './getTranslate.js';\nimport setTranslate from './setTranslate.js';\nimport minTranslate from './minTranslate.js';\nimport maxTranslate from './maxTranslate.js';\nimport translateTo from './translateTo.js';\n\nexport default {\n  getTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo,\n};\n", "import { getTranslate } from '../../shared/utils.js';\n\nexport default function getSwiperTranslate(axis = this.isHorizontal() ? 'x' : 'y') {\n  const swiper = this;\n\n  const { params, rtlTranslate: rtl, translate, wrapperEl } = swiper;\n\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  currentTranslate += swiper.cssOverflowAdjustment();\n  if (rtl) currentTranslate = -currentTranslate;\n\n  return currentTranslate || 0;\n}\n", "export default function setTranslate(translate, byController) {\n  const swiper = this;\n  const { rtlTranslate: rtl, params, wrapperEl, progress } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    if (swiper.isHorizontal()) {\n      x -= swiper.cssOverflowAdjustment();\n    } else {\n      y -= swiper.cssOverflowAdjustment();\n    }\n    wrapperEl.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;\n  }\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n\n  swiper.emit('setTranslate', swiper.translate, byController);\n}\n", "export default function minTranslate() {\n  return -this.snapGrid[0];\n}\n", "export default function maxTranslate() {\n  return -this.snapGrid[this.snapGrid.length - 1];\n}\n", "import { animateCSSModeScroll } from '../../shared/utils.js';\n\nexport default function translateTo(\n  translate = 0,\n  speed = this.params.speed,\n  runCallbacks = true,\n  translateBounds = true,\n  internal,\n) {\n  const swiper = this;\n\n  const { params, wrapperEl } = swiper;\n\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;\n  else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;\n  else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({ swiper, targetPosition: -newTranslate, side: isH ? 'left' : 'top' });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth',\n      });\n    }\n    return true;\n  }\n\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener(\n            'transitionend',\n            swiper.onTranslateToWrapperTransitionEnd,\n          );\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n\n  return true;\n}\n", "export default function transitionEmit({ swiper, runCallbacks, direction, step }) {\n  const { activeIndex, previousIndex } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';\n    else if (activeIndex < previousIndex) dir = 'prev';\n    else dir = 'reset';\n  }\n\n  swiper.emit(`transition${step}`);\n\n  if (runCallbacks && activeIndex !== previousIndex) {\n    if (dir === 'reset') {\n      swiper.emit(`slideResetTransition${step}`);\n      return;\n    }\n    swiper.emit(`slideChangeTransition${step}`);\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}\n", "import slideTo from './slideTo.js';\nimport slideToLoop from './slideToLoop.js';\nimport slideNext from './slideNext.js';\nimport slidePrev from './slidePrev.js';\nimport slideReset from './slideReset.js';\nimport slideToClosest from './slideToClosest.js';\nimport slideToClickedSlide from './slideToClickedSlide.js';\n\nexport default {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide,\n};\n", "import { animateCSSModeScroll } from '../../shared/utils.js';\n\nexport default function slideTo(\n  index = 0,\n  speed = this.params.speed,\n  runCallbacks = true,\n  internal,\n  initial,\n) {\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled,\n  } = swiper;\n\n  if (\n    (swiper.animating && params.preventInteractionOnTransition) ||\n    (!enabled && !internal && !initial)\n  ) {\n    return false;\n  }\n\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (\n          normalizedTranslate >= normalizedGrid &&\n          normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2\n        ) {\n          slideIndex = i;\n        } else if (\n          normalizedTranslate >= normalizedGrid &&\n          normalizedTranslate < normalizedGridNext\n        ) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (\n      !swiper.allowSlideNext &&\n      translate < swiper.translate &&\n      translate < swiper.minTranslate()\n    ) {\n      return false;\n    }\n    if (\n      !swiper.allowSlidePrev &&\n      translate > swiper.translate &&\n      translate > swiper.maxTranslate()\n    ) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';\n  else if (slideIndex < activeIndex) direction = 'prev';\n  else direction = 'reset';\n\n  // Update Index\n  if ((rtl && -translate === swiper.translate) || (!rtl && translate === swiper.translate)) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({ swiper, targetPosition: t, side: isH ? 'left' : 'top' });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth',\n      });\n    }\n    return true;\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n\n  return true;\n}\n", "export default function slideToLoop(\n  index = 0,\n  speed = this.params.speed,\n  runCallbacks = true,\n  internal,\n) {\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n\n    index = indexAsNumber;\n  }\n\n  const swiper = this;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      newIndex = swiper.getSlideIndexByData(newIndex);\n    }\n  }\n\n  return swiper.slideTo(newIndex, speed, runCallbacks, internal);\n}\n", "/* eslint no-unused-vars: \"off\" */\nexport default function slideNext(speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  const { enabled, params, animating } = swiper;\n  if (!enabled) return swiper;\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({ direction: 'next' });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}\n", "/* eslint no-unused-vars: \"off\" */\nexport default function slidePrev(speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  const { params, snapGrid, slidesGrid, rtlTranslate, enabled, animating } = swiper;\n  if (!enabled) return swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n\n    swiper.loopFix({ direction: 'prev' });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map((val) => normalize(val));\n\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && params.cssMode) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (\n      params.slidesPerView === 'auto' &&\n      params.slidesPerGroup === 1 &&\n      params.slidesPerGroupAuto\n    ) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex =\n      swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual\n        ? swiper.virtual.slides.length - 1\n        : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}\n", "/* eslint no-unused-vars: \"off\" */\nexport default function slideReset(speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}\n", "/* eslint no-unused-vars: \"off\" */\nexport default function slideToClosest(\n  speed = this.params.speed,\n  runCallbacks = true,\n  internal,\n  threshold = 0.5,\n) {\n  const swiper = this;\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}\n", "import { elementChildren, nextTick } from '../../shared/utils.js';\n\nexport default function slideToClickedSlide() {\n  const swiper = this;\n  const { params, slidesEl } = swiper;\n\n  const slidesPerView =\n    params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.clickedIndex;\n  let realIndex;\n  const slideSelector = swiper.isElement ? `swiper-slide` : `.${params.slideClass}`;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      if (\n        slideToIndex < swiper.loopedSlides - slidesPerView / 2 ||\n        slideToIndex > swiper.slides.length - swiper.loopedSlides + slidesPerView / 2\n      ) {\n        swiper.loopFix();\n        slideToIndex = swiper.getSlideIndex(\n          elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0],\n        );\n\n        nextTick(() => {\n          swiper.slideTo(slideToIndex);\n        });\n      } else {\n        swiper.slideTo(slideToIndex);\n      }\n    } else if (slideToIndex > swiper.slides.length - slidesPerView) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(\n        elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0],\n      );\n\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}\n", "import loopCreate from './loopCreate.js';\nimport loopFix from './loopFix.js';\nimport loopDestroy from './loopDestroy.js';\n\nexport default {\n  loopCreate,\n  loopFix,\n  loopDestroy,\n};\n", "import { elementChildren } from '../../shared/utils.js';\n\nexport default function loopCreate(slideRealIndex) {\n  const swiper = this;\n  const { params, slidesEl } = swiper;\n  if (!params.loop || (swiper.virtual && swiper.params.virtual.enabled)) return;\n\n  const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n\n  slides.forEach((el, index) => {\n    el.setAttribute('data-swiper-slide-index', index);\n  });\n\n  swiper.loopFix({ slideRealIndex, direction: params.centeredSlides ? undefined : 'next' });\n}\n", "export default function loopFix({\n  slideRealIndex,\n  slideTo = true,\n  direction,\n  setTranslate,\n  activeSlideIndex,\n  byC<PERSON>roller,\n  byMousewheel,\n} = {}) {\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n\n  const { slides, allowSlidePrev, allowSlideNext, slidesEl, params } = swiper;\n\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n\n  const slidesPerView =\n    params.slidesPerView === 'auto'\n      ? swiper.slidesPerViewDynamic()\n      : Math.ceil(parseFloat(params.slidesPerView, 10));\n  let loopedSlides = params.loopedSlides || slidesPerView;\n  if (loopedSlides % params.slidesPerGroup !== 0) {\n    loopedSlides += params.slidesPerGroup - (loopedSlides % params.slidesPerGroup);\n  }\n  swiper.loopedSlides = loopedSlides;\n\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n\n  let activeIndex = swiper.activeIndex;\n\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(\n      swiper.slides.filter((el) => el.classList.contains(params.slideActiveClass))[0],\n    );\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  // prepend last slides before start\n  if (activeSlideIndex < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeSlideIndex, params.slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeSlideIndex; i += 1) {\n      const index = i - Math.floor(i / slides.length) * slides.length;\n      prependSlidesIndexes.push(slides.length - index - 1);\n    }\n  } else if (activeSlideIndex /* + slidesPerView */ > swiper.slides.length - loopedSlides * 2) {\n    slidesAppended = Math.max(\n      activeSlideIndex - (swiper.slides.length - loopedSlides * 2),\n      params.slidesPerGroup,\n    );\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / slides.length) * slides.length;\n      appendSlidesIndexes.push(index);\n    }\n  }\n\n  if (isPrev) {\n    prependSlidesIndexes.forEach((index) => {\n      slidesEl.prepend(swiper.slides[index]);\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach((index) => {\n      slidesEl.append(swiper.slides[index]);\n    });\n  }\n\n  swiper.recalcSlides();\n  if (params.slidesPerView === 'auto') {\n    swiper.updateSlides();\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + slidesPrepended, 0, false, true);\n          if (setTranslate) {\n            swiper.touches[swiper.isHorizontal() ? 'startX' : 'startY'] += diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          swiper.slideToLoop(slideRealIndex, 0, false, true);\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touches[swiper.isHorizontal() ? 'startX' : 'startY'] += diff;\n          }\n        }\n      } else {\n        swiper.slideToLoop(slideRealIndex, 0, false, true);\n      }\n    }\n  }\n\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      slideTo: false,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true,\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach((c) => {\n        if (!c.destroyed && c.params.loop) c.loopFix(loopParams);\n      });\n    } else if (\n      swiper.controller.control instanceof swiper.constructor &&\n      swiper.controller.control.params.loop\n    ) {\n      swiper.controller.control.loopFix(loopParams);\n    }\n  }\n\n  swiper.emit('loopFix');\n}\n", "export default function loopDestroy() {\n  const swiper = this;\n  const { params, slidesEl } = swiper;\n  if (!params.loop || (swiper.virtual && swiper.params.virtual.enabled)) return;\n  swiper.recalcSlides();\n\n  const newSlidesOrder = [];\n  swiper.slides.forEach((slideEl) => {\n    const index =\n      typeof slideEl.swiperSlideIndex === 'undefined'\n        ? slideEl.getAttribute('data-swiper-slide-index') * 1\n        : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  swiper.slides.forEach((slideEl) => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach((slideEl) => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}\n", "import { getWindow, getDocument } from 'ssr-window';\nimport { now } from '../../shared/utils.js';\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector, base = this) {\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\n\nexport default function onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  const window = getWindow();\n\n  const data = swiper.touchEventsData;\n  data.evCache.push(event);\n  const { params, touches, enabled } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetEl = e.target;\n\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!swiper.wrapperEl.contains(targetEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = event.composedPath ? event.composedPath() : event.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n\n  const noSwipingSelector = params.noSwipingSelector\n    ? params.noSwipingSelector\n    : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (\n    params.noSwiping &&\n    (isTargetShadow\n      ? closestElement(noSwipingSelector, targetEl)\n      : targetEl.closest(noSwipingSelector))\n  ) {\n    swiper.allowClick = true;\n    return;\n  }\n\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  const edgeSwipeDetection = params.edgeSwipeDetection || params.iOSEdgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold || params.iOSEdgeSwipeThreshold;\n  if (\n    edgeSwipeDetection &&\n    (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)\n  ) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n    } else {\n      return;\n    }\n  }\n\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined,\n  });\n\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (\n    document.activeElement &&\n    document.activeElement.matches(data.focusableElements) &&\n    document.activeElement !== targetEl\n  ) {\n    document.activeElement.blur();\n  }\n\n  const shouldPreventDefault =\n    preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if (\n    (params.touchStartForcePreventDefault || shouldPreventDefault) &&\n    !targetEl.isContentEditable\n  ) {\n    e.preventDefault();\n  }\n  if (\n    swiper.params.freeMode &&\n    swiper.params.freeMode.enabled &&\n    swiper.freeMode &&\n    swiper.animating &&\n    !params.cssMode\n  ) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}\n", "import { getDocument } from 'ssr-window';\nimport { now } from '../../shared/utils.js';\n\nexport default function onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const { params, touches, rtlTranslate: rtl, enabled } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n\n  const pointerIndex = data.evCache.findIndex((cachedEv) => cachedEv.pointerId === e.pointerId);\n  if (pointerIndex >= 0) data.evCache[pointerIndex] = e;\n  const targetTouch = data.evCache.length > 1 ? data.evCache[0] : e;\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        prevX: swiper.touches.currentX,\n        prevY: swiper.touches.currentY,\n        currentX: pageX,\n        currentY: pageY,\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (\n        (pageY < touches.startY && swiper.translate <= swiper.maxTranslate()) ||\n        (pageY > touches.startY && swiper.translate >= swiper.minTranslate())\n      ) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (\n      (pageX < touches.startX && swiper.translate <= swiper.maxTranslate()) ||\n      (pageX > touches.startX && swiper.translate >= swiper.minTranslate())\n    ) {\n      return;\n    }\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  if (e.targetTouches && e.targetTouches.length > 1) return;\n\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold)\n    return;\n\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (\n      (swiper.isHorizontal() && touches.currentY === touches.startY) ||\n      (swiper.isVertical() && touches.currentX === touches.startX)\n    ) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = (Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180) / Math.PI;\n        data.isScrolling = swiper.isHorizontal()\n          ? touchAngle > params.touchAngle\n          : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (\n    data.isScrolling ||\n    (swiper.zoom && swiper.params.zoom && swiper.params.zoom.enabled && data.evCache.length > 1)\n  ) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal()\n    ? touches.currentX - touches.previousX\n    : touches.currentY - touches.previousY;\n\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n\n  const isLoop = swiper.params.loop && !params.cssMode;\n\n  if (!data.isMoved) {\n    if (isLoop) {\n      swiper.loopFix({ direction: swiper.swipeDirection });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true,\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  if (\n    data.isMoved &&\n    prevTouchesDirection !== swiper.touchesDirection &&\n    isLoop &&\n    Math.abs(diff) >= 1\n  ) {\n    // need another loop fix\n    swiper.loopFix({ direction: swiper.swipeDirection, setTranslate: true });\n    loopFixed = true;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n\n  data.currentTranslate = diff + data.startTranslate;\n\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (\n      isLoop &&\n      !loopFixed &&\n      data.currentTranslate >\n        (params.centeredSlides ? swiper.minTranslate() - swiper.size / 2 : swiper.minTranslate())\n    ) {\n      swiper.loopFix({ direction: 'prev', setTranslate: true, activeSlideIndex: 0 });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate =\n          swiper.minTranslate() -\n          1 +\n          (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (\n      isLoop &&\n      !loopFixed &&\n      data.currentTranslate <\n        (params.centeredSlides ? swiper.maxTranslate() + swiper.size / 2 : swiper.maxTranslate())\n    ) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex:\n          swiper.slides.length -\n          (params.slidesPerView === 'auto'\n            ? swiper.slidesPerViewDynamic()\n            : Math.ceil(parseFloat(params.slidesPerView, 10))),\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate =\n          swiper.maxTranslate() +\n          1 -\n          (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (\n    !swiper.allowSlideNext &&\n    swiper.swipeDirection === 'next' &&\n    data.currentTranslate < data.startTranslate\n  ) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (\n    !swiper.allowSlidePrev &&\n    swiper.swipeDirection === 'prev' &&\n    data.currentTranslate > data.startTranslate\n  ) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal()\n          ? touches.currentX - touches.startX\n          : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (\n    (params.freeMode && params.freeMode.enabled && swiper.freeMode) ||\n    params.watchSlidesProgress\n  ) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (swiper.params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}\n", "import { now, nextTick } from '../../shared/utils.js';\n\nexport default function onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const pointerIndex = data.evCache.findIndex((cachedEv) => cachedEv.pointerId === event.pointerId);\n  if (pointerIndex >= 0) {\n    data.evCache.splice(pointerIndex, 1);\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave'].includes(event.type)) {\n    const proceed =\n      event.type === 'pointercancel' && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n\n  const { params, touches, rtlTranslate: rtl, slidesGrid, enabled } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  // Return Grab Cursor\n  if (\n    params.grabCursor &&\n    data.isMoved &&\n    data.isTouched &&\n    (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)\n  ) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || (e.composedPath && e.composedPath());\n    swiper.updateClickedSlide((pathTree && pathTree[0]) || e.target);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n\n  if (\n    !data.isTouched ||\n    !data.isMoved ||\n    !swiper.swipeDirection ||\n    touches.diff === 0 ||\n    data.currentTranslate === data.startTranslate\n  ) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n\n  if (params.cssMode) {\n    return;\n  }\n\n  if (swiper.params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({ currentPos });\n    return;\n  }\n\n  // Find current slide\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (\n    let i = 0;\n    i < slidesGrid.length;\n    i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup\n  ) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex =\n        swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual\n          ? swiper.virtual.slides.length - 1\n          : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio)\n        swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);\n      else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (\n        rewindLastIndex !== null &&\n        ratio < 0 &&\n        Math.abs(ratio) > params.longSwipesRatio\n      ) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget =\n      swiper.navigation &&\n      (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}\n", "export default function onResize() {\n  const swiper = this;\n\n  const { params, el } = swiper;\n\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const { allowSlideNext, allowSlidePrev, snapGrid } = swiper;\n\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n\n  swiper.updateSize();\n  swiper.updateSlides();\n\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if (\n    (params.slidesPerView === 'auto' || params.slidesPerView > 1) &&\n    swiper.isEnd &&\n    !swiper.isBeginning &&\n    !swiper.params.centeredSlides &&\n    !isVirtualLoop\n  ) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(swiper.autoplay.resizeTimeout);\n    swiper.autoplay.resizeTimeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}\n", "export default function onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}\n", "export default function onScroll() {\n  const swiper = this;\n  const { wrapperEl, rtlTranslate, enabled } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n\n  swiper.emit('setTranslate', swiper.translate, false);\n}\n", "import { processLazyPreloader } from '../../shared/process-lazy-preloader.js';\n\nexport default function onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  swiper.update();\n}\n", "import { getDocument } from 'ssr-window';\n\nimport onTouchStart from './onTouchStart.js';\nimport onTouchMove from './onTouchMove.js';\nimport onTouchEnd from './onTouchEnd.js';\nimport onResize from './onResize.js';\nimport onClick from './onClick.js';\nimport onScroll from './onScroll.js';\nimport onLoad from './onLoad.js';\n\nlet dummyEventAttached = false;\nfunction dummyEventListener() {}\n\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const { params, el, wrapperEl, device } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n\n  // Touch Events\n  el[domMethod]('pointerdown', swiper.onTouchStart, { passive: false });\n  document[domMethod]('pointermove', swiper.onTouchMove, { passive: false, capture });\n  document[domMethod]('pointerup', swiper.onTouchEnd, { passive: true });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, { passive: true });\n  document[domMethod]('pointerout', swiper.onTouchEnd, { passive: true });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, { passive: true });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](\n      device.ios || device.android\n        ? 'resize orientationchange observerUpdate'\n        : 'resize observerUpdate',\n      onResize,\n      true,\n    );\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, { capture: true });\n};\n\nfunction attachEvents() {\n  const swiper = this;\n  const document = getDocument();\n  const { params } = swiper;\n\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n\n  if (!dummyEventAttached) {\n    document.addEventListener('touchstart', dummyEventListener);\n    dummyEventAttached = true;\n  }\n\n  events(swiper, 'on');\n}\n\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\n\nexport default {\n  attachEvents,\n  detachEvents,\n};\n", "import { extend } from '../../shared/utils.js';\n\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\n\nexport default function setBreakpoint() {\n  const swiper = this;\n  const { realIndex, initialized, params, el } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || (breakpoints && Object.keys(breakpoints).length === 0)) return;\n\n  // Get breakpoint for window width and update parameters\n  const breakpoint = swiper.getBreakpoint(breakpoints, swiper.params.breakpointsBase, swiper.el);\n\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n\n  const wasEnabled = params.enabled;\n\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(\n      `${params.containerModifierClass}grid`,\n      `${params.containerModifierClass}grid-column`,\n    );\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(`${params.containerModifierClass}grid`);\n    if (\n      (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column') ||\n      (!breakpointParams.grid.fill && params.grid.fill === 'column')\n    ) {\n      el.classList.add(`${params.containerModifierClass}grid-column`);\n    }\n    swiper.emitContainerClasses();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach((prop) => {\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n\n  const directionChanged =\n    breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop =\n    params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n\n  const isEnabled = swiper.params.enabled;\n\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev,\n  });\n\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n\n  swiper.currentBreakpoint = breakpoint;\n\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n\n  if (needsReLoop && initialized) {\n    swiper.loopDestroy();\n    swiper.loopCreate(realIndex);\n    swiper.updateSlides();\n  }\n\n  swiper.emit('breakpoint', breakpointParams);\n}\n", "function checkOverflow() {\n  const swiper = this;\n  const { isLocked: wasLocked, params } = swiper;\n  const { slidesOffsetBefore } = params;\n\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge =\n      swiper.slidesGrid[lastSlideIndex] +\n      swiper.slidesSizesGrid[lastSlideIndex] +\n      slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\n\nexport default { checkOverflow };\n", "export default {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n\n  // Overrides\n  width: null,\n  height: null,\n\n  //\n  preventInteractionOnTransition: false,\n\n  // ssr\n  userAgent: null,\n  url: null,\n\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n\n  // Autoheight\n  autoHeight: false,\n\n  // Set wrapper width\n  setWrapperSize: false,\n\n  // Virtual Translate\n  virtualTranslate: false,\n\n  // Effects\n  effect: 'slide', // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0, // in px\n  slidesOffsetAfter: 0, // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n\n  // Round length\n  roundLengths: false,\n\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n\n  // Progress\n  watchSlidesProgress: false,\n\n  // Cursor\n  grabCursor: false,\n\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n\n  // loop\n  loop: false,\n  loopedSlides: null,\n  loopPreventsSliding: true,\n\n  // rewind\n  rewind: false,\n\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null, // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n\n  // Passive Listeners\n  passiveListeners: true,\n\n  maxBackfaceHiddenSlides: 10,\n\n  // NS\n  containerModifierClass: 'swiper-', // NEW\n  slideClass: 'swiper-slide',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n  lazyPreloadPrevNext: 0,\n\n  // Callbacks\n  runCallbacksOnInit: true,\n\n  // Internals\n  _emitClasses: false,\n};\n", "import { extend } from '../shared/utils.js';\n\nexport default function moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj = {}) {\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (\n      ['navigation', 'pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 &&\n      params[moduleParamName] === true\n    ) {\n      params[moduleParamName] = { auto: true };\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = { enabled: true };\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = { enabled: false };\n    extend(allModulesParams, obj);\n  };\n}\n", "/* eslint no-param-reassign: \"off\" */\nimport { getDocument } from 'ssr-window';\nimport {\n  extend,\n  deleteProps,\n  createElement,\n  elementChildren,\n  elementStyle,\n  elementIndex,\n} from '../shared/utils.js';\nimport { getSupport } from '../shared/get-support.js';\nimport { getDevice } from '../shared/get-device.js';\nimport { getBrowser } from '../shared/get-browser.js';\n\nimport Resize from './modules/resize/resize.js';\nimport Observer from './modules/observer/observer.js';\n\nimport eventsEmitter from './events-emitter.js';\n\nimport update from './update/index.js';\nimport translate from './translate/index.js';\nimport transition from './transition/index.js';\nimport slide from './slide/index.js';\nimport loop from './loop/index.js';\nimport grabCursor from './grab-cursor/index.js';\nimport events from './events/index.js';\nimport breakpoints from './breakpoints/index.js';\nimport classes from './classes/index.js';\nimport checkOverflow from './check-overflow/index.js';\n\nimport defaults from './defaults.js';\nimport moduleExtendParams from './moduleExtendParams.js';\nimport { processLazyPreloader, preload } from '../shared/process-lazy-preloader.js';\n\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events,\n  breakpoints,\n  checkOverflow,\n  classes,\n};\n\nconst extendedDefaults = {};\n\nclass Swiper {\n  constructor(...args) {\n    let el;\n    let params;\n    if (\n      args.length === 1 &&\n      args[0].constructor &&\n      Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object'\n    ) {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n\n    const document = getDocument();\n\n    if (\n      params.el &&\n      typeof params.el === 'string' &&\n      document.querySelectorAll(params.el).length > 1\n    ) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach((containerEl) => {\n        const newParams = extend({}, params, { el: containerEl });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({ userAgent: params.userAgent });\n    swiper.browser = getBrowser();\n\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n\n    const allModulesParams = {};\n    swiper.modules.forEach((mod) => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper),\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach((eventName) => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n\n      // Classes\n      classNames: [],\n\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n\n      //\n      isBeginning: true,\n      isEnd: false,\n\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n\n      cssOverflowAdjustment() {\n        // Returns 0 unless `translate` is > 2**23\n        // Should be subtracted from css values to prevent overflow\n        return Math.trunc(this.translate / 2 ** 23) * 2 ** 23;\n      },\n\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: 0,\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        evCache: [],\n      },\n\n      // Clicks\n      allowClick: true,\n\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0,\n      },\n\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0,\n    });\n\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n\n  getSlideIndex(slideEl) {\n    const { slidesEl, params } = this;\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n\n  getSlideIndexByData(index) {\n    return this.getSlideIndex(\n      this.slides.filter(\n        (slideEl) => slideEl.getAttribute('data-swiper-slide-index') * 1 === index,\n      )[0],\n    );\n  }\n\n  recalcSlides() {\n    const swiper = this;\n    const { slidesEl, params } = swiper;\n    swiper.slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  }\n\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter((className) => {\n      return (\n        className.indexOf('swiper') === 0 ||\n        className.indexOf(swiper.params.containerModifierClass) === 0\n      );\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n\n    return slideEl.className\n      .split(' ')\n      .filter((className) => {\n        return (\n          className.indexOf('swiper-slide') === 0 ||\n          className.indexOf(swiper.params.slideClass) === 0\n        );\n      })\n      .join(' ');\n  }\n\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach((slideEl) => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({ slideEl, classNames });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n\n  slidesPerViewDynamic(view = 'current', exact = false) {\n    const swiper = this;\n    const { params, slides, slidesGrid, slidesSizesGrid, size: swiperSize, activeIndex } = swiper;\n    let spv = 1;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex].swiperSlideSize;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact\n            ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize\n            : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const { snapGrid, params } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach((imageEl) => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(\n        Math.max(translateValue, swiper.maxTranslate()),\n        swiper.minTranslate(),\n      );\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (swiper.params.freeMode && swiper.params.freeMode.enabled) {\n      setTranslate();\n      if (swiper.params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if (\n        (swiper.params.slidesPerView === 'auto' || swiper.params.slidesPerView > 1) &&\n        swiper.isEnd &&\n        !swiper.params.centeredSlides\n      ) {\n        const slides =\n          swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides : swiper.slides;\n        translated = swiper.slideTo(slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n\n  changeDirection(newDirection, needUpdate = true) {\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (\n      newDirection === currentDirection ||\n      (newDirection !== 'horizontal' && newDirection !== 'vertical')\n    ) {\n      return swiper;\n    }\n\n    swiper.el.classList.remove(`${swiper.params.containerModifierClass}${currentDirection}`);\n    swiper.el.classList.add(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n\n    swiper.params.direction = newDirection;\n\n    swiper.slides.forEach((slideEl) => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n\n    return swiper;\n  }\n\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if ((swiper.rtl && direction === 'rtl') || (!swiper.rtl && direction === 'ltr')) return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n\n    el.swiper = swiper;\n    if (el.shadowEl) {\n      swiper.isElement = true;\n    }\n\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, `.${swiper.params.slideClass}`).forEach((slideEl) => {\n        wrapperEl.append(slideEl);\n      });\n    }\n\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement ? el : wrapperEl,\n      mounted: true,\n\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate:\n        swiper.params.direction === 'horizontal' &&\n        (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box',\n    });\n\n    return true;\n  }\n\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(\n        swiper.params.initialSlide + swiper.virtual.slidesBefore,\n        0,\n        swiper.params.runCallbacksOnInit,\n        false,\n        true,\n      );\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate();\n    }\n\n    // Attach events\n    swiper.attachEvents();\n\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach((imageEl) => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', (e) => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n    preload(swiper);\n\n    // Init Flag\n    swiper.initialized = true;\n\n    preload(swiper);\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n\n    return swiper;\n  }\n\n  destroy(deleteInstance = true, cleanStyles = true) {\n    const swiper = this;\n    const { params, el, wrapperEl, slides } = swiper;\n\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      el.removeAttribute('style');\n      wrapperEl.removeAttribute('style');\n      if (slides && slides.length) {\n        slides.forEach((slideEl) => {\n          slideEl.classList.remove(\n            params.slideVisibleClass,\n            params.slideActiveClass,\n            params.slideNextClass,\n            params.slidePrevClass,\n          );\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach((eventName) => {\n      swiper.off(eventName);\n    });\n\n    if (deleteInstance !== false) {\n      swiper.el.swiper = null;\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n\n    return null;\n  }\n\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n\n  static get defaults() {\n    return defaults;\n  }\n\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach((m) => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\n\nObject.keys(prototypes).forEach((prototypeGroup) => {\n  Object.keys(prototypes[prototypeGroup]).forEach((protoMethod) => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\n\nSwiper.use([Resize, Observer]);\n\nexport default Swiper;\n", "import setTransition from './setTransition.js';\nimport transitionStart from './transitionStart.js';\nimport transitionEnd from './transitionEnd.js';\n\nexport default {\n  setTransition,\n  transitionStart,\n  transitionEnd,\n};\n", "export default function setTransition(duration, byController) {\n  const swiper = this;\n\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = `${duration}ms`;\n  }\n\n  swiper.emit('setTransition', duration, byController);\n}\n", "import transitionEmit from './transitionEmit.js';\n\nexport default function transitionStart(runCallbacks = true, direction) {\n  const swiper = this;\n  const { params } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n\n  transitionEmit({ swiper, runCallbacks, direction, step: 'Start' });\n}\n", "import transitionEmit from './transitionEmit.js';\n\nexport default function transitionEnd(runCallbacks = true, direction) {\n  const swiper = this;\n  const { params } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n\n  transitionEmit({ swiper, runCallbacks, direction, step: 'End' });\n}\n", "import setGrabCursor from './setGrabCursor.js';\nimport unsetGrabCursor from './unsetGrabCursor.js';\n\nexport default {\n  setGrabCursor,\n  unsetGrabCursor,\n};\n", "export default function setGrabCursor(moving) {\n  const swiper = this;\n  if (\n    !swiper.params.simulateTouch ||\n    (swiper.params.watchOverflow && swiper.isLocked) ||\n    swiper.params.cssMode\n  )\n    return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n", "export default function unsetGrabCursor() {\n  const swiper = this;\n  if ((swiper.params.watchOverflow && swiper.isLocked) || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n", "import setBreakpoint from './setBreakpoint.js';\nimport getBreakpoint from './getBreakpoint.js';\n\nexport default { setBreakpoint, getBreakpoint };\n", "import { getWindow } from 'ssr-window';\n\nexport default function getBreakpoint(breakpoints, base = 'window', containerEl) {\n  if (!breakpoints || (base === 'container' && !containerEl)) return undefined;\n  let breakpoint = false;\n\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n\n  const points = Object.keys(breakpoints).map((point) => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return { value, point };\n    }\n    return { value: point, point };\n  });\n\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const { point, value } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}\n", "import addClasses from './addClasses.js';\nimport removeClasses from './removeClasses.js';\n\nexport default { addClasses, removeClasses };\n", "function prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach((item) => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach((classNames) => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\n\nexport default function addClasses() {\n  const swiper = this;\n  const { classNames, params, rtl, el, device } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses([\n    'initialized',\n    params.direction,\n    { 'free-mode': swiper.params.freeMode && params.freeMode.enabled },\n    { 'autoheight': params.autoHeight },\n    { 'rtl': rtl },\n    { 'grid': params.grid && params.grid.rows > 1 },\n    { 'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column' },\n    { 'android': device.android },\n    { 'ios': device.ios },\n    { 'css-mode': params.cssMode },\n    { 'centered': params.cssMode && params.centeredSlides },\n    { 'watch-progress': params.watchSlidesProgress },\n  ], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}\n", "export default function removeClasses() {\n  const swiper = this;\n  const { el, classNames } = swiper;\n\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}\n", "import { createElement, elementChildren } from './utils.js';\n\nexport default function createElementIfNotDefined(swiper, originalParams, params, checkProps) {\n  if (swiper.params.createElements) {\n    Object.keys(checkProps).forEach((key) => {\n      if (!params[key] && params.auto === true) {\n        let element = elementChildren(swiper.el, `.${checkProps[key]}`)[0];\n        if (!element) {\n          element = createElement('div', checkProps[key]);\n          element.className = checkProps[key];\n          swiper.el.append(element);\n        }\n        params[key] = element;\n        originalParams[key] = element;\n      }\n    });\n  }\n  return params;\n}\n", "export default function classesToSelector(classes = '') {\n  return `.${classes\n    .trim()\n    .replace(/([\\.:!+\\/])/g, '\\\\$1') // eslint-disable-line\n    .replace(/ /g, '.')}`;\n}\n", "export default function appendSlide(slides) {\n  const swiper = this;\n  const { params, slidesEl } = swiper;\n\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n\n  const appendElement = (slideEl) => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      tempDOM.innerHTML = slideEl;\n      slidesEl.append(tempDOM.children[0]);\n      tempDOM.innerHTML = '';\n    } else {\n      slidesEl.append(slideEl);\n    }\n  };\n\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) appendElement(slides[i]);\n    }\n  } else {\n    appendElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n}\n", "export default function prependSlide(slides) {\n  const swiper = this;\n  const { params, activeIndex, slidesEl } = swiper;\n\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndex + 1;\n  const prependElement = (slideEl) => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      tempDOM.innerHTML = slideEl;\n      slidesEl.prepend(tempDOM.children[0]);\n      tempDOM.innerHTML = '';\n    } else {\n      slidesEl.prepend(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) prependElement(slides[i]);\n    }\n    newActiveIndex = activeIndex + slides.length;\n  } else {\n    prependElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  swiper.slideTo(newActiveIndex, 0, false);\n}\n", "export default function addSlide(index, slides) {\n  const swiper = this;\n  const { params, activeIndex, slidesEl } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n    swiper.recalcSlides();\n  }\n  const baseLength = swiper.slides.length;\n  if (index <= 0) {\n    swiper.prependSlide(slides);\n    return;\n  }\n  if (index >= baseLength) {\n    swiper.appendSlide(slides);\n    return;\n  }\n  let newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + 1 : activeIndexBuffer;\n\n  const slidesBuffer = [];\n  for (let i = baseLength - 1; i >= index; i -= 1) {\n    const currentSlide = swiper.slides[i];\n    currentSlide.remove();\n    slidesBuffer.unshift(currentSlide);\n  }\n\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) slidesEl.append(slides[i]);\n    }\n    newActiveIndex =\n      activeIndexBuffer > index ? activeIndexBuffer + slides.length : activeIndexBuffer;\n  } else {\n    slidesEl.append(slides);\n  }\n\n  for (let i = 0; i < slidesBuffer.length; i += 1) {\n    slidesEl.append(slidesBuffer[i]);\n  }\n\n  swiper.recalcSlides();\n\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}\n", "export default function removeSlide(slidesIndexes) {\n  const swiper = this;\n  const { params, activeIndex } = swiper;\n\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndexBuffer;\n  let indexToRemove;\n\n  if (typeof slidesIndexes === 'object' && 'length' in slidesIndexes) {\n    for (let i = 0; i < slidesIndexes.length; i += 1) {\n      indexToRemove = slidesIndexes[i];\n      if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n      if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    }\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  } else {\n    indexToRemove = slidesIndexes;\n    if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n    if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  }\n\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}\n", "export default function removeAllSlides() {\n  const swiper = this;\n\n  const slidesIndexes = [];\n  for (let i = 0; i < swiper.slides.length; i += 1) {\n    slidesIndexes.push(i);\n  }\n  swiper.removeSlide(slidesIndexes);\n}\n", "import { getWindow } from 'ssr-window';\n\nexport default function Resize({ swiper, on, emit }) {\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver((entries) => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const { width, height } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(({ contentBoxSize, contentRect, target }) => {\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect\n            ? contentRect.width\n            : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect\n            ? contentRect.height\n            : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}\n", "import { getWindow } from 'ssr-window';\nimport { elementParents } from '../../../shared/utils.js';\n\nexport default function Observer({ swiper, extendParams, on, emit }) {\n  const observers = [];\n  const window = getWindow();\n  const attach = (target, options = {}) => {\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc((mutations) => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: typeof options.childList === 'undefined' ? true : options.childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData,\n    });\n\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.el);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.el, {\n      childList: swiper.params.observeSlideChildren,\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, { attributes: false });\n  };\n  const destroy = () => {\n    observers.forEach((observer) => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false,\n  });\n  on('init', init);\n  on('destroy', destroy);\n}\n", "// Swiper Class\nimport Swiper from './core/core.js';\n\n//IMPORT_MODULES\n\nconst modules = [\n  //INSTALL_MODULES\n];\n\nSwiper.use(modules);\n\n//EXPORT\n", "import { getDocument } from 'ssr-window';\nimport { createElement, elementChildren, setCSSProperty } from '../../shared/utils.js';\n\nexport default function Virtual({ swiper, extendParams, on, emit }) {\n  extendParams({\n    virtual: {\n      enabled: false,\n      slides: [],\n      cache: true,\n      renderSlide: null,\n      renderExternal: null,\n      renderExternalUpdate: true,\n      addSlidesBefore: 0,\n      addSlidesAfter: 0,\n    },\n  });\n\n  let cssModeTimeout;\n  const document = getDocument();\n\n  swiper.virtual = {\n    cache: {},\n    from: undefined,\n    to: undefined,\n    slides: [],\n    offset: 0,\n    slidesGrid: [],\n  };\n\n  const tempDOM = document.createElement('div');\n\n  function renderSlide(slide, index) {\n    const params = swiper.params.virtual;\n    if (params.cache && swiper.virtual.cache[index]) {\n      return swiper.virtual.cache[index];\n    }\n    // eslint-disable-next-line\n    let slideEl;\n    if (params.renderSlide) {\n      slideEl = params.renderSlide.call(swiper, slide, index);\n      if (typeof slideEl === 'string') {\n        tempDOM.innerHTML = slideEl;\n        slideEl = tempDOM.children[0];\n      }\n    } else if (swiper.isElement) {\n      slideEl = createElement('swiper-slide');\n    } else {\n      slideEl = createElement('div', swiper.params.slideClass);\n    }\n    slideEl.setAttribute('data-swiper-slide-index', index);\n    if (!params.renderSlide) {\n      slideEl.innerHTML = slide;\n    }\n\n    if (params.cache) swiper.virtual.cache[index] = slideEl;\n    return slideEl;\n  }\n\n  function update(force) {\n    const { slidesPerView, slidesPerGroup, centeredSlides, loop: isLoop } = swiper.params;\n    const { addSlidesBefore, addSlidesAfter } = swiper.params.virtual;\n    const {\n      from: previousFrom,\n      to: previousTo,\n      slides,\n      slidesGrid: previousSlidesGrid,\n      offset: previousOffset,\n    } = swiper.virtual;\n    if (!swiper.params.cssMode) {\n      swiper.updateActiveIndex();\n    }\n\n    const activeIndex = swiper.activeIndex || 0;\n\n    let offsetProp;\n    if (swiper.rtlTranslate) offsetProp = 'right';\n    else offsetProp = swiper.isHorizontal() ? 'left' : 'top';\n\n    let slidesAfter;\n    let slidesBefore;\n    if (centeredSlides) {\n      slidesAfter = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesAfter;\n      slidesBefore = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesBefore;\n    } else {\n      slidesAfter = slidesPerView + (slidesPerGroup - 1) + addSlidesAfter;\n      slidesBefore = (isLoop ? slidesPerView : slidesPerGroup) + addSlidesBefore;\n    }\n    let from = activeIndex - slidesBefore;\n    let to = activeIndex + slidesAfter;\n    if (!isLoop) {\n      from = Math.max(from, 0);\n      to = Math.min(to, slides.length - 1);\n    }\n    let offset = (swiper.slidesGrid[from] || 0) - (swiper.slidesGrid[0] || 0);\n    if (isLoop && activeIndex >= slidesBefore) {\n      from -= slidesBefore;\n      if (!centeredSlides) offset += swiper.slidesGrid[0];\n    } else if (isLoop && activeIndex < slidesBefore) {\n      from = -slidesBefore;\n      if (centeredSlides) offset += swiper.slidesGrid[0];\n    }\n\n    Object.assign(swiper.virtual, {\n      from,\n      to,\n      offset,\n      slidesGrid: swiper.slidesGrid,\n      slidesBefore,\n      slidesAfter,\n    });\n\n    function onRendered() {\n      swiper.updateSlides();\n      swiper.updateProgress();\n      swiper.updateSlidesClasses();\n      emit('virtualUpdate');\n    }\n\n    if (previousFrom === from && previousTo === to && !force) {\n      if (swiper.slidesGrid !== previousSlidesGrid && offset !== previousOffset) {\n        swiper.slides.forEach((slideEl) => {\n          slideEl.style[offsetProp] = `${offset - Math.abs(swiper.cssOverflowAdjustment())}px`;\n        });\n      }\n      swiper.updateProgress();\n      emit('virtualUpdate');\n      return;\n    }\n    if (swiper.params.virtual.renderExternal) {\n      swiper.params.virtual.renderExternal.call(swiper, {\n        offset,\n        from,\n        to,\n        slides: (function getSlides() {\n          const slidesToRender = [];\n          for (let i = from; i <= to; i += 1) {\n            slidesToRender.push(slides[i]);\n          }\n          return slidesToRender;\n        })(),\n      });\n      if (swiper.params.virtual.renderExternalUpdate) {\n        onRendered();\n      } else {\n        emit('virtualUpdate');\n      }\n      return;\n    }\n    const prependIndexes = [];\n    const appendIndexes = [];\n\n    const getSlideIndex = (index) => {\n      let slideIndex = index;\n      if (index < 0) {\n        slideIndex = slides.length + index;\n      } else if (slideIndex >= slides.length) {\n        // eslint-disable-next-line\n        slideIndex = slideIndex - slides.length;\n      }\n      return slideIndex;\n    };\n\n    if (force) {\n      swiper.slidesEl\n        .querySelectorAll(`.${swiper.params.slideClass}, swiper-slide`)\n        .forEach((slideEl) => {\n          slideEl.remove();\n        });\n    } else {\n      for (let i = previousFrom; i <= previousTo; i += 1) {\n        if (i < from || i > to) {\n          const slideIndex = getSlideIndex(i);\n          swiper.slidesEl\n            .querySelectorAll(\n              `.${swiper.params.slideClass}[data-swiper-slide-index=\"${slideIndex}\"], swiper-slide[data-swiper-slide-index=\"${slideIndex}\"]`,\n            )\n            .forEach((slideEl) => {\n              slideEl.remove();\n            });\n        }\n      }\n    }\n\n    const loopFrom = isLoop ? -slides.length : 0;\n    const loopTo = isLoop ? slides.length * 2 : slides.length;\n    for (let i = loopFrom; i < loopTo; i += 1) {\n      if (i >= from && i <= to) {\n        const slideIndex = getSlideIndex(i);\n        if (typeof previousTo === 'undefined' || force) {\n          appendIndexes.push(slideIndex);\n        } else {\n          if (i > previousTo) appendIndexes.push(slideIndex);\n          if (i < previousFrom) prependIndexes.push(slideIndex);\n        }\n      }\n    }\n    appendIndexes.forEach((index) => {\n      swiper.slidesEl.append(renderSlide(slides[index], index));\n    });\n    if (isLoop) {\n      for (let i = prependIndexes.length - 1; i >= 0; i -= 1) {\n        const index = prependIndexes[i];\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      }\n    } else {\n      prependIndexes.sort((a, b) => b - a);\n      prependIndexes.forEach((index) => {\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      });\n    }\n    elementChildren(swiper.slidesEl, '.swiper-slide, swiper-slide').forEach((slideEl) => {\n      slideEl.style[offsetProp] = `${offset - Math.abs(swiper.cssOverflowAdjustment())}px`;\n    });\n    onRendered();\n  }\n\n  function appendSlide(slides) {\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.push(slides[i]);\n      }\n    } else {\n      swiper.virtual.slides.push(slides);\n    }\n    update(true);\n  }\n  function prependSlide(slides) {\n    const activeIndex = swiper.activeIndex;\n    let newActiveIndex = activeIndex + 1;\n    let numberOfNewSlides = 1;\n\n    if (Array.isArray(slides)) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.unshift(slides[i]);\n      }\n      newActiveIndex = activeIndex + slides.length;\n      numberOfNewSlides = slides.length;\n    } else {\n      swiper.virtual.slides.unshift(slides);\n    }\n    if (swiper.params.virtual.cache) {\n      const cache = swiper.virtual.cache;\n      const newCache = {};\n      Object.keys(cache).forEach((cachedIndex) => {\n        const cachedEl = cache[cachedIndex];\n        const cachedElIndex = cachedEl.getAttribute('data-swiper-slide-index');\n        if (cachedElIndex) {\n          cachedEl.setAttribute(\n            'data-swiper-slide-index',\n            parseInt(cachedElIndex, 10) + numberOfNewSlides,\n          );\n        }\n        newCache[parseInt(cachedIndex, 10) + numberOfNewSlides] = cachedEl;\n      });\n      swiper.virtual.cache = newCache;\n    }\n    update(true);\n    swiper.slideTo(newActiveIndex, 0);\n  }\n  function removeSlide(slidesIndexes) {\n    if (typeof slidesIndexes === 'undefined' || slidesIndexes === null) return;\n    let activeIndex = swiper.activeIndex;\n    if (Array.isArray(slidesIndexes)) {\n      for (let i = slidesIndexes.length - 1; i >= 0; i -= 1) {\n        swiper.virtual.slides.splice(slidesIndexes[i], 1);\n        if (swiper.params.virtual.cache) {\n          delete swiper.virtual.cache[slidesIndexes[i]];\n        }\n        if (slidesIndexes[i] < activeIndex) activeIndex -= 1;\n        activeIndex = Math.max(activeIndex, 0);\n      }\n    } else {\n      swiper.virtual.slides.splice(slidesIndexes, 1);\n      if (swiper.params.virtual.cache) {\n        delete swiper.virtual.cache[slidesIndexes];\n      }\n      if (slidesIndexes < activeIndex) activeIndex -= 1;\n      activeIndex = Math.max(activeIndex, 0);\n    }\n    update(true);\n    swiper.slideTo(activeIndex, 0);\n  }\n  function removeAllSlides() {\n    swiper.virtual.slides = [];\n    if (swiper.params.virtual.cache) {\n      swiper.virtual.cache = {};\n    }\n    update(true);\n    swiper.slideTo(0, 0);\n  }\n\n  on('beforeInit', () => {\n    if (!swiper.params.virtual.enabled) return;\n    let domSlidesAssigned;\n    if (typeof swiper.passedParams.virtual.slides === 'undefined') {\n      const slides = [...swiper.slidesEl.children].filter((el) =>\n        el.matches(`.${swiper.params.slideClass}, swiper-slide`),\n      );\n      if (slides && slides.length) {\n        swiper.virtual.slides = [...slides];\n        domSlidesAssigned = true;\n        slides.forEach((slideEl, slideIndex) => {\n          slideEl.setAttribute('data-swiper-slide-index', slideIndex);\n          swiper.virtual.cache[slideIndex] = slideEl;\n          slideEl.remove();\n        });\n      }\n    }\n    if (!domSlidesAssigned) {\n      swiper.virtual.slides = swiper.params.virtual.slides;\n    }\n\n    swiper.classNames.push(`${swiper.params.containerModifierClass}virtual`);\n\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n\n    if (!swiper.params.initialSlide) {\n      update();\n    }\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode && !swiper._immediateVirtual) {\n      clearTimeout(cssModeTimeout);\n      cssModeTimeout = setTimeout(() => {\n        update();\n      }, 100);\n    } else {\n      update();\n    }\n  });\n  on('init update resize', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode) {\n      setCSSProperty(swiper.wrapperEl, '--swiper-virtual-size', `${swiper.virtualSize}px`);\n    }\n  });\n\n  Object.assign(swiper.virtual, {\n    appendSlide,\n    prependSlide,\n    removeSlide,\n    removeAllSlides,\n    update,\n  });\n}\n", "/* eslint-disable consistent-return */\nimport { getWindow, getDocument } from 'ssr-window';\nimport { elementOffset, elementParents } from '../../shared/utils.js';\n\nexport default function Keyboard({ swiper, extendParams, on, emit }) {\n  const document = getDocument();\n  const window = getWindow();\n  swiper.keyboard = {\n    enabled: false,\n  };\n  extendParams({\n    keyboard: {\n      enabled: false,\n      onlyInViewport: true,\n      pageUpDown: true,\n    },\n  });\n\n  function handle(event) {\n    if (!swiper.enabled) return;\n\n    const { rtlTranslate: rtl } = swiper;\n    let e = event;\n    if (e.originalEvent) e = e.originalEvent; // jquery fix\n    const kc = e.keyCode || e.charCode;\n    const pageUpDown = swiper.params.keyboard.pageUpDown;\n    const isPageUp = pageUpDown && kc === 33;\n    const isPageDown = pageUpDown && kc === 34;\n    const isArrowLeft = kc === 37;\n    const isArrowRight = kc === 39;\n    const isArrowUp = kc === 38;\n    const isArrowDown = kc === 40;\n    // Directions locks\n    if (\n      !swiper.allowSlideNext &&\n      ((swiper.isHorizontal() && isArrowRight) ||\n        (swiper.isVertical() && isArrowDown) ||\n        isPageDown)\n    ) {\n      return false;\n    }\n    if (\n      !swiper.allowSlidePrev &&\n      ((swiper.isHorizontal() && isArrowLeft) || (swiper.isVertical() && isArrowUp) || isPageUp)\n    ) {\n      return false;\n    }\n    if (e.shiftKey || e.altKey || e.ctrlKey || e.metaKey) {\n      return undefined;\n    }\n    if (\n      document.activeElement &&\n      document.activeElement.nodeName &&\n      (document.activeElement.nodeName.toLowerCase() === 'input' ||\n        document.activeElement.nodeName.toLowerCase() === 'textarea')\n    ) {\n      return undefined;\n    }\n    if (\n      swiper.params.keyboard.onlyInViewport &&\n      (isPageUp || isPageDown || isArrowLeft || isArrowRight || isArrowUp || isArrowDown)\n    ) {\n      let inView = false;\n      // Check that swiper should be inside of visible area of window\n      if (\n        elementParents(swiper.el, `.${swiper.params.slideClass}, swiper-slide`).length > 0 &&\n        elementParents(swiper.el, `.${swiper.params.slideActiveClass}`).length === 0\n      ) {\n        return undefined;\n      }\n\n      const el = swiper.el;\n      const swiperWidth = el.clientWidth;\n      const swiperHeight = el.clientHeight;\n      const windowWidth = window.innerWidth;\n      const windowHeight = window.innerHeight;\n      const swiperOffset = elementOffset(el);\n      if (rtl) swiperOffset.left -= el.scrollLeft;\n      const swiperCoord = [\n        [swiperOffset.left, swiperOffset.top],\n        [swiperOffset.left + swiperWidth, swiperOffset.top],\n        [swiperOffset.left, swiperOffset.top + swiperHeight],\n        [swiperOffset.left + swiperWidth, swiperOffset.top + swiperHeight],\n      ];\n      for (let i = 0; i < swiperCoord.length; i += 1) {\n        const point = swiperCoord[i];\n        if (point[0] >= 0 && point[0] <= windowWidth && point[1] >= 0 && point[1] <= windowHeight) {\n          if (point[0] === 0 && point[1] === 0) continue; // eslint-disable-line\n          inView = true;\n        }\n      }\n      if (!inView) return undefined;\n    }\n    if (swiper.isHorizontal()) {\n      if (isPageUp || isPageDown || isArrowLeft || isArrowRight) {\n        if (e.preventDefault) e.preventDefault();\n        else e.returnValue = false;\n      }\n      if (((isPageDown || isArrowRight) && !rtl) || ((isPageUp || isArrowLeft) && rtl))\n        swiper.slideNext();\n      if (((isPageUp || isArrowLeft) && !rtl) || ((isPageDown || isArrowRight) && rtl))\n        swiper.slidePrev();\n    } else {\n      if (isPageUp || isPageDown || isArrowUp || isArrowDown) {\n        if (e.preventDefault) e.preventDefault();\n        else e.returnValue = false;\n      }\n      if (isPageDown || isArrowDown) swiper.slideNext();\n      if (isPageUp || isArrowUp) swiper.slidePrev();\n    }\n    emit('keyPress', kc);\n    return undefined;\n  }\n  function enable() {\n    if (swiper.keyboard.enabled) return;\n    document.addEventListener('keydown', handle);\n    swiper.keyboard.enabled = true;\n  }\n  function disable() {\n    if (!swiper.keyboard.enabled) return;\n    document.removeEventListener('keydown', handle);\n    swiper.keyboard.enabled = false;\n  }\n\n  on('init', () => {\n    if (swiper.params.keyboard.enabled) {\n      enable();\n    }\n  });\n  on('destroy', () => {\n    if (swiper.keyboard.enabled) {\n      disable();\n    }\n  });\n\n  Object.assign(swiper.keyboard, {\n    enable,\n    disable,\n  });\n}\n", "/* eslint-disable consistent-return */\nimport { getWindow } from 'ssr-window';\nimport { now, nextTick } from '../../shared/utils.js';\n\nexport default function Mousewheel({ swiper, extendParams, on, emit }) {\n  const window = getWindow();\n\n  extendParams({\n    mousewheel: {\n      enabled: false,\n      releaseOnEdges: false,\n      invert: false,\n      forceToAxis: false,\n      sensitivity: 1,\n      eventsTarget: 'container',\n      thresholdDelta: null,\n      thresholdTime: null,\n    },\n  });\n\n  swiper.mousewheel = {\n    enabled: false,\n  };\n\n  let timeout;\n  let lastScrollTime = now();\n  let lastEventBeforeSnap;\n  const recentWheelEvents = [];\n\n  function normalize(e) {\n    // Reasonable defaults\n    const PIXEL_STEP = 10;\n    const LINE_HEIGHT = 40;\n    const PAGE_HEIGHT = 800;\n\n    let sX = 0;\n    let sY = 0; // spinX, spinY\n    let pX = 0;\n    let pY = 0; // pixelX, pixelY\n\n    // Legacy\n    if ('detail' in e) {\n      sY = e.detail;\n    }\n    if ('wheelDelta' in e) {\n      sY = -e.wheelDelta / 120;\n    }\n    if ('wheelDeltaY' in e) {\n      sY = -e.wheelDeltaY / 120;\n    }\n    if ('wheelDeltaX' in e) {\n      sX = -e.wheelDeltaX / 120;\n    }\n\n    // side scrolling on FF with DOMMouseScroll\n    if ('axis' in e && e.axis === e.HORIZONTAL_AXIS) {\n      sX = sY;\n      sY = 0;\n    }\n\n    pX = sX * PIXEL_STEP;\n    pY = sY * PIXEL_STEP;\n\n    if ('deltaY' in e) {\n      pY = e.deltaY;\n    }\n    if ('deltaX' in e) {\n      pX = e.deltaX;\n    }\n\n    if (e.shiftKey && !pX) {\n      // if user scrolls with shift he wants horizontal scroll\n      pX = pY;\n      pY = 0;\n    }\n\n    if ((pX || pY) && e.deltaMode) {\n      if (e.deltaMode === 1) {\n        // delta in LINE units\n        pX *= LINE_HEIGHT;\n        pY *= LINE_HEIGHT;\n      } else {\n        // delta in PAGE units\n        pX *= PAGE_HEIGHT;\n        pY *= PAGE_HEIGHT;\n      }\n    }\n\n    // Fall-back if spin cannot be determined\n    if (pX && !sX) {\n      sX = pX < 1 ? -1 : 1;\n    }\n    if (pY && !sY) {\n      sY = pY < 1 ? -1 : 1;\n    }\n\n    return {\n      spinX: sX,\n      spinY: sY,\n      pixelX: pX,\n      pixelY: pY,\n    };\n  }\n  function handleMouseEnter() {\n    if (!swiper.enabled) return;\n    swiper.mouseEntered = true;\n  }\n  function handleMouseLeave() {\n    if (!swiper.enabled) return;\n    swiper.mouseEntered = false;\n  }\n  function animateSlider(newEvent) {\n    if (\n      swiper.params.mousewheel.thresholdDelta &&\n      newEvent.delta < swiper.params.mousewheel.thresholdDelta\n    ) {\n      // Prevent if delta of wheel scroll delta is below configured threshold\n      return false;\n    }\n\n    if (\n      swiper.params.mousewheel.thresholdTime &&\n      now() - lastScrollTime < swiper.params.mousewheel.thresholdTime\n    ) {\n      // Prevent if time between scrolls is below configured threshold\n      return false;\n    }\n\n    // If the movement is NOT big enough and\n    // if the last time the user scrolled was too close to the current one (avoid continuously triggering the slider):\n    //   Don't go any further (avoid insignificant scroll movement).\n    if (newEvent.delta >= 6 && now() - lastScrollTime < 60) {\n      // Return false as a default\n      return true;\n    }\n    // If user is scrolling towards the end:\n    //   If the slider hasn't hit the latest slide or\n    //   if the slider is a loop and\n    //   if the slider isn't moving right now:\n    //     Go to next slide and\n    //     emit a scroll event.\n    // Else (the user is scrolling towards the beginning) and\n    // if the slider hasn't hit the first slide or\n    // if the slider is a loop and\n    // if the slider isn't moving right now:\n    //   Go to prev slide and\n    //   emit a scroll event.\n    if (newEvent.direction < 0) {\n      if ((!swiper.isEnd || swiper.params.loop) && !swiper.animating) {\n        swiper.slideNext();\n        emit('scroll', newEvent.raw);\n      }\n    } else if ((!swiper.isBeginning || swiper.params.loop) && !swiper.animating) {\n      swiper.slidePrev();\n      emit('scroll', newEvent.raw);\n    }\n    // If you got here is because an animation has been triggered so store the current time\n    lastScrollTime = new window.Date().getTime();\n    // Return false as a default\n    return false;\n  }\n  function releaseScroll(newEvent) {\n    const params = swiper.params.mousewheel;\n    if (newEvent.direction < 0) {\n      if (swiper.isEnd && !swiper.params.loop && params.releaseOnEdges) {\n        // Return true to animate scroll on edges\n        return true;\n      }\n    } else if (swiper.isBeginning && !swiper.params.loop && params.releaseOnEdges) {\n      // Return true to animate scroll on edges\n      return true;\n    }\n    return false;\n  }\n  function handle(event) {\n    let e = event;\n    let disableParentSwiper = true;\n    if (!swiper.enabled) return;\n    const params = swiper.params.mousewheel;\n\n    if (swiper.params.cssMode) {\n      e.preventDefault();\n    }\n\n    let targetEl = swiper.el;\n    if (swiper.params.mousewheel.eventsTarget !== 'container') {\n      targetEl = document.querySelector(swiper.params.mousewheel.eventsTarget);\n    }\n    const targetElContainsTarget = targetEl && targetEl.contains(e.target);\n    if (!swiper.mouseEntered && !targetElContainsTarget && !params.releaseOnEdges) return true;\n\n    if (e.originalEvent) e = e.originalEvent; // jquery fix\n    let delta = 0;\n    const rtlFactor = swiper.rtlTranslate ? -1 : 1;\n\n    const data = normalize(e);\n\n    if (params.forceToAxis) {\n      if (swiper.isHorizontal()) {\n        if (Math.abs(data.pixelX) > Math.abs(data.pixelY)) delta = -data.pixelX * rtlFactor;\n        else return true;\n      } else if (Math.abs(data.pixelY) > Math.abs(data.pixelX)) delta = -data.pixelY;\n      else return true;\n    } else {\n      delta =\n        Math.abs(data.pixelX) > Math.abs(data.pixelY) ? -data.pixelX * rtlFactor : -data.pixelY;\n    }\n\n    if (delta === 0) return true;\n\n    if (params.invert) delta = -delta;\n\n    // Get the scroll positions\n    let positions = swiper.getTranslate() + delta * params.sensitivity;\n\n    if (positions >= swiper.minTranslate()) positions = swiper.minTranslate();\n    if (positions <= swiper.maxTranslate()) positions = swiper.maxTranslate();\n\n    // When loop is true:\n    //     the disableParentSwiper will be true.\n    // When loop is false:\n    //     if the scroll positions is not on edge,\n    //     then the disableParentSwiper will be true.\n    //     if the scroll on edge positions,\n    //     then the disableParentSwiper will be false.\n    disableParentSwiper = swiper.params.loop\n      ? true\n      : !(positions === swiper.minTranslate() || positions === swiper.maxTranslate());\n\n    if (disableParentSwiper && swiper.params.nested) e.stopPropagation();\n\n    if (!swiper.params.freeMode || !swiper.params.freeMode.enabled) {\n      // Register the new event in a variable which stores the relevant data\n      const newEvent = {\n        time: now(),\n        delta: Math.abs(delta),\n        direction: Math.sign(delta),\n        raw: event,\n      };\n\n      // Keep the most recent events\n      if (recentWheelEvents.length >= 2) {\n        recentWheelEvents.shift(); // only store the last N events\n      }\n      const prevEvent = recentWheelEvents.length\n        ? recentWheelEvents[recentWheelEvents.length - 1]\n        : undefined;\n      recentWheelEvents.push(newEvent);\n\n      // If there is at least one previous recorded event:\n      //   If direction has changed or\n      //   if the scroll is quicker than the previous one:\n      //     Animate the slider.\n      // Else (this is the first time the wheel is moved):\n      //     Animate the slider.\n      if (prevEvent) {\n        if (\n          newEvent.direction !== prevEvent.direction ||\n          newEvent.delta > prevEvent.delta ||\n          newEvent.time > prevEvent.time + 150\n        ) {\n          animateSlider(newEvent);\n        }\n      } else {\n        animateSlider(newEvent);\n      }\n\n      // If it's time to release the scroll:\n      //   Return now so you don't hit the preventDefault.\n      if (releaseScroll(newEvent)) {\n        return true;\n      }\n    } else {\n      // Freemode or scrollContainer:\n\n      // If we recently snapped after a momentum scroll, then ignore wheel events\n      // to give time for the deceleration to finish. Stop ignoring after 500 msecs\n      // or if it's a new scroll (larger delta or inverse sign as last event before\n      // an end-of-momentum snap).\n      const newEvent = {\n        time: now(),\n        delta: Math.abs(delta),\n        direction: Math.sign(delta),\n      };\n\n      const ignoreWheelEvents =\n        lastEventBeforeSnap &&\n        newEvent.time < lastEventBeforeSnap.time + 500 &&\n        newEvent.delta <= lastEventBeforeSnap.delta &&\n        newEvent.direction === lastEventBeforeSnap.direction;\n      if (!ignoreWheelEvents) {\n        lastEventBeforeSnap = undefined;\n\n        let position = swiper.getTranslate() + delta * params.sensitivity;\n        const wasBeginning = swiper.isBeginning;\n        const wasEnd = swiper.isEnd;\n\n        if (position >= swiper.minTranslate()) position = swiper.minTranslate();\n        if (position <= swiper.maxTranslate()) position = swiper.maxTranslate();\n\n        swiper.setTransition(0);\n        swiper.setTranslate(position);\n        swiper.updateProgress();\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n\n        if ((!wasBeginning && swiper.isBeginning) || (!wasEnd && swiper.isEnd)) {\n          swiper.updateSlidesClasses();\n        }\n        if (swiper.params.loop) {\n          swiper.loopFix({\n            direction: newEvent.direction < 0 ? 'next' : 'prev',\n            byMousewheel: true,\n          });\n        }\n\n        if (swiper.params.freeMode.sticky) {\n          // When wheel scrolling starts with sticky (aka snap) enabled, then detect\n          // the end of a momentum scroll by storing recent (N=15?) wheel events.\n          // 1. do all N events have decreasing or same (absolute value) delta?\n          // 2. did all N events arrive in the last M (M=500?) msecs?\n          // 3. does the earliest event have an (absolute value) delta that's\n          //    at least P (P=1?) larger than the most recent event's delta?\n          // 4. does the latest event have a delta that's smaller than Q (Q=6?) pixels?\n          // If 1-4 are \"yes\" then we're near the end of a momentum scroll deceleration.\n          // Snap immediately and ignore remaining wheel events in this scroll.\n          // See comment above for \"remaining wheel events in this scroll\" determination.\n          // If 1-4 aren't satisfied, then wait to snap until 500ms after the last event.\n          clearTimeout(timeout);\n          timeout = undefined;\n          if (recentWheelEvents.length >= 15) {\n            recentWheelEvents.shift(); // only store the last N events\n          }\n          const prevEvent = recentWheelEvents.length\n            ? recentWheelEvents[recentWheelEvents.length - 1]\n            : undefined;\n          const firstEvent = recentWheelEvents[0];\n          recentWheelEvents.push(newEvent);\n          if (\n            prevEvent &&\n            (newEvent.delta > prevEvent.delta || newEvent.direction !== prevEvent.direction)\n          ) {\n            // Increasing or reverse-sign delta means the user started scrolling again. Clear the wheel event log.\n            recentWheelEvents.splice(0);\n          } else if (\n            recentWheelEvents.length >= 15 &&\n            newEvent.time - firstEvent.time < 500 &&\n            firstEvent.delta - newEvent.delta >= 1 &&\n            newEvent.delta <= 6\n          ) {\n            // We're at the end of the deceleration of a momentum scroll, so there's no need\n            // to wait for more events. Snap ASAP on the next tick.\n            // Also, because there's some remaining momentum we'll bias the snap in the\n            // direction of the ongoing scroll because it's better UX for the scroll to snap\n            // in the same direction as the scroll instead of reversing to snap.  Therefore,\n            // if it's already scrolled more than 20% in the current direction, keep going.\n            const snapToThreshold = delta > 0 ? 0.8 : 0.2;\n            lastEventBeforeSnap = newEvent;\n            recentWheelEvents.splice(0);\n            timeout = nextTick(() => {\n              swiper.slideToClosest(swiper.params.speed, true, undefined, snapToThreshold);\n            }, 0); // no delay; move on next tick\n          }\n          if (!timeout) {\n            // if we get here, then we haven't detected the end of a momentum scroll, so\n            // we'll consider a scroll \"complete\" when there haven't been any wheel events\n            // for 500ms.\n            timeout = nextTick(() => {\n              const snapToThreshold = 0.5;\n              lastEventBeforeSnap = newEvent;\n              recentWheelEvents.splice(0);\n              swiper.slideToClosest(swiper.params.speed, true, undefined, snapToThreshold);\n            }, 500);\n          }\n        }\n\n        // Emit event\n        if (!ignoreWheelEvents) emit('scroll', e);\n\n        // Stop autoplay\n        if (swiper.params.autoplay && swiper.params.autoplayDisableOnInteraction)\n          swiper.autoplay.stop();\n        // Return page scroll on edge positions\n        if (position === swiper.minTranslate() || position === swiper.maxTranslate()) return true;\n      }\n    }\n\n    if (e.preventDefault) e.preventDefault();\n    else e.returnValue = false;\n    return false;\n  }\n\n  function events(method) {\n    let targetEl = swiper.el;\n    if (swiper.params.mousewheel.eventsTarget !== 'container') {\n      targetEl = document.querySelector(swiper.params.mousewheel.eventsTarget);\n    }\n    targetEl[method]('mouseenter', handleMouseEnter);\n    targetEl[method]('mouseleave', handleMouseLeave);\n    targetEl[method]('wheel', handle);\n  }\n\n  function enable() {\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.removeEventListener('wheel', handle);\n      return true;\n    }\n    if (swiper.mousewheel.enabled) return false;\n    events('addEventListener');\n    swiper.mousewheel.enabled = true;\n    return true;\n  }\n  function disable() {\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.addEventListener(event, handle);\n      return true;\n    }\n    if (!swiper.mousewheel.enabled) return false;\n    events('removeEventListener');\n    swiper.mousewheel.enabled = false;\n    return true;\n  }\n\n  on('init', () => {\n    if (!swiper.params.mousewheel.enabled && swiper.params.cssMode) {\n      disable();\n    }\n    if (swiper.params.mousewheel.enabled) enable();\n  });\n  on('destroy', () => {\n    if (swiper.params.cssMode) {\n      enable();\n    }\n    if (swiper.mousewheel.enabled) disable();\n  });\n\n  Object.assign(swiper.mousewheel, {\n    enable,\n    disable,\n  });\n}\n", "import createElementIfNotDefined from '../../shared/create-element-if-not-defined.js';\n\nexport default function Navigation({ swiper, extendParams, on, emit }) {\n  extendParams({\n    navigation: {\n      nextEl: null,\n      prevEl: null,\n\n      hideOnClick: false,\n      disabledClass: 'swiper-button-disabled',\n      hiddenClass: 'swiper-button-hidden',\n      lockClass: 'swiper-button-lock',\n      navigationDisabledClass: 'swiper-navigation-disabled',\n    },\n  });\n\n  swiper.navigation = {\n    nextEl: null,\n    prevEl: null,\n  };\n\n  const makeElementsArray = (el) => {\n    if (!Array.isArray(el)) el = [el].filter((e) => !!e);\n    return el;\n  };\n\n  function getEl(el) {\n    let res;\n    if (el && typeof el === 'string' && swiper.isElement) {\n      res = swiper.el.shadowRoot.querySelector(el);\n      if (res) return res;\n    }\n    if (el) {\n      if (typeof el === 'string') res = [...document.querySelectorAll(el)];\n      if (\n        swiper.params.uniqueNavElements &&\n        typeof el === 'string' &&\n        res.length > 1 &&\n        swiper.el.querySelectorAll(el).length === 1\n      ) {\n        res = swiper.el.querySelector(el);\n      }\n    }\n    if (el && !res) return el;\n    // if (Array.isArray(res) && res.length === 1) res = res[0];\n    return res;\n  }\n\n  function toggleEl(el, disabled) {\n    const params = swiper.params.navigation;\n    el = makeElementsArray(el);\n    el.forEach((subEl) => {\n      if (subEl) {\n        subEl.classList[disabled ? 'add' : 'remove'](...params.disabledClass.split(' '));\n        if (subEl.tagName === 'BUTTON') subEl.disabled = disabled;\n        if (swiper.params.watchOverflow && swiper.enabled) {\n          subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n        }\n      }\n    });\n  }\n  function update() {\n    // Update Navigation Buttons\n    const { nextEl, prevEl } = swiper.navigation;\n    if (swiper.params.loop) {\n      toggleEl(prevEl, false);\n      toggleEl(nextEl, false);\n      return;\n    }\n\n    toggleEl(prevEl, swiper.isBeginning && !swiper.params.rewind);\n    toggleEl(nextEl, swiper.isEnd && !swiper.params.rewind);\n  }\n  function onPrevClick(e) {\n    e.preventDefault();\n    if (swiper.isBeginning && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slidePrev();\n    emit('navigationPrev');\n  }\n  function onNextClick(e) {\n    e.preventDefault();\n    if (swiper.isEnd && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slideNext();\n    emit('navigationNext');\n  }\n  function init() {\n    const params = swiper.params.navigation;\n\n    swiper.params.navigation = createElementIfNotDefined(\n      swiper,\n      swiper.originalParams.navigation,\n      swiper.params.navigation,\n      {\n        nextEl: 'swiper-button-next',\n        prevEl: 'swiper-button-prev',\n      },\n    );\n    if (!(params.nextEl || params.prevEl)) return;\n\n    let nextEl = getEl(params.nextEl);\n    let prevEl = getEl(params.prevEl);\n\n    Object.assign(swiper.navigation, {\n      nextEl,\n      prevEl,\n    });\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n\n    const initButton = (el, dir) => {\n      if (el) {\n        el.addEventListener('click', dir === 'next' ? onNextClick : onPrevClick);\n      }\n      if (!swiper.enabled && el) {\n        el.classList.add(...params.lockClass.split(' '));\n      }\n    };\n\n    nextEl.forEach((el) => initButton(el, 'next'));\n    prevEl.forEach((el) => initButton(el, 'prev'));\n  }\n  function destroy() {\n    let { nextEl, prevEl } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const destroyButton = (el, dir) => {\n      el.removeEventListener('click', dir === 'next' ? onNextClick : onPrevClick);\n      el.classList.remove(...swiper.params.navigation.disabledClass.split(' '));\n    };\n    nextEl.forEach((el) => destroyButton(el, 'next'));\n    prevEl.forEach((el) => destroyButton(el, 'prev'));\n  }\n\n  on('init', () => {\n    if (swiper.params.navigation.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      update();\n    }\n  });\n  on('toEdge fromEdge lock unlock', () => {\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let { nextEl, prevEl } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    [...nextEl, ...prevEl]\n      .filter((el) => !!el)\n      .forEach((el) =>\n        el.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.navigation.lockClass),\n      );\n  });\n  on('click', (_s, e) => {\n    let { nextEl, prevEl } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const targetEl = e.target;\n    if (\n      swiper.params.navigation.hideOnClick &&\n      !prevEl.includes(targetEl) &&\n      !nextEl.includes(targetEl)\n    ) {\n      if (\n        swiper.pagination &&\n        swiper.params.pagination &&\n        swiper.params.pagination.clickable &&\n        (swiper.pagination.el === targetEl || swiper.pagination.el.contains(targetEl))\n      )\n        return;\n      let isHidden;\n      if (nextEl.length) {\n        isHidden = nextEl[0].classList.contains(swiper.params.navigation.hiddenClass);\n      } else if (prevEl.length) {\n        isHidden = prevEl[0].classList.contains(swiper.params.navigation.hiddenClass);\n      }\n      if (isHidden === true) {\n        emit('navigationShow');\n      } else {\n        emit('navigationHide');\n      }\n      [...nextEl, ...prevEl]\n        .filter((el) => !!el)\n        .forEach((el) => el.classList.toggle(swiper.params.navigation.hiddenClass));\n    }\n  });\n\n  const enable = () => {\n    swiper.el.classList.remove(...swiper.params.navigation.navigationDisabledClass.split(' '));\n    init();\n    update();\n  };\n\n  const disable = () => {\n    swiper.el.classList.add(...swiper.params.navigation.navigationDisabledClass.split(' '));\n    destroy();\n  };\n\n  Object.assign(swiper.navigation, {\n    enable,\n    disable,\n    update,\n    init,\n    destroy,\n  });\n}\n", "import classesToSelector from '../../shared/classes-to-selector.js';\nimport createElementIfNotDefined from '../../shared/create-element-if-not-defined.js';\nimport { elementIndex, elementOuterSize, elementParents } from '../../shared/utils.js';\n\nexport default function Pagination({ swiper, extendParams, on, emit }) {\n  const pfx = 'swiper-pagination';\n  extendParams({\n    pagination: {\n      el: null,\n      bulletElement: 'span',\n      clickable: false,\n      hideOnClick: false,\n      renderBullet: null,\n      renderProgressbar: null,\n      renderFraction: null,\n      renderCustom: null,\n      progressbarOpposite: false,\n      type: 'bullets', // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n      dynamicBullets: false,\n      dynamicMainBullets: 1,\n      formatFractionCurrent: (number) => number,\n      formatFractionTotal: (number) => number,\n      bulletClass: `${pfx}-bullet`,\n      bulletActiveClass: `${pfx}-bullet-active`,\n      modifierClass: `${pfx}-`,\n      currentClass: `${pfx}-current`,\n      totalClass: `${pfx}-total`,\n      hiddenClass: `${pfx}-hidden`,\n      progressbarFillClass: `${pfx}-progressbar-fill`,\n      progressbarOppositeClass: `${pfx}-progressbar-opposite`,\n      clickableClass: `${pfx}-clickable`,\n      lockClass: `${pfx}-lock`,\n      horizontalClass: `${pfx}-horizontal`,\n      verticalClass: `${pfx}-vertical`,\n      paginationDisabledClass: `${pfx}-disabled`,\n    },\n  });\n\n  swiper.pagination = {\n    el: null,\n    bullets: [],\n  };\n\n  let bulletSize;\n  let dynamicBulletIndex = 0;\n\n  const makeElementsArray = (el) => {\n    if (!Array.isArray(el)) el = [el].filter((e) => !!e);\n    return el;\n  };\n\n  function isPaginationDisabled() {\n    return (\n      !swiper.params.pagination.el ||\n      !swiper.pagination.el ||\n      (Array.isArray(swiper.pagination.el) && swiper.pagination.el.length === 0)\n    );\n  }\n\n  function setSideBullets(bulletEl, position) {\n    const { bulletActiveClass } = swiper.params.pagination;\n    if (!bulletEl) return;\n    bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n    if (bulletEl) {\n      bulletEl.classList.add(`${bulletActiveClass}-${position}`);\n      bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n      if (bulletEl) {\n        bulletEl.classList.add(`${bulletActiveClass}-${position}-${position}`);\n      }\n    }\n  }\n\n  function onBulletClick(e) {\n    const bulletEl = e.target.closest(classesToSelector(swiper.params.pagination.bulletClass));\n    if (!bulletEl) {\n      return;\n    }\n    e.preventDefault();\n    const index = elementIndex(bulletEl) * swiper.params.slidesPerGroup;\n    if (swiper.params.loop) {\n      if (swiper.realIndex === index) return;\n      const newSlideIndex = swiper.getSlideIndexByData(index);\n      const currentSlideIndex = swiper.getSlideIndexByData(swiper.realIndex);\n      if (newSlideIndex > swiper.slides.length - swiper.loopedSlides) {\n        swiper.loopFix({\n          direction: newSlideIndex > currentSlideIndex ? 'next' : 'prev',\n          activeSlideIndex: newSlideIndex,\n          slideTo: false,\n        });\n      }\n\n      swiper.slideToLoop(index);\n    } else {\n      swiper.slideTo(index);\n    }\n  }\n\n  function update() {\n    // Render || Update Pagination bullets/items\n    const rtl = swiper.rtl;\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    // Current/Total\n    let current;\n    let previousIndex;\n    const slidesLength =\n      swiper.virtual && swiper.params.virtual.enabled\n        ? swiper.virtual.slides.length\n        : swiper.slides.length;\n    const total = swiper.params.loop\n      ? Math.ceil(slidesLength / swiper.params.slidesPerGroup)\n      : swiper.snapGrid.length;\n    if (swiper.params.loop) {\n      previousIndex = swiper.previousRealIndex || 0;\n      current =\n        swiper.params.slidesPerGroup > 1\n          ? Math.floor(swiper.realIndex / swiper.params.slidesPerGroup)\n          : swiper.realIndex;\n    } else if (typeof swiper.snapIndex !== 'undefined') {\n      current = swiper.snapIndex;\n      previousIndex = swiper.previousSnapIndex;\n    } else {\n      previousIndex = swiper.previousIndex || 0;\n      current = swiper.activeIndex || 0;\n    }\n    // Types\n    if (\n      params.type === 'bullets' &&\n      swiper.pagination.bullets &&\n      swiper.pagination.bullets.length > 0\n    ) {\n      const bullets = swiper.pagination.bullets;\n      let firstIndex;\n      let lastIndex;\n      let midIndex;\n      if (params.dynamicBullets) {\n        bulletSize = elementOuterSize(bullets[0], swiper.isHorizontal() ? 'width' : 'height', true);\n        el.forEach((subEl) => {\n          subEl.style[swiper.isHorizontal() ? 'width' : 'height'] = `${\n            bulletSize * (params.dynamicMainBullets + 4)\n          }px`;\n        });\n        if (params.dynamicMainBullets > 1 && previousIndex !== undefined) {\n          dynamicBulletIndex += current - (previousIndex || 0);\n          if (dynamicBulletIndex > params.dynamicMainBullets - 1) {\n            dynamicBulletIndex = params.dynamicMainBullets - 1;\n          } else if (dynamicBulletIndex < 0) {\n            dynamicBulletIndex = 0;\n          }\n        }\n        firstIndex = Math.max(current - dynamicBulletIndex, 0);\n        lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n        midIndex = (lastIndex + firstIndex) / 2;\n      }\n      bullets.forEach((bulletEl) => {\n        const classesToRemove = [\n          ...['', '-next', '-next-next', '-prev', '-prev-prev', '-main'].map(\n            (suffix) => `${params.bulletActiveClass}${suffix}`,\n          ),\n        ]\n          .map((s) => (typeof s === 'string' && s.includes(' ') ? s.split(' ') : s))\n          .flat();\n        bulletEl.classList.remove(...classesToRemove);\n      });\n\n      if (el.length > 1) {\n        bullets.forEach((bullet) => {\n          const bulletIndex = elementIndex(bullet);\n          if (bulletIndex === current) {\n            bullet.classList.add(...params.bulletActiveClass.split(' '));\n          }\n          if (params.dynamicBullets) {\n            if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n              bullet.classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n            if (bulletIndex === firstIndex) {\n              setSideBullets(bullet, 'prev');\n            }\n            if (bulletIndex === lastIndex) {\n              setSideBullets(bullet, 'next');\n            }\n          }\n        });\n      } else {\n        const bullet = bullets[current];\n        if (bullet) {\n          bullet.classList.add(...params.bulletActiveClass.split(' '));\n        }\n\n        if (params.dynamicBullets) {\n          const firstDisplayedBullet = bullets[firstIndex];\n          const lastDisplayedBullet = bullets[lastIndex];\n          for (let i = firstIndex; i <= lastIndex; i += 1) {\n            if (bullets[i]) {\n              bullets[i].classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n          }\n\n          setSideBullets(firstDisplayedBullet, 'prev');\n          setSideBullets(lastDisplayedBullet, 'next');\n        }\n      }\n      if (params.dynamicBullets) {\n        const dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n        const bulletsOffset =\n          (bulletSize * dynamicBulletsLength - bulletSize) / 2 - midIndex * bulletSize;\n        const offsetProp = rtl ? 'right' : 'left';\n        bullets.forEach((bullet) => {\n          bullet.style[swiper.isHorizontal() ? offsetProp : 'top'] = `${bulletsOffset}px`;\n        });\n      }\n    }\n    el.forEach((subEl, subElIndex) => {\n      if (params.type === 'fraction') {\n        subEl.querySelectorAll(classesToSelector(params.currentClass)).forEach((fractionEl) => {\n          fractionEl.textContent = params.formatFractionCurrent(current + 1);\n        });\n        subEl.querySelectorAll(classesToSelector(params.totalClass)).forEach((totalEl) => {\n          totalEl.textContent = params.formatFractionTotal(total);\n        });\n      }\n      if (params.type === 'progressbar') {\n        let progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        const scale = (current + 1) / total;\n        let scaleX = 1;\n        let scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        subEl\n          .querySelectorAll(classesToSelector(params.progressbarFillClass))\n          .forEach((progressEl) => {\n            progressEl.style.transform = `translate3d(0,0,0) scaleX(${scaleX}) scaleY(${scaleY})`;\n            progressEl.style.transitionDuration = `${swiper.params.speed}ms`;\n          });\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        subEl.innerHTML = params.renderCustom(swiper, current + 1, total);\n        if (subElIndex === 0) emit('paginationRender', subEl);\n      } else {\n        if (subElIndex === 0) emit('paginationRender', subEl);\n        emit('paginationUpdate', subEl);\n      }\n      if (swiper.params.watchOverflow && swiper.enabled) {\n        subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n      }\n    });\n  }\n  function render() {\n    // Render Container\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const slidesLength =\n      swiper.virtual && swiper.params.virtual.enabled\n        ? swiper.virtual.slides.length\n        : swiper.slides.length;\n\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    let paginationHTML = '';\n    if (params.type === 'bullets') {\n      let numberOfBullets = swiper.params.loop\n        ? Math.ceil(slidesLength / swiper.params.slidesPerGroup)\n        : swiper.snapGrid.length;\n      if (\n        swiper.params.freeMode &&\n        swiper.params.freeMode.enabled &&\n        numberOfBullets > slidesLength\n      ) {\n        numberOfBullets = slidesLength;\n      }\n      for (let i = 0; i < numberOfBullets; i += 1) {\n        if (params.renderBullet) {\n          paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n        } else {\n          paginationHTML += `<${params.bulletElement} class=\"${params.bulletClass}\"></${params.bulletElement}>`;\n        }\n      }\n    }\n    if (params.type === 'fraction') {\n      if (params.renderFraction) {\n        paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n      } else {\n        paginationHTML =\n          `<span class=\"${params.currentClass}\"></span>` +\n          ' / ' +\n          `<span class=\"${params.totalClass}\"></span>`;\n      }\n    }\n    if (params.type === 'progressbar') {\n      if (params.renderProgressbar) {\n        paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n      } else {\n        paginationHTML = `<span class=\"${params.progressbarFillClass}\"></span>`;\n      }\n    }\n    swiper.pagination.bullets = [];\n    el.forEach((subEl) => {\n      if (params.type !== 'custom') {\n        subEl.innerHTML = paginationHTML || '';\n      }\n      if (params.type === 'bullets') {\n        swiper.pagination.bullets.push(\n          ...subEl.querySelectorAll(classesToSelector(params.bulletClass)),\n        );\n      }\n    });\n    if (params.type !== 'custom') {\n      emit('paginationRender', el[0]);\n    }\n  }\n  function init() {\n    swiper.params.pagination = createElementIfNotDefined(\n      swiper,\n      swiper.originalParams.pagination,\n      swiper.params.pagination,\n      { el: 'swiper-pagination' },\n    );\n    const params = swiper.params.pagination;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.shadowRoot.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = [...document.querySelectorAll(params.el)];\n    }\n    if (!el) {\n      el = params.el;\n    }\n    if (!el || el.length === 0) return;\n\n    if (\n      swiper.params.uniqueNavElements &&\n      typeof params.el === 'string' &&\n      Array.isArray(el) &&\n      el.length > 1\n    ) {\n      el = [...swiper.el.querySelectorAll(params.el)];\n      // check if it belongs to another nested Swiper\n      if (el.length > 1) {\n        el = el.filter((subEl) => {\n          if (elementParents(subEl, '.swiper')[0] !== swiper.el) return false;\n          return true;\n        })[0];\n      }\n    }\n    if (Array.isArray(el) && el.length === 1) el = el[0];\n\n    Object.assign(swiper.pagination, {\n      el,\n    });\n\n    el = makeElementsArray(el);\n    el.forEach((subEl) => {\n      if (params.type === 'bullets' && params.clickable) {\n        subEl.classList.add(params.clickableClass);\n      }\n\n      subEl.classList.add(params.modifierClass + params.type);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        subEl.classList.add(`${params.modifierClass}${params.type}-dynamic`);\n        dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        subEl.classList.add(params.progressbarOppositeClass);\n      }\n\n      if (params.clickable) {\n        subEl.addEventListener('click', onBulletClick);\n      }\n\n      if (!swiper.enabled) {\n        subEl.classList.add(params.lockClass);\n      }\n    });\n  }\n\n  function destroy() {\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach((subEl) => {\n        subEl.classList.remove(params.hiddenClass);\n        subEl.classList.remove(params.modifierClass + params.type);\n        subEl.classList.remove(\n          swiper.isHorizontal() ? params.horizontalClass : params.verticalClass,\n        );\n        if (params.clickable) {\n          subEl.removeEventListener('click', onBulletClick);\n        }\n      });\n    }\n\n    if (swiper.pagination.bullets)\n      swiper.pagination.bullets.forEach((subEl) =>\n        subEl.classList.remove(...params.bulletActiveClass.split(' ')),\n      );\n  }\n\n  on('changeDirection', () => {\n    if (!swiper.pagination || !swiper.pagination.el) return;\n    const params = swiper.params.pagination;\n    let { el } = swiper.pagination;\n    el = makeElementsArray(el);\n    el.forEach((subEl) => {\n      subEl.classList.remove(params.horizontalClass, params.verticalClass);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    });\n  });\n\n  on('init', () => {\n    if (swiper.params.pagination.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      render();\n      update();\n    }\n  });\n  on('activeIndexChange', () => {\n    if (typeof swiper.snapIndex === 'undefined') {\n      update();\n    }\n  });\n  on('snapIndexChange', () => {\n    update();\n  });\n  on('snapGridLengthChange', () => {\n    render();\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let { el } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach((subEl) =>\n        subEl.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.pagination.lockClass),\n      );\n    }\n  });\n  on('lock unlock', () => {\n    update();\n  });\n  on('click', (_s, e) => {\n    const targetEl = e.target;\n    let { el } = swiper.pagination;\n    if (!Array.isArray(el)) el = [el].filter((element) => !!element);\n    if (\n      swiper.params.pagination.el &&\n      swiper.params.pagination.hideOnClick &&\n      el &&\n      el.length > 0 &&\n      !targetEl.classList.contains(swiper.params.pagination.bulletClass)\n    ) {\n      if (\n        swiper.navigation &&\n        ((swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl) ||\n          (swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl))\n      )\n        return;\n      const isHidden = el[0].classList.contains(swiper.params.pagination.hiddenClass);\n      if (isHidden === true) {\n        emit('paginationShow');\n      } else {\n        emit('paginationHide');\n      }\n      el.forEach((subEl) => subEl.classList.toggle(swiper.params.pagination.hiddenClass));\n    }\n  });\n\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.pagination.paginationDisabledClass);\n    let { el } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach((subEl) =>\n        subEl.classList.remove(swiper.params.pagination.paginationDisabledClass),\n      );\n    }\n    init();\n    render();\n    update();\n  };\n\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.pagination.paginationDisabledClass);\n    let { el } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach((subEl) => subEl.classList.add(swiper.params.pagination.paginationDisabledClass));\n    }\n    destroy();\n  };\n\n  Object.assign(swiper.pagination, {\n    enable,\n    disable,\n    render,\n    update,\n    init,\n    destroy,\n  });\n}\n", "import { getDocument } from 'ssr-window';\nimport { createElement, elementOffset, nextTick } from '../../shared/utils.js';\nimport createElementIfNotDefined from '../../shared/create-element-if-not-defined.js';\n\nexport default function Scrollbar({ swiper, extendParams, on, emit }) {\n  const document = getDocument();\n\n  let isTouched = false;\n  let timeout = null;\n  let dragTimeout = null;\n  let dragStartPos;\n  let dragSize;\n  let trackSize;\n  let divider;\n\n  extendParams({\n    scrollbar: {\n      el: null,\n      dragSize: 'auto',\n      hide: false,\n      draggable: false,\n      snapOnRelease: true,\n      lockClass: 'swiper-scrollbar-lock',\n      dragClass: 'swiper-scrollbar-drag',\n      scrollbarDisabledClass: 'swiper-scrollbar-disabled',\n      horizontalClass: `swiper-scrollbar-horizontal`,\n      verticalClass: `swiper-scrollbar-vertical`,\n    },\n  });\n\n  swiper.scrollbar = {\n    el: null,\n    dragEl: null,\n  };\n\n  function setTranslate() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    const { scrollbar, rtlTranslate: rtl } = swiper;\n    const { dragEl, el } = scrollbar;\n    const params = swiper.params.scrollbar;\n    const progress = swiper.params.loop ? swiper.progressLoop : swiper.progress;\n\n    let newSize = dragSize;\n    let newPos = (trackSize - dragSize) * progress;\n    if (rtl) {\n      newPos = -newPos;\n      if (newPos > 0) {\n        newSize = dragSize - newPos;\n        newPos = 0;\n      } else if (-newPos + dragSize > trackSize) {\n        newSize = trackSize + newPos;\n      }\n    } else if (newPos < 0) {\n      newSize = dragSize + newPos;\n      newPos = 0;\n    } else if (newPos + dragSize > trackSize) {\n      newSize = trackSize - newPos;\n    }\n    if (swiper.isHorizontal()) {\n      dragEl.style.transform = `translate3d(${newPos}px, 0, 0)`;\n      dragEl.style.width = `${newSize}px`;\n    } else {\n      dragEl.style.transform = `translate3d(0px, ${newPos}px, 0)`;\n      dragEl.style.height = `${newSize}px`;\n    }\n    if (params.hide) {\n      clearTimeout(timeout);\n      el.style.opacity = 1;\n      timeout = setTimeout(() => {\n        el.style.opacity = 0;\n        el.style.transitionDuration = '400ms';\n      }, 1000);\n    }\n  }\n  function setTransition(duration) {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    swiper.scrollbar.dragEl.style.transitionDuration = `${duration}ms`;\n  }\n  function updateSize() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n\n    const { scrollbar } = swiper;\n    const { dragEl, el } = scrollbar;\n\n    dragEl.style.width = '';\n    dragEl.style.height = '';\n    trackSize = swiper.isHorizontal() ? el.offsetWidth : el.offsetHeight;\n\n    divider =\n      swiper.size /\n      (swiper.virtualSize +\n        swiper.params.slidesOffsetBefore -\n        (swiper.params.centeredSlides ? swiper.snapGrid[0] : 0));\n    if (swiper.params.scrollbar.dragSize === 'auto') {\n      dragSize = trackSize * divider;\n    } else {\n      dragSize = parseInt(swiper.params.scrollbar.dragSize, 10);\n    }\n\n    if (swiper.isHorizontal()) {\n      dragEl.style.width = `${dragSize}px`;\n    } else {\n      dragEl.style.height = `${dragSize}px`;\n    }\n\n    if (divider >= 1) {\n      el.style.display = 'none';\n    } else {\n      el.style.display = '';\n    }\n    if (swiper.params.scrollbar.hide) {\n      el.style.opacity = 0;\n    }\n\n    if (swiper.params.watchOverflow && swiper.enabled) {\n      scrollbar.el.classList[swiper.isLocked ? 'add' : 'remove'](swiper.params.scrollbar.lockClass);\n    }\n  }\n  function getPointerPosition(e) {\n    return swiper.isHorizontal() ? e.clientX : e.clientY;\n  }\n  function setDragPosition(e) {\n    const { scrollbar, rtlTranslate: rtl } = swiper;\n    const { el } = scrollbar;\n\n    let positionRatio;\n    positionRatio =\n      (getPointerPosition(e) -\n        elementOffset(el)[swiper.isHorizontal() ? 'left' : 'top'] -\n        (dragStartPos !== null ? dragStartPos : dragSize / 2)) /\n      (trackSize - dragSize);\n    positionRatio = Math.max(Math.min(positionRatio, 1), 0);\n    if (rtl) {\n      positionRatio = 1 - positionRatio;\n    }\n\n    const position =\n      swiper.minTranslate() + (swiper.maxTranslate() - swiper.minTranslate()) * positionRatio;\n\n    swiper.updateProgress(position);\n    swiper.setTranslate(position);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  function onDragStart(e) {\n    const params = swiper.params.scrollbar;\n    const { scrollbar, wrapperEl } = swiper;\n    const { el, dragEl } = scrollbar;\n    isTouched = true;\n    dragStartPos =\n      e.target === dragEl\n        ? getPointerPosition(e) -\n          e.target.getBoundingClientRect()[swiper.isHorizontal() ? 'left' : 'top']\n        : null;\n    e.preventDefault();\n    e.stopPropagation();\n\n    wrapperEl.style.transitionDuration = '100ms';\n    dragEl.style.transitionDuration = '100ms';\n    setDragPosition(e);\n\n    clearTimeout(dragTimeout);\n\n    el.style.transitionDuration = '0ms';\n    if (params.hide) {\n      el.style.opacity = 1;\n    }\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style['scroll-snap-type'] = 'none';\n    }\n    emit('scrollbarDragStart', e);\n  }\n  function onDragMove(e) {\n    const { scrollbar, wrapperEl } = swiper;\n    const { el, dragEl } = scrollbar;\n\n    if (!isTouched) return;\n    if (e.preventDefault) e.preventDefault();\n    else e.returnValue = false;\n    setDragPosition(e);\n    wrapperEl.style.transitionDuration = '0ms';\n    el.style.transitionDuration = '0ms';\n    dragEl.style.transitionDuration = '0ms';\n    emit('scrollbarDragMove', e);\n  }\n  function onDragEnd(e) {\n    const params = swiper.params.scrollbar;\n    const { scrollbar, wrapperEl } = swiper;\n    const { el } = scrollbar;\n\n    if (!isTouched) return;\n    isTouched = false;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style['scroll-snap-type'] = '';\n      wrapperEl.style.transitionDuration = '';\n    }\n    if (params.hide) {\n      clearTimeout(dragTimeout);\n      dragTimeout = nextTick(() => {\n        el.style.opacity = 0;\n        el.style.transitionDuration = '400ms';\n      }, 1000);\n    }\n    emit('scrollbarDragEnd', e);\n    if (params.snapOnRelease) {\n      swiper.slideToClosest();\n    }\n  }\n\n  function events(method) {\n    const { scrollbar, params } = swiper;\n    const el = scrollbar.el;\n    if (!el) return;\n    const target = el;\n    const activeListener = params.passiveListeners ? { passive: false, capture: false } : false;\n    const passiveListener = params.passiveListeners ? { passive: true, capture: false } : false;\n    if (!target) return;\n    const eventMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n    target[eventMethod]('pointerdown', onDragStart, activeListener);\n    document[eventMethod]('pointermove', onDragMove, activeListener);\n    document[eventMethod]('pointerup', onDragEnd, passiveListener);\n  }\n\n  function enableDraggable() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    events('on');\n  }\n  function disableDraggable() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    events('off');\n  }\n  function init() {\n    const { scrollbar, el: swiperEl } = swiper;\n    swiper.params.scrollbar = createElementIfNotDefined(\n      swiper,\n      swiper.originalParams.scrollbar,\n      swiper.params.scrollbar,\n      { el: 'swiper-scrollbar' },\n    );\n    const params = swiper.params.scrollbar;\n    if (!params.el) return;\n\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.shadowRoot.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = document.querySelectorAll(params.el);\n    } else if (!el) {\n      el = params.el;\n    }\n\n    if (\n      swiper.params.uniqueNavElements &&\n      typeof params.el === 'string' &&\n      el.length > 1 &&\n      swiperEl.querySelectorAll(params.el).length === 1\n    ) {\n      el = swiperEl.querySelector(params.el);\n    }\n    if (el.length > 0) el = el[0];\n\n    el.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n\n    let dragEl;\n    if (el) {\n      dragEl = el.querySelector(`.${swiper.params.scrollbar.dragClass}`);\n      if (!dragEl) {\n        dragEl = createElement('div', swiper.params.scrollbar.dragClass);\n        el.append(dragEl);\n      }\n    }\n\n    Object.assign(scrollbar, {\n      el,\n      dragEl,\n    });\n\n    if (params.draggable) {\n      enableDraggable();\n    }\n\n    if (el) {\n      el.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.scrollbar.lockClass);\n    }\n  }\n  function destroy() {\n    const params = swiper.params.scrollbar;\n    const el = swiper.scrollbar.el;\n    if (el) {\n      el.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    }\n\n    disableDraggable();\n  }\n\n  on('init', () => {\n    if (swiper.params.scrollbar.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      updateSize();\n      setTranslate();\n    }\n  });\n  on('update resize observerUpdate lock unlock', () => {\n    updateSize();\n  });\n  on('setTranslate', () => {\n    setTranslate();\n  });\n  on('setTransition', (_s, duration) => {\n    setTransition(duration);\n  });\n  on('enable disable', () => {\n    const { el } = swiper.scrollbar;\n    if (el) {\n      el.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.scrollbar.lockClass);\n    }\n  });\n  on('destroy', () => {\n    destroy();\n  });\n\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.scrollbar.scrollbarDisabledClass);\n    if (swiper.scrollbar.el) {\n      swiper.scrollbar.el.classList.remove(swiper.params.scrollbar.scrollbarDisabledClass);\n    }\n    init();\n    updateSize();\n    setTranslate();\n  };\n\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.scrollbar.scrollbarDisabledClass);\n    if (swiper.scrollbar.el) {\n      swiper.scrollbar.el.classList.add(swiper.params.scrollbar.scrollbarDisabledClass);\n    }\n    destroy();\n  };\n\n  Object.assign(swiper.scrollbar, {\n    enable,\n    disable,\n    updateSize,\n    setTranslate,\n    init,\n    destroy,\n  });\n}\n", "/* eslint no-underscore-dangle: \"off\" */\n/* eslint no-use-before-define: \"off\" */\nimport { getDocument } from 'ssr-window';\n\nexport default function Autoplay({ swiper, extendParams, on, emit, params }) {\n  swiper.autoplay = {\n    running: false,\n    paused: false,\n    timeLeft: 0,\n  };\n\n  extendParams({\n    autoplay: {\n      enabled: false,\n      delay: 3000,\n      waitForTransition: true,\n      disableOnInteraction: true,\n      stopOnLastSlide: false,\n      reverseDirection: false,\n      pauseOnMouseEnter: false,\n    },\n  });\n  let timeout;\n  let raf;\n  let autoplayDelayTotal = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayDelayCurrent = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayTimeLeft;\n  let autoplayStartTime = new Date().getTime;\n  let wasPaused;\n  let isTouched;\n  let pausedByTouch;\n  let touchStartTimeout;\n  let slideChanged;\n  let pausedByInteraction;\n\n  function onTransitionEnd(e) {\n    if (!swiper || swiper.destroyed || !swiper.wrapperEl) return;\n    if (e.target !== swiper.wrapperEl) return;\n    swiper.wrapperEl.removeEventListener('transitionend', onTransitionEnd);\n    resume();\n  }\n\n  const calcTimeLeft = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.autoplay.paused) {\n      wasPaused = true;\n    } else if (wasPaused) {\n      autoplayDelayCurrent = autoplayTimeLeft;\n      wasPaused = false;\n    }\n    const timeLeft = swiper.autoplay.paused\n      ? autoplayTimeLeft\n      : autoplayStartTime + autoplayDelayCurrent - new Date().getTime();\n    swiper.autoplay.timeLeft = timeLeft;\n    emit('autoplayTimeLeft', timeLeft, timeLeft / autoplayDelayTotal);\n    raf = requestAnimationFrame(() => {\n      calcTimeLeft();\n    });\n  };\n\n  const getSlideDelay = () => {\n    let activeSlideEl;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      activeSlideEl = swiper.slides.filter((slideEl) =>\n        slideEl.classList.contains('swiper-slide-active'),\n      )[0];\n    } else {\n      activeSlideEl = swiper.slides[swiper.activeIndex];\n    }\n    if (!activeSlideEl) return undefined;\n    const currentSlideDelay = parseInt(activeSlideEl.getAttribute('data-swiper-autoplay'), 10);\n    return currentSlideDelay;\n  };\n\n  const run = (delayForce) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    cancelAnimationFrame(raf);\n    calcTimeLeft();\n\n    let delay = typeof delayForce === 'undefined' ? swiper.params.autoplay.delay : delayForce;\n    autoplayDelayTotal = swiper.params.autoplay.delay;\n    autoplayDelayCurrent = swiper.params.autoplay.delay;\n    const currentSlideDelay = getSlideDelay();\n    if (\n      !Number.isNaN(currentSlideDelay) &&\n      currentSlideDelay > 0 &&\n      typeof delayForce === 'undefined'\n    ) {\n      delay = currentSlideDelay;\n      autoplayDelayTotal = currentSlideDelay;\n      autoplayDelayCurrent = currentSlideDelay;\n    }\n    autoplayTimeLeft = delay;\n    const speed = swiper.params.speed;\n    const proceed = () => {\n      if (!swiper || swiper.destroyed) return;\n      if (swiper.params.autoplay.reverseDirection) {\n        if (!swiper.isBeginning || swiper.params.loop || swiper.params.rewind) {\n          swiper.slidePrev(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(swiper.slides.length - 1, speed, true, true);\n          emit('autoplay');\n        }\n      } else {\n        if (!swiper.isEnd || swiper.params.loop || swiper.params.rewind) {\n          swiper.slideNext(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, speed, true, true);\n          emit('autoplay');\n        }\n      }\n      if (swiper.params.cssMode) {\n        autoplayStartTime = new Date().getTime();\n        requestAnimationFrame(() => {\n          run();\n        });\n      }\n    };\n    if (delay > 0) {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        proceed();\n      }, delay);\n    } else {\n      requestAnimationFrame(() => {\n        proceed();\n      });\n    }\n\n    // eslint-disable-next-line\n    return delay;\n  };\n\n  const start = () => {\n    swiper.autoplay.running = true;\n    run();\n    emit('autoplayStart');\n  };\n\n  const stop = () => {\n    swiper.autoplay.running = false;\n    clearTimeout(timeout);\n    cancelAnimationFrame(raf);\n    emit('autoplayStop');\n  };\n  const pause = (internal, reset) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    clearTimeout(timeout);\n    if (!internal) {\n      pausedByInteraction = true;\n    }\n\n    const proceed = () => {\n      emit('autoplayPause');\n      if (swiper.params.autoplay.waitForTransition) {\n        swiper.wrapperEl.addEventListener('transitionend', onTransitionEnd);\n      } else {\n        resume();\n      }\n    };\n\n    swiper.autoplay.paused = true;\n    if (reset) {\n      if (slideChanged) {\n        autoplayTimeLeft = swiper.params.autoplay.delay;\n      }\n      slideChanged = false;\n      proceed();\n      return;\n    }\n    const delay = autoplayTimeLeft || swiper.params.autoplay.delay;\n    autoplayTimeLeft = delay - (new Date().getTime() - autoplayStartTime);\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop) return;\n    if (autoplayTimeLeft < 0) autoplayTimeLeft = 0;\n    proceed();\n  };\n\n  const resume = () => {\n    if (\n      (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop) ||\n      swiper.destroyed ||\n      !swiper.autoplay.running\n    )\n      return;\n    autoplayStartTime = new Date().getTime();\n    if (pausedByInteraction) {\n      pausedByInteraction = false;\n      run(autoplayTimeLeft);\n    } else {\n      run();\n    }\n    swiper.autoplay.paused = false;\n    emit('autoplayResume');\n  };\n\n  const onVisibilityChange = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    const document = getDocument();\n    if (document.visibilityState === 'hidden') {\n      pausedByInteraction = true;\n      pause(true);\n    }\n    if (document.visibilityState === 'visible') {\n      resume();\n    }\n  };\n\n  const onPointerEnter = (e) => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByInteraction = true;\n    pause(true);\n  };\n\n  const onPointerLeave = (e) => {\n    if (e.pointerType !== 'mouse') return;\n    if (swiper.autoplay.paused) {\n      resume();\n    }\n  };\n\n  const attachMouseEvents = () => {\n    if (swiper.params.autoplay.pauseOnMouseEnter) {\n      swiper.el.addEventListener('pointerenter', onPointerEnter);\n      swiper.el.addEventListener('pointerleave', onPointerLeave);\n    }\n  };\n\n  const detachMouseEvents = () => {\n    swiper.el.removeEventListener('pointerenter', onPointerEnter);\n    swiper.el.removeEventListener('pointerleave', onPointerLeave);\n  };\n\n  const attachDocumentEvents = () => {\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n  };\n\n  const detachDocumentEvents = () => {\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n  };\n\n  on('init', () => {\n    if (swiper.params.autoplay.enabled) {\n      attachMouseEvents();\n      attachDocumentEvents();\n      autoplayStartTime = new Date().getTime();\n      start();\n    }\n  });\n\n  on('destroy', () => {\n    detachMouseEvents();\n    detachDocumentEvents();\n    if (swiper.autoplay.running) {\n      stop();\n    }\n  });\n\n  on('beforeTransitionStart', (_s, speed, internal) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (internal || !swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n\n  on('sliderFirstMove', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n\n    if (swiper.params.autoplay.disableOnInteraction) {\n      stop();\n      return;\n    }\n    isTouched = true;\n    pausedByTouch = false;\n    pausedByInteraction = false;\n    touchStartTimeout = setTimeout(() => {\n      pausedByInteraction = true;\n      pausedByTouch = true;\n      pause(true);\n    }, 200);\n  });\n\n  on('touchEnd', () => {\n    if (swiper.destroyed || !swiper.autoplay.running || !isTouched) return;\n    clearTimeout(touchStartTimeout);\n    clearTimeout(timeout);\n\n    if (swiper.params.autoplay.disableOnInteraction) {\n      pausedByTouch = false;\n      isTouched = false;\n      return;\n    }\n\n    if (pausedByTouch && swiper.params.cssMode) resume();\n    pausedByTouch = false;\n    isTouched = false;\n  });\n\n  on('slideChange', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    slideChanged = true;\n  });\n\n  Object.assign(swiper.autoplay, {\n    start,\n    stop,\n    pause,\n    resume,\n  });\n}\n", "import { getDocument } from 'ssr-window';\nimport { elementChildren, isObject } from '../../shared/utils.js';\n\nexport default function Thumb({ swiper, extendParams, on }) {\n  extendParams({\n    thumbs: {\n      swiper: null,\n      multipleActiveThumbs: true,\n      autoScrollOffset: 0,\n      slideThumbActiveClass: 'swiper-slide-thumb-active',\n      thumbsContainerClass: 'swiper-thumbs',\n    },\n  });\n\n  let initialized = false;\n  let swiperCreated = false;\n\n  swiper.thumbs = {\n    swiper: null,\n  };\n\n  function onThumbClick() {\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper || thumbsSwiper.destroyed) return;\n\n    const clickedIndex = thumbsSwiper.clickedIndex;\n    const clickedSlide = thumbsSwiper.clickedSlide;\n    if (clickedSlide && clickedSlide.classList.contains(swiper.params.thumbs.slideThumbActiveClass))\n      return;\n    if (typeof clickedIndex === 'undefined' || clickedIndex === null) return;\n    let slideToIndex;\n    if (thumbsSwiper.params.loop) {\n      slideToIndex = parseInt(\n        thumbsSwiper.clickedSlide.getAttribute('data-swiper-slide-index'),\n        10,\n      );\n    } else {\n      slideToIndex = clickedIndex;\n    }\n    if (swiper.params.loop) {\n      swiper.slideToLoop(slideToIndex);\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  }\n\n  function init() {\n    const { thumbs: thumbsParams } = swiper.params;\n    if (initialized) return false;\n    initialized = true;\n    const SwiperClass = swiper.constructor;\n    if (thumbsParams.swiper instanceof SwiperClass) {\n      swiper.thumbs.swiper = thumbsParams.swiper;\n      Object.assign(swiper.thumbs.swiper.originalParams, {\n        watchSlidesProgress: true,\n        slideToClickedSlide: false,\n      });\n      Object.assign(swiper.thumbs.swiper.params, {\n        watchSlidesProgress: true,\n        slideToClickedSlide: false,\n      });\n      swiper.thumbs.swiper.update();\n    } else if (isObject(thumbsParams.swiper)) {\n      const thumbsSwiperParams = Object.assign({}, thumbsParams.swiper);\n      Object.assign(thumbsSwiperParams, {\n        watchSlidesProgress: true,\n        slideToClickedSlide: false,\n      });\n      swiper.thumbs.swiper = new SwiperClass(thumbsSwiperParams);\n      swiperCreated = true;\n    }\n    swiper.thumbs.swiper.el.classList.add(swiper.params.thumbs.thumbsContainerClass);\n    swiper.thumbs.swiper.on('tap', onThumbClick);\n    return true;\n  }\n\n  function update(initial) {\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper || thumbsSwiper.destroyed) return;\n\n    const slidesPerView =\n      thumbsSwiper.params.slidesPerView === 'auto'\n        ? thumbsSwiper.slidesPerViewDynamic()\n        : thumbsSwiper.params.slidesPerView;\n\n    // Activate thumbs\n    let thumbsToActivate = 1;\n    const thumbActiveClass = swiper.params.thumbs.slideThumbActiveClass;\n\n    if (swiper.params.slidesPerView > 1 && !swiper.params.centeredSlides) {\n      thumbsToActivate = swiper.params.slidesPerView;\n    }\n\n    if (!swiper.params.thumbs.multipleActiveThumbs) {\n      thumbsToActivate = 1;\n    }\n\n    thumbsToActivate = Math.floor(thumbsToActivate);\n\n    thumbsSwiper.slides.forEach((slideEl) => slideEl.classList.remove(thumbActiveClass));\n    if (\n      thumbsSwiper.params.loop ||\n      (thumbsSwiper.params.virtual && thumbsSwiper.params.virtual.enabled)\n    ) {\n      for (let i = 0; i < thumbsToActivate; i += 1) {\n        elementChildren(\n          thumbsSwiper.slidesEl,\n          `[data-swiper-slide-index=\"${swiper.realIndex + i}\"]`,\n        ).forEach((slideEl) => {\n          slideEl.classList.add(thumbActiveClass);\n        });\n      }\n    } else {\n      for (let i = 0; i < thumbsToActivate; i += 1) {\n        if (thumbsSwiper.slides[swiper.realIndex + i]) {\n          thumbsSwiper.slides[swiper.realIndex + i].classList.add(thumbActiveClass);\n        }\n      }\n    }\n\n    const autoScrollOffset = swiper.params.thumbs.autoScrollOffset;\n    const useOffset = autoScrollOffset && !thumbsSwiper.params.loop;\n    if (swiper.realIndex !== thumbsSwiper.realIndex || useOffset) {\n      const currentThumbsIndex = thumbsSwiper.activeIndex;\n      let newThumbsIndex;\n      let direction;\n      if (thumbsSwiper.params.loop) {\n        const newThumbsSlide = thumbsSwiper.slides.filter(\n          (slideEl) => slideEl.getAttribute('data-swiper-slide-index') === `${swiper.realIndex}`,\n        )[0];\n        newThumbsIndex = thumbsSwiper.slides.indexOf(newThumbsSlide);\n\n        direction = swiper.activeIndex > swiper.previousIndex ? 'next' : 'prev';\n      } else {\n        newThumbsIndex = swiper.realIndex;\n        direction = newThumbsIndex > swiper.previousIndex ? 'next' : 'prev';\n      }\n      if (useOffset) {\n        newThumbsIndex += direction === 'next' ? autoScrollOffset : -1 * autoScrollOffset;\n      }\n\n      if (\n        thumbsSwiper.visibleSlidesIndexes &&\n        thumbsSwiper.visibleSlidesIndexes.indexOf(newThumbsIndex) < 0\n      ) {\n        if (thumbsSwiper.params.centeredSlides) {\n          if (newThumbsIndex > currentThumbsIndex) {\n            newThumbsIndex = newThumbsIndex - Math.floor(slidesPerView / 2) + 1;\n          } else {\n            newThumbsIndex = newThumbsIndex + Math.floor(slidesPerView / 2) - 1;\n          }\n        } else if (\n          newThumbsIndex > currentThumbsIndex &&\n          thumbsSwiper.params.slidesPerGroup === 1\n        ) {\n          // newThumbsIndex = newThumbsIndex - slidesPerView + 1;\n        }\n        thumbsSwiper.slideTo(newThumbsIndex, initial ? 0 : undefined);\n      }\n    }\n  }\n\n  on('beforeInit', () => {\n    const { thumbs } = swiper.params;\n    if (!thumbs || !thumbs.swiper) return;\n    if (typeof thumbs.swiper === 'string' || thumbs.swiper instanceof HTMLElement) {\n      const document = getDocument();\n      const getThumbsElementAndInit = () => {\n        const thumbsElement =\n          typeof thumbs.swiper === 'string' ? document.querySelector(thumbs.swiper) : thumbs.swiper;\n        if (thumbsElement && thumbsElement.swiper) {\n          thumbs.swiper = thumbsElement.swiper;\n          init();\n          update(true);\n        } else if (thumbsElement) {\n          const onThumbsSwiper = (e) => {\n            thumbs.swiper = e.detail[0];\n            thumbsElement.removeEventListener('init', onThumbsSwiper);\n            init();\n            update(true);\n            thumbs.swiper.update();\n            swiper.update();\n          };\n          thumbsElement.addEventListener('init', onThumbsSwiper);\n        }\n        return thumbsElement;\n      };\n\n      const watchForThumbsToAppear = () => {\n        if (swiper.destroyed) return;\n        const thumbsElement = getThumbsElementAndInit();\n        if (!thumbsElement) {\n          requestAnimationFrame(watchForThumbsToAppear);\n        }\n      };\n      requestAnimationFrame(watchForThumbsToAppear);\n    } else {\n      init();\n      update(true);\n    }\n  });\n  on('slideChange update resize observerUpdate', () => {\n    update();\n  });\n  on('setTransition', (_s, duration) => {\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper || thumbsSwiper.destroyed) return;\n    thumbsSwiper.setTransition(duration);\n  });\n  on('beforeDestroy', () => {\n    const thumbsSwiper = swiper.thumbs.swiper;\n    if (!thumbsSwiper || thumbsSwiper.destroyed) return;\n    if (swiperCreated) {\n      thumbsSwiper.destroy();\n    }\n  });\n\n  Object.assign(swiper.thumbs, {\n    init,\n    update,\n  });\n}\n", "import { elementTransitionEnd, now } from '../../shared/utils.js';\n\nexport default function freeMode({ swiper, extendParams, emit, once }) {\n  extendParams({\n    freeMode: {\n      enabled: false,\n      momentum: true,\n      momentumRatio: 1,\n      momentumBounce: true,\n      momentumBounceRatio: 1,\n      momentumVelocityRatio: 1,\n      sticky: false,\n      minimumVelocity: 0.02,\n    },\n  });\n\n  function onTouchStart() {\n    const translate = swiper.getTranslate();\n    swiper.setTranslate(translate);\n    swiper.setTransition(0);\n    swiper.touchEventsData.velocities.length = 0;\n    swiper.freeMode.onTouchEnd({ currentPos: swiper.rtl ? swiper.translate : -swiper.translate });\n  }\n\n  function onTouchMove() {\n    const { touchEventsData: data, touches } = swiper;\n    // Velocity\n    if (data.velocities.length === 0) {\n      data.velocities.push({\n        position: touches[swiper.isHorizontal() ? 'startX' : 'startY'],\n        time: data.touchStartTime,\n      });\n    }\n    data.velocities.push({\n      position: touches[swiper.isHorizontal() ? 'currentX' : 'currentY'],\n      time: now(),\n    });\n  }\n\n  function onTouchEnd({ currentPos }) {\n    const { params, wrapperEl, rtlTranslate: rtl, snapGrid, touchEventsData: data } = swiper;\n    // Time diff\n    const touchEndTime = now();\n    const timeDiff = touchEndTime - data.touchStartTime;\n\n    if (currentPos < -swiper.minTranslate()) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (currentPos > -swiper.maxTranslate()) {\n      if (swiper.slides.length < snapGrid.length) {\n        swiper.slideTo(snapGrid.length - 1);\n      } else {\n        swiper.slideTo(swiper.slides.length - 1);\n      }\n      return;\n    }\n\n    if (params.freeMode.momentum) {\n      if (data.velocities.length > 1) {\n        const lastMoveEvent = data.velocities.pop();\n        const velocityEvent = data.velocities.pop();\n\n        const distance = lastMoveEvent.position - velocityEvent.position;\n        const time = lastMoveEvent.time - velocityEvent.time;\n        swiper.velocity = distance / time;\n        swiper.velocity /= 2;\n        if (Math.abs(swiper.velocity) < params.freeMode.minimumVelocity) {\n          swiper.velocity = 0;\n        }\n        // this implies that the user stopped moving a finger then released.\n        // There would be no events with distance zero, so the last event is stale.\n        if (time > 150 || now() - lastMoveEvent.time > 300) {\n          swiper.velocity = 0;\n        }\n      } else {\n        swiper.velocity = 0;\n      }\n      swiper.velocity *= params.freeMode.momentumVelocityRatio;\n\n      data.velocities.length = 0;\n      let momentumDuration = 1000 * params.freeMode.momentumRatio;\n      const momentumDistance = swiper.velocity * momentumDuration;\n\n      let newPosition = swiper.translate + momentumDistance;\n      if (rtl) newPosition = -newPosition;\n\n      let doBounce = false;\n      let afterBouncePosition;\n      const bounceAmount = Math.abs(swiper.velocity) * 20 * params.freeMode.momentumBounceRatio;\n      let needsLoopFix;\n      if (newPosition < swiper.maxTranslate()) {\n        if (params.freeMode.momentumBounce) {\n          if (newPosition + swiper.maxTranslate() < -bounceAmount) {\n            newPosition = swiper.maxTranslate() - bounceAmount;\n          }\n          afterBouncePosition = swiper.maxTranslate();\n          doBounce = true;\n          data.allowMomentumBounce = true;\n        } else {\n          newPosition = swiper.maxTranslate();\n        }\n        if (params.loop && params.centeredSlides) needsLoopFix = true;\n      } else if (newPosition > swiper.minTranslate()) {\n        if (params.freeMode.momentumBounce) {\n          if (newPosition - swiper.minTranslate() > bounceAmount) {\n            newPosition = swiper.minTranslate() + bounceAmount;\n          }\n          afterBouncePosition = swiper.minTranslate();\n          doBounce = true;\n          data.allowMomentumBounce = true;\n        } else {\n          newPosition = swiper.minTranslate();\n        }\n        if (params.loop && params.centeredSlides) needsLoopFix = true;\n      } else if (params.freeMode.sticky) {\n        let nextSlide;\n        for (let j = 0; j < snapGrid.length; j += 1) {\n          if (snapGrid[j] > -newPosition) {\n            nextSlide = j;\n            break;\n          }\n        }\n\n        if (\n          Math.abs(snapGrid[nextSlide] - newPosition) <\n            Math.abs(snapGrid[nextSlide - 1] - newPosition) ||\n          swiper.swipeDirection === 'next'\n        ) {\n          newPosition = snapGrid[nextSlide];\n        } else {\n          newPosition = snapGrid[nextSlide - 1];\n        }\n        newPosition = -newPosition;\n      }\n      if (needsLoopFix) {\n        once('transitionEnd', () => {\n          swiper.loopFix();\n        });\n      }\n      // Fix duration\n      if (swiper.velocity !== 0) {\n        if (rtl) {\n          momentumDuration = Math.abs((-newPosition - swiper.translate) / swiper.velocity);\n        } else {\n          momentumDuration = Math.abs((newPosition - swiper.translate) / swiper.velocity);\n        }\n        if (params.freeMode.sticky) {\n          // If freeMode.sticky is active and the user ends a swipe with a slow-velocity\n          // event, then durations can be 20+ seconds to slide one (or zero!) slides.\n          // It's easy to see this when simulating touch with mouse events. To fix this,\n          // limit single-slide swipes to the default slide duration. This also has the\n          // nice side effect of matching slide speed if the user stopped moving before\n          // lifting finger or mouse vs. moving slowly before lifting the finger/mouse.\n          // For faster swipes, also apply limits (albeit higher ones).\n          const moveDistance = Math.abs((rtl ? -newPosition : newPosition) - swiper.translate);\n          const currentSlideSize = swiper.slidesSizesGrid[swiper.activeIndex];\n          if (moveDistance < currentSlideSize) {\n            momentumDuration = params.speed;\n          } else if (moveDistance < 2 * currentSlideSize) {\n            momentumDuration = params.speed * 1.5;\n          } else {\n            momentumDuration = params.speed * 2.5;\n          }\n        }\n      } else if (params.freeMode.sticky) {\n        swiper.slideToClosest();\n        return;\n      }\n\n      if (params.freeMode.momentumBounce && doBounce) {\n        swiper.updateProgress(afterBouncePosition);\n        swiper.setTransition(momentumDuration);\n        swiper.setTranslate(newPosition);\n        swiper.transitionStart(true, swiper.swipeDirection);\n        swiper.animating = true;\n        elementTransitionEnd(wrapperEl, () => {\n          if (!swiper || swiper.destroyed || !data.allowMomentumBounce) return;\n          emit('momentumBounce');\n          swiper.setTransition(params.speed);\n          setTimeout(() => {\n            swiper.setTranslate(afterBouncePosition);\n            elementTransitionEnd(wrapperEl, () => {\n              if (!swiper || swiper.destroyed) return;\n              swiper.transitionEnd();\n            });\n          }, 0);\n        });\n      } else if (swiper.velocity) {\n        emit('_freeModeNoMomentumRelease');\n        swiper.updateProgress(newPosition);\n        swiper.setTransition(momentumDuration);\n        swiper.setTranslate(newPosition);\n        swiper.transitionStart(true, swiper.swipeDirection);\n        if (!swiper.animating) {\n          swiper.animating = true;\n          elementTransitionEnd(wrapperEl, () => {\n            if (!swiper || swiper.destroyed) return;\n            swiper.transitionEnd();\n          });\n        }\n      } else {\n        swiper.updateProgress(newPosition);\n      }\n\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    } else if (params.freeMode.sticky) {\n      swiper.slideToClosest();\n      return;\n    } else if (params.freeMode) {\n      emit('_freeModeNoMomentumRelease');\n    }\n\n    if (!params.freeMode.momentum || timeDiff >= params.longSwipesMs) {\n      swiper.updateProgress();\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n  }\n\n  Object.assign(swiper, {\n    freeMode: {\n      onTouchStart,\n      onTouchMove,\n      onTouchEnd,\n    },\n  });\n}\n", "import appendSlide from './methods/appendSlide.js';\nimport prependSlide from './methods/prependSlide.js';\nimport addSlide from './methods/addSlide.js';\nimport removeSlide from './methods/removeSlide.js';\nimport removeAllSlides from './methods/removeAllSlides.js';\n\nexport default function Manipulation({ swiper }) {\n  Object.assign(swiper, {\n    appendSlide: appendSlide.bind(swiper),\n    prependSlide: prependSlide.bind(swiper),\n    addSlide: addSlide.bind(swiper),\n    removeSlide: removeSlide.bind(swiper),\n    removeAllSlides: removeAllSlides.bind(swiper),\n  });\n}\n"], "mappings": ";;;;;;;;;;;;uOAYA,SAASA,EAASC,GACd,OAAgB,OAARA,GACW,iBAARA,GACP,gBAAiBA,GACjBA,EAAIC,cAAgBC,MAC3B,CACD,SAASC,EAAOC,EAAaC,QAAU,IAAvBD,MAAS,SAAc,IAAVC,MAAM,IAC/BH,OAAOI,KAAKD,GAAKE,SAASC,SACK,IAAhBJ,EAAOI,GACdJ,EAAOI,GAAOH,EAAIG,GACbT,EAASM,EAAIG,KAClBT,EAASK,EAAOI,KAChBN,OAAOI,KAAKD,EAAIG,IAAMC,OAAS,GAC/BN,EAAOC,EAAOI,GAAMH,EAAIG,GAC3B,GAER,CAED,MAAME,EAAc,CAChBC,KAAM,GACNC,mBAAmB,EACnBC,sBAAsB,EACtBC,cAAe,CACXC,OAAO,EACPC,SAAU,IAEdC,cAAa,IACF,KAEXC,iBAAgB,IACL,GAEXC,eAAc,IACH,KAEXC,YAAW,KACA,CACHC,YAAY,IAGpBC,cAAa,KACF,CACHC,SAAU,GACVC,WAAY,GACZC,MAAO,GACPC,eAAe,EACfC,qBAAoB,IACT,KAInBC,gBAAe,KACJ,IAEXC,WAAU,IACC,KAEXC,SAAU,CACNC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,KAGhB,SAASC,IACL,MAAMC,EAA0B,oBAAbC,SAA2BA,SAAW,GAEzD,OADAtC,EAAOqC,EAAK9B,GACL8B,CACV,CAED,MAAME,EAAY,CACdD,SAAU/B,EACViC,UAAW,CACPC,UAAW,IAEfd,SAAU,CACNC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,IAEZO,QAAS,CACLC,eAAe,EACfC,YAAY,EACZC,KAAK,EACLC,OAAO,GAEXC,YAAa,WACT,OAAOC,I,EAEXvC,mBAAmB,EACnBC,sBAAsB,EACtBuC,iBAAgB,KACL,CACHC,iBAAgB,IACL,KAInBC,QAAQ,EACRC,OAAO,EACPC,OAAQ,GACRC,aAAa,EACbC,eAAe,EACfC,WAAU,KACC,IAEXC,sBAAsBC,GACQ,oBAAfJ,YACPI,IACO,MAEJJ,WAAWI,EAAU,GAEhCC,qBAAqBC,GACS,oBAAfN,YAGXC,aAAaK,EAChB,GAEL,SAASC,IACL,MAAMC,EAAwB,oBAAXC,OAAyBA,OAAS,GAErD,OADA/D,EAAO8D,EAAKvB,GACLuB,CACV,CChID,SAASE,EAASN,EAAUO,GAC1B,YADqC,IAAXA,MAAQ,GAC3BX,WAAWI,EAAUO,EAC7B,CACD,SAASC,IACP,OAAOd,KAAKc,KACb,CAgBD,SAASC,EAAaC,EAAIC,QAAY,IAAZA,MAAO,KAC/B,MAAMN,EAASF,IACf,IAAIS,EACAC,EACAC,EAEJ,MAAMC,EArBR,SAA0BL,GACxB,MAAML,EAASF,IACf,IAAIvC,EAWJ,OAVIyC,EAAOd,mBACT3B,EAAQyC,EAAOd,iBAAiBmB,EAAI,QAEjC9C,GAAS8C,EAAGM,eACfpD,EAAQ8C,EAAGM,cAERpD,IACHA,EAAQ8C,EAAG9C,OAGNA,CACR,CAOkB2B,CAAiBmB,GAwClC,OAtCIL,EAAOY,iBACTJ,EAAeE,EAASG,WAAaH,EAASI,gBAC1CN,EAAaO,MAAM,KAAKxE,OAAS,IACnCiE,EAAeA,EACZO,MAAM,MACNC,KAAKC,GAAMA,EAAEC,QAAQ,IAAK,OAC1BC,KAAK,OAIVV,EAAkB,IAAIT,EAAOY,gBAAiC,SAAjBJ,EAA0B,GAAKA,KAE5EC,EACEC,EAASU,cACTV,EAASW,YACTX,EAASY,aACTZ,EAASa,aACTb,EAASG,WACTH,EAASvB,iBAAiB,aAAa+B,QAAQ,aAAc,sBAC/DX,EAASE,EAAgBe,WAAWT,MAAM,MAG/B,MAATT,IAE0BE,EAAxBR,EAAOY,gBAAgCH,EAAgBgB,IAEhC,KAAlBlB,EAAOhE,OAA8BmF,WAAWnB,EAAO,KAE5CmB,WAAWnB,EAAO,KAE3B,MAATD,IAE0BE,EAAxBR,EAAOY,gBAAgCH,EAAgBkB,IAEhC,KAAlBpB,EAAOhE,OAA8BmF,WAAWnB,EAAO,KAE5CmB,WAAWnB,EAAO,KAEjCC,GAAgB,CACxB,CACD,SAAS3E,EAAS+F,GAChB,MACe,iBAANA,GACD,OAANA,GACAA,EAAE7F,aACiD,WAAnDC,OAAO6F,UAAUL,SAASM,KAAKF,GAAGG,MAAM,GAAI,EAE/C,CACD,SAASC,EAAOC,GAEd,MAAsB,oBAAXjC,aAAwD,IAAvBA,OAAOkC,YAC1CD,aAAgBC,YAElBD,IAA2B,IAAlBA,EAAKE,UAAoC,KAAlBF,EAAKE,SAC7C,CACD,SAASlG,IACP,MAAMmG,EAAKpG,OAAXqG,UAAA9F,QAAA,OAAA+F,EAAAD,UAAA,IACME,EAAW,CAAC,YAAa,cAAe,aAC9C,IAAK,IAAIC,EAAI,EAAGA,EAAIH,UAAK9F,OAAQiG,GAAK,EAAG,CACvC,MAAMC,EAAkBD,EAAR,GAAAH,UAAA9F,QAAQiG,OAARF,EAAAD,UAAQG,GACxB,GAAIC,UAAoDT,EAAOS,GAAa,CAC1E,MAAMC,EAAY1G,OAAOI,KAAKJ,OAAOyG,IAAaE,QAAQrG,GAAQiG,EAASK,QAAQtG,GAAO,IAC1F,IAAK,IAAIuG,EAAY,EAAGC,EAAMJ,EAAUnG,OAAQsG,EAAYC,EAAKD,GAAa,EAAG,CAC/E,MAAME,EAAUL,EAAUG,GACpBG,EAAOhH,OAAOiH,yBAAyBR,EAAYM,QAC5CT,IAATU,GAAsBA,EAAKE,aACzBrH,EAASuG,EAAGW,KAAalH,EAAS4G,EAAWM,IAC3CN,EAAWM,GAASI,WACtBf,EAAGW,GAAWN,EAAWM,GAEzB9G,EAAOmG,EAAGW,GAAUN,EAAWM,KAEvBlH,EAASuG,EAAGW,KAAalH,EAAS4G,EAAWM,KACvDX,EAAGW,GAAW,GACVN,EAAWM,GAASI,WACtBf,EAAGW,GAAWN,EAAWM,GAEzB9G,EAAOmG,EAAGW,GAAUN,EAAWM,KAGjCX,EAAGW,GAAWN,EAAWM,GAG9B,CACF,CACF,CACD,OAAOX,CACR,CAED,SAASgB,EAAe/C,EAAIgD,EAASC,GACnCjD,EAAG9C,MAAMgG,YAAYF,EAASC,EAC/B,CAED,SAASE,EAAuDC,GAAA,IAAlCC,OAAEA,EAAFC,eAAUA,EAAVC,KAA0BA,GAAQH,EAC9D,MAAMzD,EAASF,IACT+D,GAAiBH,EAAOI,UAC9B,IACIC,EADAC,EAAY,KAEhB,MAAMC,EAAWP,EAAOQ,OAAOC,MAE/BT,EAAOU,UAAU7G,MAAM8G,eAAiB,OACxCrE,EAAOJ,qBAAqB8D,EAAOY,gBAEnC,MAAMC,EAAMZ,EAAiBE,EAAgB,OAAS,OAEhDW,EAAe,CAACC,EAASvI,IACb,SAARqI,GAAkBE,GAAWvI,GAAoB,SAARqI,GAAkBE,GAAWvI,EAG1EwI,EAAU,KACdX,GAAO,IAAI1E,MAAOsF,UACA,OAAdX,IACFA,EAAYD,GAGd,MAAMa,EAAWC,KAAKC,IAAID,KAAKE,KAAKhB,EAAOC,GAAaC,EAAU,GAAI,GAChEe,EAAe,GAAMH,KAAKI,IAAIL,EAAWC,KAAKK,IAAM,EAC1D,IAAIC,EAAkBtB,EAAgBmB,GAAgBrB,EAAiBE,GAQvE,GANIW,EAAaW,EAAiBxB,KAChCwB,EAAkBxB,GAEpBD,EAAOU,UAAUgB,SAAS,CACxBxB,CAACA,GAAOuB,IAENX,EAAaW,EAAiBxB,GAUhC,OATAD,EAAOU,UAAU7G,MAAM8H,SAAW,SAClC3B,EAAOU,UAAU7G,MAAM8G,eAAiB,GACxC9E,YAAW,KACTmE,EAAOU,UAAU7G,MAAM8H,SAAW,GAClC3B,EAAOU,UAAUgB,SAAS,CACxBxB,CAACA,GAAOuB,GADV,SAIFnF,EAAOJ,qBAAqB8D,EAAOY,gBAGrCZ,EAAOY,eAAiBtE,EAAON,sBAAsBgF,EAArD,EAEFA,GACD,CAiBD,SAASY,EAAgBC,EAASC,GAChC,YAD+C,IAAfA,MAAW,IACpC,IAAID,EAAQlI,UAAUsF,QAAQtC,GAAOA,EAAGoF,QAAQD,IACxD,CAED,SAASpI,EAAcsI,EAAKC,QAAc,IAAdA,MAAU,IACpC,MAAMtF,EAAK9B,SAASnB,cAAcsI,GAElC,OADArF,EAAGuF,UAAUC,OAAQC,MAAMC,QAAQJ,GAAWA,EAAU,CAACA,IAClDtF,CACR,CACD,SAAS2F,EAAc3F,GACrB,MAAML,EAASF,IACTvB,EAAWF,IACX4H,EAAM5F,EAAG6F,wBACTzJ,EAAO8B,EAAS9B,KAChB0J,EAAY9F,EAAG8F,WAAa1J,EAAK0J,WAAa,EAC9CC,EAAa/F,EAAG+F,YAAc3J,EAAK2J,YAAc,EACjDC,EAAYhG,IAAOL,EAASA,EAAOsG,QAAUjG,EAAGgG,UAChDE,EAAalG,IAAOL,EAASA,EAAOwG,QAAUnG,EAAGkG,WACvD,MAAO,CACLE,IAAKR,EAAIQ,IAAMJ,EAAYF,EAC3BO,KAAMT,EAAIS,KAAOH,EAAaH,EAEjC,CAuBD,SAASO,EAAatG,EAAIuG,GAExB,OADe9G,IACDZ,iBAAiBmB,EAAI,MAAMlB,iBAAiByH,EAC3D,CACD,SAASC,EAAaxG,GACpB,IACImC,EADAsE,EAAQzG,EAEZ,GAAIyG,EAAO,CAGT,IAFAtE,EAAI,EAEuC,QAAnCsE,EAAQA,EAAMC,kBACG,IAAnBD,EAAM3E,WAAgBK,GAAK,GAEjC,OAAOA,CACR,CAEF,CAED,SAASwE,EAAe3G,EAAImF,GAC1B,MAAMyB,EAAU,GAChB,IAAIC,EAAS7G,EAAG8G,cAChB,KAAOD,GACD1B,EACE0B,EAAOzB,QAAQD,IAAWyB,EAAQG,KAAKF,GAE3CD,EAAQG,KAAKF,GAEfA,EAASA,EAAOC,cAElB,OAAOF,CACR,CAED,SAASI,EAAqBhH,EAAIV,GAM5BA,GACFU,EAAG3D,iBAAiB,iBANtB,SAAS4K,EAAaC,GAChBA,EAAErL,SAAWmE,IACjBV,EAASmC,KAAKzB,EAAIkH,GAClBlH,EAAG1D,oBAAoB,gBAAiB2K,GACzC,GAIF,CAED,SAASE,EAAiBnH,EAAIoH,EAAMC,GAClC,MAAM1H,EAASF,IACf,OAAI4H,EAEArH,EAAY,UAAToH,EAAmB,cAAgB,gBACtC/F,WACE1B,EACGd,iBAAiBmB,EAAI,MACrBlB,iBAA0B,UAATsI,EAAmB,eAAiB,eAE1D/F,WACE1B,EACGd,iBAAiBmB,EAAI,MACrBlB,iBAA0B,UAATsI,EAAmB,cAAgB,kBAItDpH,EAAGsH,WACX,CCnTD,IAAIC,ECCAC,ECDAC,EFgBJ,SAASC,IAIP,OAHKH,IACHA,EAhBJ,WACE,MAAM5H,EAASF,IACTvB,EAAWF,IAEjB,MAAO,CACL2J,aAAczJ,EAAS0J,iBAAmB,mBAAoB1J,EAAS0J,gBAAgB1K,MAEvF2K,SACE,iBAAkBlI,GACjBA,EAAOmI,eAAiB5J,aAAoByB,EAAOmI,eAGzD,CAIaC,IAELR,CACR,CC2CD,SAASS,EAAUC,GAIjB,YAJiC,IAAhBA,MAAY,IACxBT,IACHA,EA/DJ,SAAwCU,GAAA,IAApB7J,UAAEA,QAAkB,IAAA6J,EAAJ,GAAIA,EACtC,MAAMX,EAAUG,IACV/H,EAASF,IACT0I,EAAWxI,EAAOvB,UAAU+J,SAC5BC,EAAK/J,GAAasB,EAAOvB,UAAUC,UAEnCgK,EAAS,CACbC,KAAK,EACLC,SAAS,GAGLC,EAAc7I,EAAOV,OAAOwJ,MAC5BC,EAAe/I,EAAOV,OAAO0J,OAE7BJ,EAAUH,EAAGQ,MAAM,+BACzB,IAAIC,EAAOT,EAAGQ,MAAM,wBACpB,MAAME,EAAOV,EAAGQ,MAAM,2BAChBG,GAAUF,GAAQT,EAAGQ,MAAM,8BAC3BI,EAAuB,UAAbb,EAChB,IAAIc,EAAqB,aAAbd,EAuCZ,OArBGU,GACDI,GACA1B,EAAQM,OAjBU,CAClB,YACA,YACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,YAMYtF,QAAS,GAAEiG,KAAeE,MAAmB,IAEzDG,EAAOT,EAAGQ,MAAM,uBACXC,IAAMA,EAAO,CAAC,EAAG,EAAG,WACzBI,GAAQ,GAINV,IAAYS,IACdX,EAAOa,GAAK,UACZb,EAAOE,SAAU,IAEfM,GAAQE,GAAUD,KACpBT,EAAOa,GAAK,MACZb,EAAOC,KAAM,GAIRD,CACR,CAIkBc,CAAWlB,IAErBT,CACR,CC1CD,SAAS4B,IAIP,OAHK3B,IACHA,EA3BJ,WACE,MAAM9H,EAASF,IACf,IAAI4J,GAAqB,EACzB,SAASC,IACP,MAAMlB,EAAKzI,EAAOvB,UAAUC,UAAUkL,cACtC,OAAOnB,EAAG7F,QAAQ,WAAa,GAAK6F,EAAG7F,QAAQ,UAAY,GAAK6F,EAAG7F,QAAQ,WAAa,CACzF,CACD,GAAI+G,IAAY,CACd,MAAMlB,EAAKoB,OAAO7J,EAAOvB,UAAUC,WACnC,GAAI+J,EAAGqB,SAAS,YAAa,CAC3B,MAAOC,EAAOC,GAASvB,EACpB1H,MAAM,YAAY,GAClBA,MAAM,KAAK,GACXA,MAAM,KACNC,KAAKiJ,GAAQC,OAAOD,KACvBP,EAAqBK,EAAQ,IAAiB,KAAVA,GAAgBC,EAAQ,CAC7D,CACF,CACD,MAAO,CACLL,SAAUD,GAAsBC,IAChCD,qBACAS,UAAW,+CAA+CC,KAAKpK,EAAOvB,UAAUC,WAEnF,CAIa2L,IAELvC,CACR,CChCD,IAAAwC,EAAe,CACbC,GAAGC,EAAQC,EAASC,GAClB,MAAMC,EAAO1L,KACb,IAAK0L,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,GAAuB,mBAAZF,EAAwB,OAAOE,EAC1C,MAAMG,EAASJ,EAAW,UAAY,OAKtC,OAJAF,EAAOzJ,MAAM,KAAK1E,SAAS0O,IACpBJ,EAAKC,gBAAgBG,KAAQJ,EAAKC,gBAAgBG,GAAS,IAChEJ,EAAKC,gBAAgBG,GAAOD,GAAQL,EAApC,IAEKE,C,EAGTK,KAAKR,EAAQC,EAASC,GACpB,MAAMC,EAAO1L,KACb,IAAK0L,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,GAAuB,mBAAZF,EAAwB,OAAOE,EAC1C,SAASM,IACPN,EAAKO,IAAIV,EAAQS,GACbA,EAAYE,uBACPF,EAAYE,eAHO,QAAAC,EAAA/I,UAAA9F,OAAN8O,EAAM,IAAAvF,MAAAsF,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAND,EAAMC,GAAAjJ,UAAAiJ,GAK5Bb,EAAQc,MAAMZ,EAAMU,EACrB,CAED,OADAJ,EAAYE,eAAiBV,EACtBE,EAAKJ,GAAGC,EAAQS,EAAaP,E,EAGtCc,MAAMf,EAASC,GACb,MAAMC,EAAO1L,KACb,IAAK0L,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,GAAuB,mBAAZF,EAAwB,OAAOE,EAC1C,MAAMG,EAASJ,EAAW,UAAY,OAItC,OAHIC,EAAKc,mBAAmB7I,QAAQ6H,GAAW,GAC7CE,EAAKc,mBAAmBX,GAAQL,GAE3BE,C,EAGTe,OAAOjB,GACL,MAAME,EAAO1L,KACb,IAAK0L,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,IAAKA,EAAKc,mBAAoB,OAAOd,EACrC,MAAMgB,EAAQhB,EAAKc,mBAAmB7I,QAAQ6H,GAI9C,OAHIkB,GAAS,GACXhB,EAAKc,mBAAmBG,OAAOD,EAAO,GAEjChB,C,EAGTO,IAAIV,EAAQC,GACV,MAAME,EAAO1L,KACb,OAAK0L,EAAKC,iBAAmBD,EAAKE,UAAkBF,EAC/CA,EAAKC,iBACVJ,EAAOzJ,MAAM,KAAK1E,SAAS0O,SACF,IAAZN,EACTE,EAAKC,gBAAgBG,GAAS,GACrBJ,EAAKC,gBAAgBG,IAC9BJ,EAAKC,gBAAgBG,GAAO1O,SAAQ,CAACwP,EAAcF,MAE/CE,IAAiBpB,GAChBoB,EAAaV,gBAAkBU,EAAaV,iBAAmBV,IAEhEE,EAAKC,gBAAgBG,GAAOa,OAAOD,EAAO,EAC3C,GAEJ,IAEIhB,GAf2BA,C,EAkBpCmB,OACE,MAAMnB,EAAO1L,KACb,IAAK0L,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,IAAKA,EAAKC,gBAAiB,OAAOD,EAClC,IAAIH,EACAuB,EACAC,EANQ,QAAAC,EAAA5J,UAAA9F,OAAN8O,EAAM,IAAAvF,MAAAmG,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAANb,EAAMa,GAAA7J,UAAA6J,GAOW,iBAAZb,EAAK,IAAmBvF,MAAMC,QAAQsF,EAAK,KACpDb,EAASa,EAAK,GACdU,EAAOV,EAAKtJ,MAAM,EAAGsJ,EAAK9O,QAC1ByP,EAAUrB,IAEVH,EAASa,EAAK,GAAGb,OACjBuB,EAAOV,EAAK,GAAGU,KACfC,EAAUX,EAAK,GAAGW,SAAWrB,GAE/BoB,EAAKI,QAAQH,GAeb,OAdoBlG,MAAMC,QAAQyE,GAAUA,EAASA,EAAOzJ,MAAM,MAEtD1E,SAAS0O,IACfJ,EAAKc,oBAAsBd,EAAKc,mBAAmBlP,QACrDoO,EAAKc,mBAAmBpP,SAASwP,IAC/BA,EAAaN,MAAMS,EAAS,CAACjB,KAAUgB,GAAvC,IAGApB,EAAKC,iBAAmBD,EAAKC,gBAAgBG,IAC/CJ,EAAKC,gBAAgBG,GAAO1O,SAASwP,IACnCA,EAAaN,MAAMS,EAASD,EAA5B,GAEH,IAEIpB,CACR,GCzGI,MAAMyB,EAAuB,CAAC1I,EAAQ2I,KAC3C,IAAK3I,GAAUA,EAAOmH,YAAcnH,EAAOQ,OAAQ,OACnD,MACMoI,EAAUD,EAAQE,QADK7I,EAAO8I,UAAa,eAAiB,IAAG9I,EAAOQ,OAAOuI,cAEnF,GAAIH,EAAS,CACX,MAAMI,EAASJ,EAAQvP,cAAe,IAAG2G,EAAOQ,OAAOyI,sBACnDD,GAAQA,EAAOE,QACpB,GAGGC,EAAS,CAACnJ,EAAQiI,KACtB,IAAKjI,EAAOoJ,OAAOnB,GAAQ,OAC3B,MAAMU,EAAU3I,EAAOoJ,OAAOnB,GAAO5O,cAAc,oBAC/CsP,GAASA,EAAQU,gBAAgB,UAAxB,EAGFC,EAAWtJ,IACtB,IAAKA,GAAUA,EAAOmH,YAAcnH,EAAOQ,OAAQ,OACnD,IAAI+I,EAASvJ,EAAOQ,OAAOgJ,oBAC3B,MAAMpK,EAAMY,EAAOoJ,OAAOvQ,OAC1B,IAAKuG,IAAQmK,GAAUA,EAAS,EAAG,OACnCA,EAASpI,KAAKE,IAAIkI,EAAQnK,GAC1B,MAAMqK,EAC4B,SAAhCzJ,EAAOQ,OAAOiJ,cACVzJ,EAAO0J,uBACPvI,KAAKwI,KAAK3J,EAAOQ,OAAOiJ,eACxBG,EAAc5J,EAAO4J,YACrBC,EAAuBD,EAAcH,EAAgB,EAC3D,GAAIzJ,EAAOQ,OAAOsJ,OAChB,IAAK,IAAIhL,EAAI8K,EAAcL,EAAQzK,GAAK+K,EAAuBN,EAAQzK,GAAK,EAAG,CAC7E,MAAMiL,GAAcjL,EAAIM,EAAOA,GAAOA,EAClC2K,IAAcH,GAAeG,EAAYF,GAAsBV,EAAOnJ,EAAQ+J,EACnF,MAED,IACE,IAAIjL,EAAIqC,KAAKC,IAAIyI,EAAuBN,EAAQ,GAChDzK,GAAKqC,KAAKE,IAAIwI,EAAuBN,EAAQnK,EAAM,GACnDN,GAAK,EAEDA,IAAM8K,GAAe9K,EAAI+K,GAAsBV,EAAOnJ,EAAQlB,EAErE,EC/BH,IAAAkL,EAAe,CACbC,WCTa,WACb,MAAMjK,EAASzE,KACf,IAAI6J,EACAE,EACJ,MAAM3I,EAAKqD,EAAOrD,GAEhByI,OADiC,IAAxBpF,EAAOQ,OAAO4E,OAAiD,OAAxBpF,EAAOQ,OAAO4E,MACtDpF,EAAOQ,OAAO4E,MAEdzI,EAAGuN,YAGX5E,OADkC,IAAzBtF,EAAOQ,OAAO8E,QAAmD,OAAzBtF,EAAOQ,OAAO8E,OACtDtF,EAAOQ,OAAO8E,OAEd3I,EAAGwN,aAEC,IAAV/E,GAAepF,EAAOoK,gBAA+B,IAAX9E,GAAgBtF,EAAOqK,eAKtEjF,EACEA,EACAkF,SAASrH,EAAatG,EAAI,iBAAmB,EAAG,IAChD2N,SAASrH,EAAatG,EAAI,kBAAoB,EAAG,IACnD2I,EACEA,EACAgF,SAASrH,EAAatG,EAAI,gBAAkB,EAAG,IAC/C2N,SAASrH,EAAatG,EAAI,mBAAqB,EAAG,IAEhD6J,OAAO+D,MAAMnF,KAAQA,EAAQ,GAC7BoB,OAAO+D,MAAMjF,KAASA,EAAS,GAEnChN,OAAOkS,OAAOxK,EAAQ,CACpBoF,QACAE,SACAvB,KAAM/D,EAAOoK,eAAiBhF,EAAQE,IAEzC,ED3BCmF,aELa,WACb,MAAMzK,EAASzE,KACf,SAASmP,EAAkBC,GACzB,OAAI3K,EAAOoK,eACFO,EAGF,CACLvF,MAAS,SACT,aAAc,cACd,iBAAkB,eAClB,cAAe,aACf,eAAgB,gBAChB,eAAgB,cAChB,gBAAiB,iBACjBwF,YAAe,gBACfD,EACH,CACD,SAASE,EAA0BtM,EAAMuM,GACvC,OAAO9M,WAAWO,EAAK9C,iBAAiBiP,EAAkBI,KAAW,EACtE,CAED,MAAMtK,EAASR,EAAOQ,QAEhBE,UAAEA,EAAFqK,SAAaA,EAAUhH,KAAMiH,EAAYC,aAAcC,EAAvDC,SAA4DA,GAAanL,EACzEoL,EAAYpL,EAAOqL,SAAW7K,EAAO6K,QAAQC,QAC7CC,EAAuBH,EAAYpL,EAAOqL,QAAQjC,OAAOvQ,OAASmH,EAAOoJ,OAAOvQ,OAChFuQ,EAASxH,EAAgBmJ,EAAW,IAAG/K,EAAOQ,OAAOuI,4BACrDyC,EAAeJ,EAAYpL,EAAOqL,QAAQjC,OAAOvQ,OAASuQ,EAAOvQ,OACvE,IAAI4S,EAAW,GACf,MAAMC,EAAa,GACbC,EAAkB,GAExB,IAAIC,EAAepL,EAAOqL,mBACE,mBAAjBD,IACTA,EAAepL,EAAOqL,mBAAmBzN,KAAK4B,IAGhD,IAAI8L,EAActL,EAAOuL,kBACE,mBAAhBD,IACTA,EAActL,EAAOuL,kBAAkB3N,KAAK4B,IAG9C,MAAMgM,EAAyBhM,EAAOyL,SAAS5S,OACzCoT,EAA2BjM,EAAO0L,WAAW7S,OAEnD,IAAIqT,EAAe1L,EAAO0L,aACtBC,GAAiBP,EACjBQ,EAAgB,EAChBnE,EAAQ,EACZ,QAA0B,IAAf+C,EACT,OAE0B,iBAAjBkB,GAA6BA,EAAahN,QAAQ,MAAQ,IACnEgN,EAAgBlO,WAAWkO,EAAa1O,QAAQ,IAAK,KAAO,IAAOwN,GAGrEhL,EAAOqM,aAAeH,EAGtB9C,EAAOzQ,SAASiQ,IACVsC,EACFtC,EAAQ/O,MAAMyS,WAAa,GAE3B1D,EAAQ/O,MAAM+Q,YAAc,GAE9BhC,EAAQ/O,MAAM0S,aAAe,GAC7B3D,EAAQ/O,MAAM2S,UAAY,EAA1B,IAIEhM,EAAOiM,gBAAkBjM,EAAOkM,UAClChN,EAAegB,EAAW,kCAAmC,IAC7DhB,EAAegB,EAAW,iCAAkC,KAG9D,MAAMiM,EAAcnM,EAAOoM,MAAQpM,EAAOoM,KAAKC,KAAO,GAAK7M,EAAO4M,KAMlE,IAAIE,EALAH,GACF3M,EAAO4M,KAAKG,WAAWvB,GAMzB,MAAMwB,EACqB,SAAzBxM,EAAOiJ,eACPjJ,EAAOyM,aACP3U,OAAOI,KAAK8H,EAAOyM,aAAahO,QAAQrG,QACkB,IAA1C4H,EAAOyM,YAAYrU,GAAK6Q,gBACrC5Q,OAAS,EAEd,IAAK,IAAIiG,EAAI,EAAGA,EAAI0M,EAAc1M,GAAK,EAAG,CAExC,IAAIoO,EAKJ,GANAJ,EAAY,EAER1D,EAAOtK,KAAIoO,EAAQ9D,EAAOtK,IAC1B6N,GACF3M,EAAO4M,KAAKO,YAAYrO,EAAGoO,EAAO1B,EAAcd,IAE9CtB,EAAOtK,IAAyC,SAAnCmE,EAAaiK,EAAO,WAArC,CAEA,GAA6B,SAAzB1M,EAAOiJ,cAA0B,CAC/BuD,IACF5D,EAAOtK,GAAGjF,MAAM6Q,EAAkB,UAAa,IAEjD,MAAM0C,EAAc5R,iBAAiB0R,GAC/BG,EAAmBH,EAAMrT,MAAMsD,UAC/BmQ,EAAyBJ,EAAMrT,MAAMuD,gBAO3C,GANIiQ,IACFH,EAAMrT,MAAMsD,UAAY,QAEtBmQ,IACFJ,EAAMrT,MAAMuD,gBAAkB,QAE5BoD,EAAO+M,aACTT,EAAY9M,EAAOoK,eACftG,EAAiBoJ,EAAO,SAAS,GACjCpJ,EAAiBoJ,EAAO,UAAU,OACjC,CAEL,MAAM9H,EAAQyF,EAA0BuC,EAAa,SAC/CI,EAAc3C,EAA0BuC,EAAa,gBACrDK,EAAe5C,EAA0BuC,EAAa,iBACtDd,EAAazB,EAA0BuC,EAAa,eACpDxC,EAAcC,EAA0BuC,EAAa,gBACrDM,EAAYN,EAAY3R,iBAAiB,cAC/C,GAAIiS,GAA2B,eAAdA,EACfZ,EAAY1H,EAAQkH,EAAa1B,MAC5B,CACL,MAAMV,YAAEA,EAAFjG,YAAeA,GAAgBiJ,EACrCJ,EACE1H,EACAoI,EACAC,EACAnB,EACA1B,GACC3G,EAAciG,EAClB,CACF,CACGmD,IACFH,EAAMrT,MAAMsD,UAAYkQ,GAEtBC,IACFJ,EAAMrT,MAAMuD,gBAAkBkQ,GAE5B9M,EAAO+M,eAAcT,EAAY3L,KAAKwM,MAAMb,GACjD,MACCA,GAAa9B,GAAcxK,EAAOiJ,cAAgB,GAAKyC,GAAgB1L,EAAOiJ,cAC1EjJ,EAAO+M,eAAcT,EAAY3L,KAAKwM,MAAMb,IAE5C1D,EAAOtK,KACTsK,EAAOtK,GAAGjF,MAAM6Q,EAAkB,UAAa,GAAEoC,OAGjD1D,EAAOtK,KACTsK,EAAOtK,GAAG8O,gBAAkBd,GAE9BnB,EAAgBjI,KAAKoJ,GAEjBtM,EAAOiM,gBACTN,EAAgBA,EAAgBW,EAAY,EAAIV,EAAgB,EAAIF,EAC9C,IAAlBE,GAA6B,IAANtN,IACzBqN,EAAgBA,EAAgBnB,EAAa,EAAIkB,GACzC,IAANpN,IAASqN,EAAgBA,EAAgBnB,EAAa,EAAIkB,GAC1D/K,KAAK0M,IAAI1B,GAAiB,OAAUA,EAAgB,GACpD3L,EAAO+M,eAAcpB,EAAgBhL,KAAKwM,MAAMxB,IAChDlE,EAAQzH,EAAOsN,gBAAmB,GAAGrC,EAAS/H,KAAKyI,GACvDT,EAAWhI,KAAKyI,KAEZ3L,EAAO+M,eAAcpB,EAAgBhL,KAAKwM,MAAMxB,KAEjDlE,EAAQ9G,KAAKE,IAAIrB,EAAOQ,OAAOuN,mBAAoB9F,IAClDjI,EAAOQ,OAAOsN,gBAChB,GAEArC,EAAS/H,KAAKyI,GAChBT,EAAWhI,KAAKyI,GAChBA,EAAgBA,EAAgBW,EAAYZ,GAG9ClM,EAAOqM,aAAeS,EAAYZ,EAElCE,EAAgBU,EAEhB7E,GAAS,CArFmD,CAsF7D,CAgBD,GAdAjI,EAAOqM,YAAclL,KAAKC,IAAIpB,EAAOqM,YAAarB,GAAcc,EAE5DZ,GAAOC,IAA+B,UAAlB3K,EAAOwN,QAAwC,cAAlBxN,EAAOwN,UAC1DtN,EAAU7G,MAAMuL,MAAS,GAAEpF,EAAOqM,YAAc7L,EAAO0L,kBAErD1L,EAAOyN,iBACTvN,EAAU7G,MAAM6Q,EAAkB,UAAa,GAAE1K,EAAOqM,YAAc7L,EAAO0L,kBAG3ES,GACF3M,EAAO4M,KAAKsB,kBAAkBpB,EAAWrB,EAAUf,IAIhDlK,EAAOiM,eAAgB,CAC1B,MAAM0B,EAAgB,GACtB,IAAK,IAAIrP,EAAI,EAAGA,EAAI2M,EAAS5S,OAAQiG,GAAK,EAAG,CAC3C,IAAIsP,EAAiB3C,EAAS3M,GAC1B0B,EAAO+M,eAAca,EAAiBjN,KAAKwM,MAAMS,IACjD3C,EAAS3M,IAAMkB,EAAOqM,YAAcrB,GACtCmD,EAAczK,KAAK0K,EAEtB,CACD3C,EAAW0C,EAGThN,KAAKwM,MAAM3N,EAAOqM,YAAcrB,GAAc7J,KAAKwM,MAAMlC,EAASA,EAAS5S,OAAS,IACpF,GAEA4S,EAAS/H,KAAK1D,EAAOqM,YAAcrB,EAEtC,CACD,GAAII,GAAa5K,EAAO6N,KAAM,CAC5B,MAAMtK,EAAO4H,EAAgB,GAAKO,EAClC,GAAI1L,EAAOsN,eAAiB,EAAG,CAC7B,MAAMQ,EAASnN,KAAKwI,MACjB3J,EAAOqL,QAAQkD,aAAevO,EAAOqL,QAAQmD,aAAehO,EAAOsN,gBAEhEW,EAAY1K,EAAOvD,EAAOsN,eAChC,IAAK,IAAIhP,EAAI,EAAGA,EAAIwP,EAAQxP,GAAK,EAC/B2M,EAAS/H,KAAK+H,EAASA,EAAS5S,OAAS,GAAK4V,EAEjD,CACD,IAAK,IAAI3P,EAAI,EAAGA,EAAIkB,EAAOqL,QAAQkD,aAAevO,EAAOqL,QAAQmD,YAAa1P,GAAK,EACnD,IAA1B0B,EAAOsN,gBACTrC,EAAS/H,KAAK+H,EAASA,EAAS5S,OAAS,GAAKkL,GAEhD2H,EAAWhI,KAAKgI,EAAWA,EAAW7S,OAAS,GAAKkL,GACpD/D,EAAOqM,aAAetI,CAEzB,CAGD,GAFwB,IAApB0H,EAAS5S,SAAc4S,EAAW,CAAC,IAEX,IAAxBjL,EAAO0L,aAAoB,CAC7B,MAAMtT,EAAMoH,EAAOoK,gBAAkBc,EAAM,aAAeR,EAAkB,eAC5EtB,EACGnK,QAAO,CAACyP,EAAGC,MACLnO,EAAOkM,UAAWlM,EAAO6N,OAC1BM,IAAevF,EAAOvQ,OAAS,IAKpCF,SAASiQ,IACRA,EAAQ/O,MAAMjB,GAAQ,GAAEsT,KAAxB,GAEL,CAED,GAAI1L,EAAOiM,gBAAkBjM,EAAOoO,qBAAsB,CACxD,IAAIC,EAAgB,EACpBlD,EAAgBhT,SAASmW,IACvBD,GAAiBC,GAAkBtO,EAAO0L,aAAe1L,EAAO0L,aAAe,EAA/E,IAEF2C,GAAiBrO,EAAO0L,aACxB,MAAM6C,EAAUF,EAAgB7D,EAChCS,EAAWA,EAASnO,KAAK0R,GACnBA,EAAO,GAAWpD,EAClBoD,EAAOD,EAAgBA,EAAUjD,EAC9BkD,GAEV,CAED,GAAIxO,EAAOyO,yBAA0B,CACnC,IAAIJ,EAAgB,EAKpB,GAJAlD,EAAgBhT,SAASmW,IACvBD,GAAiBC,GAAkBtO,EAAO0L,aAAe1L,EAAO0L,aAAe,EAA/E,IAEF2C,GAAiBrO,EAAO0L,aACpB2C,EAAgB7D,EAAY,CAC9B,MAAMkE,GAAmBlE,EAAa6D,GAAiB,EACvDpD,EAAS9S,SAAQ,CAACqW,EAAMG,KACtB1D,EAAS0D,GAAaH,EAAOE,CAA7B,IAEFxD,EAAW/S,SAAQ,CAACqW,EAAMG,KACxBzD,EAAWyD,GAAaH,EAAOE,CAA/B,GAEH,CACF,CASD,GAPA5W,OAAOkS,OAAOxK,EAAQ,CACpBoJ,SACAqC,WACAC,aACAC,oBAGEnL,EAAOiM,gBAAkBjM,EAAOkM,UAAYlM,EAAOoO,qBAAsB,CAC3ElP,EAAegB,EAAW,mCAAuC+K,EAAS,GAAZ,MAC9D/L,EACEgB,EACA,iCACGV,EAAO+D,KAAO,EAAI4H,EAAgBA,EAAgB9S,OAAS,GAAK,EAAlE,MAEH,MAAMuW,GAAiBpP,EAAOyL,SAAS,GACjC4D,GAAmBrP,EAAO0L,WAAW,GAC3C1L,EAAOyL,SAAWzL,EAAOyL,SAASnO,KAAKgS,GAAMA,EAAIF,IACjDpP,EAAO0L,WAAa1L,EAAO0L,WAAWpO,KAAKgS,GAAMA,EAAID,GACtD,CAiBD,GAfI7D,IAAiBD,GACnBvL,EAAOoI,KAAK,sBAEVqD,EAAS5S,SAAWmT,IAClBhM,EAAOQ,OAAO+O,eAAevP,EAAOwP,gBACxCxP,EAAOoI,KAAK,yBAEVsD,EAAW7S,SAAWoT,GACxBjM,EAAOoI,KAAK,0BAGV5H,EAAOiP,qBACTzP,EAAO0P,uBAGJtE,GAAc5K,EAAOkM,SAA8B,UAAlBlM,EAAOwN,QAAwC,SAAlBxN,EAAOwN,QAAoB,CAC5F,MAAM2B,EAAuB,GAAEnP,EAAOoP,wCAChCC,EAA6B7P,EAAOrD,GAAGuF,UAAU4N,SAASH,GAC5DnE,GAAgBhL,EAAOuP,wBACpBF,GAA4B7P,EAAOrD,GAAGuF,UAAUC,IAAIwN,GAChDE,GACT7P,EAAOrD,GAAGuF,UAAUgH,OAAOyG,EAE9B,CACF,EFnUCK,iBGba,SAA0BvP,GACvC,MAAMT,EAASzE,KACT0U,EAAe,GACf7E,EAAYpL,EAAOqL,SAAWrL,EAAOQ,OAAO6K,QAAQC,QAC1D,IACIxM,EADAoR,EAAY,EAEK,iBAAVzP,EACTT,EAAOmQ,cAAc1P,IACF,IAAVA,GACTT,EAAOmQ,cAAcnQ,EAAOQ,OAAOC,OAGrC,MAAM2P,EAAmBnI,GACnBmD,EACKpL,EAAOoJ,OAAOpJ,EAAOqQ,oBAAoBpI,IAE3CjI,EAAOoJ,OAAOnB,GAGvB,GAAoC,SAAhCjI,EAAOQ,OAAOiJ,eAA4BzJ,EAAOQ,OAAOiJ,cAAgB,EAC1E,GAAIzJ,EAAOQ,OAAOiM,gBACfzM,EAAOsQ,eAAiB,IAAI3X,SAASuU,IACpC+C,EAAavM,KAAKwJ,EAAlB,SAGF,IAAKpO,EAAI,EAAGA,EAAIqC,KAAKwI,KAAK3J,EAAOQ,OAAOiJ,eAAgB3K,GAAK,EAAG,CAC9D,MAAMmJ,EAAQjI,EAAO4J,YAAc9K,EACnC,GAAImJ,EAAQjI,EAAOoJ,OAAOvQ,SAAWuS,EAAW,MAChD6E,EAAavM,KAAK0M,EAAgBnI,GACnC,MAGHgI,EAAavM,KAAK0M,EAAgBpQ,EAAO4J,cAI3C,IAAK9K,EAAI,EAAGA,EAAImR,EAAapX,OAAQiG,GAAK,EACxC,QAA+B,IAApBmR,EAAanR,GAAoB,CAC1C,MAAMwG,EAAS2K,EAAanR,GAAGyR,aAC/BL,EAAY5K,EAAS4K,EAAY5K,EAAS4K,CAC3C,EAICA,GAA2B,IAAdA,KAAiBlQ,EAAOU,UAAU7G,MAAMyL,OAAU,GAAE4K,MACtE,EH/BCR,mBIda,WACb,MAAM1P,EAASzE,KACT6N,EAASpJ,EAAOoJ,OAEhBoH,EAAcxQ,EAAO8I,UACvB9I,EAAOoK,eACLpK,EAAOU,UAAU+P,WACjBzQ,EAAOU,UAAUgQ,UACnB,EACJ,IAAK,IAAI5R,EAAI,EAAGA,EAAIsK,EAAOvQ,OAAQiG,GAAK,EACtCsK,EAAOtK,GAAG6R,mBACP3Q,EAAOoK,eAAiBhB,EAAOtK,GAAG2R,WAAarH,EAAOtK,GAAG4R,WAC1DF,EACAxQ,EAAO4Q,uBAEZ,EJACC,qBKfa,SAA8BzQ,QAA2C,IAA3CA,MAAa7E,MAAQA,KAAK6E,WAAc,GACnF,MAAMJ,EAASzE,KACTiF,EAASR,EAAOQ,QAEhB4I,OAAEA,EAAQ6B,aAAcC,EAAxBO,SAA6BA,GAAazL,EAEhD,GAAsB,IAAlBoJ,EAAOvQ,OAAc,YACkB,IAAhCuQ,EAAO,GAAGuH,mBAAmC3Q,EAAO0P,qBAE/D,IAAIoB,GAAgB1Q,EAChB8K,IAAK4F,EAAe1Q,GAGxBgJ,EAAOzQ,SAASiQ,IACdA,EAAQ1G,UAAUgH,OAAO1I,EAAOuQ,kBAAhC,IAGF/Q,EAAOgR,qBAAuB,GAC9BhR,EAAOsQ,cAAgB,GAEvB,IAAK,IAAIxR,EAAI,EAAGA,EAAIsK,EAAOvQ,OAAQiG,GAAK,EAAG,CACzC,MAAMoO,EAAQ9D,EAAOtK,GACrB,IAAImS,EAAc/D,EAAMyD,kBACpBnQ,EAAOkM,SAAWlM,EAAOiM,iBAC3BwE,GAAe7H,EAAO,GAAGuH,mBAG3B,MAAMO,GACHJ,GAAgBtQ,EAAOiM,eAAiBzM,EAAOmR,eAAiB,GAAKF,IACrE/D,EAAMU,gBAAkBpN,EAAO0L,cAC5BkF,GACHN,EACCrF,EAAS,IACRjL,EAAOiM,eAAiBzM,EAAOmR,eAAiB,GACjDF,IACD/D,EAAMU,gBAAkBpN,EAAO0L,cAC5BmF,IAAgBP,EAAeG,GAC/BK,EAAaD,EAAcrR,EAAO2L,gBAAgB7M,IAErDuS,GAAe,GAAKA,EAAcrR,EAAO+D,KAAO,GAChDuN,EAAa,GAAKA,GAActR,EAAO+D,MACvCsN,GAAe,GAAKC,GAActR,EAAO+D,QAE1C/D,EAAOsQ,cAAc5M,KAAKwJ,GAC1BlN,EAAOgR,qBAAqBtN,KAAK5E,GACjCsK,EAAOtK,GAAGoD,UAAUC,IAAI3B,EAAOuQ,oBAEjC7D,EAAMhM,SAAWgK,GAAOgG,EAAgBA,EACxChE,EAAMqE,iBAAmBrG,GAAOkG,EAAwBA,CACzD,CACF,ELlCCI,eMhBa,SAAwBpR,GACrC,MAAMJ,EAASzE,KACf,QAAyB,IAAd6E,EAA2B,CACpC,MAAMqR,EAAazR,EAAOiL,cAAgB,EAAI,EAE9C7K,EAAaJ,GAAUA,EAAOI,WAAaJ,EAAOI,UAAYqR,GAAe,CAC9E,CACD,MAAMjR,EAASR,EAAOQ,OAChBkR,EAAiB1R,EAAO2R,eAAiB3R,EAAOmR,eACtD,IAAIjQ,SAAEA,EAAF0Q,YAAYA,EAAZC,MAAyBA,EAAzBC,aAAgCA,GAAiB9R,EACrD,MAAM+R,EAAeH,EACfI,EAASH,EACf,GAAuB,IAAnBH,EACFxQ,EAAW,EACX0Q,GAAc,EACdC,GAAQ,MACH,CACL3Q,GAAYd,EAAYJ,EAAOmR,gBAAkBO,EACjD,MAAMO,EAAqB9Q,KAAK0M,IAAIzN,EAAYJ,EAAOmR,gBAAkB,EACnEe,EAAe/Q,KAAK0M,IAAIzN,EAAYJ,EAAO2R,gBAAkB,EACnEC,EAAcK,GAAsB/Q,GAAY,EAChD2Q,EAAQK,GAAgBhR,GAAY,EAChC+Q,IAAoB/Q,EAAW,GAC/BgR,IAAchR,EAAW,EAC9B,CAED,GAAIV,EAAO6N,KAAM,CACf,MAAM8D,EAAkBnS,EAAOqQ,oBAAoB,GAC7C+B,EAAiBpS,EAAOqQ,oBAAoBrQ,EAAOoJ,OAAOvQ,OAAS,GACnEwZ,EAAsBrS,EAAO0L,WAAWyG,GACxCG,EAAqBtS,EAAO0L,WAAW0G,GACvCG,EAAevS,EAAO0L,WAAW1L,EAAO0L,WAAW7S,OAAS,GAC5D2Z,EAAerR,KAAK0M,IAAIzN,GAE5B0R,EADEU,GAAgBH,GACFG,EAAeH,GAAuBE,GAEtCC,EAAeD,EAAeD,GAAsBC,EAElET,EAAe,IAAGA,GAAgB,EACvC,CAEDxZ,OAAOkS,OAAOxK,EAAQ,CACpBkB,WACA4Q,eACAF,cACAC,WAGErR,EAAOiP,qBAAwBjP,EAAOiM,gBAAkBjM,EAAOiS,aACjEzS,EAAO6Q,qBAAqBzQ,GAE1BwR,IAAgBG,GAClB/R,EAAOoI,KAAK,yBAEVyJ,IAAUG,GACZhS,EAAOoI,KAAK,oBAET2J,IAAiBH,GAAiBI,IAAWH,IAChD7R,EAAOoI,KAAK,YAGdpI,EAAOoI,KAAK,WAAYlH,EACzB,EN7CCwR,oBOfa,WACb,MAAM1S,EAASzE,MAET6N,OAAEA,EAAF5I,OAAUA,EAAVuK,SAAkBA,EAAlBnB,YAA4BA,GAAgB5J,EAC5CoL,EAAYpL,EAAOqL,SAAW7K,EAAO6K,QAAQC,QAE7CqH,EAAoB7Q,GACjBF,EACLmJ,EACC,IAAGvK,EAAOuI,aAAajH,kBAAyBA,KACjD,GAMJ,IAAI8Q,EACJ,GALAxJ,EAAOzQ,SAASiQ,IACdA,EAAQ1G,UAAUgH,OAAO1I,EAAOqS,iBAAkBrS,EAAOsS,eAAgBtS,EAAOuS,eAAhF,IAIE3H,EACF,GAAI5K,EAAO6N,KAAM,CACf,IAAIM,EAAa/E,EAAc5J,EAAOqL,QAAQkD,aAC1CI,EAAa,IAAGA,EAAa3O,EAAOqL,QAAQjC,OAAOvQ,OAAS8V,GAC5DA,GAAc3O,EAAOqL,QAAQjC,OAAOvQ,SAAQ8V,GAAc3O,EAAOqL,QAAQjC,OAAOvQ,QACpF+Z,EAAcD,EAAkB,6BAA4BhE,MAC7D,MACCiE,EAAcD,EAAkB,6BAA4B/I,YAG9DgJ,EAAcxJ,EAAOQ,GAGvB,GAAIgJ,EAAa,CAEfA,EAAY1Q,UAAUC,IAAI3B,EAAOqS,kBAGjC,IAAIG,EbwMR,SAAwBrW,EAAImF,GAC1B,MAAMmR,EAAU,GAChB,KAAOtW,EAAGuW,oBAAoB,CAC5B,MAAMC,EAAOxW,EAAGuW,mBACZpR,EACEqR,EAAKpR,QAAQD,IAAWmR,EAAQvP,KAAKyP,GACpCF,EAAQvP,KAAKyP,GACpBxW,EAAKwW,CACN,CACD,OAAOF,CACR,CalNmBG,CAAeR,EAAc,IAAGpS,EAAOuI,4BAA4B,GAC/EvI,EAAO6N,OAAS2E,IAClBA,EAAY5J,EAAO,IAEjB4J,GACFA,EAAU9Q,UAAUC,IAAI3B,EAAOsS,gBAGjC,IAAIO,EbqLR,SAAwB1W,EAAImF,GAC1B,MAAMwR,EAAU,GAChB,KAAO3W,EAAG4W,wBAAwB,CAChC,MAAMC,EAAO7W,EAAG4W,uBACZzR,EACE0R,EAAKzR,QAAQD,IAAWwR,EAAQ5P,KAAK8P,GACpCF,EAAQ5P,KAAK8P,GACpB7W,EAAK6W,CACN,CACD,OAAOF,CACR,Ca/LmBG,CAAeb,EAAc,IAAGpS,EAAOuI,4BAA4B,GAC/EvI,EAAO6N,MAAuB,KAAdgF,IAClBA,EAAYjK,EAAOA,EAAOvQ,OAAS,IAEjCwa,GACFA,EAAUnR,UAAUC,IAAI3B,EAAOuS,eAElC,CAED/S,EAAO0T,mBACR,EPrCCC,kBQQa,SAA2BC,GACxC,MAAM5T,EAASzE,KACT6E,EAAYJ,EAAOiL,aAAejL,EAAOI,WAAaJ,EAAOI,WAC7DqL,SACJA,EADIjL,OAEJA,EACAoJ,YAAaiK,EACb9J,UAAW+J,EACX3E,UAAW4E,GACT/T,EACJ,IACImP,EADAvF,EAAcgK,EAGlB,MAAMI,EAAuBC,IAC3B,IAAIlK,EAAYkK,EAASjU,EAAOqL,QAAQkD,aAOxC,OANIxE,EAAY,IACdA,EAAY/J,EAAOqL,QAAQjC,OAAOvQ,OAASkR,GAEzCA,GAAa/J,EAAOqL,QAAQjC,OAAOvQ,SACrCkR,GAAa/J,EAAOqL,QAAQjC,OAAOvQ,QAE9BkR,CAAP,EAKF,QAH2B,IAAhBH,IACTA,EAhDG,SAAmC5J,GACxC,MAAM0L,WAAEA,EAAFlL,OAAcA,GAAWR,EACzBI,EAAYJ,EAAOiL,aAAejL,EAAOI,WAAaJ,EAAOI,UACnE,IAAIwJ,EACJ,IAAK,IAAI9K,EAAI,EAAGA,EAAI4M,EAAW7S,OAAQiG,GAAK,OACT,IAAtB4M,EAAW5M,EAAI,GAEtBsB,GAAasL,EAAW5M,IACxBsB,EAAYsL,EAAW5M,EAAI,IAAM4M,EAAW5M,EAAI,GAAK4M,EAAW5M,IAAM,EAEtE8K,EAAc9K,EACLsB,GAAasL,EAAW5M,IAAMsB,EAAYsL,EAAW5M,EAAI,KAClE8K,EAAc9K,EAAI,GAEXsB,GAAasL,EAAW5M,KACjC8K,EAAc9K,GAOlB,OAHI0B,EAAO0T,sBACLtK,EAAc,QAA4B,IAAhBA,KAA6BA,EAAc,GAEpEA,CACR,CAyBiBuK,CAA0BnU,IAEtCyL,EAASvM,QAAQkB,IAAc,EACjC+O,EAAY1D,EAASvM,QAAQkB,OACxB,CACL,MAAMgU,EAAOjT,KAAKE,IAAIb,EAAOuN,mBAAoBnE,GACjDuF,EAAYiF,EAAOjT,KAAKwM,OAAO/D,EAAcwK,GAAQ5T,EAAOsN,eAC7D,CAED,GADIqB,GAAa1D,EAAS5S,SAAQsW,EAAY1D,EAAS5S,OAAS,GAC5D+Q,IAAgBiK,EAQlB,OAPI1E,IAAc4E,IAChB/T,EAAOmP,UAAYA,EACnBnP,EAAOoI,KAAK,yBAEVpI,EAAOQ,OAAO6N,MAAQrO,EAAOqL,SAAWrL,EAAOQ,OAAO6K,QAAQC,UAChEtL,EAAO+J,UAAYiK,EAAoBpK,KAK3C,IAAIG,EAEFA,EADE/J,EAAOqL,SAAW7K,EAAO6K,QAAQC,SAAW9K,EAAO6N,KACzC2F,EAAoBpK,GACvB5J,EAAOoJ,OAAOQ,GACXU,SACVtK,EAAOoJ,OAAOQ,GAAayK,aAAa,4BAA8BzK,EACtE,IAGUA,EAGdtR,OAAOkS,OAAOxK,EAAQ,CACpB+T,oBACA5E,YACA2E,oBACA/J,YACA8J,gBACAjK,gBAGE5J,EAAOsU,aACThL,EAAQtJ,GAEVA,EAAOoI,KAAK,qBACZpI,EAAOoI,KAAK,mBACR0L,IAAsB/J,GACxB/J,EAAOoI,KAAK,oBAEVpI,EAAOsU,aAAetU,EAAOQ,OAAO+T,qBACtCvU,EAAOoI,KAAK,cAEf,ERnFCoM,mBSnBa,SAA4B3Q,GACzC,MAAM7D,EAASzE,KACTiF,EAASR,EAAOQ,OAChB0M,EAAQrJ,EAAEgF,QAAS,IAAGrI,EAAOuI,4BACnC,IACI4F,EADA8F,GAAa,EAGjB,GAAIvH,EACF,IAAK,IAAIpO,EAAI,EAAGA,EAAIkB,EAAOoJ,OAAOvQ,OAAQiG,GAAK,EAC7C,GAAIkB,EAAOoJ,OAAOtK,KAAOoO,EAAO,CAC9BuH,GAAa,EACb9F,EAAa7P,EACb,KACD,CAIL,IAAIoO,IAASuH,EAUX,OAFAzU,EAAO0U,kBAAe9V,OACtBoB,EAAO2U,kBAAe/V,GARtBoB,EAAO0U,aAAexH,EAClBlN,EAAOqL,SAAWrL,EAAOQ,OAAO6K,QAAQC,QAC1CtL,EAAO2U,aAAerK,SAAS4C,EAAMmH,aAAa,2BAA4B,IAE9ErU,EAAO2U,aAAehG,EAQxBnO,EAAOoU,0BACiBhW,IAAxBoB,EAAO2U,cACP3U,EAAO2U,eAAiB3U,EAAO4J,aAE/B5J,EAAO4U,qBAEV,GC9BD,IAAAxU,EAAe,C,aCJA,SAA4BxD,QAAwC,IAAxCA,MAAOrB,KAAK6O,eAAiB,IAAM,KAC5E,MAEM5J,OAAEA,EAAQyK,aAAcC,EAAxB9K,UAA6BA,EAA7BM,UAAwCA,GAF/BnF,KAIf,GAAIiF,EAAOqU,iBACT,OAAO3J,GAAO9K,EAAYA,EAE5B,GAAII,EAAOkM,QACT,OAAOtM,EAGT,IAAI0U,EAAmBpY,EAAagE,EAAW9D,GAI/C,OAHAkY,GAZevZ,KAYYqV,wBACvB1F,IAAK4J,GAAoBA,GAEtBA,GAAoB,CAC5B,EDXCC,aERa,SAAsB3U,EAAW4U,GAC9C,MAAMhV,EAASzE,MACP0P,aAAcC,EAAhB1K,OAAqBA,EAArBE,UAA6BA,EAA7BQ,SAAwCA,GAAalB,EAC3D,IA8BIiV,EA9BAC,EAAI,EACJC,EAAI,EAGJnV,EAAOoK,eACT8K,EAAIhK,GAAO9K,EAAYA,EAEvB+U,EAAI/U,EAGFI,EAAO+M,eACT2H,EAAI/T,KAAKwM,MAAMuH,GACfC,EAAIhU,KAAKwM,MAAMwH,IAGjBnV,EAAOoV,kBAAoBpV,EAAOI,UAClCJ,EAAOI,UAAYJ,EAAOoK,eAAiB8K,EAAIC,EAE3C3U,EAAOkM,QACThM,EAAUV,EAAOoK,eAAiB,aAAe,aAAepK,EAAOoK,gBAAkB8K,GAAKC,EACpF3U,EAAOqU,mBACb7U,EAAOoK,eACT8K,GAAKlV,EAAO4Q,wBAEZuE,GAAKnV,EAAO4Q,wBAEdlQ,EAAU7G,MAAMsD,UAAa,eAAc+X,QAAQC,aAKrD,MAAMzD,EAAiB1R,EAAO2R,eAAiB3R,EAAOmR,eAEpD8D,EADqB,IAAnBvD,EACY,GAECtR,EAAYJ,EAAOmR,gBAAkBO,EAElDuD,IAAgB/T,GAClBlB,EAAOwR,eAAepR,GAGxBJ,EAAOoI,KAAK,eAAgBpI,EAAOI,UAAW4U,EAC/C,EFpCC7D,aGTa,WACb,OAAQ5V,KAAKkQ,SAAS,EACvB,EHQCkG,aIVa,WACb,OAAQpW,KAAKkQ,SAASlQ,KAAKkQ,SAAS5S,OAAS,EAC9C,EJSCwc,YKTa,SACbjV,EACAK,EACA6U,EACAC,EACAC,QACA,IALApV,MAAY,QAKZ,IAJAK,MAAQlF,KAAKiF,OAAOC,YAIpB,IAHA6U,OAAe,QAGf,IAFAC,OAAkB,GAGlB,MAAMvV,EAASzE,MAETiF,OAAEA,EAAFE,UAAUA,GAAcV,EAE9B,GAAIA,EAAOyV,WAAajV,EAAOkV,+BAC7B,OAAO,EAGT,MAAMvE,EAAenR,EAAOmR,eACtBQ,EAAe3R,EAAO2R,eAC5B,IAAIgE,EAQJ,GAPiDA,EAA7CJ,GAAmBnV,EAAY+Q,EAA6BA,EACvDoE,GAAmBnV,EAAYuR,EAA6BA,EACjDvR,EAGpBJ,EAAOwR,eAAemE,GAElBnV,EAAOkM,QAAS,CAClB,MAAMkJ,EAAM5V,EAAOoK,eACnB,GAAc,IAAV3J,EACFC,EAAUkV,EAAM,aAAe,cAAgBD,MAC1C,CACL,IAAK3V,EAAOkE,QAAQI,aAElB,OADAxE,EAAqB,CAAEE,SAAQC,gBAAiB0V,EAAczV,KAAM0V,EAAM,OAAS,SAC5E,EAETlV,EAAUgB,SAAS,CACjB,CAACkU,EAAM,OAAS,QAASD,EACzBE,SAAU,UAEb,CACD,OAAO,CACR,CAqCD,OAnCc,IAAVpV,GACFT,EAAOmQ,cAAc,GACrBnQ,EAAO+U,aAAaY,GAChBL,IACFtV,EAAOoI,KAAK,wBAAyB3H,EAAO+U,GAC5CxV,EAAOoI,KAAK,oBAGdpI,EAAOmQ,cAAc1P,GACrBT,EAAO+U,aAAaY,GAChBL,IACFtV,EAAOoI,KAAK,wBAAyB3H,EAAO+U,GAC5CxV,EAAOoI,KAAK,oBAETpI,EAAOyV,YACVzV,EAAOyV,WAAY,EACdzV,EAAO8V,oCACV9V,EAAO8V,kCAAoC,SAAuBjS,GAC3D7D,IAAUA,EAAOmH,WAClBtD,EAAErL,SAAW+C,OACjByE,EAAOU,UAAUzH,oBACf,gBACA+G,EAAO8V,mCAET9V,EAAO8V,kCAAoC,YACpC9V,EAAO8V,kCACVR,GACFtV,EAAOoI,KAAK,iB,GAIlBpI,EAAOU,UAAU1H,iBAAiB,gBAAiBgH,EAAO8V,sCAIvD,CACR,GChFc,SAASC,EAA0DhW,GAAA,IAA3CC,OAAEA,EAAFsV,aAAUA,EAAVU,UAAwBA,EAAxBC,KAAmCA,GAAQlW,EAChF,MAAM6J,YAAEA,EAAFiK,cAAeA,GAAkB7T,EACvC,IAAIa,EAAMmV,EASV,GARKnV,IAC8BA,EAA7B+I,EAAciK,EAAqB,OAC9BjK,EAAciK,EAAqB,OACjC,SAGb7T,EAAOoI,KAAM,aAAY6N,KAErBX,GAAgB1L,IAAgBiK,EAAe,CACjD,GAAY,UAARhT,EAEF,YADAb,EAAOoI,KAAM,uBAAsB6N,KAGrCjW,EAAOoI,KAAM,wBAAuB6N,KACxB,SAARpV,EACFb,EAAOoI,KAAM,sBAAqB6N,KAElCjW,EAAOoI,KAAM,sBAAqB6N,IAErC,CACF,CCfD,IAAA/I,EAAe,CACbgJ,QCPa,SACbjO,EACAxH,EACA6U,EACAE,EACAW,QACA,IALAlO,MAAQ,QAKR,IAJAxH,MAAQlF,KAAKiF,OAAOC,YAIpB,IAHA6U,OAAe,GAIM,iBAAVrN,IACTA,EAAQqC,SAASrC,EAAO,KAG1B,MAAMjI,EAASzE,KACf,IAAIoT,EAAa1G,EACb0G,EAAa,IAAGA,EAAa,GAEjC,MAAMnO,OACJA,EADIiL,SAEJA,EAFIC,WAGJA,EAHImI,cAIJA,EAJIjK,YAKJA,EACAqB,aAAcC,EANVxK,UAOJA,EAPI4K,QAQJA,GACEtL,EAEJ,GACGA,EAAOyV,WAAajV,EAAOkV,iCAC1BpK,IAAYkK,IAAaW,EAE3B,OAAO,EAGT,MAAM/B,EAAOjT,KAAKE,IAAIrB,EAAOQ,OAAOuN,mBAAoBY,GACxD,IAAIQ,EAAYiF,EAAOjT,KAAKwM,OAAOgB,EAAayF,GAAQpU,EAAOQ,OAAOsN,gBAClEqB,GAAa1D,EAAS5S,SAAQsW,EAAY1D,EAAS5S,OAAS,GAEhE,MAAMuH,GAAaqL,EAAS0D,GAE5B,GAAI3O,EAAO0T,oBACT,IAAK,IAAIpV,EAAI,EAAGA,EAAI4M,EAAW7S,OAAQiG,GAAK,EAAG,CAC7C,MAAMsX,GAAuBjV,KAAKwM,MAAkB,IAAZvN,GAClCiW,EAAiBlV,KAAKwM,MAAsB,IAAhBjC,EAAW5M,IACvCwX,EAAqBnV,KAAKwM,MAA0B,IAApBjC,EAAW5M,EAAI,SACpB,IAAtB4M,EAAW5M,EAAI,GAEtBsX,GAAuBC,GACvBD,EAAsBE,GAAsBA,EAAqBD,GAAkB,EAEnF1H,EAAa7P,EAEbsX,GAAuBC,GACvBD,EAAsBE,IAEtB3H,EAAa7P,EAAI,GAEVsX,GAAuBC,IAChC1H,EAAa7P,EAEhB,CAGH,GAAIkB,EAAOsU,aAAe3F,IAAe/E,EAAa,CACpD,IACG5J,EAAOuW,gBACRnW,EAAYJ,EAAOI,WACnBA,EAAYJ,EAAOmR,eAEnB,OAAO,EAET,IACGnR,EAAOwW,gBACRpW,EAAYJ,EAAOI,WACnBA,EAAYJ,EAAO2R,iBAEd/H,GAAe,KAAO+E,EACzB,OAAO,CAGZ,CASD,IAAIqH,EAMJ,GAbIrH,KAAgBkF,GAAiB,IAAMyB,GACzCtV,EAAOoI,KAAK,0BAIdpI,EAAOwR,eAAepR,GAGQ4V,EAA1BrH,EAAa/E,EAAyB,OACjC+E,EAAa/E,EAAyB,OAC9B,QAGZsB,IAAQ9K,IAAcJ,EAAOI,YAAgB8K,GAAO9K,IAAcJ,EAAOI,UAc5E,OAbAJ,EAAO2T,kBAAkBhF,GAErBnO,EAAOiS,YACTzS,EAAOgQ,mBAEThQ,EAAO0S,sBACe,UAAlBlS,EAAOwN,QACThO,EAAO+U,aAAa3U,GAEJ,UAAd4V,IACFhW,EAAOyW,gBAAgBnB,EAAcU,GACrChW,EAAO0W,cAAcpB,EAAcU,KAE9B,EAET,GAAIxV,EAAOkM,QAAS,CAClB,MAAMkJ,EAAM5V,EAAOoK,eACbuM,EAAIzL,EAAM9K,GAAaA,EAC7B,GAAc,IAAVK,EAAa,CACf,MAAM2K,EAAYpL,EAAOqL,SAAWrL,EAAOQ,OAAO6K,QAAQC,QACtDF,IACFpL,EAAOU,UAAU7G,MAAM8G,eAAiB,OACxCX,EAAO4W,mBAAoB,GAGzBxL,IAAcpL,EAAO6W,2BAA6B7W,EAAOQ,OAAOsW,aAAe,GACjF9W,EAAO6W,2BAA4B,EACnC7a,uBAAsB,KACpB0E,EAAUkV,EAAM,aAAe,aAAee,CAA9C,KAGFjW,EAAUkV,EAAM,aAAe,aAAee,EAE5CvL,GACFpP,uBAAsB,KACpBgE,EAAOU,UAAU7G,MAAM8G,eAAiB,GACxCX,EAAO4W,mBAAoB,CAA3B,GAGL,KAAM,CACL,IAAK5W,EAAOkE,QAAQI,aAElB,OADAxE,EAAqB,CAAEE,SAAQC,eAAgB0W,EAAGzW,KAAM0V,EAAM,OAAS,SAChE,EAETlV,EAAUgB,SAAS,CACjB,CAACkU,EAAM,OAAS,OAAQe,EACxBd,SAAU,UAEb,CACD,OAAO,CACR,CAyBD,OAxBA7V,EAAOmQ,cAAc1P,GACrBT,EAAO+U,aAAa3U,GACpBJ,EAAO2T,kBAAkBhF,GACzB3O,EAAO0S,sBACP1S,EAAOoI,KAAK,wBAAyB3H,EAAO+U,GAC5CxV,EAAOyW,gBAAgBnB,EAAcU,GAEvB,IAAVvV,EACFT,EAAO0W,cAAcpB,EAAcU,GACzBhW,EAAOyV,YACjBzV,EAAOyV,WAAY,EACdzV,EAAO+W,gCACV/W,EAAO+W,8BAAgC,SAAuBlT,GACvD7D,IAAUA,EAAOmH,WAClBtD,EAAErL,SAAW+C,OACjByE,EAAOU,UAAUzH,oBAAoB,gBAAiB+G,EAAO+W,+BAC7D/W,EAAO+W,8BAAgC,YAChC/W,EAAO+W,8BACd/W,EAAO0W,cAAcpB,EAAcU,G,GAGvChW,EAAOU,UAAU1H,iBAAiB,gBAAiBgH,EAAO+W,iCAGrD,CACR,EDnKCC,YEVa,SACb/O,EACAxH,EACA6U,EACAE,GAEA,QADA,IAJAvN,MAAQ,QAIR,IAHAxH,MAAQlF,KAAKiF,OAAOC,YAGpB,IAFA6U,OAAe,GAGM,iBAAVrN,EAAoB,CAG7BA,EAFsBqC,SAASrC,EAAO,GAGvC,CAED,MAAMjI,EAASzE,KACf,IAAI0b,EAAWhP,EAUf,OATIjI,EAAOQ,OAAO6N,OACZrO,EAAOqL,SAAWrL,EAAOQ,OAAO6K,QAAQC,QAE1C2L,GAAsBjX,EAAOqL,QAAQkD,aAErC0I,EAAWjX,EAAOqQ,oBAAoB4G,IAInCjX,EAAOkW,QAAQe,EAAUxW,EAAO6U,EAAcE,EACtD,EFbC0B,UGVa,SAAmBzW,EAA2B6U,EAAqBE,QAAU,IAA1D/U,MAAQlF,KAAKiF,OAAOC,YAAsC,IAA/B6U,OAAe,GAC1E,MAAMtV,EAASzE,MACT+P,QAAEA,EAAF9K,OAAWA,EAAXiV,UAAmBA,GAAczV,EACvC,IAAKsL,EAAS,OAAOtL,EACrB,IAAImX,EAAW3W,EAAOsN,eACO,SAAzBtN,EAAOiJ,eAAsD,IAA1BjJ,EAAOsN,gBAAwBtN,EAAO4W,qBAC3ED,EAAWhW,KAAKC,IAAIpB,EAAO0J,qBAAqB,WAAW,GAAO,IAEpE,MAAM2N,EAAYrX,EAAO4J,YAAcpJ,EAAOuN,mBAAqB,EAAIoJ,EACjE/L,EAAYpL,EAAOqL,SAAW7K,EAAO6K,QAAQC,QACnD,GAAI9K,EAAO6N,KAAM,CACf,GAAIoH,IAAcrK,GAAa5K,EAAO8W,oBAAqB,OAAO,EAClEtX,EAAOuX,QAAQ,CAAEvB,UAAW,SAE5BhW,EAAOwX,YAAcxX,EAAOU,UAAUgC,UACvC,CACD,OAAIlC,EAAOsJ,QAAU9J,EAAO6R,MACnB7R,EAAOkW,QAAQ,EAAGzV,EAAO6U,EAAcE,GAEzCxV,EAAOkW,QAAQlW,EAAO4J,YAAcyN,EAAW5W,EAAO6U,EAAcE,EAC5E,EHTCiC,UIXa,SAAmBhX,EAA2B6U,EAAqBE,QAAU,IAA1D/U,MAAQlF,KAAKiF,OAAOC,YAAsC,IAA/B6U,OAAe,GAC1E,MAAMtV,EAASzE,MACTiF,OAAEA,EAAFiL,SAAUA,EAAVC,WAAoBA,EAApBT,aAAgCA,EAAhCK,QAA8CA,EAA9CmK,UAAuDA,GAAczV,EAC3E,IAAKsL,EAAS,OAAOtL,EACrB,MAAMoL,EAAYpL,EAAOqL,SAAW7K,EAAO6K,QAAQC,QAEnD,GAAI9K,EAAO6N,KAAM,CACf,GAAIoH,IAAcrK,GAAa5K,EAAO8W,oBAAqB,OAAO,EAElEtX,EAAOuX,QAAQ,CAAEvB,UAAW,SAE5BhW,EAAOwX,YAAcxX,EAAOU,UAAUgC,UACvC,CAGD,SAASgV,EAAUC,GACjB,OAAIA,EAAM,GAAWxW,KAAKwM,MAAMxM,KAAK0M,IAAI8J,IAClCxW,KAAKwM,MAAMgK,EACnB,CACD,MAAMvB,EAAsBsB,EANVzM,EAAejL,EAAOI,WAAaJ,EAAOI,WAOtDwX,EAAqBnM,EAASnO,KAAKqa,GAAQD,EAAUC,KAE3D,IAAIE,EAAWpM,EAASmM,EAAmB1Y,QAAQkX,GAAuB,GAC1E,QAAwB,IAAbyB,GAA4BrX,EAAOkM,QAAS,CACrD,IAAIoL,EACJrM,EAAS9S,SAAQ,CAACqW,EAAMG,KAClBiH,GAAuBpH,IAEzB8I,EAAgB3I,EACjB,SAE0B,IAAlB2I,IACTD,EAAWpM,EAASqM,EAAgB,EAAIA,EAAgB,EAAIA,GAE/D,CACD,IAAIC,EAAY,EAahB,QAZwB,IAAbF,IACTE,EAAYrM,EAAWxM,QAAQ2Y,GAC3BE,EAAY,IAAGA,EAAY/X,EAAO4J,YAAc,GAEzB,SAAzBpJ,EAAOiJ,eACmB,IAA1BjJ,EAAOsN,gBACPtN,EAAO4W,qBAEPW,EAAYA,EAAY/X,EAAO0J,qBAAqB,YAAY,GAAQ,EACxEqO,EAAY5W,KAAKC,IAAI2W,EAAW,KAGhCvX,EAAOsJ,QAAU9J,EAAO4R,YAAa,CACvC,MAAMoG,EACJhY,EAAOQ,OAAO6K,SAAWrL,EAAOQ,OAAO6K,QAAQC,SAAWtL,EAAOqL,QAC7DrL,EAAOqL,QAAQjC,OAAOvQ,OAAS,EAC/BmH,EAAOoJ,OAAOvQ,OAAS,EAC7B,OAAOmH,EAAOkW,QAAQ8B,EAAWvX,EAAO6U,EAAcE,EACvD,CACD,OAAOxV,EAAOkW,QAAQ6B,EAAWtX,EAAO6U,EAAcE,EACvD,EJ5CCyC,WKZa,SAAoBxX,EAA2B6U,EAAqBE,GAEjF,YAF2F,IAA1D/U,MAAQlF,KAAKiF,OAAOC,YAAsC,IAA/B6U,OAAe,GAC5D/Z,KACD2a,QADC3a,KACcqO,YAAanJ,EAAO6U,EAAcE,EAChE,ELUC0C,eMba,SACbzX,EACA6U,EACAE,EACA2C,QACA,IAJA1X,MAAQlF,KAAKiF,OAAOC,YAIpB,IAHA6U,OAAe,QAGf,IADA6C,MAAY,IAEZ,MAAMnY,EAASzE,KACf,IAAI0M,EAAQjI,EAAO4J,YACnB,MAAMwK,EAAOjT,KAAKE,IAAIrB,EAAOQ,OAAOuN,mBAAoB9F,GAClDkH,EAAYiF,EAAOjT,KAAKwM,OAAO1F,EAAQmM,GAAQpU,EAAOQ,OAAOsN,gBAE7D1N,EAAYJ,EAAOiL,aAAejL,EAAOI,WAAaJ,EAAOI,UAEnE,GAAIA,GAAaJ,EAAOyL,SAAS0D,GAAY,CAG3C,MAAMiJ,EAAcpY,EAAOyL,SAAS0D,GAEhC/O,EAAYgY,GADCpY,EAAOyL,SAAS0D,EAAY,GACHiJ,GAAeD,IACvDlQ,GAASjI,EAAOQ,OAAOsN,eAE1B,KAAM,CAGL,MAAM+J,EAAW7X,EAAOyL,SAAS0D,EAAY,GAEzC/O,EAAYyX,IADI7X,EAAOyL,SAAS0D,GACO0I,GAAYM,IACrDlQ,GAASjI,EAAOQ,OAAOsN,eAE1B,CAID,OAHA7F,EAAQ9G,KAAKC,IAAI6G,EAAO,GACxBA,EAAQ9G,KAAKE,IAAI4G,EAAOjI,EAAO0L,WAAW7S,OAAS,GAE5CmH,EAAOkW,QAAQjO,EAAOxH,EAAO6U,EAAcE,EACnD,ENpBCZ,oBOba,WACb,MAAM5U,EAASzE,MACTiF,OAAEA,EAAFuK,SAAUA,GAAa/K,EAEvByJ,EACqB,SAAzBjJ,EAAOiJ,cAA2BzJ,EAAO0J,uBAAyBlJ,EAAOiJ,cAC3E,IACIM,EADAsO,EAAerY,EAAO2U,aAE1B,MAAM2D,EAAgBtY,EAAO8I,UAAa,eAAiB,IAAGtI,EAAOuI,aACrE,GAAIvI,EAAO6N,KAAM,CACf,GAAIrO,EAAOyV,UAAW,OACtB1L,EAAYO,SAAStK,EAAO0U,aAAaL,aAAa,2BAA4B,IAC9E7T,EAAOiM,eAEP4L,EAAerY,EAAOuY,aAAe9O,EAAgB,GACrD4O,EAAerY,EAAOoJ,OAAOvQ,OAASmH,EAAOuY,aAAe9O,EAAgB,GAE5EzJ,EAAOuX,UACPc,EAAerY,EAAOwY,cACpB5W,EAAgBmJ,EAAW,GAAEuN,8BAA0CvO,OAAe,IAGxFxN,GAAS,KACPyD,EAAOkW,QAAQmC,EAAf,KAGFrY,EAAOkW,QAAQmC,GAERA,EAAerY,EAAOoJ,OAAOvQ,OAAS4Q,GAC/CzJ,EAAOuX,UACPc,EAAerY,EAAOwY,cACpB5W,EAAgBmJ,EAAW,GAAEuN,8BAA0CvO,OAAe,IAGxFxN,GAAS,KACPyD,EAAOkW,QAAQmC,EAAf,KAGFrY,EAAOkW,QAAQmC,EAElB,MACCrY,EAAOkW,QAAQmC,EAElB,GCzCD,IAAAhK,EAAe,CACboK,WCHa,SAAoBC,GACjC,MAAM1Y,EAASzE,MACTiF,OAAEA,EAAFuK,SAAUA,GAAa/K,EAC7B,IAAKQ,EAAO6N,MAASrO,EAAOqL,SAAWrL,EAAOQ,OAAO6K,QAAQC,QAAU,OAExD1J,EAAgBmJ,EAAW,IAAGvK,EAAOuI,4BAE7CpQ,SAAQ,CAACgE,EAAIsL,KAClBtL,EAAG7C,aAAa,0BAA2BmO,EAA3C,IAGFjI,EAAOuX,QAAQ,CAAEmB,iBAAgB1C,UAAWxV,EAAOiM,oBAAiB7N,EAAY,QACjF,EDRC2Y,QENa,SAQP1S,GAAA,IARwB6T,eAC9BA,EAD8BxC,QAE9BA,GAAU,EAFoBF,UAG9BA,EAH8BjB,aAI9BA,EAJ8B4D,iBAK9BA,EAL8B3D,aAM9BA,EAN8B4D,aAO9BA,QACM,IAAA/T,EAAJ,GAAIA,EACN,MAAM7E,EAASzE,KACf,IAAKyE,EAAOQ,OAAO6N,KAAM,OACzBrO,EAAOoI,KAAK,iBAEZ,MAAMgB,OAAEA,EAAFoN,eAAUA,EAAVD,eAA0BA,EAA1BxL,SAA0CA,EAA1CvK,OAAoDA,GAAWR,EAKrE,GAHAA,EAAOwW,gBAAiB,EACxBxW,EAAOuW,gBAAiB,EAEpBvW,EAAOqL,SAAW7K,EAAO6K,QAAQC,QAanC,OAZI4K,IACG1V,EAAOiM,gBAAuC,IAArBzM,EAAOmP,UAE1B3O,EAAOiM,gBAAkBzM,EAAOmP,UAAY3O,EAAOiJ,cAC5DzJ,EAAOkW,QAAQlW,EAAOqL,QAAQjC,OAAOvQ,OAASmH,EAAOmP,UAAW,GAAG,GAAO,GACjEnP,EAAOmP,YAAcnP,EAAOyL,SAAS5S,OAAS,GACvDmH,EAAOkW,QAAQlW,EAAOqL,QAAQkD,aAAc,GAAG,GAAO,GAJtDvO,EAAOkW,QAAQlW,EAAOqL,QAAQjC,OAAOvQ,OAAQ,GAAG,GAAO,IAO3DmH,EAAOwW,eAAiBA,EACxBxW,EAAOuW,eAAiBA,OACxBvW,EAAOoI,KAAK,WAId,MAAMqB,EACqB,SAAzBjJ,EAAOiJ,cACHzJ,EAAO0J,uBACPvI,KAAKwI,KAAK3L,WAAWwC,EAAOiJ,cAAe,KACjD,IAAI8O,EAAe/X,EAAO+X,cAAgB9O,EACtC8O,EAAe/X,EAAOsN,gBAAmB,IAC3CyK,GAAgB/X,EAAOsN,eAAkByK,EAAe/X,EAAOsN,gBAEjE9N,EAAOuY,aAAeA,EAEtB,MAAMM,EAAuB,GACvBC,EAAsB,GAE5B,IAAIlP,EAAc5J,EAAO4J,iBAEO,IAArB+O,EACTA,EAAmB3Y,EAAOwY,cACxBxY,EAAOoJ,OAAOnK,QAAQtC,GAAOA,EAAGuF,UAAU4N,SAAStP,EAAOqS,oBAAmB,IAG/EjJ,EAAc+O,EAGhB,MAAMI,EAAuB,SAAd/C,IAAyBA,EAClCgD,EAAuB,SAAdhD,IAAyBA,EAExC,IAAIiD,EAAkB,EAClBC,EAAiB,EAErB,GAAIP,EAAmBJ,EAAc,CACnCU,EAAkB9X,KAAKC,IAAImX,EAAeI,EAAkBnY,EAAOsN,gBACnE,IAAK,IAAIhP,EAAI,EAAGA,EAAIyZ,EAAeI,EAAkB7Z,GAAK,EAAG,CAC3D,MAAMmJ,EAAQnJ,EAAIqC,KAAKwM,MAAM7O,EAAIsK,EAAOvQ,QAAUuQ,EAAOvQ,OACzDggB,EAAqBnV,KAAK0F,EAAOvQ,OAASoP,EAAQ,EACnD,CACF,MAAM,GAAI0Q,EAAyC3Y,EAAOoJ,OAAOvQ,OAAwB,EAAf0f,EAAkB,CAC3FW,EAAiB/X,KAAKC,IACpBuX,GAAoB3Y,EAAOoJ,OAAOvQ,OAAwB,EAAf0f,GAC3C/X,EAAOsN,gBAET,IAAK,IAAIhP,EAAI,EAAGA,EAAIoa,EAAgBpa,GAAK,EAAG,CAC1C,MAAMmJ,EAAQnJ,EAAIqC,KAAKwM,MAAM7O,EAAIsK,EAAOvQ,QAAUuQ,EAAOvQ,OACzDigB,EAAoBpV,KAAKuE,EAC1B,CACF,CAqBD,GAnBI+Q,GACFH,EAAqBlgB,SAASsP,IAC5B8C,EAASoO,QAAQnZ,EAAOoJ,OAAOnB,GAA/B,IAGA8Q,GACFD,EAAoBngB,SAASsP,IAC3B8C,EAASqO,OAAOpZ,EAAOoJ,OAAOnB,GAA9B,IAIJjI,EAAOqZ,eACsB,SAAzB7Y,EAAOiJ,eACTzJ,EAAOyK,eAELjK,EAAOiP,qBACTzP,EAAO0P,qBAGLwG,EACF,GAAI2C,EAAqBhgB,OAAS,GAAKmgB,EACrC,QAA8B,IAAnBN,EAAgC,CACzC,MAAMY,EAAwBtZ,EAAO0L,WAAW9B,GAE1C2P,EADoBvZ,EAAO0L,WAAW9B,EAAcqP,GACzBK,EAC7BV,EACF5Y,EAAO+U,aAAa/U,EAAOI,UAAYmZ,IAEvCvZ,EAAOkW,QAAQtM,EAAcqP,EAAiB,GAAG,GAAO,GACpDlE,IACF/U,EAAOwZ,QAAQxZ,EAAOoK,eAAiB,SAAW,WAAamP,GAGpE,MACKxE,GACF/U,EAAOgX,YAAY0B,EAAgB,GAAG,GAAO,QAG5C,GAAII,EAAoBjgB,OAAS,GAAKkgB,EAC3C,QAA8B,IAAnBL,EAAgC,CACzC,MAAMY,EAAwBtZ,EAAO0L,WAAW9B,GAE1C2P,EADoBvZ,EAAO0L,WAAW9B,EAAcsP,GACzBI,EAC7BV,EACF5Y,EAAO+U,aAAa/U,EAAOI,UAAYmZ,IAEvCvZ,EAAOkW,QAAQtM,EAAcsP,EAAgB,GAAG,GAAO,GACnDnE,IACF/U,EAAOwZ,QAAQxZ,EAAOoK,eAAiB,SAAW,WAAamP,GAGpE,MACCvZ,EAAOgX,YAAY0B,EAAgB,GAAG,GAAO,GAQnD,GAHA1Y,EAAOwW,eAAiBA,EACxBxW,EAAOuW,eAAiBA,EAEpBvW,EAAOyZ,YAAczZ,EAAOyZ,WAAWC,UAAY1E,EAAc,CACnE,MAAM2E,EAAa,CACjBjB,iBACAxC,SAAS,EACTF,YACAjB,eACA4D,mBACA3D,cAAc,GAEZ5S,MAAMC,QAAQrC,EAAOyZ,WAAWC,SAClC1Z,EAAOyZ,WAAWC,QAAQ/gB,SAASihB,KAC5BA,EAAEzS,WAAayS,EAAEpZ,OAAO6N,MAAMuL,EAAErC,QAAQoC,EAAV,IAGrC3Z,EAAOyZ,WAAWC,mBAAmB1Z,EAAO3H,aAC5C2H,EAAOyZ,WAAWC,QAAQlZ,OAAO6N,MAEjCrO,EAAOyZ,WAAWC,QAAQnC,QAAQoC,EAErC,CAED3Z,EAAOoI,KAAK,UACb,EF3JCyR,YGPa,WACb,MAAM7Z,EAASzE,MACTiF,OAAEA,EAAFuK,SAAUA,GAAa/K,EAC7B,IAAKQ,EAAO6N,MAASrO,EAAOqL,SAAWrL,EAAOQ,OAAO6K,QAAQC,QAAU,OACvEtL,EAAOqZ,eAEP,MAAMS,EAAiB,GACvB9Z,EAAOoJ,OAAOzQ,SAASiQ,IACrB,MAAMX,OACgC,IAA7BW,EAAQmR,iBACuC,EAAlDnR,EAAQyL,aAAa,2BACrBzL,EAAQmR,iBACdD,EAAe7R,GAASW,CAAxB,IAEF5I,EAAOoJ,OAAOzQ,SAASiQ,IACrBA,EAAQS,gBAAgB,0BAAxB,IAEFyQ,EAAenhB,SAASiQ,IACtBmC,EAASqO,OAAOxQ,EAAhB,IAEF5I,EAAOqZ,eACPrZ,EAAOkW,QAAQlW,EAAO+J,UAAW,EAClC,GCLc,SAASiQ,EAAa3S,GACnC,MAAMrH,EAASzE,KACTV,EAAWF,IACX2B,EAASF,IAETiM,EAAOrI,EAAOia,gBACpB5R,EAAK6R,QAAQxW,KAAK2D,GAClB,MAAM7G,OAAEA,EAAFgZ,QAAUA,EAAVlO,QAAmBA,GAAYtL,EACrC,IAAKsL,EAAS,OACd,IAAK9K,EAAO2Z,eAAuC,UAAtB9S,EAAM+S,YAAyB,OAE5D,GAAIpa,EAAOyV,WAAajV,EAAOkV,+BAC7B,QAEG1V,EAAOyV,WAAajV,EAAOkM,SAAWlM,EAAO6N,MAChDrO,EAAOuX,UAET,IAAI1T,EAAIwD,EACJxD,EAAEwW,gBAAexW,EAAIA,EAAEwW,eAC3B,IAAIC,EAAWzW,EAAErL,OAEjB,GAAiC,YAA7BgI,EAAO+Z,oBACJva,EAAOU,UAAUoP,SAASwK,GAAW,OAE5C,GAAI,UAAWzW,GAAiB,IAAZA,EAAE2W,MAAa,OACnC,GAAI,WAAY3W,GAAKA,EAAE4W,OAAS,EAAG,OACnC,GAAIpS,EAAKqS,WAAarS,EAAKsS,QAAS,OAGpC,MAAMC,IAAyBpa,EAAOqa,gBAA4C,KAA1Bra,EAAOqa,eAEzDC,EAAYzT,EAAM0T,aAAe1T,EAAM0T,eAAiB1T,EAAM2T,KAChEJ,GAAwB/W,EAAErL,QAAUqL,EAAErL,OAAOyiB,YAAcH,IAC7DR,EAAWQ,EAAU,IAGvB,MAAMI,EAAoB1a,EAAO0a,kBAC7B1a,EAAO0a,kBACN,IAAG1a,EAAOqa,iBACTM,KAAoBtX,EAAErL,SAAUqL,EAAErL,OAAOyiB,YAG/C,GACEza,EAAO4a,YACND,EAzDL,SAAwBrZ,EAAUuZ,GAUhC,YAV6C,IAAbA,MAAO9f,MACvC,SAAS+f,EAAc3e,GACrB,IAAKA,GAAMA,IAAOhC,KAAiBgC,IAAOP,IAAa,OAAO,KAC1DO,EAAG4e,eAAc5e,EAAKA,EAAG4e,cAC7B,MAAMC,EAAQ7e,EAAGkM,QAAQ/G,GACzB,OAAK0Z,GAAU7e,EAAG8e,YAGXD,GAASF,EAAc3e,EAAG8e,cAAcrhB,MAFtC,IAGV,CACMkhB,CAAcD,EACtB,CA+COK,CAAeR,EAAmBZ,GAClCA,EAASzR,QAAQqS,IAGrB,YADAlb,EAAO2b,YAAa,GAItB,GAAInb,EAAOob,eACJtB,EAASzR,QAAQrI,EAAOob,cAAe,OAG9CpC,EAAQqC,SAAWhY,EAAEiY,MACrBtC,EAAQuC,SAAWlY,EAAEmY,MACrB,MAAMC,EAASzC,EAAQqC,SACjBK,EAAS1C,EAAQuC,SAIjBI,EAAqB3b,EAAO2b,oBAAsB3b,EAAO4b,sBACzDC,EAAqB7b,EAAO6b,oBAAsB7b,EAAO8b,sBAC/D,GACEH,IACCF,GAAUI,GAAsBJ,GAAU3f,EAAOigB,WAAaF,GAC/D,CACA,GAA2B,YAAvBF,EAGF,OAFA9U,EAAMmV,gBAIT,CAEDlkB,OAAOkS,OAAOnC,EAAM,CAClBqS,WAAW,EACXC,SAAS,EACT8B,qBAAqB,EACrBC,iBAAa9d,EACb+d,iBAAa/d,IAGf4a,EAAQyC,OAASA,EACjBzC,EAAQ0C,OAASA,EACjB7T,EAAKuU,eAAiBngB,IACtBuD,EAAO2b,YAAa,EACpB3b,EAAOiK,aACPjK,EAAO6c,oBAAiBje,EACpB4B,EAAO2X,UAAY,IAAG9P,EAAKyU,oBAAqB,GACpD,IAAIN,GAAiB,EACjBlC,EAASvY,QAAQsG,EAAK0U,qBACxBP,GAAiB,EACS,WAAtBlC,EAASlhB,WACXiP,EAAKqS,WAAY,IAInB7f,EAAS3B,eACT2B,EAAS3B,cAAc6I,QAAQsG,EAAK0U,oBACpCliB,EAAS3B,gBAAkBohB,GAE3Bzf,EAAS3B,cAAcC,OAGzB,MAAM6jB,EACJR,GAAkBxc,EAAOid,gBAAkBzc,EAAO0c,0BAEjD1c,EAAO2c,gCAAiCH,GACxC1C,EAAS8C,mBAEVvZ,EAAE2Y,iBAGFxc,EAAOQ,OAAO6c,UACdrd,EAAOQ,OAAO6c,SAAS/R,SACvBtL,EAAOqd,UACPrd,EAAOyV,YACNjV,EAAOkM,SAER1M,EAAOqd,SAASrD,eAElBha,EAAOoI,KAAK,aAAcvE,EAC3B,CC1Ic,SAASyZ,EAAYjW,GAClC,MAAMxM,EAAWF,IACXqF,EAASzE,KACT8M,EAAOrI,EAAOia,iBACdzZ,OAAEA,EAAFgZ,QAAUA,EAASvO,aAAcC,EAAjCI,QAAsCA,GAAYtL,EACxD,IAAKsL,EAAS,OACd,IAAK9K,EAAO2Z,eAAuC,UAAtB9S,EAAM+S,YAAyB,OAE5D,IAAIvW,EAAIwD,EAER,GADIxD,EAAEwW,gBAAexW,EAAIA,EAAEwW,gBACtBhS,EAAKqS,UAIR,YAHIrS,EAAKsU,aAAetU,EAAKqU,aAC3B1c,EAAOoI,KAAK,oBAAqBvE,IAKrC,MAAM0Z,EAAelV,EAAK6R,QAAQsD,WAAWC,GAAaA,EAASC,YAAc7Z,EAAE6Z,YAC/EH,GAAgB,IAAGlV,EAAK6R,QAAQqD,GAAgB1Z,GACpD,MAAM8Z,EAActV,EAAK6R,QAAQrhB,OAAS,EAAIwP,EAAK6R,QAAQ,GAAKrW,EAC1DiY,EAAQ6B,EAAY7B,MACpBE,EAAQ2B,EAAY3B,MAE1B,GAAInY,EAAE+Z,wBAGJ,OAFApE,EAAQyC,OAASH,OACjBtC,EAAQ0C,OAASF,GAGnB,IAAKhc,EAAOid,eAeV,OAdKpZ,EAAErL,OAAOuJ,QAAQsG,EAAK0U,qBACzB/c,EAAO2b,YAAa,QAElBtT,EAAKqS,YACPpiB,OAAOkS,OAAOgP,EAAS,CACrByC,OAAQH,EACRI,OAAQF,EACR6B,MAAO7d,EAAOwZ,QAAQqC,SACtBiC,MAAO9d,EAAOwZ,QAAQuC,SACtBF,SAAUC,EACVC,SAAUC,IAEZ3T,EAAKuU,eAAiBngB,MAI1B,GAAI+D,EAAOud,sBAAwBvd,EAAO6N,KACxC,GAAIrO,EAAOqK,cAET,GACG2R,EAAQxC,EAAQ0C,QAAUlc,EAAOI,WAAaJ,EAAO2R,gBACrDqK,EAAQxC,EAAQ0C,QAAUlc,EAAOI,WAAaJ,EAAOmR,eAItD,OAFA9I,EAAKqS,WAAY,OACjBrS,EAAKsS,SAAU,QAGZ,GACJmB,EAAQtC,EAAQyC,QAAUjc,EAAOI,WAAaJ,EAAO2R,gBACrDmK,EAAQtC,EAAQyC,QAAUjc,EAAOI,WAAaJ,EAAOmR,eAEtD,OAGJ,GAAItW,EAAS3B,eACP2K,EAAErL,SAAWqC,EAAS3B,eAAiB2K,EAAErL,OAAOuJ,QAAQsG,EAAK0U,mBAG/D,OAFA1U,EAAKsS,SAAU,OACf3a,EAAO2b,YAAa,GAOxB,GAHItT,EAAKoU,qBACPzc,EAAOoI,KAAK,YAAavE,GAEvBA,EAAEma,eAAiBna,EAAEma,cAAcnlB,OAAS,EAAG,OAEnD2gB,EAAQqC,SAAWC,EACnBtC,EAAQuC,SAAWC,EAEnB,MAAMiC,EAAQzE,EAAQqC,SAAWrC,EAAQyC,OACnCiC,EAAQ1E,EAAQuC,SAAWvC,EAAQ0C,OACzC,GAAIlc,EAAOQ,OAAO2X,WAAahX,KAAKgd,KAAKF,GAAS,EAAIC,GAAS,GAAKle,EAAOQ,OAAO2X,UAChF,OAEF,QAAgC,IAArB9P,EAAKqU,YAA6B,CAC3C,IAAI0B,EAEDpe,EAAOoK,gBAAkBoP,EAAQuC,WAAavC,EAAQ0C,QACtDlc,EAAOqK,cAAgBmP,EAAQqC,WAAarC,EAAQyC,OAErD5T,EAAKqU,aAAc,EAGfuB,EAAQA,EAAQC,EAAQA,GAAS,KACnCE,EAA6D,IAA/Cjd,KAAKkd,MAAMld,KAAK0M,IAAIqQ,GAAQ/c,KAAK0M,IAAIoQ,IAAiB9c,KAAKK,GACzE6G,EAAKqU,YAAc1c,EAAOoK,eACtBgU,EAAa5d,EAAO4d,WACpB,GAAKA,EAAa5d,EAAO4d,WAGlC,CASD,GARI/V,EAAKqU,aACP1c,EAAOoI,KAAK,oBAAqBvE,QAEH,IAArBwE,EAAKsU,cACVnD,EAAQqC,WAAarC,EAAQyC,QAAUzC,EAAQuC,WAAavC,EAAQ0C,SACtE7T,EAAKsU,aAAc,IAIrBtU,EAAKqU,aACJ1c,EAAOse,MAAQte,EAAOQ,OAAO8d,MAAQte,EAAOQ,OAAO8d,KAAKhT,SAAWjD,EAAK6R,QAAQrhB,OAAS,EAG1F,YADAwP,EAAKqS,WAAY,GAGnB,IAAKrS,EAAKsU,YACR,OAEF3c,EAAO2b,YAAa,GACfnb,EAAOkM,SAAW7I,EAAE0a,YACvB1a,EAAE2Y,iBAEAhc,EAAOge,2BAA6Bhe,EAAOie,QAC7C5a,EAAE6a,kBAGJ,IAAInF,EAAOvZ,EAAOoK,eAAiB6T,EAAQC,EACvCS,EAAc3e,EAAOoK,eACrBoP,EAAQqC,SAAWrC,EAAQoF,UAC3BpF,EAAQuC,SAAWvC,EAAQqF,UAE3Bre,EAAOse,iBACTvF,EAAOpY,KAAK0M,IAAI0L,IAASrO,EAAM,GAAK,GACpCyT,EAAcxd,KAAK0M,IAAI8Q,IAAgBzT,EAAM,GAAK,IAEpDsO,EAAQD,KAAOA,EAEfA,GAAQ/Y,EAAOue,WACX7T,IACFqO,GAAQA,EACRoF,GAAeA,GAGjB,MAAMK,EAAuBhf,EAAOif,iBACpCjf,EAAO6c,eAAiBtD,EAAO,EAAI,OAAS,OAC5CvZ,EAAOif,iBAAmBN,EAAc,EAAI,OAAS,OAErD,MAAMO,EAASlf,EAAOQ,OAAO6N,OAAS7N,EAAOkM,QAE7C,IAAKrE,EAAKsS,QAAS,CAMjB,GALIuE,GACFlf,EAAOuX,QAAQ,CAAEvB,UAAWhW,EAAO6c,iBAErCxU,EAAK8W,eAAiBnf,EAAOtD,eAC7BsD,EAAOmQ,cAAc,GACjBnQ,EAAOyV,UAAW,CACpB,MAAM2J,EAAM,IAAI9iB,OAAOhB,YAAY,gBAAiB,CAClD+jB,SAAS,EACTd,YAAY,IAEdve,EAAOU,UAAU4e,cAAcF,EAChC,CACD/W,EAAKkX,qBAAsB,GAEvB/e,EAAOgf,aAAyC,IAA1Bxf,EAAOuW,iBAAqD,IAA1BvW,EAAOwW,gBACjExW,EAAOyf,eAAc,GAEvBzf,EAAOoI,KAAK,kBAAmBvE,EAChC,CACD,IAAI6b,EAEFrX,EAAKsS,SACLqE,IAAyBhf,EAAOif,kBAChCC,GACA/d,KAAK0M,IAAI0L,IAAS,IAGlBvZ,EAAOuX,QAAQ,CAAEvB,UAAWhW,EAAO6c,eAAgB9H,cAAc,IACjE2K,GAAY,GAEd1f,EAAOoI,KAAK,aAAcvE,GAC1BwE,EAAKsS,SAAU,EAEftS,EAAKyM,iBAAmByE,EAAOlR,EAAK8W,eAEpC,IAAIQ,GAAsB,EACtBC,EAAkBpf,EAAOof,gBA0E7B,GAzEIpf,EAAOud,sBACT6B,EAAkB,GAEhBrG,EAAO,GAEP2F,IACCQ,GACDrX,EAAKyM,kBACFtU,EAAOiM,eAAiBzM,EAAOmR,eAAiBnR,EAAO+D,KAAO,EAAI/D,EAAOmR,iBAE5EnR,EAAOuX,QAAQ,CAAEvB,UAAW,OAAQjB,cAAc,EAAM4D,iBAAkB,IAExEtQ,EAAKyM,iBAAmB9U,EAAOmR,iBACjCwO,GAAsB,EAClBnf,EAAOqf,aACTxX,EAAKyM,iBACH9U,EAAOmR,eACP,IACEnR,EAAOmR,eAAiB9I,EAAK8W,eAAiB5F,IAASqG,KAGtDrG,EAAO,IAEd2F,IACCQ,GACDrX,EAAKyM,kBACFtU,EAAOiM,eAAiBzM,EAAO2R,eAAiB3R,EAAO+D,KAAO,EAAI/D,EAAO2R,iBAE5E3R,EAAOuX,QAAQ,CACbvB,UAAW,OACXjB,cAAc,EACd4D,iBACE3Y,EAAOoJ,OAAOvQ,QACY,SAAzB2H,EAAOiJ,cACJzJ,EAAO0J,uBACPvI,KAAKwI,KAAK3L,WAAWwC,EAAOiJ,cAAe,QAGjDpB,EAAKyM,iBAAmB9U,EAAO2R,iBACjCgO,GAAsB,EAClBnf,EAAOqf,aACTxX,EAAKyM,iBACH9U,EAAO2R,eACP,GACC3R,EAAO2R,eAAiBtJ,EAAK8W,eAAiB5F,IAASqG,KAK5DD,IACF9b,EAAE+Z,yBAA0B,IAK3B5d,EAAOuW,gBACkB,SAA1BvW,EAAO6c,gBACPxU,EAAKyM,iBAAmBzM,EAAK8W,iBAE7B9W,EAAKyM,iBAAmBzM,EAAK8W,iBAG5Bnf,EAAOwW,gBACkB,SAA1BxW,EAAO6c,gBACPxU,EAAKyM,iBAAmBzM,EAAK8W,iBAE7B9W,EAAKyM,iBAAmBzM,EAAK8W,gBAE1Bnf,EAAOwW,gBAAmBxW,EAAOuW,iBACpClO,EAAKyM,iBAAmBzM,EAAK8W,gBAI3B3e,EAAO2X,UAAY,EAAG,CACxB,KAAIhX,KAAK0M,IAAI0L,GAAQ/Y,EAAO2X,WAAa9P,EAAKyU,oBAa5C,YADAzU,EAAKyM,iBAAmBzM,EAAK8W,gBAX7B,IAAK9W,EAAKyU,mBAQR,OAPAzU,EAAKyU,oBAAqB,EAC1BtD,EAAQyC,OAASzC,EAAQqC,SACzBrC,EAAQ0C,OAAS1C,EAAQuC,SACzB1T,EAAKyM,iBAAmBzM,EAAK8W,oBAC7B3F,EAAQD,KAAOvZ,EAAOoK,eAClBoP,EAAQqC,SAAWrC,EAAQyC,OAC3BzC,EAAQuC,SAAWvC,EAAQ0C,OAOpC,CAEI1b,EAAOsf,eAAgBtf,EAAOkM,WAIhClM,EAAO6c,UAAY7c,EAAO6c,SAAS/R,SAAWtL,EAAOqd,UACtD7c,EAAOiP,uBAEPzP,EAAO2T,oBACP3T,EAAO0S,uBAEL1S,EAAOQ,OAAO6c,UAAY7c,EAAO6c,SAAS/R,SAAWtL,EAAOqd,UAC9Drd,EAAOqd,SAASC,cAGlBtd,EAAOwR,eAAenJ,EAAKyM,kBAE3B9U,EAAO+U,aAAa1M,EAAKyM,kBAC1B,CCxSc,SAASiL,EAAW1Y,GACjC,MAAMrH,EAASzE,KACT8M,EAAOrI,EAAOia,gBACdsD,EAAelV,EAAK6R,QAAQsD,WAAWC,GAAaA,EAASC,YAAcrW,EAAMqW,YAIvF,GAHIH,GAAgB,GAClBlV,EAAK6R,QAAQhS,OAAOqV,EAAc,GAEhC,CAAC,gBAAiB,aAAc,gBAAgBnX,SAASiB,EAAM2Y,MAAO,CAGxE,KADiB,kBAAf3Y,EAAM2Y,OAA6BhgB,EAAOoE,QAAQ6B,UAAYjG,EAAOoE,QAAQqC,YAE7E,MAEH,CAED,MAAMjG,OAAEA,EAAFgZ,QAAUA,EAASvO,aAAcC,EAAjCQ,WAAsCA,EAAtCJ,QAAkDA,GAAYtL,EACpE,IAAKsL,EAAS,OACd,IAAK9K,EAAO2Z,eAAuC,UAAtB9S,EAAM+S,YAAyB,OAE5D,IAAIvW,EAAIwD,EAMR,GALIxD,EAAEwW,gBAAexW,EAAIA,EAAEwW,eACvBhS,EAAKoU,qBACPzc,EAAOoI,KAAK,WAAYvE,GAE1BwE,EAAKoU,qBAAsB,GACtBpU,EAAKqS,UAMR,OALIrS,EAAKsS,SAAWna,EAAOgf,YACzBxf,EAAOyf,eAAc,GAEvBpX,EAAKsS,SAAU,OACftS,EAAKsU,aAAc,GAKnBnc,EAAOgf,YACPnX,EAAKsS,SACLtS,EAAKqS,aACsB,IAA1B1a,EAAOuW,iBAAqD,IAA1BvW,EAAOwW,iBAE1CxW,EAAOyf,eAAc,GAIvB,MAAMQ,EAAexjB,IACfyjB,EAAWD,EAAe5X,EAAKuU,eAGrC,GAAI5c,EAAO2b,WAAY,CACrB,MAAMwE,EAAWtc,EAAEmX,MAASnX,EAAEkX,cAAgBlX,EAAEkX,eAChD/a,EAAOwU,mBAAoB2L,GAAYA,EAAS,IAAOtc,EAAErL,QACzDwH,EAAOoI,KAAK,YAAavE,GACrBqc,EAAW,KAAOD,EAAe5X,EAAK+X,cAAgB,KACxDpgB,EAAOoI,KAAK,wBAAyBvE,EAExC,CAOD,GALAwE,EAAK+X,cAAgB3jB,IACrBF,GAAS,KACFyD,EAAOmH,YAAWnH,EAAO2b,YAAa,EAApB,KAItBtT,EAAKqS,YACLrS,EAAKsS,UACL3a,EAAO6c,gBACS,IAAjBrD,EAAQD,MACRlR,EAAKyM,mBAAqBzM,EAAK8W,eAK/B,OAHA9W,EAAKqS,WAAY,EACjBrS,EAAKsS,SAAU,OACftS,EAAKsU,aAAc,GAOrB,IAAI0D,EAOJ,GAXAhY,EAAKqS,WAAY,EACjBrS,EAAKsS,SAAU,EACftS,EAAKsU,aAAc,EAIjB0D,EADE7f,EAAOsf,aACI5U,EAAMlL,EAAOI,WAAaJ,EAAOI,WAEhCiI,EAAKyM,iBAGjBtU,EAAOkM,QACT,OAGF,GAAI1M,EAAOQ,OAAO6c,UAAY7c,EAAO6c,SAAS/R,QAE5C,YADAtL,EAAOqd,SAAS0C,WAAW,CAAEM,eAK/B,IAAIC,EAAY,EACZ7R,EAAYzO,EAAO2L,gBAAgB,GACvC,IACE,IAAI7M,EAAI,EACRA,EAAI4M,EAAW7S,OACfiG,GAAKA,EAAI0B,EAAOuN,mBAAqB,EAAIvN,EAAOsN,eAChD,CACA,MAAMuJ,EAAYvY,EAAI0B,EAAOuN,mBAAqB,EAAI,EAAIvN,EAAOsN,oBACxB,IAA9BpC,EAAW5M,EAAIuY,GACpBgJ,GAAc3U,EAAW5M,IAAMuhB,EAAa3U,EAAW5M,EAAIuY,KAC7DiJ,EAAYxhB,EACZ2P,EAAY/C,EAAW5M,EAAIuY,GAAa3L,EAAW5M,IAE5CuhB,GAAc3U,EAAW5M,KAClCwhB,EAAYxhB,EACZ2P,EAAY/C,EAAWA,EAAW7S,OAAS,GAAK6S,EAAWA,EAAW7S,OAAS,GAElF,CAED,IAAI0nB,EAAmB,KACnBC,EAAkB,KAClBhgB,EAAOsJ,SACL9J,EAAO4R,YACT4O,EACExgB,EAAOQ,OAAO6K,SAAWrL,EAAOQ,OAAO6K,QAAQC,SAAWtL,EAAOqL,QAC7DrL,EAAOqL,QAAQjC,OAAOvQ,OAAS,EAC/BmH,EAAOoJ,OAAOvQ,OAAS,EACpBmH,EAAO6R,QAChB0O,EAAmB,IAIvB,MAAME,GAASJ,EAAa3U,EAAW4U,IAAc7R,EAC/C4I,EAAYiJ,EAAY9f,EAAOuN,mBAAqB,EAAI,EAAIvN,EAAOsN,eACzE,GAAIoS,EAAW1f,EAAOkgB,aAAc,CAElC,IAAKlgB,EAAOmgB,WAEV,YADA3gB,EAAOkW,QAAQlW,EAAO4J,aAGM,SAA1B5J,EAAO6c,iBACL4D,GAASjgB,EAAOogB,gBAClB5gB,EAAOkW,QAAQ1V,EAAOsJ,QAAU9J,EAAO6R,MAAQ0O,EAAmBD,EAAYjJ,GAC3ErX,EAAOkW,QAAQoK,IAEQ,SAA1BtgB,EAAO6c,iBACL4D,EAAQ,EAAIjgB,EAAOogB,gBACrB5gB,EAAOkW,QAAQoK,EAAYjJ,GAEP,OAApBmJ,GACAC,EAAQ,GACRtf,KAAK0M,IAAI4S,GAASjgB,EAAOogB,gBAEzB5gB,EAAOkW,QAAQsK,GAEfxgB,EAAOkW,QAAQoK,GAGpB,KAAM,CAEL,IAAK9f,EAAOqgB,YAEV,YADA7gB,EAAOkW,QAAQlW,EAAO4J,aAItB5J,EAAO8gB,aACNjd,EAAErL,SAAWwH,EAAO8gB,WAAWC,QAAUld,EAAErL,SAAWwH,EAAO8gB,WAAWE,QAQhEnd,EAAErL,SAAWwH,EAAO8gB,WAAWC,OACxC/gB,EAAOkW,QAAQoK,EAAYjJ,GAE3BrX,EAAOkW,QAAQoK,IATe,SAA1BtgB,EAAO6c,gBACT7c,EAAOkW,QAA6B,OAArBqK,EAA4BA,EAAmBD,EAAYjJ,GAE9C,SAA1BrX,EAAO6c,gBACT7c,EAAOkW,QAA4B,OAApBsK,EAA2BA,EAAkBF,GAOjE,CACF,CCjLc,SAASW,IACtB,MAAMjhB,EAASzE,MAETiF,OAAEA,EAAF7D,GAAUA,GAAOqD,EAEvB,GAAIrD,GAAyB,IAAnBA,EAAGsH,YAAmB,OAG5BzD,EAAOyM,aACTjN,EAAOkhB,gBAIT,MAAM3K,eAAEA,EAAFC,eAAkBA,EAAlB/K,SAAkCA,GAAazL,EAE/CoL,EAAYpL,EAAOqL,SAAWrL,EAAOQ,OAAO6K,QAAQC,QAG1DtL,EAAOuW,gBAAiB,EACxBvW,EAAOwW,gBAAiB,EAExBxW,EAAOiK,aACPjK,EAAOyK,eAEPzK,EAAO0S,sBACP,MAAMyO,EAAgB/V,GAAa5K,EAAO6N,OAEd,SAAzB7N,EAAOiJ,eAA4BjJ,EAAOiJ,cAAgB,KAC3DzJ,EAAO6R,OACN7R,EAAO4R,aACP5R,EAAOQ,OAAOiM,gBACd0U,EAIGnhB,EAAOQ,OAAO6N,OAASjD,EACzBpL,EAAOgX,YAAYhX,EAAO+J,UAAW,GAAG,GAAO,GAE/C/J,EAAOkW,QAAQlW,EAAO4J,YAAa,GAAG,GAAO,GAL/C5J,EAAOkW,QAAQlW,EAAOoJ,OAAOvQ,OAAS,EAAG,GAAG,GAAO,GASjDmH,EAAOohB,UAAYphB,EAAOohB,SAASC,SAAWrhB,EAAOohB,SAASE,SAChExlB,aAAakE,EAAOohB,SAASG,eAC7BvhB,EAAOohB,SAASG,cAAgB1lB,YAAW,KACrCmE,EAAOohB,UAAYphB,EAAOohB,SAASC,SAAWrhB,EAAOohB,SAASE,QAChEthB,EAAOohB,SAASI,QACjB,GACA,MAGLxhB,EAAOwW,eAAiBA,EACxBxW,EAAOuW,eAAiBA,EAEpBvW,EAAOQ,OAAO+O,eAAiB9D,IAAazL,EAAOyL,UACrDzL,EAAOwP,eAEV,CCzDc,SAASiS,EAAQ5d,GAC9B,MAAM7D,EAASzE,KACVyE,EAAOsL,UACPtL,EAAO2b,aACN3b,EAAOQ,OAAOkhB,eAAe7d,EAAE2Y,iBAC/Bxc,EAAOQ,OAAOmhB,0BAA4B3hB,EAAOyV,YACnD5R,EAAE6a,kBACF7a,EAAE+d,6BAGP,CCVc,SAASC,IACtB,MAAM7hB,EAASzE,MACTmF,UAAEA,EAAFuK,aAAaA,EAAbK,QAA2BA,GAAYtL,EAC7C,IAAKsL,EAAS,OAad,IAAI2J,EAZJjV,EAAOoV,kBAAoBpV,EAAOI,UAC9BJ,EAAOoK,eACTpK,EAAOI,WAAaM,EAAUmC,WAE9B7C,EAAOI,WAAaM,EAAUiC,UAGP,IAArB3C,EAAOI,YAAiBJ,EAAOI,UAAY,GAE/CJ,EAAO2T,oBACP3T,EAAO0S,sBAGP,MAAMhB,EAAiB1R,EAAO2R,eAAiB3R,EAAOmR,eAEpD8D,EADqB,IAAnBvD,EACY,GAEC1R,EAAOI,UAAYJ,EAAOmR,gBAAkBO,EAEzDuD,IAAgBjV,EAAOkB,UACzBlB,EAAOwR,eAAevG,GAAgBjL,EAAOI,UAAYJ,EAAOI,WAGlEJ,EAAOoI,KAAK,eAAgBpI,EAAOI,WAAW,EAC/C,CC1Bc,SAAS0hB,EAAOje,GAE7B6E,EADenN,KACcsI,EAAErL,QADhB+C,KAERyO,QACR,CCID,IAAI+X,GAAqB,EACzB,SAASC,IAAqB,CAE9B,MAAMlb,EAAS,CAAC9G,EAAQoH,KACtB,MAAMvM,EAAWF,KACX6F,OAAEA,EAAF7D,GAAUA,EAAV+D,UAAcA,EAAdsE,OAAyBA,GAAWhF,EACpCiiB,IAAYzhB,EAAOie,OACnByD,EAAuB,OAAX9a,EAAkB,mBAAqB,sBACnD+a,EAAe/a,EAGrBzK,EAAGulB,GAAW,cAAeliB,EAAOga,aAAc,CAAEoI,SAAS,IAC7DvnB,EAASqnB,GAAW,cAAeliB,EAAOsd,YAAa,CAAE8E,SAAS,EAAOH,YACzEpnB,EAASqnB,GAAW,YAAaliB,EAAO+f,WAAY,CAAEqC,SAAS,IAC/DvnB,EAASqnB,GAAW,gBAAiBliB,EAAO+f,WAAY,CAAEqC,SAAS,IACnEvnB,EAASqnB,GAAW,aAAcliB,EAAO+f,WAAY,CAAEqC,SAAS,IAChEvnB,EAASqnB,GAAW,eAAgBliB,EAAO+f,WAAY,CAAEqC,SAAS,KAG9D5hB,EAAOkhB,eAAiBlhB,EAAOmhB,2BACjChlB,EAAGulB,GAAW,QAASliB,EAAOyhB,SAAS,GAErCjhB,EAAOkM,SACThM,EAAUwhB,GAAW,SAAUliB,EAAO6hB,UAIpCrhB,EAAO6hB,qBACTriB,EAAOmiB,GACLnd,EAAOC,KAAOD,EAAOE,QACjB,0CACA,wBACJ+b,GACA,GAGFjhB,EAAOmiB,GAAc,iBAAkBlB,GAAU,GAInDtkB,EAAGulB,GAAW,OAAQliB,EAAO8hB,OAAQ,CAAEG,SAAS,GAAhD,EChDF,MAAMK,EAAgB,CAACtiB,EAAQQ,IACtBR,EAAO4M,MAAQpM,EAAOoM,MAAQpM,EAAOoM,KAAKC,KAAO,EC2B1D,IC9BA0V,EAAe,CACbC,MAAM,EACNxM,UAAW,aACX8I,gBAAgB,EAChBvE,kBAAmB,UACnBzD,aAAc,EACdrW,MAAO,IACPiM,SAAS,EACT2V,sBAAsB,EACtBI,gBAAgB,EAChBhE,QAAQ,EACRiE,gBAAgB,EAChBpX,SAAS,EACTyR,kBAAmB,wDAGnB3X,MAAO,KACPE,OAAQ,KAGRoQ,gCAAgC,EAGhC1a,UAAW,KACX2nB,IAAK,KAGLxG,oBAAoB,EACpBE,mBAAoB,GAGpB5J,YAAY,EAGZxE,gBAAgB,EAGhB4G,kBAAkB,EAGlB7G,OAAQ,QAGRf,iBAAarO,EACbgkB,gBAAiB,SAGjB1W,aAAc,EACdzC,cAAe,EACfqE,eAAgB,EAChBC,mBAAoB,EACpBqJ,oBAAoB,EACpB3K,gBAAgB,EAChBmC,sBAAsB,EACtB/C,mBAAoB,EACpBE,kBAAmB,EACnBmI,qBAAqB,EACrBjF,0BAA0B,EAG1BM,eAAe,EAGfhC,cAAc,EAGdwR,WAAY,EACZX,WAAY,GACZjE,eAAe,EACf0G,aAAa,EACbF,YAAY,EACZC,gBAAiB,GACjBF,aAAc,IACdZ,cAAc,EACd7C,gBAAgB,EAChB9E,UAAW,EACXqG,0BAA0B,EAC1BtB,0BAA0B,EAC1BC,+BAA+B,EAC/BY,qBAAqB,EAGrB8E,mBAAmB,EAGnBhD,YAAY,EACZD,gBAAiB,IAGjBnQ,qBAAqB,EAGrB+P,YAAY,EAGZkC,eAAe,EACfC,0BAA0B,EAC1B/M,qBAAqB,EAGrBvG,MAAM,EACNkK,aAAc,KACdjB,qBAAqB,EAGrBxN,QAAQ,EAGR0M,gBAAgB,EAChBD,gBAAgB,EAChBqF,aAAc,KACdR,WAAW,EACXP,eAAgB,oBAChBK,kBAAmB,KAGnB4H,kBAAkB,EAElB/S,wBAAyB,GAGzBH,uBAAwB,UACxB7G,WAAY,eACZ8J,iBAAkB,sBAClB9B,kBAAmB,uBACnB+B,eAAgB,oBAChBC,eAAgB,oBAChBgQ,aAAc,iBACd9Z,mBAAoB,wBACpBO,oBAAqB,EAGrB+K,oBAAoB,EAGpByO,cAAc,GCrID,SAASC,EAAmBziB,EAAQ0iB,GACjD,OAAO,SAAsB9qB,QAAU,IAAVA,MAAM,IACjC,MAAM+qB,EAAkB7qB,OAAOI,KAAKN,GAAK,GACnCgrB,EAAehrB,EAAI+qB,GACG,iBAAjBC,GAA8C,OAAjBA,GAKtC,CAAC,aAAc,aAAc,aAAalkB,QAAQikB,IAAoB,IAC1C,IAA5B3iB,EAAO2iB,KAEP3iB,EAAO2iB,GAAmB,CAAEE,MAAM,IAE9BF,KAAmB3iB,GAAU,YAAa4iB,IAIhB,IAA5B5iB,EAAO2iB,KACT3iB,EAAO2iB,GAAmB,CAAE7X,SAAS,IAEA,iBAA5B9K,EAAO2iB,IAAmC,YAAa3iB,EAAO2iB,KACvE3iB,EAAO2iB,GAAiB7X,SAAU,GAE/B9K,EAAO2iB,KAAkB3iB,EAAO2iB,GAAmB,CAAE7X,SAAS,IACnE/S,EAAO2qB,EAAkB9qB,IAVvBG,EAAO2qB,EAAkB9qB,IAVzBG,EAAO2qB,EAAkB9qB,E,CAsB9B,CCKD,MAAMkrB,EAAa,CACjB1c,gBACAoD,SACA5J,YACAmjB,WClCa,CACbpT,cCLa,SAAuB5P,EAAUyU,GAC9C,MAAMhV,EAASzE,KAEVyE,EAAOQ,OAAOkM,UACjB1M,EAAOU,UAAU7G,MAAM2pB,mBAAsB,GAAEjjB,OAGjDP,EAAOoI,KAAK,gBAAiB7H,EAAUyU,EACxC,EDFCyB,gBEJa,SAAyBnB,EAAqBU,QAAW,IAAhCV,OAAe,GACrD,MAAMtV,EAASzE,MACTiF,OAAEA,GAAWR,EACfQ,EAAOkM,UACPlM,EAAOiS,YACTzS,EAAOgQ,mBAGT+F,EAAe,CAAE/V,SAAQsV,eAAcU,YAAWC,KAAM,UACzD,EFJCS,cGLa,SAAuBpB,EAAqBU,QAAW,IAAhCV,OAAe,GACnD,MAAMtV,EAASzE,MACTiF,OAAEA,GAAWR,EACnBA,EAAOyV,WAAY,EACfjV,EAAOkM,UACX1M,EAAOmQ,cAAc,GAErB4F,EAAe,CAAE/V,SAAQsV,eAAcU,YAAWC,KAAM,QACzD,GJ6BC/I,QACAmB,OACAmR,WKtCa,CACbC,cCJa,SAAuBgE,GACpC,MAAMzjB,EAASzE,KACf,IACGyE,EAAOQ,OAAO2Z,eACdna,EAAOQ,OAAO+O,eAAiBvP,EAAO0jB,UACvC1jB,EAAOQ,OAAOkM,QAEd,OACF,MAAM/P,EAAyC,cAApCqD,EAAOQ,OAAO+Z,kBAAoCva,EAAOrD,GAAKqD,EAAOU,UAC5EV,EAAO8I,YACT9I,EAAO2jB,qBAAsB,GAE/BhnB,EAAG9C,MAAM+pB,OAAS,OAClBjnB,EAAG9C,MAAM+pB,OAASH,EAAS,WAAa,OACpCzjB,EAAO8I,WACT9M,uBAAsB,KACpBgE,EAAO2jB,qBAAsB,CAA7B,GAGL,EDdCE,gBELa,WACb,MAAM7jB,EAASzE,KACVyE,EAAOQ,OAAO+O,eAAiBvP,EAAO0jB,UAAa1jB,EAAOQ,OAAOkM,UAGlE1M,EAAO8I,YACT9I,EAAO2jB,qBAAsB,GAE/B3jB,EAA2C,cAApCA,EAAOQ,OAAO+Z,kBAAoC,KAAO,aAAa1gB,MAAM+pB,OAAS,GACxF5jB,EAAO8I,WACT9M,uBAAsB,KACpBgE,EAAO2jB,qBAAsB,CAA7B,IAGL,G,OZoEc,CACbG,aA9BF,WACE,MAAM9jB,EAASzE,KACTV,EAAWF,KACX6F,OAAEA,GAAWR,EAEnBA,EAAOga,aAAeA,EAAa+J,KAAK/jB,GACxCA,EAAOsd,YAAcA,EAAYyG,KAAK/jB,GACtCA,EAAO+f,WAAaA,EAAWgE,KAAK/jB,GAEhCQ,EAAOkM,UACT1M,EAAO6hB,SAAWA,EAASkC,KAAK/jB,IAGlCA,EAAOyhB,QAAUA,EAAQsC,KAAK/jB,GAC9BA,EAAO8hB,OAASA,EAAOiC,KAAK/jB,GAEvB+hB,IACHlnB,EAAS7B,iBAAiB,aAAcgpB,GACxCD,GAAqB,GAGvBjb,EAAO9G,EAAQ,KAChB,EASCgkB,aAPF,WAEEld,EADevL,KACA,MAChB,GKrCC0R,YQxCa,CAAEiU,cZGF,WACb,MAAMlhB,EAASzE,MACTwO,UAAEA,EAAFuK,YAAaA,EAAb9T,OAA0BA,EAA1B7D,GAAkCA,GAAOqD,EACzCiN,EAAczM,EAAOyM,YAC3B,IAAKA,GAAgBA,GAAmD,IAApC3U,OAAOI,KAAKuU,GAAapU,OAAe,OAG5E,MAAMorB,EAAajkB,EAAOkkB,cAAcjX,EAAajN,EAAOQ,OAAOoiB,gBAAiB5iB,EAAOrD,IAE3F,IAAKsnB,GAAcjkB,EAAOmkB,oBAAsBF,EAAY,OAE5D,MACMG,GADuBH,KAAchX,EAAcA,EAAYgX,QAAcrlB,IAClCoB,EAAOqkB,eAClDC,EAAchC,EAActiB,EAAQQ,GACpC+jB,EAAajC,EAActiB,EAAQokB,GAEnCI,EAAahkB,EAAO8K,QAEtBgZ,IAAgBC,GAClB5nB,EAAGuF,UAAUgH,OACV,GAAE1I,EAAOoP,6BACT,GAAEpP,EAAOoP,qCAEZ5P,EAAOykB,yBACGH,GAAeC,IACzB5nB,EAAGuF,UAAUC,IAAK,GAAE3B,EAAOoP,+BAExBwU,EAAiBxX,KAAK8X,MAAuC,WAA/BN,EAAiBxX,KAAK8X,OACnDN,EAAiBxX,KAAK8X,MAA6B,WAArBlkB,EAAOoM,KAAK8X,OAE5C/nB,EAAGuF,UAAUC,IAAK,GAAE3B,EAAOoP,qCAE7B5P,EAAOykB,wBAIT,CAAC,aAAc,aAAc,aAAa9rB,SAASuK,IACjD,MAAMyhB,EAAmBnkB,EAAO0C,IAAS1C,EAAO0C,GAAMoI,QAChDsZ,EAAkBR,EAAiBlhB,IAASkhB,EAAiBlhB,GAAMoI,QACrEqZ,IAAqBC,GACvB5kB,EAAOkD,GAAM2hB,WAEVF,GAAoBC,GACvB5kB,EAAOkD,GAAM4hB,QACd,IAGH,MAAMC,EACJX,EAAiBpO,WAAaoO,EAAiBpO,YAAcxV,EAAOwV,UAChEgP,EACJxkB,EAAO6N,OAAS+V,EAAiB3a,gBAAkBjJ,EAAOiJ,eAAiBsb,GAEzEA,GAAoBzQ,GACtBtU,EAAOilB,kBAET1sB,EAAOyH,EAAOQ,OAAQ4jB,GAEtB,MAAMc,EAAYllB,EAAOQ,OAAO8K,QAEhChT,OAAOkS,OAAOxK,EAAQ,CACpBid,eAAgBjd,EAAOQ,OAAOyc,eAC9B1G,eAAgBvW,EAAOQ,OAAO+V,eAC9BC,eAAgBxW,EAAOQ,OAAOgW,iBAG5BgO,IAAeU,EACjBllB,EAAO6kB,WACGL,GAAcU,GACxBllB,EAAO8kB,SAGT9kB,EAAOmkB,kBAAoBF,EAE3BjkB,EAAOoI,KAAK,oBAAqBgc,GAE7BY,GAAe1Q,IACjBtU,EAAO6Z,cACP7Z,EAAOyY,WAAW1O,GAClB/J,EAAOyK,gBAGTzK,EAAOoI,KAAK,aAAcgc,EAC3B,EYrF+BF,cCDjB,SAAuBjX,EAAaoO,EAAiB8J,GAClE,QAD+E,IAA9B9J,MAAO,WACnDpO,GAAyB,cAAToO,IAAyB8J,EAAc,OAC5D,IAAIlB,GAAa,EAEjB,MAAM3nB,EAASF,IACTgpB,EAAyB,WAAT/J,EAAoB/e,EAAO+oB,YAAcF,EAAYhb,aAErEmb,EAAShtB,OAAOI,KAAKuU,GAAa3P,KAAKioB,IAC3C,GAAqB,iBAAVA,GAA6C,IAAvBA,EAAMrmB,QAAQ,KAAY,CACzD,MAAMsmB,EAAWxnB,WAAWunB,EAAME,OAAO,IAEzC,MAAO,CAAEC,MADKN,EAAgBI,EACdD,QACjB,CACD,MAAO,CAAEG,MAAOH,EAAOA,QAAvB,IAGFD,EAAOK,MAAK,CAACpoB,EAAGqoB,IAAMtb,SAAS/M,EAAEmoB,MAAO,IAAMpb,SAASsb,EAAEF,MAAO,MAChE,IAAK,IAAI5mB,EAAI,EAAGA,EAAIwmB,EAAOzsB,OAAQiG,GAAK,EAAG,CACzC,MAAMymB,MAAEA,EAAFG,MAASA,GAAUJ,EAAOxmB,GACnB,WAATuc,EACE/e,EAAOP,WAAY,eAAc2pB,QAAY3jB,UAC/CkiB,EAAasB,GAENG,GAASP,EAAYjb,cAC9B+Z,EAAasB,EAEhB,CACD,OAAOtB,GAAc,KACtB,G,cZAc,CAAEzU,cA9BjB,WACE,MAAMxP,EAASzE,MACPmoB,SAAUmC,EAAZrlB,OAAuBA,GAAWR,GAClC6L,mBAAEA,GAAuBrL,EAE/B,GAAIqL,EAAoB,CACtB,MAAMuG,EAAiBpS,EAAOoJ,OAAOvQ,OAAS,EACxCitB,EACJ9lB,EAAO0L,WAAW0G,GAClBpS,EAAO2L,gBAAgByG,GACF,EAArBvG,EACF7L,EAAO0jB,SAAW1jB,EAAO+D,KAAO+hB,CACjC,MACC9lB,EAAO0jB,SAAsC,IAA3B1jB,EAAOyL,SAAS5S,QAEN,IAA1B2H,EAAO+V,iBACTvW,EAAOuW,gBAAkBvW,EAAO0jB,WAEJ,IAA1BljB,EAAOgW,iBACTxW,EAAOwW,gBAAkBxW,EAAO0jB,UAG9BmC,GAAaA,IAAc7lB,EAAO0jB,WACpC1jB,EAAO6R,OAAQ,GAEbgU,IAAc7lB,EAAO0jB,UACvB1jB,EAAOoI,KAAKpI,EAAO0jB,SAAW,OAAS,SAE1C,GGiBCzhB,QU1Ca,CAAE8jB,WCaF,WACb,MAAM/lB,EAASzE,MACTyqB,WAAEA,EAAFxlB,OAAcA,EAAd0K,IAAsBA,EAAtBvO,GAA2BA,EAA3BqI,OAA+BA,GAAWhF,EAE1CimB,EApBR,SAAwBC,EAASC,GAC/B,MAAMC,EAAgB,GAYtB,OAXAF,EAAQvtB,SAAS0tB,IACK,iBAATA,EACT/tB,OAAOI,KAAK2tB,GAAM1tB,SAASqtB,IACrBK,EAAKL,IACPI,EAAc1iB,KAAKyiB,EAASH,EAC7B,IAEsB,iBAATK,GAChBD,EAAc1iB,KAAKyiB,EAASE,EAC7B,IAEID,CACR,CAMkBE,CAAe,CAC9B,cACA9lB,EAAOwV,UACP,CAAE,YAAahW,EAAOQ,OAAO6c,UAAY7c,EAAO6c,SAAS/R,SACzD,CAAEib,WAAc/lB,EAAOiS,YACvB,CAAEvH,IAAOA,GACT,CAAE0B,KAAQpM,EAAOoM,MAAQpM,EAAOoM,KAAKC,KAAO,GAC5C,CAAE,cAAerM,EAAOoM,MAAQpM,EAAOoM,KAAKC,KAAO,GAA0B,WAArBrM,EAAOoM,KAAK8X,MACpE,CAAExf,QAAWF,EAAOE,SACpB,CAAED,IAAOD,EAAOC,KAChB,CAAE,WAAYzE,EAAOkM,SACrB,CAAE8Z,SAAYhmB,EAAOkM,SAAWlM,EAAOiM,gBACvC,CAAE,iBAAkBjM,EAAOiP,sBAC1BjP,EAAOoP,wBACVoW,EAAWtiB,QAAQuiB,GACnBtpB,EAAGuF,UAAUC,OAAO6jB,GACpBhmB,EAAOykB,sBACR,EDlC4BgC,cEHd,WACb,MACM9pB,GAAEA,EAAFqpB,WAAMA,GADGzqB,KAGfoB,EAAGuF,UAAUgH,UAAU8c,GAHRzqB,KAIRkpB,sBACR,IZ0CKiC,EAAmB,GAEzB,MAAMC,EACJtuB,cACE,IAAIsE,EACA6D,EAFe,QAAAkH,EAAA/I,UAAA9F,OAAN8O,EAAM,IAAAvF,MAAAsF,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAND,EAAMC,GAAAjJ,UAAAiJ,GAID,IAAhBD,EAAK9O,QACL8O,EAAK,GAAGtP,aACiD,WAAzDC,OAAO6F,UAAUL,SAASM,KAAKuJ,EAAK,IAAItJ,MAAM,GAAI,GAElDmC,EAASmH,EAAK,IAEbhL,EAAI6D,GAAUmH,EAEZnH,IAAQA,EAAS,IAEtBA,EAASjI,EAAO,GAAIiI,GAChB7D,IAAO6D,EAAO7D,KAAI6D,EAAO7D,GAAKA,GAElC,MAAM9B,EAAWF,IAEjB,GACE6F,EAAO7D,IACc,iBAAd6D,EAAO7D,IACd9B,EAASvB,iBAAiBkH,EAAO7D,IAAI9D,OAAS,EAC9C,CACA,MAAM+tB,EAAU,GAMhB,OALA/rB,EAASvB,iBAAiBkH,EAAO7D,IAAIhE,SAASwsB,IAC5C,MAAM0B,EAAYtuB,EAAO,GAAIiI,EAAQ,CAAE7D,GAAIwoB,IAC3CyB,EAAQljB,KAAK,IAAIijB,EAAOE,GAAxB,IAGKD,CACR,CAGD,MAAM5mB,EAASzE,KACfyE,EAAOP,YAAa,EACpBO,EAAOkE,QAAUG,IACjBrE,EAAOgF,OAASL,EAAU,CAAE3J,UAAWwF,EAAOxF,YAC9CgF,EAAOoE,QAAU2B,IAEjB/F,EAAOkH,gBAAkB,GACzBlH,EAAO+H,mBAAqB,GAC5B/H,EAAO8mB,QAAU,IAAI9mB,EAAO+mB,aACxBvmB,EAAOsmB,SAAW1kB,MAAMC,QAAQ7B,EAAOsmB,UACzC9mB,EAAO8mB,QAAQpjB,QAAQlD,EAAOsmB,SAGhC,MAAM5D,EAAmB,GACzBljB,EAAO8mB,QAAQnuB,SAASquB,IACtBA,EAAI,CACFxmB,SACAR,SACAinB,aAAchE,EAAmBziB,EAAQ0iB,GACzCrc,GAAI7G,EAAO6G,GAAGkd,KAAK/jB,GACnBsH,KAAMtH,EAAOsH,KAAKyc,KAAK/jB,GACvBwH,IAAKxH,EAAOwH,IAAIuc,KAAK/jB,GACrBoI,KAAMpI,EAAOoI,KAAK2b,KAAK/jB,IAPzB,IAYF,MAAMknB,EAAe3uB,EAAO,GAAIgqB,EAAUW,GAkH1C,OA/GAljB,EAAOQ,OAASjI,EAAO,GAAI2uB,EAAcR,EAAkBlmB,GAC3DR,EAAOqkB,eAAiB9rB,EAAO,GAAIyH,EAAOQ,QAC1CR,EAAOmnB,aAAe5uB,EAAO,GAAIiI,GAG7BR,EAAOQ,QAAUR,EAAOQ,OAAOqG,IACjCvO,OAAOI,KAAKsH,EAAOQ,OAAOqG,IAAIlO,SAASyuB,IACrCpnB,EAAO6G,GAAGugB,EAAWpnB,EAAOQ,OAAOqG,GAAGugB,GAAtC,IAGApnB,EAAOQ,QAAUR,EAAOQ,OAAOsH,OACjC9H,EAAO8H,MAAM9H,EAAOQ,OAAOsH,OAI7BxP,OAAOkS,OAAOxK,EAAQ,CACpBsL,QAAStL,EAAOQ,OAAO8K,QACvB3O,KAGAqpB,WAAY,GAGZ5c,OAAQ,GACRsC,WAAY,GACZD,SAAU,GACVE,gBAAiB,GAGjBvB,aAAY,IACyB,eAA5BpK,EAAOQ,OAAOwV,UAEvB3L,WAAU,IAC2B,aAA5BrK,EAAOQ,OAAOwV,UAIvBpM,YAAa,EACbG,UAAW,EAGX6H,aAAa,EACbC,OAAO,EAGPzR,UAAW,EACXgV,kBAAmB,EACnBlU,SAAU,EACVmmB,SAAU,EACV5R,WAAW,EAEX7E,wBAGE,OAAOzP,KAAKmmB,MAAM/rB,KAAK6E,UAAY,GAAK,IAAM,GAAK,E,EAIrDmW,eAAgBvW,EAAOQ,OAAO+V,eAC9BC,eAAgBxW,EAAOQ,OAAOgW,eAG9ByD,gBAAiB,CACfS,eAAW9b,EACX+b,aAAS/b,EACT6d,yBAAqB7d,EACrBge,oBAAgBhe,EAChB8d,iBAAa9d,EACbkW,sBAAkBlW,EAClBugB,oBAAgBvgB,EAChBke,wBAAoBle,EAEpBme,kBAAmB/c,EAAOQ,OAAOuc,kBAEjCqD,cAAe,EACfmH,kBAAc3oB,EAEd4oB,WAAY,GACZjI,yBAAqB3gB,EACrB+d,iBAAa/d,EACbsb,QAAS,IAIXyB,YAAY,EAGZsB,eAAgBjd,EAAOQ,OAAOyc,eAE9BzD,QAAS,CACPyC,OAAQ,EACRC,OAAQ,EACRL,SAAU,EACVE,SAAU,EACVxC,KAAM,GAIRkO,aAAc,GACdC,aAAc,IAGhB1nB,EAAOoI,KAAK,WAGRpI,EAAOQ,OAAOgiB,MAChBxiB,EAAOwiB,OAKFxiB,CACR,CAEDwY,cAAc5P,GACZ,MAAMmC,SAAEA,EAAFvK,OAAYA,GAAWjF,KAEvB4W,EAAkBhP,EADTvB,EAAgBmJ,EAAW,IAAGvK,EAAOuI,4BACR,IAC5C,OAAO5F,EAAayF,GAAWuJ,CAChC,CAED9B,oBAAoBpI,GAClB,OAAO1M,KAAKid,cACVjd,KAAK6N,OAAOnK,QACT2J,GAA8D,EAAlDA,EAAQyL,aAAa,6BAAmCpM,IACrE,GAEL,CAEDoR,eACE,MACMtO,SAAEA,EAAFvK,OAAYA,GADHjF,UAER6N,OAASxH,EAAgBmJ,EAAW,IAAGvK,EAAOuI,2BACtD,CAED+b,SACE,MAAM9kB,EAASzE,KACXyE,EAAOsL,UACXtL,EAAOsL,SAAU,EACbtL,EAAOQ,OAAOgf,YAChBxf,EAAOyf,gBAETzf,EAAOoI,KAAK,UACb,CAEDyc,UACE,MAAM7kB,EAASzE,KACVyE,EAAOsL,UACZtL,EAAOsL,SAAU,EACbtL,EAAOQ,OAAOgf,YAChBxf,EAAO6jB,kBAET7jB,EAAOoI,KAAK,WACb,CAEDuf,YAAYzmB,EAAUT,GACpB,MAAMT,EAASzE,KACf2F,EAAWC,KAAKE,IAAIF,KAAKC,IAAIF,EAAU,GAAI,GAC3C,MAAMG,EAAMrB,EAAOmR,eAEbpQ,GADMf,EAAO2R,eACItQ,GAAOH,EAAWG,EACzCrB,EAAOqV,YAAYtU,OAA0B,IAAVN,EAAwB,EAAIA,GAC/DT,EAAO2T,oBACP3T,EAAO0S,qBACR,CAED+R,uBACE,MAAMzkB,EAASzE,KACf,IAAKyE,EAAOQ,OAAOwiB,eAAiBhjB,EAAOrD,GAAI,OAC/C,MAAMirB,EAAM5nB,EAAOrD,GAAGkrB,UAAUxqB,MAAM,KAAK4B,QAAQ4oB,GAEf,IAAhCA,EAAU3oB,QAAQ,WAC0C,IAA5D2oB,EAAU3oB,QAAQc,EAAOQ,OAAOoP,0BAGpC5P,EAAOoI,KAAK,oBAAqBwf,EAAInqB,KAAK,KAC3C,CAEDqqB,gBAAgBlf,GACd,MAAM5I,EAASzE,KACf,OAAIyE,EAAOmH,UAAkB,GAEtByB,EAAQif,UACZxqB,MAAM,KACN4B,QAAQ4oB,GAEiC,IAAtCA,EAAU3oB,QAAQ,iBAC8B,IAAhD2oB,EAAU3oB,QAAQc,EAAOQ,OAAOuI,cAGnCtL,KAAK,IACT,CAEDiW,oBACE,MAAM1T,EAASzE,KACf,IAAKyE,EAAOQ,OAAOwiB,eAAiBhjB,EAAOrD,GAAI,OAC/C,MAAMorB,EAAU,GAChB/nB,EAAOoJ,OAAOzQ,SAASiQ,IACrB,MAAMod,EAAahmB,EAAO8nB,gBAAgBlf,GAC1Cmf,EAAQrkB,KAAK,CAAEkF,UAASod,eACxBhmB,EAAOoI,KAAK,cAAeQ,EAASod,EAApC,IAEFhmB,EAAOoI,KAAK,gBAAiB2f,EAC9B,CAEDre,qBAAqBse,EAAkBC,QAAe,IAAjCD,MAAO,gBAA0B,IAAfC,OAAQ,GAC7C,MACMznB,OAAEA,EAAF4I,OAAUA,EAAVsC,WAAkBA,EAAlBC,gBAA8BA,EAAiB5H,KAAMiH,EAArDpB,YAAiEA,GADxDrO,KAEf,IAAI2sB,EAAM,EACV,GAAI1nB,EAAOiM,eAAgB,CACzB,IACI0b,EADArb,EAAY1D,EAAOQ,GAAagE,gBAEpC,IAAK,IAAI9O,EAAI8K,EAAc,EAAG9K,EAAIsK,EAAOvQ,OAAQiG,GAAK,EAChDsK,EAAOtK,KAAOqpB,IAChBrb,GAAa1D,EAAOtK,GAAG8O,gBACvBsa,GAAO,EACHpb,EAAY9B,IAAYmd,GAAY,IAG5C,IAAK,IAAIrpB,EAAI8K,EAAc,EAAG9K,GAAK,EAAGA,GAAK,EACrCsK,EAAOtK,KAAOqpB,IAChBrb,GAAa1D,EAAOtK,GAAG8O,gBACvBsa,GAAO,EACHpb,EAAY9B,IAAYmd,GAAY,GAG7C,MAEC,GAAa,YAATH,EACF,IAAK,IAAIlpB,EAAI8K,EAAc,EAAG9K,EAAIsK,EAAOvQ,OAAQiG,GAAK,EAAG,EACnCmpB,EAChBvc,EAAW5M,GAAK6M,EAAgB7M,GAAK4M,EAAW9B,GAAeoB,EAC/DU,EAAW5M,GAAK4M,EAAW9B,GAAeoB,KAE5Ckd,GAAO,EAEV,MAGD,IAAK,IAAIppB,EAAI8K,EAAc,EAAG9K,GAAK,EAAGA,GAAK,EAAG,CACxB4M,EAAW9B,GAAe8B,EAAW5M,GAAKkM,IAE5Dkd,GAAO,EAEV,CAGL,OAAOA,CACR,CAEDle,SACE,MAAMhK,EAASzE,KACf,IAAKyE,GAAUA,EAAOmH,UAAW,OACjC,MAAMsE,SAAEA,EAAFjL,OAAYA,GAAWR,EAiB7B,SAAS+U,IACP,MAAMqT,EAAiBpoB,EAAOiL,cAAmC,EAApBjL,EAAOI,UAAiBJ,EAAOI,UACtEuV,EAAexU,KAAKE,IACxBF,KAAKC,IAAIgnB,EAAgBpoB,EAAO2R,gBAChC3R,EAAOmR,gBAETnR,EAAO+U,aAAaY,GACpB3V,EAAO2T,oBACP3T,EAAO0S,qBACR,CACD,IAAI2V,EACJ,GA1BI7nB,EAAOyM,aACTjN,EAAOkhB,gBAGT,IAAIlhB,EAAOrD,GAAGrD,iBAAiB,qBAAqBX,SAASgQ,IACvDA,EAAQ2f,UACV5f,EAAqB1I,EAAQ2I,EAC9B,IAGH3I,EAAOiK,aACPjK,EAAOyK,eACPzK,EAAOwR,iBACPxR,EAAO0S,sBAaH1S,EAAOQ,OAAO6c,UAAYrd,EAAOQ,OAAO6c,SAAS/R,QACnDyJ,IACI/U,EAAOQ,OAAOiS,YAChBzS,EAAOgQ,uBAEJ,CACL,IACmC,SAAhChQ,EAAOQ,OAAOiJ,eAA4BzJ,EAAOQ,OAAOiJ,cAAgB,IACzEzJ,EAAO6R,QACN7R,EAAOQ,OAAOiM,eACf,CACA,MAAMrD,EACJpJ,EAAOqL,SAAWrL,EAAOQ,OAAO6K,QAAQC,QAAUtL,EAAOqL,QAAQjC,OAASpJ,EAAOoJ,OACnFif,EAAaroB,EAAOkW,QAAQ9M,EAAOvQ,OAAS,EAAG,GAAG,GAAO,EAC1D,MACCwvB,EAAaroB,EAAOkW,QAAQlW,EAAO4J,YAAa,GAAG,GAAO,GAEvDye,GACHtT,GAEH,CACGvU,EAAO+O,eAAiB9D,IAAazL,EAAOyL,UAC9CzL,EAAOwP,gBAETxP,EAAOoI,KAAK,SACb,CAED6c,gBAAgBsD,EAAcC,QAAmB,IAAnBA,OAAa,GACzC,MAAMxoB,EAASzE,KACTktB,EAAmBzoB,EAAOQ,OAAOwV,UAKvC,OAJKuS,IAEHA,EAAoC,eAArBE,EAAoC,WAAa,cAGhEF,IAAiBE,GACC,eAAjBF,GAAkD,aAAjBA,IAKpCvoB,EAAOrD,GAAGuF,UAAUgH,OAAQ,GAAElJ,EAAOQ,OAAOoP,yBAAyB6Y,KACrEzoB,EAAOrD,GAAGuF,UAAUC,IAAK,GAAEnC,EAAOQ,OAAOoP,yBAAyB2Y,KAClEvoB,EAAOykB,uBAEPzkB,EAAOQ,OAAOwV,UAAYuS,EAE1BvoB,EAAOoJ,OAAOzQ,SAASiQ,IACA,aAAjB2f,EACF3f,EAAQ/O,MAAMuL,MAAQ,GAEtBwD,EAAQ/O,MAAMyL,OAAS,EACxB,IAGHtF,EAAOoI,KAAK,mBACRogB,GAAYxoB,EAAOgK,UAlBdhK,CAqBV,CAED0oB,wBAAwB1S,GACtB,MAAMhW,EAASzE,KACVyE,EAAOkL,KAAqB,QAAd8K,IAA0BhW,EAAOkL,KAAqB,QAAd8K,IAC3DhW,EAAOkL,IAAoB,QAAd8K,EACbhW,EAAOiL,aAA2C,eAA5BjL,EAAOQ,OAAOwV,WAA8BhW,EAAOkL,IACrElL,EAAOkL,KACTlL,EAAOrD,GAAGuF,UAAUC,IAAK,GAAEnC,EAAOQ,OAAOoP,6BACzC5P,EAAOrD,GAAGkE,IAAM,QAEhBb,EAAOrD,GAAGuF,UAAUgH,OAAQ,GAAElJ,EAAOQ,OAAOoP,6BAC5C5P,EAAOrD,GAAGkE,IAAM,OAElBb,EAAOgK,SACR,CAED2e,MAAM9mB,GACJ,MAAM7B,EAASzE,KACf,GAAIyE,EAAO4oB,QAAS,OAAO,EAG3B,IAAIjsB,EAAKkF,GAAW7B,EAAOQ,OAAO7D,GAIlC,GAHkB,iBAAPA,IACTA,EAAK9B,SAASxB,cAAcsD,KAEzBA,EACH,OAAO,EAGTA,EAAGqD,OAASA,EACRrD,EAAGksB,WACL7oB,EAAO8I,WAAY,GAGrB,MAAMggB,EAAqB,IACjB,KAAI9oB,EAAOQ,OAAOuiB,cAAgB,IAAIgG,OAAO1rB,MAAM,KAAKI,KAAK,OAYvE,IAAIiD,EATe,MACjB,GAAI/D,GAAMA,EAAGse,YAActe,EAAGse,WAAW5hB,cAAe,CAGtD,OAFYsD,EAAGse,WAAW5hB,cAAcyvB,IAGzC,CACD,OAAOlnB,EAAgBjF,EAAImsB,KAAsB,EAAjD,EAGcE,GAuBhB,OAtBKtoB,GAAaV,EAAOQ,OAAOkiB,iBAC9BhiB,EAAYhH,EAAc,MAAOsG,EAAOQ,OAAOuiB,cAC/CpmB,EAAGyc,OAAO1Y,GACVkB,EAAgBjF,EAAK,IAAGqD,EAAOQ,OAAOuI,cAAcpQ,SAASiQ,IAC3DlI,EAAU0Y,OAAOxQ,EAAjB,KAIJtQ,OAAOkS,OAAOxK,EAAQ,CACpBrD,KACA+D,YACAqK,SAAU/K,EAAO8I,UAAYnM,EAAK+D,EAClCkoB,SAAS,EAGT1d,IAA8B,QAAzBvO,EAAGkE,IAAIqF,eAA6D,QAAlCjD,EAAatG,EAAI,aACxDsO,aAC8B,eAA5BjL,EAAOQ,OAAOwV,YACY,QAAzBrZ,EAAGkE,IAAIqF,eAA6D,QAAlCjD,EAAatG,EAAI,cACtDwO,SAAiD,gBAAvClI,EAAavC,EAAW,cAG7B,CACR,CAED8hB,KAAK7lB,GACH,MAAMqD,EAASzE,KACf,GAAIyE,EAAOsU,YAAa,OAAOtU,EAG/B,OAAgB,IADAA,EAAO2oB,MAAMhsB,KAG7BqD,EAAOoI,KAAK,cAGRpI,EAAOQ,OAAOyM,aAChBjN,EAAOkhB,gBAITlhB,EAAO+lB,aAGP/lB,EAAOiK,aAGPjK,EAAOyK,eAEHzK,EAAOQ,OAAO+O,eAChBvP,EAAOwP,gBAILxP,EAAOQ,OAAOgf,YAAcxf,EAAOsL,SACrCtL,EAAOyf,gBAILzf,EAAOQ,OAAO6N,MAAQrO,EAAOqL,SAAWrL,EAAOQ,OAAO6K,QAAQC,QAChEtL,EAAOkW,QACLlW,EAAOQ,OAAOsW,aAAe9W,EAAOqL,QAAQkD,aAC5C,EACAvO,EAAOQ,OAAO+T,oBACd,GACA,GAGFvU,EAAOkW,QAAQlW,EAAOQ,OAAOsW,aAAc,EAAG9W,EAAOQ,OAAO+T,oBAAoB,GAAO,GAIrFvU,EAAOQ,OAAO6N,MAChBrO,EAAOyY,aAITzY,EAAO8jB,eAEP,IAAI9jB,EAAOrD,GAAGrD,iBAAiB,qBAAqBX,SAASgQ,IACvDA,EAAQ2f,SACV5f,EAAqB1I,EAAQ2I,GAE7BA,EAAQ3P,iBAAiB,QAAS6K,IAChC6E,EAAqB1I,EAAQ6D,EAAErL,OAA/B,GAEH,IAEH8Q,EAAQtJ,GAGRA,EAAOsU,aAAc,EAErBhL,EAAQtJ,GAGRA,EAAOoI,KAAK,QACZpI,EAAOoI,KAAK,cAlEkBpI,CAqE/B,CAEDipB,QAAQC,EAAuBC,QAAoB,IAA3CD,OAAiB,QAA0B,IAApBC,OAAc,GAC3C,MAAMnpB,EAASzE,MACTiF,OAAEA,EAAF7D,GAAUA,EAAV+D,UAAcA,EAAd0I,OAAyBA,GAAWpJ,EAE1C,YAA6B,IAAlBA,EAAOQ,QAA0BR,EAAOmH,YAInDnH,EAAOoI,KAAK,iBAGZpI,EAAOsU,aAAc,EAGrBtU,EAAOgkB,eAGHxjB,EAAO6N,MACTrO,EAAO6Z,cAILsP,IACFnpB,EAAOymB,gBACP9pB,EAAG0M,gBAAgB,SACnB3I,EAAU2I,gBAAgB,SACtBD,GAAUA,EAAOvQ,QACnBuQ,EAAOzQ,SAASiQ,IACdA,EAAQ1G,UAAUgH,OAChB1I,EAAOuQ,kBACPvQ,EAAOqS,iBACPrS,EAAOsS,eACPtS,EAAOuS,gBAETnK,EAAQS,gBAAgB,SACxBT,EAAQS,gBAAgB,0BAAxB,KAKNrJ,EAAOoI,KAAK,WAGZ9P,OAAOI,KAAKsH,EAAOkH,iBAAiBvO,SAASyuB,IAC3CpnB,EAAOwH,IAAI4f,EAAX,KAGqB,IAAnB8B,IACFlpB,EAAOrD,GAAGqD,OAAS,K/C3oBzB,SAAqB5H,GACnB,MAAMgxB,EAAShxB,EACfE,OAAOI,KAAK0wB,GAAQzwB,SAASC,IAC3B,IACEwwB,EAAOxwB,GAAO,IAGf,CAFC,MAAOiL,GAER,CACD,WACSulB,EAAOxwB,EAGf,CAFC,MAAOiL,GAER,IAEJ,C+C8nBKwlB,CAAYrpB,IAEdA,EAAOmH,WAAY,GA9CV,IAiDV,CAEoBmiB,sBAACC,GACpBhxB,EAAOmuB,EAAkB6C,EAC1B,CAEU7C,8BACT,OAAOA,CACR,CAEUnE,sBACT,OAAOA,CACR,CAEmB+G,qBAACtC,GACdL,EAAOxoB,UAAU4oB,cAAaJ,EAAOxoB,UAAU4oB,YAAc,IAClE,MAAMD,EAAUH,EAAOxoB,UAAU4oB,YAEd,mBAARC,GAAsBF,EAAQ5nB,QAAQ8nB,GAAO,GACtDF,EAAQpjB,KAAKsjB,EAEhB,CAESsC,WAACE,GACT,OAAIpnB,MAAMC,QAAQmnB,IAChBA,EAAO7wB,SAAS8wB,GAAM9C,EAAO+C,cAAcD,KACpC9C,IAETA,EAAO+C,cAAcF,GACd7C,EACR,Ea/qBY,SAASgD,EAA0B3pB,EAAQqkB,EAAgB7jB,EAAQopB,GAehF,OAdI5pB,EAAOQ,OAAOkiB,gBAChBpqB,OAAOI,KAAKkxB,GAAYjxB,SAASC,IAC/B,IAAK4H,EAAO5H,KAAwB,IAAhB4H,EAAO6iB,KAAe,CACxC,IAAIxhB,EAAUD,EAAgB5B,EAAOrD,GAAK,IAAGitB,EAAWhxB,MAAQ,GAC3DiJ,IACHA,EAAUnI,EAAc,MAAOkwB,EAAWhxB,IAC1CiJ,EAAQgmB,UAAY+B,EAAWhxB,GAC/BoH,EAAOrD,GAAGyc,OAAOvX,IAEnBrB,EAAO5H,GAAOiJ,EACdwiB,EAAezrB,GAAOiJ,CACvB,KAGErB,CACR,CClBc,SAASqpB,GAAkB5nB,GACxC,YADsD,IAAdA,MAAU,IAC1C,IAAGA,EACR8mB,OACAvrB,QAAQ,eAAgB,QACxBA,QAAQ,KAAM,MAClB,CCLc,SAASssB,GAAY1gB,GAClC,MAAMpJ,EAASzE,MACTiF,OAAEA,EAAFuK,SAAUA,GAAa/K,EAEzBQ,EAAO6N,MACTrO,EAAO6Z,cAGT,MAAMkQ,EAAiBnhB,IACrB,GAAuB,iBAAZA,EAAsB,CAC/B,MAAMohB,EAAUnvB,SAASnB,cAAc,OACvCswB,EAAQC,UAAYrhB,EACpBmC,EAASqO,OAAO4Q,EAAQrwB,SAAS,IACjCqwB,EAAQC,UAAY,EACrB,MACClf,EAASqO,OAAOxQ,EACjB,EAGH,GAAsB,iBAAXQ,GAAuB,WAAYA,EAC5C,IAAK,IAAItK,EAAI,EAAGA,EAAIsK,EAAOvQ,OAAQiG,GAAK,EAClCsK,EAAOtK,IAAIirB,EAAc3gB,EAAOtK,SAGtCirB,EAAc3gB,GAEhBpJ,EAAOqZ,eACH7Y,EAAO6N,MACTrO,EAAOyY,aAEJjY,EAAO0pB,WAAYlqB,EAAO8I,WAC7B9I,EAAOgK,QAEV,CCjCc,SAASmgB,GAAa/gB,GACnC,MAAMpJ,EAASzE,MACTiF,OAAEA,EAAFoJ,YAAUA,EAAVmB,SAAuBA,GAAa/K,EAEtCQ,EAAO6N,MACTrO,EAAO6Z,cAET,IAAIjG,EAAiBhK,EAAc,EACnC,MAAMwgB,EAAkBxhB,IACtB,GAAuB,iBAAZA,EAAsB,CAC/B,MAAMohB,EAAUnvB,SAASnB,cAAc,OACvCswB,EAAQC,UAAYrhB,EACpBmC,EAASoO,QAAQ6Q,EAAQrwB,SAAS,IAClCqwB,EAAQC,UAAY,EACrB,MACClf,EAASoO,QAAQvQ,EAClB,EAEH,GAAsB,iBAAXQ,GAAuB,WAAYA,EAAQ,CACpD,IAAK,IAAItK,EAAI,EAAGA,EAAIsK,EAAOvQ,OAAQiG,GAAK,EAClCsK,EAAOtK,IAAIsrB,EAAehhB,EAAOtK,IAEvC8U,EAAiBhK,EAAcR,EAAOvQ,MACvC,MACCuxB,EAAehhB,GAEjBpJ,EAAOqZ,eACH7Y,EAAO6N,MACTrO,EAAOyY,aAEJjY,EAAO0pB,WAAYlqB,EAAO8I,WAC7B9I,EAAOgK,SAEThK,EAAOkW,QAAQtC,EAAgB,GAAG,EACnC,CClCc,SAASyW,GAASpiB,EAAOmB,GACtC,MAAMpJ,EAASzE,MACTiF,OAAEA,EAAFoJ,YAAUA,EAAVmB,SAAuBA,GAAa/K,EAC1C,IAAIsqB,EAAoB1gB,EACpBpJ,EAAO6N,OACTic,GAAqBtqB,EAAOuY,aAC5BvY,EAAO6Z,cACP7Z,EAAOqZ,gBAET,MAAMkR,EAAavqB,EAAOoJ,OAAOvQ,OACjC,GAAIoP,GAAS,EAEX,YADAjI,EAAOmqB,aAAa/gB,GAGtB,GAAInB,GAASsiB,EAEX,YADAvqB,EAAO8pB,YAAY1gB,GAGrB,IAAIwK,EAAiB0W,EAAoBriB,EAAQqiB,EAAoB,EAAIA,EAEzE,MAAME,EAAe,GACrB,IAAK,IAAI1rB,EAAIyrB,EAAa,EAAGzrB,GAAKmJ,EAAOnJ,GAAK,EAAG,CAC/C,MAAM2rB,EAAezqB,EAAOoJ,OAAOtK,GACnC2rB,EAAavhB,SACbshB,EAAa/hB,QAAQgiB,EACtB,CAED,GAAsB,iBAAXrhB,GAAuB,WAAYA,EAAQ,CACpD,IAAK,IAAItK,EAAI,EAAGA,EAAIsK,EAAOvQ,OAAQiG,GAAK,EAClCsK,EAAOtK,IAAIiM,EAASqO,OAAOhQ,EAAOtK,IAExC8U,EACE0W,EAAoBriB,EAAQqiB,EAAoBlhB,EAAOvQ,OAASyxB,CACnE,MACCvf,EAASqO,OAAOhQ,GAGlB,IAAK,IAAItK,EAAI,EAAGA,EAAI0rB,EAAa3xB,OAAQiG,GAAK,EAC5CiM,EAASqO,OAAOoR,EAAa1rB,IAG/BkB,EAAOqZ,eAEH7Y,EAAO6N,MACTrO,EAAOyY,aAEJjY,EAAO0pB,WAAYlqB,EAAO8I,WAC7B9I,EAAOgK,SAELxJ,EAAO6N,KACTrO,EAAOkW,QAAQtC,EAAiB5T,EAAOuY,aAAc,GAAG,GAExDvY,EAAOkW,QAAQtC,EAAgB,GAAG,EAErC,CCtDc,SAAS8W,GAAYC,GAClC,MAAM3qB,EAASzE,MACTiF,OAAEA,EAAFoJ,YAAUA,GAAgB5J,EAEhC,IAAIsqB,EAAoB1gB,EACpBpJ,EAAO6N,OACTic,GAAqBtqB,EAAOuY,aAC5BvY,EAAO6Z,eAET,IACI+Q,EADAhX,EAAiB0W,EAGrB,GAA6B,iBAAlBK,GAA8B,WAAYA,EAAe,CAClE,IAAK,IAAI7rB,EAAI,EAAGA,EAAI6rB,EAAc9xB,OAAQiG,GAAK,EAC7C8rB,EAAgBD,EAAc7rB,GAC1BkB,EAAOoJ,OAAOwhB,IAAgB5qB,EAAOoJ,OAAOwhB,GAAe1hB,SAC3D0hB,EAAgBhX,IAAgBA,GAAkB,GAExDA,EAAiBzS,KAAKC,IAAIwS,EAAgB,EAC3C,MACCgX,EAAgBD,EACZ3qB,EAAOoJ,OAAOwhB,IAAgB5qB,EAAOoJ,OAAOwhB,GAAe1hB,SAC3D0hB,EAAgBhX,IAAgBA,GAAkB,GACtDA,EAAiBzS,KAAKC,IAAIwS,EAAgB,GAG5C5T,EAAOqZ,eACH7Y,EAAO6N,MACTrO,EAAOyY,aAGJjY,EAAO0pB,WAAYlqB,EAAO8I,WAC7B9I,EAAOgK,SAELxJ,EAAO6N,KACTrO,EAAOkW,QAAQtC,EAAiB5T,EAAOuY,aAAc,GAAG,GAExDvY,EAAOkW,QAAQtC,EAAgB,GAAG,EAErC,CCvCc,SAASiX,KACtB,MAAM7qB,EAASzE,KAETovB,EAAgB,GACtB,IAAK,IAAI7rB,EAAI,EAAGA,EAAIkB,EAAOoJ,OAAOvQ,OAAQiG,GAAK,EAC7C6rB,EAAcjnB,KAAK5E,GAErBkB,EAAO0qB,YAAYC,EACpB,CnB4qBDryB,OAAOI,KAAK4qB,GAAY3qB,SAASmyB,IAC/BxyB,OAAOI,KAAK4qB,EAAWwH,IAAiBnyB,SAASoyB,IAC/CpE,EAAOxoB,UAAU4sB,GAAezH,EAAWwH,GAAgBC,EAA3D,GADF,IAKFpE,EAAOqE,IAAI,CoBxrBI,SAAsCjrB,GAAA,IAAtBC,OAAEA,EAAF6G,GAAUA,EAAVuB,KAAcA,GAAQrI,EACnD,MAAMzD,EAASF,IACf,IAAI8tB,EAAW,KACXe,EAAiB,KAErB,MAAMC,EAAgB,KACflrB,IAAUA,EAAOmH,WAAcnH,EAAOsU,cAC3ClM,EAAK,gBACLA,EAAK,UAAL,EAqCI+iB,EAA2B,KAC1BnrB,IAAUA,EAAOmH,WAAcnH,EAAOsU,aAC3ClM,EAAK,oBAAL,EAGFvB,EAAG,QAAQ,KACL7G,EAAOQ,OAAOiiB,qBAAmD,IAA1BnmB,EAAO8uB,eAvC7CprB,IAAUA,EAAOmH,WAAcnH,EAAOsU,cAC3C4V,EAAW,IAAIkB,gBAAgBlF,IAC7B+E,EAAiB3uB,EAAON,uBAAsB,KAC5C,MAAMoJ,MAAEA,EAAFE,OAASA,GAAWtF,EAC1B,IAAIqrB,EAAWjmB,EACX8K,EAAY5K,EAChB4gB,EAAQvtB,SAAQ2yB,IAA6C,IAA5CC,eAAEA,EAAFC,YAAkBA,EAAlBhzB,OAA+BA,GAAa8yB,EACvD9yB,GAAUA,IAAWwH,EAAOrD,KAChC0uB,EAAWG,EACPA,EAAYpmB,OACXmmB,EAAe,IAAMA,GAAgBE,WAC1Cvb,EAAYsb,EACRA,EAAYlmB,QACXimB,EAAe,IAAMA,GAAgBG,UAF1C,IAIEL,IAAajmB,GAAS8K,IAAc5K,GACtC4lB,GACD,GAfH,IAkBFhB,EAASyB,QAAQ3rB,EAAOrD,MAuBxBL,EAAOtD,iBAAiB,SAAUkyB,GAClC5uB,EAAOtD,iBAAiB,oBAAqBmyB,GAA7C,IAGFtkB,EAAG,WAAW,KAvBRokB,GACF3uB,EAAOJ,qBAAqB+uB,GAE1Bf,GAAYA,EAAS0B,WAAa5rB,EAAOrD,KAC3CutB,EAAS0B,UAAU5rB,EAAOrD,IAC1ButB,EAAW,MAoBb5tB,EAAOrD,oBAAoB,SAAUiyB,GACrC5uB,EAAOrD,oBAAoB,oBAAqBkyB,EAAhD,GAEH,EC/Dc,SAAsDprB,GAAA,IAApCC,OAAEA,EAAFinB,aAAUA,EAAVpgB,GAAwBA,EAAxBuB,KAA4BA,GAAQrI,EACnE,MAAM8rB,EAAY,GACZvvB,EAASF,IACT0vB,EAAS,SAACtzB,EAAQuzB,QAAiB,IAAjBA,MAAU,IAChC,MACM7B,EAAW,IADI5tB,EAAO0vB,kBAAoB1vB,EAAO2vB,yBACpBC,IAIjC,GAAIlsB,EAAO2jB,oBAAqB,OAChC,GAAyB,IAArBuI,EAAUrzB,OAEZ,YADAuP,EAAK,iBAAkB8jB,EAAU,IAInC,MAAMC,EAAiB,WACrB/jB,EAAK,iBAAkB8jB,EAAU,G,EAG/B5vB,EAAON,sBACTM,EAAON,sBAAsBmwB,GAE7B7vB,EAAOT,WAAWswB,EAAgB,EACnC,IAGHjC,EAASyB,QAAQnzB,EAAQ,CACvB4zB,gBAA0C,IAAvBL,EAAQK,YAAoCL,EAAQK,WACvEC,eAAwC,IAAtBN,EAAQM,WAAmCN,EAAQM,UACrEC,mBAAgD,IAA1BP,EAAQO,eAAuCP,EAAQO,gBAG/ET,EAAUnoB,KAAKwmB,E,EAyBjBjD,EAAa,CACXiD,UAAU,EACVqC,gBAAgB,EAChBC,sBAAsB,IAExB3lB,EAAG,QA5BU,KACX,GAAK7G,EAAOQ,OAAO0pB,SAAnB,CACA,GAAIlqB,EAAOQ,OAAO+rB,eAAgB,CAChC,MAAME,EAAmBnpB,EAAetD,EAAOrD,IAC/C,IAAK,IAAImC,EAAI,EAAGA,EAAI2tB,EAAiB5zB,OAAQiG,GAAK,EAChDgtB,EAAOW,EAAiB3tB,GAE3B,CAEDgtB,EAAO9rB,EAAOrD,GAAI,CAChB0vB,UAAWrsB,EAAOQ,OAAOgsB,uBAI3BV,EAAO9rB,EAAOU,UAAW,CAAE0rB,YAAY,GAbV,CAa7B,IAeFvlB,EAAG,WAba,KACdglB,EAAUlzB,SAASuxB,IACjBA,EAASwC,YAAT,IAEFb,EAAU3jB,OAAO,EAAG2jB,EAAUhzB,OAA9B,GAUH,IC9DD,MAAMiuB,GAAU,CCFD,SAAqD/mB,GAAA,IAc9D4sB,GAd0B3sB,OAAEA,EAAFinB,aAAUA,EAAVpgB,GAAwBA,EAAxBuB,KAA4BA,GAAQrI,EAClEknB,EAAa,CACX5b,QAAS,CACPC,SAAS,EACTlC,OAAQ,GACRwjB,OAAO,EACPC,YAAa,KACbC,eAAgB,KAChBC,sBAAsB,EACtBC,gBAAiB,EACjBC,eAAgB,KAKpB,MAAMpyB,EAAWF,IAEjBqF,EAAOqL,QAAU,CACfuhB,MAAO,GACPM,UAAMtuB,EACNF,QAAIE,EACJwK,OAAQ,GACR+jB,OAAQ,EACRzhB,WAAY,IAGd,MAAMse,EAAUnvB,EAASnB,cAAc,OAEvC,SAASmzB,EAAY3f,EAAOjF,GAC1B,MAAMzH,EAASR,EAAOQ,OAAO6K,QAC7B,GAAI7K,EAAOosB,OAAS5sB,EAAOqL,QAAQuhB,MAAM3kB,GACvC,OAAOjI,EAAOqL,QAAQuhB,MAAM3kB,GAG9B,IAAIW,EAkBJ,OAjBIpI,EAAOqsB,aACTjkB,EAAUpI,EAAOqsB,YAAYzuB,KAAK4B,EAAQkN,EAAOjF,GAC1B,iBAAZW,IACTohB,EAAQC,UAAYrhB,EACpBA,EAAUohB,EAAQrwB,SAAS,KAG7BiP,EADS5I,EAAO8I,UACNpP,EAAc,gBAEdA,EAAc,MAAOsG,EAAOQ,OAAOuI,YAE/CH,EAAQ9O,aAAa,0BAA2BmO,GAC3CzH,EAAOqsB,cACVjkB,EAAQqhB,UAAY/c,GAGlB1M,EAAOosB,QAAO5sB,EAAOqL,QAAQuhB,MAAM3kB,GAASW,GACzCA,CACR,CAED,SAASoB,EAAOojB,GACd,MAAM3jB,cAAEA,EAAFqE,eAAiBA,EAAjBrB,eAAiCA,EAAgB4B,KAAM6Q,GAAWlf,EAAOQ,QACzEwsB,gBAAEA,EAAFC,eAAmBA,GAAmBjtB,EAAOQ,OAAO6K,SAExD6hB,KAAMG,EACN3uB,GAAI4uB,EAFAlkB,OAGJA,EACAsC,WAAY6hB,EACZJ,OAAQK,GACNxtB,EAAOqL,QACNrL,EAAOQ,OAAOkM,SACjB1M,EAAO2T,oBAGT,MAAM/J,EAAc5J,EAAO4J,aAAe,EAE1C,IAAI6jB,EAIAjf,EACAD,EAJqBkf,EAArBztB,EAAOiL,aAA2B,QACpBjL,EAAOoK,eAAiB,OAAS,MAI/CqC,GACF+B,EAAcrN,KAAKwM,MAAMlE,EAAgB,GAAKqE,EAAiBmf,EAC/D1e,EAAepN,KAAKwM,MAAMlE,EAAgB,GAAKqE,EAAiBkf,IAEhExe,EAAc/E,GAAiBqE,EAAiB,GAAKmf,EACrD1e,GAAgB2Q,EAASzV,EAAgBqE,GAAkBkf,GAE7D,IAAIE,EAAOtjB,EAAc2E,EACrB7P,EAAKkL,EAAc4E,EAClB0Q,IACHgO,EAAO/rB,KAAKC,IAAI8rB,EAAM,GACtBxuB,EAAKyC,KAAKE,IAAI3C,EAAI0K,EAAOvQ,OAAS,IAEpC,IAAIs0B,GAAUntB,EAAO0L,WAAWwhB,IAAS,IAAMltB,EAAO0L,WAAW,IAAM,GAkBvE,SAASgiB,IACP1tB,EAAOyK,eACPzK,EAAOwR,iBACPxR,EAAO0S,sBACPtK,EAAK,gBACN,CAED,GAxBI8W,GAAUtV,GAAe2E,GAC3B2e,GAAQ3e,EACH9B,IAAgB0gB,GAAUntB,EAAO0L,WAAW,KACxCwT,GAAUtV,EAAc2E,IACjC2e,GAAQ3e,EACJ9B,IAAgB0gB,GAAUntB,EAAO0L,WAAW,KAGlDpT,OAAOkS,OAAOxK,EAAOqL,QAAS,CAC5B6hB,OACAxuB,KACAyuB,SACAzhB,WAAY1L,EAAO0L,WACnB6C,eACAC,gBAUE6e,IAAiBH,GAAQI,IAAe5uB,IAAO0uB,EAQjD,OAPIptB,EAAO0L,aAAe6hB,GAAsBJ,IAAWK,GACzDxtB,EAAOoJ,OAAOzQ,SAASiQ,IACrBA,EAAQ/O,MAAM4zB,GAAiBN,EAAShsB,KAAK0M,IAAI7N,EAAO4Q,yBAA3B,IAA7B,IAGJ5Q,EAAOwR,sBACPpJ,EAAK,iBAGP,GAAIpI,EAAOQ,OAAO6K,QAAQyhB,eAkBxB,OAjBA9sB,EAAOQ,OAAO6K,QAAQyhB,eAAe1uB,KAAK4B,EAAQ,CAChDmtB,SACAD,OACAxuB,KACA0K,OAAS,WACP,MAAMukB,EAAiB,GACvB,IAAK,IAAI7uB,EAAIouB,EAAMpuB,GAAKJ,EAAII,GAAK,EAC/B6uB,EAAejqB,KAAK0F,EAAOtK,IAE7B,OAAO6uB,C,CALA,UAQP3tB,EAAOQ,OAAO6K,QAAQ0hB,qBACxBW,IAEAtlB,EAAK,kBAIT,MAAMwlB,EAAiB,GACjBC,EAAgB,GAEhBrV,EAAiBvQ,IACrB,IAAI0G,EAAa1G,EAOjB,OANIA,EAAQ,EACV0G,EAAavF,EAAOvQ,OAASoP,EACpB0G,GAAcvF,EAAOvQ,SAE9B8V,GAA0BvF,EAAOvQ,QAE5B8V,CAAP,EAGF,GAAIye,EACFptB,EAAO+K,SACJzR,iBAAkB,IAAG0G,EAAOQ,OAAOuI,4BACnCpQ,SAASiQ,IACRA,EAAQM,QAAR,SAGJ,IAAK,IAAIpK,EAAIuuB,EAAcvuB,GAAKwuB,EAAYxuB,GAAK,EAC/C,GAAIA,EAAIouB,GAAQpuB,EAAIJ,EAAI,CACtB,MAAMiQ,EAAa6J,EAAc1Z,GACjCkB,EAAO+K,SACJzR,iBACE,IAAG0G,EAAOQ,OAAOuI,uCAAuC4F,8CAAuDA,OAEjHhW,SAASiQ,IACRA,EAAQM,QAAR,GAEL,CAIL,MAAM4kB,EAAW5O,GAAU9V,EAAOvQ,OAAS,EACrCk1B,EAAS7O,EAAyB,EAAhB9V,EAAOvQ,OAAauQ,EAAOvQ,OACnD,IAAK,IAAIiG,EAAIgvB,EAAUhvB,EAAIivB,EAAQjvB,GAAK,EACtC,GAAIA,GAAKouB,GAAQpuB,GAAKJ,EAAI,CACxB,MAAMiQ,EAAa6J,EAAc1Z,QACP,IAAfwuB,GAA8BF,EACvCS,EAAcnqB,KAAKiL,IAEf7P,EAAIwuB,GAAYO,EAAcnqB,KAAKiL,GACnC7P,EAAIuuB,GAAcO,EAAelqB,KAAKiL,GAE7C,CAKH,GAHAkf,EAAcl1B,SAASsP,IACrBjI,EAAO+K,SAASqO,OAAOyT,EAAYzjB,EAAOnB,GAAQA,GAAlD,IAEEiX,EACF,IAAK,IAAIpgB,EAAI8uB,EAAe/0B,OAAS,EAAGiG,GAAK,EAAGA,GAAK,EAAG,CACtD,MAAMmJ,EAAQ2lB,EAAe9uB,GAC7BkB,EAAO+K,SAASoO,QAAQ0T,EAAYzjB,EAAOnB,GAAQA,GACpD,MAED2lB,EAAejI,MAAK,CAACpoB,EAAGqoB,IAAMA,EAAIroB,IAClCqwB,EAAej1B,SAASsP,IACtBjI,EAAO+K,SAASoO,QAAQ0T,EAAYzjB,EAAOnB,GAAQA,GAAnD,IAGJrG,EAAgB5B,EAAO+K,SAAU,+BAA+BpS,SAASiQ,IACvEA,EAAQ/O,MAAM4zB,GAAiBN,EAAShsB,KAAK0M,IAAI7N,EAAO4Q,yBAA3B,IAA7B,IAEF8c,GACD,CA6ED7mB,EAAG,cAAc,KACf,IAAK7G,EAAOQ,OAAO6K,QAAQC,QAAS,OACpC,IAAI0iB,EACJ,QAAkD,IAAvChuB,EAAOmnB,aAAa9b,QAAQjC,OAAwB,CAC7D,MAAMA,EAAS,IAAIpJ,EAAO+K,SAASpR,UAAUsF,QAAQtC,GACnDA,EAAGoF,QAAS,IAAG/B,EAAOQ,OAAOuI,8BAE3BK,GAAUA,EAAOvQ,SACnBmH,EAAOqL,QAAQjC,OAAS,IAAIA,GAC5B4kB,GAAoB,EACpB5kB,EAAOzQ,SAAQ,CAACiQ,EAAS+F,KACvB/F,EAAQ9O,aAAa,0BAA2B6U,GAChD3O,EAAOqL,QAAQuhB,MAAMje,GAAc/F,EACnCA,EAAQM,QAAR,IAGL,CACI8kB,IACHhuB,EAAOqL,QAAQjC,OAASpJ,EAAOQ,OAAO6K,QAAQjC,QAGhDpJ,EAAOgmB,WAAWtiB,KAAM,GAAE1D,EAAOQ,OAAOoP,iCAExC5P,EAAOQ,OAAOiP,qBAAsB,EACpCzP,EAAOqkB,eAAe5U,qBAAsB,EAEvCzP,EAAOQ,OAAOsW,cACjB9M,GACD,IAEHnD,EAAG,gBAAgB,KACZ7G,EAAOQ,OAAO6K,QAAQC,UACvBtL,EAAOQ,OAAOkM,UAAY1M,EAAO4W,mBACnC9a,aAAa6wB,GACbA,EAAiB9wB,YAAW,KAC1BmO,GAAM,GACL,MAEHA,IACD,IAEHnD,EAAG,sBAAsB,KAClB7G,EAAOQ,OAAO6K,QAAQC,SACvBtL,EAAOQ,OAAOkM,SAChBhN,EAAeM,EAAOU,UAAW,wBAA0B,GAAEV,EAAOqM,gBACrE,IAGH/T,OAAOkS,OAAOxK,EAAOqL,QAAS,CAC5Bye,YA5HF,SAAqB1gB,GACnB,GAAsB,iBAAXA,GAAuB,WAAYA,EAC5C,IAAK,IAAItK,EAAI,EAAGA,EAAIsK,EAAOvQ,OAAQiG,GAAK,EAClCsK,EAAOtK,IAAIkB,EAAOqL,QAAQjC,OAAO1F,KAAK0F,EAAOtK,SAGnDkB,EAAOqL,QAAQjC,OAAO1F,KAAK0F,GAE7BY,GAAO,EACR,EAoHCmgB,aAnHF,SAAsB/gB,GACpB,MAAMQ,EAAc5J,EAAO4J,YAC3B,IAAIgK,EAAiBhK,EAAc,EAC/BqkB,EAAoB,EAExB,GAAI7rB,MAAMC,QAAQ+G,GAAS,CACzB,IAAK,IAAItK,EAAI,EAAGA,EAAIsK,EAAOvQ,OAAQiG,GAAK,EAClCsK,EAAOtK,IAAIkB,EAAOqL,QAAQjC,OAAOX,QAAQW,EAAOtK,IAEtD8U,EAAiBhK,EAAcR,EAAOvQ,OACtCo1B,EAAoB7kB,EAAOvQ,MAC5B,MACCmH,EAAOqL,QAAQjC,OAAOX,QAAQW,GAEhC,GAAIpJ,EAAOQ,OAAO6K,QAAQuhB,MAAO,CAC/B,MAAMA,EAAQ5sB,EAAOqL,QAAQuhB,MACvBsB,EAAW,GACjB51B,OAAOI,KAAKk0B,GAAOj0B,SAASw1B,IAC1B,MAAMC,EAAWxB,EAAMuB,GACjBE,EAAgBD,EAAS/Z,aAAa,2BACxCga,GACFD,EAASt0B,aACP,0BACAwQ,SAAS+jB,EAAe,IAAMJ,GAGlCC,EAAS5jB,SAAS6jB,EAAa,IAAMF,GAAqBG,CAA1D,IAEFpuB,EAAOqL,QAAQuhB,MAAQsB,CACxB,CACDlkB,GAAO,GACPhK,EAAOkW,QAAQtC,EAAgB,EAChC,EAoFC8W,YAnFF,SAAqBC,GACnB,GAAI,MAAOA,EAAyD,OACpE,IAAI/gB,EAAc5J,EAAO4J,YACzB,GAAIxH,MAAMC,QAAQsoB,GAChB,IAAK,IAAI7rB,EAAI6rB,EAAc9xB,OAAS,EAAGiG,GAAK,EAAGA,GAAK,EAClDkB,EAAOqL,QAAQjC,OAAOlB,OAAOyiB,EAAc7rB,GAAI,GAC3CkB,EAAOQ,OAAO6K,QAAQuhB,cACjB5sB,EAAOqL,QAAQuhB,MAAMjC,EAAc7rB,IAExC6rB,EAAc7rB,GAAK8K,IAAaA,GAAe,GACnDA,EAAczI,KAAKC,IAAIwI,EAAa,QAGtC5J,EAAOqL,QAAQjC,OAAOlB,OAAOyiB,EAAe,GACxC3qB,EAAOQ,OAAO6K,QAAQuhB,cACjB5sB,EAAOqL,QAAQuhB,MAAMjC,GAE1BA,EAAgB/gB,IAAaA,GAAe,GAChDA,EAAczI,KAAKC,IAAIwI,EAAa,GAEtCI,GAAO,GACPhK,EAAOkW,QAAQtM,EAAa,EAC7B,EA8DCihB,gBA7DF,WACE7qB,EAAOqL,QAAQjC,OAAS,GACpBpJ,EAAOQ,OAAO6K,QAAQuhB,QACxB5sB,EAAOqL,QAAQuhB,MAAQ,IAEzB5iB,GAAO,GACPhK,EAAOkW,QAAQ,EAAG,EACnB,EAuDClM,UAEH,ECtVc,SAAsDjK,GAAA,IAApCC,OAAEA,EAAFinB,aAAUA,EAAVpgB,GAAwBA,EAAxBuB,KAA4BA,GAAQrI,EACnE,MAAMlF,EAAWF,IACX2B,EAASF,IAYf,SAASkyB,EAAOjnB,GACd,IAAKrH,EAAOsL,QAAS,OAErB,MAAQL,aAAcC,GAAQlL,EAC9B,IAAI6D,EAAIwD,EACJxD,EAAEwW,gBAAexW,EAAIA,EAAEwW,eAC3B,MAAMkU,EAAK1qB,EAAE2qB,SAAW3qB,EAAE4qB,SACpBC,EAAa1uB,EAAOQ,OAAOmuB,SAASD,WACpCE,EAAWF,GAAqB,KAAPH,EACzBM,EAAaH,GAAqB,KAAPH,EAC3BO,EAAqB,KAAPP,EACdQ,EAAsB,KAAPR,EACfS,EAAmB,KAAPT,EACZU,EAAqB,KAAPV,EAEpB,IACGvuB,EAAOuW,iBACNvW,EAAOoK,gBAAkB2kB,GACxB/uB,EAAOqK,cAAgB4kB,GACxBJ,GAEF,OAAO,EAET,IACG7uB,EAAOwW,iBACNxW,EAAOoK,gBAAkB0kB,GAAiB9uB,EAAOqK,cAAgB2kB,GAAcJ,GAEjF,OAAO,EAET,KAAI/qB,EAAEqrB,UAAYrrB,EAAEsrB,QAAUtrB,EAAEurB,SAAWvrB,EAAEwrB,SAI3Cx0B,EAAS3B,eACT2B,EAAS3B,cAAcE,WAC4B,UAAlDyB,EAAS3B,cAAcE,SAAS8M,eACmB,aAAlDrL,EAAS3B,cAAcE,SAAS8M,gBAJpC,CAQA,GACElG,EAAOQ,OAAOmuB,SAASW,iBACtBV,GAAYC,GAAcC,GAAeC,GAAgBC,GAAaC,GACvE,CACA,IAAIM,GAAS,EAEb,GACEjsB,EAAetD,EAAOrD,GAAK,IAAGqD,EAAOQ,OAAOuI,4BAA4BlQ,OAAS,GACN,IAA3EyK,EAAetD,EAAOrD,GAAK,IAAGqD,EAAOQ,OAAOqS,oBAAoBha,OAEhE,OAGF,MAAM8D,EAAKqD,EAAOrD,GACZ6yB,EAAc7yB,EAAGuN,YACjBulB,EAAe9yB,EAAGwN,aAClBulB,EAAcpzB,EAAOigB,WACrBoT,EAAerzB,EAAO+oB,YACtBuK,EAAettB,EAAc3F,GAC/BuO,IAAK0kB,EAAa5sB,MAAQrG,EAAGkG,YACjC,MAAMgtB,EAAc,CAClB,CAACD,EAAa5sB,KAAM4sB,EAAa7sB,KACjC,CAAC6sB,EAAa5sB,KAAOwsB,EAAaI,EAAa7sB,KAC/C,CAAC6sB,EAAa5sB,KAAM4sB,EAAa7sB,IAAM0sB,GACvC,CAACG,EAAa5sB,KAAOwsB,EAAaI,EAAa7sB,IAAM0sB,IAEvD,IAAK,IAAI3wB,EAAI,EAAGA,EAAI+wB,EAAYh3B,OAAQiG,GAAK,EAAG,CAC9C,MAAMymB,EAAQsK,EAAY/wB,GAC1B,GAAIymB,EAAM,IAAM,GAAKA,EAAM,IAAMmK,GAAenK,EAAM,IAAM,GAAKA,EAAM,IAAMoK,EAAc,CACzF,GAAiB,IAAbpK,EAAM,IAAyB,IAAbA,EAAM,GAAU,SACtCgK,GAAS,CACV,CACF,CACD,IAAKA,EAAQ,MACd,CACGvvB,EAAOoK,iBACLwkB,GAAYC,GAAcC,GAAeC,KACvClrB,EAAE2Y,eAAgB3Y,EAAE2Y,iBACnB3Y,EAAEisB,aAAc,KAEjBjB,GAAcE,KAAkB7jB,IAAU0jB,GAAYE,IAAgB5jB,IAC1ElL,EAAOkX,cACH0X,GAAYE,KAAiB5jB,IAAU2jB,GAAcE,IAAiB7jB,IAC1ElL,EAAOyX,eAELmX,GAAYC,GAAcG,GAAaC,KACrCprB,EAAE2Y,eAAgB3Y,EAAE2Y,iBACnB3Y,EAAEisB,aAAc,IAEnBjB,GAAcI,IAAajvB,EAAOkX,aAClC0X,GAAYI,IAAWhvB,EAAOyX,aAEpCrP,EAAK,WAAYmmB,EArDhB,CAuDF,CACD,SAASzJ,IACH9kB,EAAO2uB,SAASrjB,UACpBzQ,EAAS7B,iBAAiB,UAAWs1B,GACrCtuB,EAAO2uB,SAASrjB,SAAU,EAC3B,CACD,SAASuZ,IACF7kB,EAAO2uB,SAASrjB,UACrBzQ,EAAS5B,oBAAoB,UAAWq1B,GACxCtuB,EAAO2uB,SAASrjB,SAAU,EAC3B,CAnHDtL,EAAO2uB,SAAW,CAChBrjB,SAAS,GAEX2b,EAAa,CACX0H,SAAU,CACRrjB,SAAS,EACTgkB,gBAAgB,EAChBZ,YAAY,KA8GhB7nB,EAAG,QAAQ,KACL7G,EAAOQ,OAAOmuB,SAASrjB,SACzBwZ,GACD,IAEHje,EAAG,WAAW,KACR7G,EAAO2uB,SAASrjB,SAClBuZ,GACD,IAGHvsB,OAAOkS,OAAOxK,EAAO2uB,SAAU,CAC7B7J,SACAD,WAEH,ECvIc,SAAwD9kB,GAAA,IAApCC,OAAEA,EAAFinB,aAAUA,EAAVpgB,GAAwBA,EAAxBuB,KAA4BA,GAAQrI,EACrE,MAAMzD,EAASF,IAmBf,IAAI2zB,EAjBJ9I,EAAa,CACX+I,WAAY,CACV1kB,SAAS,EACT2kB,gBAAgB,EAChBC,QAAQ,EACRC,aAAa,EACbC,YAAa,EACbC,aAAc,YACdC,eAAgB,KAChBC,cAAe,QAInBvwB,EAAOgwB,WAAa,CAClB1kB,SAAS,GAIX,IACIklB,EADAC,EAAiBh0B,IAErB,MAAMi0B,EAAoB,GA4E1B,SAASC,IACF3wB,EAAOsL,UACZtL,EAAO4wB,cAAe,EACvB,CACD,SAASC,IACF7wB,EAAOsL,UACZtL,EAAO4wB,cAAe,EACvB,CACD,SAASE,EAAcC,GACrB,QACE/wB,EAAOQ,OAAOwvB,WAAWM,gBACzBS,EAASC,MAAQhxB,EAAOQ,OAAOwvB,WAAWM,oBAO1CtwB,EAAOQ,OAAOwvB,WAAWO,eACzB9zB,IAAQg0B,EAAiBzwB,EAAOQ,OAAOwvB,WAAWO,iBAShDQ,EAASC,OAAS,GAAKv0B,IAAQg0B,EAAiB,KAgBhDM,EAAS/a,UAAY,EACjBhW,EAAO6R,QAAS7R,EAAOQ,OAAO6N,MAAUrO,EAAOyV,YACnDzV,EAAOkX,YACP9O,EAAK,SAAU2oB,EAASE,MAEfjxB,EAAO4R,cAAe5R,EAAOQ,OAAO6N,MAAUrO,EAAOyV,YAChEzV,EAAOyX,YACPrP,EAAK,SAAU2oB,EAASE,MAG1BR,GAAiB,IAAIn0B,EAAOX,MAAOsF,WAE5B,IACR,CAcD,SAASqtB,EAAOjnB,GACd,IAAIxD,EAAIwD,EACJsY,GAAsB,EAC1B,IAAK3f,EAAOsL,QAAS,OACrB,MAAM9K,EAASR,EAAOQ,OAAOwvB,WAEzBhwB,EAAOQ,OAAOkM,SAChB7I,EAAE2Y,iBAGJ,IAAIlC,EAAWta,EAAOrD,GACwB,cAA1CqD,EAAOQ,OAAOwvB,WAAWK,eAC3B/V,EAAWzf,SAASxB,cAAc2G,EAAOQ,OAAOwvB,WAAWK,eAE7D,MAAMa,EAAyB5W,GAAYA,EAASxK,SAASjM,EAAErL,QAC/D,IAAKwH,EAAO4wB,eAAiBM,IAA2B1wB,EAAOyvB,eAAgB,OAAO,EAElFpsB,EAAEwW,gBAAexW,EAAIA,EAAEwW,eAC3B,IAAI2W,EAAQ,EACZ,MAAMG,EAAYnxB,EAAOiL,cAAgB,EAAI,EAEvC5C,EAtKR,SAAmBxE,GAMjB,IAAIutB,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EA0DT,MAvDI,WAAY1tB,IACdwtB,EAAKxtB,EAAE2tB,QAEL,eAAgB3tB,IAClBwtB,GAAMxtB,EAAE4tB,WAAa,KAEnB,gBAAiB5tB,IACnBwtB,GAAMxtB,EAAE6tB,YAAc,KAEpB,gBAAiB7tB,IACnButB,GAAMvtB,EAAE8tB,YAAc,KAIpB,SAAU9tB,GAAKA,EAAEjH,OAASiH,EAAE+tB,kBAC9BR,EAAKC,EACLA,EAAK,GAGPC,EA7BmB,GA6BdF,EACLG,EA9BmB,GA8BdF,EAED,WAAYxtB,IACd0tB,EAAK1tB,EAAEguB,QAEL,WAAYhuB,IACdytB,EAAKztB,EAAEiuB,QAGLjuB,EAAEqrB,WAAaoC,IAEjBA,EAAKC,EACLA,EAAK,IAGFD,GAAMC,IAAO1tB,EAAEkuB,YACE,IAAhBluB,EAAEkuB,WAEJT,GA/CgB,GAgDhBC,GAhDgB,KAmDhBD,GAlDgB,IAmDhBC,GAnDgB,MAwDhBD,IAAOF,IACTA,EAAKE,EAAK,GAAK,EAAI,GAEjBC,IAAOF,IACTA,EAAKE,EAAK,GAAK,EAAI,GAGd,CACLS,MAAOZ,EACPa,MAAOZ,EACPa,OAAQZ,EACRa,OAAQZ,EAEX,CA6Fc7Z,CAAU7T,GAEvB,GAAIrD,EAAO2vB,YACT,GAAInwB,EAAOoK,eAAgB,CACzB,KAAIjJ,KAAK0M,IAAIxF,EAAK6pB,QAAU/wB,KAAK0M,IAAIxF,EAAK8pB,SACrC,OAAO,EADuCnB,GAAS3oB,EAAK6pB,OAASf,CAE3E,KAAM,MAAIhwB,KAAK0M,IAAIxF,EAAK8pB,QAAUhxB,KAAK0M,IAAIxF,EAAK6pB,SAC5C,OAAO,EAD8ClB,GAAS3oB,EAAK8pB,MACnE,MAELnB,EACE7vB,KAAK0M,IAAIxF,EAAK6pB,QAAU/wB,KAAK0M,IAAIxF,EAAK8pB,SAAW9pB,EAAK6pB,OAASf,GAAa9oB,EAAK8pB,OAGrF,GAAc,IAAVnB,EAAa,OAAO,EAEpBxwB,EAAO0vB,SAAQc,GAASA,GAG5B,IAAIoB,EAAYpyB,EAAOtD,eAAiBs0B,EAAQxwB,EAAO4vB,YAkBvD,GAhBIgC,GAAapyB,EAAOmR,iBAAgBihB,EAAYpyB,EAAOmR,gBACvDihB,GAAapyB,EAAO2R,iBAAgBygB,EAAYpyB,EAAO2R,gBAS3DgO,IAAsB3f,EAAOQ,OAAO6N,QAE9B+jB,IAAcpyB,EAAOmR,gBAAkBihB,IAAcpyB,EAAO2R,gBAE9DgO,GAAuB3f,EAAOQ,OAAOie,QAAQ5a,EAAE6a,kBAE9C1e,EAAOQ,OAAO6c,UAAard,EAAOQ,OAAO6c,SAAS/R,QAyChD,CAOL,MAAMylB,EAAW,CACf1wB,KAAM5D,IACNu0B,MAAO7vB,KAAK0M,IAAImjB,GAChBhb,UAAW7U,KAAKkxB,KAAKrB,IAGjBsB,EACJ9B,GACAO,EAAS1wB,KAAOmwB,EAAoBnwB,KAAO,KAC3C0wB,EAASC,OAASR,EAAoBQ,OACtCD,EAAS/a,YAAcwa,EAAoBxa,UAC7C,IAAKsc,EAAmB,CACtB9B,OAAsB5xB,EAEtB,IAAI2zB,EAAWvyB,EAAOtD,eAAiBs0B,EAAQxwB,EAAO4vB,YACtD,MAAMre,EAAe/R,EAAO4R,YACtBI,EAAShS,EAAO6R,MAqBtB,GAnBI0gB,GAAYvyB,EAAOmR,iBAAgBohB,EAAWvyB,EAAOmR,gBACrDohB,GAAYvyB,EAAO2R,iBAAgB4gB,EAAWvyB,EAAO2R,gBAEzD3R,EAAOmQ,cAAc,GACrBnQ,EAAO+U,aAAawd,GACpBvyB,EAAOwR,iBACPxR,EAAO2T,oBACP3T,EAAO0S,wBAEDX,GAAgB/R,EAAO4R,cAAkBI,GAAUhS,EAAO6R,QAC9D7R,EAAO0S,sBAEL1S,EAAOQ,OAAO6N,MAChBrO,EAAOuX,QAAQ,CACbvB,UAAW+a,EAAS/a,UAAY,EAAI,OAAS,OAC7C4C,cAAc,IAId5Y,EAAOQ,OAAO6c,SAASmV,OAAQ,CAYjC12B,aAAai0B,GACbA,OAAUnxB,EACN8xB,EAAkB73B,QAAU,IAC9B63B,EAAkB+B,QAEpB,MAAMC,EAAYhC,EAAkB73B,OAChC63B,EAAkBA,EAAkB73B,OAAS,QAC7C+F,EACE+zB,EAAajC,EAAkB,GAErC,GADAA,EAAkBhtB,KAAKqtB,GAErB2B,IACC3B,EAASC,MAAQ0B,EAAU1B,OAASD,EAAS/a,YAAc0c,EAAU1c,WAGtE0a,EAAkBxoB,OAAO,QACpB,GACLwoB,EAAkB73B,QAAU,IAC5Bk4B,EAAS1wB,KAAOsyB,EAAWtyB,KAAO,KAClCsyB,EAAW3B,MAAQD,EAASC,OAAS,GACrCD,EAASC,OAAS,EAClB,CAOA,MAAM4B,EAAkB5B,EAAQ,EAAI,GAAM,GAC1CR,EAAsBO,EACtBL,EAAkBxoB,OAAO,GACzB6nB,EAAUxzB,GAAS,KACjByD,EAAOkY,eAAelY,EAAOQ,OAAOC,OAAO,OAAM7B,EAAWg0B,EAA5D,GACC,EACJ,CACI7C,IAIHA,EAAUxzB,GAAS,KAEjBi0B,EAAsBO,EACtBL,EAAkBxoB,OAAO,GACzBlI,EAAOkY,eAAelY,EAAOQ,OAAOC,OAAO,OAAM7B,EAHzB,GAGxB,GACC,KAEN,CASD,GANK0zB,GAAmBlqB,EAAK,SAAUvE,GAGnC7D,EAAOQ,OAAO4gB,UAAYphB,EAAOQ,OAAOqyB,8BAC1C7yB,EAAOohB,SAAS0R,OAEdP,IAAavyB,EAAOmR,gBAAkBohB,IAAavyB,EAAO2R,eAAgB,OAAO,CACtF,CACF,KA1J+D,CAE9D,MAAMof,EAAW,CACf1wB,KAAM5D,IACNu0B,MAAO7vB,KAAK0M,IAAImjB,GAChBhb,UAAW7U,KAAKkxB,KAAKrB,GACrBC,IAAK5pB,GAIHqpB,EAAkB73B,QAAU,GAC9B63B,EAAkB+B,QAEpB,MAAMC,EAAYhC,EAAkB73B,OAChC63B,EAAkBA,EAAkB73B,OAAS,QAC7C+F,EAuBJ,GAtBA8xB,EAAkBhtB,KAAKqtB,GAQnB2B,GAEA3B,EAAS/a,YAAc0c,EAAU1c,WACjC+a,EAASC,MAAQ0B,EAAU1B,OAC3BD,EAAS1wB,KAAOqyB,EAAUryB,KAAO,MAEjCywB,EAAcC,GAGhBD,EAAcC,GAvGpB,SAAuBA,GACrB,MAAMvwB,EAASR,EAAOQ,OAAOwvB,WAC7B,GAAIe,EAAS/a,UAAY,GACvB,GAAIhW,EAAO6R,QAAU7R,EAAOQ,OAAO6N,MAAQ7N,EAAOyvB,eAEhD,OAAO,OAEJ,GAAIjwB,EAAO4R,cAAgB5R,EAAOQ,OAAO6N,MAAQ7N,EAAOyvB,eAE7D,OAAO,EAET,OAAO,CACR,CAgGO8C,CAAchC,GAChB,OAAO,CAEV,CAqHD,OAFIltB,EAAE2Y,eAAgB3Y,EAAE2Y,iBACnB3Y,EAAEisB,aAAc,GACd,CACR,CAED,SAAShpB,EAAOM,GACd,IAAIkT,EAAWta,EAAOrD,GACwB,cAA1CqD,EAAOQ,OAAOwvB,WAAWK,eAC3B/V,EAAWzf,SAASxB,cAAc2G,EAAOQ,OAAOwvB,WAAWK,eAE7D/V,EAASlT,GAAQ,aAAcupB,GAC/BrW,EAASlT,GAAQ,aAAcypB,GAC/BvW,EAASlT,GAAQ,QAASknB,EAC3B,CAED,SAASxJ,IACP,OAAI9kB,EAAOQ,OAAOkM,SAChB1M,EAAOU,UAAUzH,oBAAoB,QAASq1B,IACvC,IAELtuB,EAAOgwB,WAAW1kB,UACtBxE,EAAO,oBACP9G,EAAOgwB,WAAW1kB,SAAU,GACrB,EACR,CACD,SAASuZ,IACP,OAAI7kB,EAAOQ,OAAOkM,SAChB1M,EAAOU,UAAU1H,iBAAiBqO,MAAOinB,IAClC,KAEJtuB,EAAOgwB,WAAW1kB,UACvBxE,EAAO,uBACP9G,EAAOgwB,WAAW1kB,SAAU,GACrB,EACR,CAEDzE,EAAG,QAAQ,MACJ7G,EAAOQ,OAAOwvB,WAAW1kB,SAAWtL,EAAOQ,OAAOkM,SACrDmY,IAEE7kB,EAAOQ,OAAOwvB,WAAW1kB,SAASwZ,GAAM,IAE9Cje,EAAG,WAAW,KACR7G,EAAOQ,OAAOkM,SAChBoY,IAEE9kB,EAAOgwB,WAAW1kB,SAASuZ,GAAO,IAGxCvsB,OAAOkS,OAAOxK,EAAOgwB,WAAY,CAC/BlL,SACAD,WAEH,ECtbc,SAAwD9kB,GAAA,IAApCC,OAAEA,EAAFinB,aAAUA,EAAVpgB,GAAwBA,EAAxBuB,KAA4BA,GAAQrI,EACrEknB,EAAa,CACXnG,WAAY,CACVC,OAAQ,KACRC,OAAQ,KAERgS,aAAa,EACbC,cAAe,yBACfC,YAAa,uBACbC,UAAW,qBACXC,wBAAyB,gCAI7BpzB,EAAO8gB,WAAa,CAClBC,OAAQ,KACRC,OAAQ,MAGV,MAAMqS,EAAqB12B,IACpByF,MAAMC,QAAQ1F,KAAKA,EAAK,CAACA,GAAIsC,QAAQ4E,KAAQA,KAC3ClH,GAGT,SAAS22B,EAAM32B,GACb,IAAI42B,EACJ,OAAI52B,GAAoB,iBAAPA,GAAmBqD,EAAO8I,YACzCyqB,EAAMvzB,EAAOrD,GAAGse,WAAW5hB,cAAcsD,GACrC42B,GAAYA,GAEd52B,IACgB,iBAAPA,IAAiB42B,EAAM,IAAI14B,SAASvB,iBAAiBqD,KAE9DqD,EAAOQ,OAAOqiB,mBACA,iBAAPlmB,GACP42B,EAAI16B,OAAS,GAC6B,IAA1CmH,EAAOrD,GAAGrD,iBAAiBqD,GAAI9D,SAE/B06B,EAAMvzB,EAAOrD,GAAGtD,cAAcsD,KAG9BA,IAAO42B,EAAY52B,EAEhB42B,EACR,CAED,SAASC,EAAS72B,EAAI82B,GACpB,MAAMjzB,EAASR,EAAOQ,OAAOsgB,YAC7BnkB,EAAK02B,EAAkB12B,IACpBhE,SAAS+6B,IACNA,IACFA,EAAMxxB,UAAUuxB,EAAW,MAAQ,aAAajzB,EAAOyyB,cAAc51B,MAAM,MACrD,WAAlBq2B,EAAMC,UAAsBD,EAAMD,SAAWA,GAC7CzzB,EAAOQ,OAAO+O,eAAiBvP,EAAOsL,SACxCooB,EAAMxxB,UAAUlC,EAAO0jB,SAAW,MAAQ,UAAUljB,EAAO2yB,WAE9D,GAEJ,CACD,SAASnpB,IAEP,MAAM+W,OAAEA,EAAFC,OAAUA,GAAWhhB,EAAO8gB,WAClC,GAAI9gB,EAAOQ,OAAO6N,KAGhB,OAFAmlB,EAASxS,GAAQ,QACjBwS,EAASzS,GAAQ,GAInByS,EAASxS,EAAQhhB,EAAO4R,cAAgB5R,EAAOQ,OAAOsJ,QACtD0pB,EAASzS,EAAQ/gB,EAAO6R,QAAU7R,EAAOQ,OAAOsJ,OACjD,CACD,SAAS8pB,EAAY/vB,GACnBA,EAAE2Y,mBACExc,EAAO4R,aAAgB5R,EAAOQ,OAAO6N,MAASrO,EAAOQ,OAAOsJ,UAChE9J,EAAOyX,YACPrP,EAAK,kBACN,CACD,SAASyrB,EAAYhwB,GACnBA,EAAE2Y,mBACExc,EAAO6R,OAAU7R,EAAOQ,OAAO6N,MAASrO,EAAOQ,OAAOsJ,UAC1D9J,EAAOkX,YACP9O,EAAK,kBACN,CACD,SAASoa,IACP,MAAMhiB,EAASR,EAAOQ,OAAOsgB,WAW7B,GATA9gB,EAAOQ,OAAOsgB,WAAa6I,EACzB3pB,EACAA,EAAOqkB,eAAevD,WACtB9gB,EAAOQ,OAAOsgB,WACd,CACEC,OAAQ,qBACRC,OAAQ,wBAGNxgB,EAAOugB,SAAUvgB,EAAOwgB,OAAS,OAEvC,IAAID,EAASuS,EAAM9yB,EAAOugB,QACtBC,EAASsS,EAAM9yB,EAAOwgB,QAE1B1oB,OAAOkS,OAAOxK,EAAO8gB,WAAY,CAC/BC,SACAC,WAEFD,EAASsS,EAAkBtS,GAC3BC,EAASqS,EAAkBrS,GAE3B,MAAM8S,EAAa,CAACn3B,EAAIkE,KAClBlE,GACFA,EAAG3D,iBAAiB,QAAiB,SAAR6H,EAAiBgzB,EAAcD,IAEzD5zB,EAAOsL,SAAW3O,GACrBA,EAAGuF,UAAUC,OAAO3B,EAAO2yB,UAAU91B,MAAM,KAC5C,EAGH0jB,EAAOpoB,SAASgE,GAAOm3B,EAAWn3B,EAAI,UACtCqkB,EAAOroB,SAASgE,GAAOm3B,EAAWn3B,EAAI,SACvC,CACD,SAASssB,IACP,IAAIlI,OAAEA,EAAFC,OAAUA,GAAWhhB,EAAO8gB,WAChCC,EAASsS,EAAkBtS,GAC3BC,EAASqS,EAAkBrS,GAC3B,MAAM+S,EAAgB,CAACp3B,EAAIkE,KACzBlE,EAAG1D,oBAAoB,QAAiB,SAAR4H,EAAiBgzB,EAAcD,GAC/Dj3B,EAAGuF,UAAUgH,UAAUlJ,EAAOQ,OAAOsgB,WAAWmS,cAAc51B,MAAM,KAApE,EAEF0jB,EAAOpoB,SAASgE,GAAOo3B,EAAcp3B,EAAI,UACzCqkB,EAAOroB,SAASgE,GAAOo3B,EAAcp3B,EAAI,SAC1C,CAEDkK,EAAG,QAAQ,MACgC,IAArC7G,EAAOQ,OAAOsgB,WAAWxV,QAE3BuZ,KAEArC,IACAxY,IACD,IAEHnD,EAAG,+BAA+B,KAChCmD,GAAM,IAERnD,EAAG,WAAW,KACZoiB,GAAO,IAETpiB,EAAG,kBAAkB,KACnB,IAAIka,OAAEA,EAAFC,OAAUA,GAAWhhB,EAAO8gB,WAChCC,EAASsS,EAAkBtS,GAC3BC,EAASqS,EAAkBrS,GAC3B,IAAID,KAAWC,GACZ/hB,QAAQtC,KAASA,IACjBhE,SAASgE,GACRA,EAAGuF,UAAUlC,EAAOsL,QAAU,SAAW,OAAOtL,EAAOQ,OAAOsgB,WAAWqS,YAH7E,IAMFtsB,EAAG,SAAS,CAACmtB,EAAInwB,KACf,IAAIkd,OAAEA,EAAFC,OAAUA,GAAWhhB,EAAO8gB,WAChCC,EAASsS,EAAkBtS,GAC3BC,EAASqS,EAAkBrS,GAC3B,MAAM1G,EAAWzW,EAAErL,OACnB,GACEwH,EAAOQ,OAAOsgB,WAAWkS,cACxBhS,EAAO5a,SAASkU,KAChByG,EAAO3a,SAASkU,GACjB,CACA,GACEta,EAAOi0B,YACPj0B,EAAOQ,OAAOyzB,YACdj0B,EAAOQ,OAAOyzB,WAAWC,YACxBl0B,EAAOi0B,WAAWt3B,KAAO2d,GAAYta,EAAOi0B,WAAWt3B,GAAGmT,SAASwK,IAEpE,OACF,IAAI6Z,EACApT,EAAOloB,OACTs7B,EAAWpT,EAAO,GAAG7e,UAAU4N,SAAS9P,EAAOQ,OAAOsgB,WAAWoS,aACxDlS,EAAOnoB,SAChBs7B,EAAWnT,EAAO,GAAG9e,UAAU4N,SAAS9P,EAAOQ,OAAOsgB,WAAWoS,cAGjE9qB,GADe,IAAb+rB,EACG,iBAEA,kBAEP,IAAIpT,KAAWC,GACZ/hB,QAAQtC,KAASA,IACjBhE,SAASgE,GAAOA,EAAGuF,UAAUkyB,OAAOp0B,EAAOQ,OAAOsgB,WAAWoS,cACjE,KAGH,MAMMrO,EAAU,KACd7kB,EAAOrD,GAAGuF,UAAUC,OAAOnC,EAAOQ,OAAOsgB,WAAWsS,wBAAwB/1B,MAAM,MAClF4rB,GAAO,EAGT3wB,OAAOkS,OAAOxK,EAAO8gB,WAAY,CAC/BgE,OAZa,KACb9kB,EAAOrD,GAAGuF,UAAUgH,UAAUlJ,EAAOQ,OAAOsgB,WAAWsS,wBAAwB/1B,MAAM,MACrFmlB,IACAxY,GAAM,EAUN6a,UACA7a,SACAwY,OACAyG,WAEH,EC9Mc,SAAwDlpB,GAAA,IAApCC,OAAEA,EAAFinB,aAAUA,EAAVpgB,GAAwBA,EAAxBuB,KAA4BA,GAAQrI,EACrE,MAAMs0B,EAAM,oBAsCZ,IAAIC,EArCJrN,EAAa,CACXgN,WAAY,CACVt3B,GAAI,KACJ43B,cAAe,OACfL,WAAW,EACXlB,aAAa,EACbwB,aAAc,KACdC,kBAAmB,KACnBC,eAAgB,KAChBC,aAAc,KACdC,qBAAqB,EACrB5U,KAAM,UACN6U,gBAAgB,EAChBC,mBAAoB,EACpBC,sBAAwBC,GAAWA,EACnCC,oBAAsBD,GAAWA,EACjCE,YAAc,GAAEb,WAChBc,kBAAoB,GAAEd,kBACtBe,cAAgB,GAAEf,KAClBgB,aAAe,GAAEhB,YACjBiB,WAAa,GAAEjB,UACfnB,YAAc,GAAEmB,WAChBkB,qBAAuB,GAAElB,qBACzBmB,yBAA2B,GAAEnB,yBAC7BoB,eAAiB,GAAEpB,cACnBlB,UAAY,GAAEkB,SACdqB,gBAAkB,GAAErB,eACpBsB,cAAgB,GAAEtB,aAClBuB,wBAA0B,GAAEvB,gBAIhCr0B,EAAOi0B,WAAa,CAClBt3B,GAAI,KACJk5B,QAAS,IAIX,IAAIC,EAAqB,EAEzB,MAAMzC,EAAqB12B,IACpByF,MAAMC,QAAQ1F,KAAKA,EAAK,CAACA,GAAIsC,QAAQ4E,KAAQA,KAC3ClH,GAGT,SAASo5B,IACP,OACG/1B,EAAOQ,OAAOyzB,WAAWt3B,KACzBqD,EAAOi0B,WAAWt3B,IAClByF,MAAMC,QAAQrC,EAAOi0B,WAAWt3B,KAAuC,IAAhCqD,EAAOi0B,WAAWt3B,GAAG9D,MAEhE,CAED,SAASm9B,EAAeC,EAAU1D,GAChC,MAAM4C,kBAAEA,GAAsBn1B,EAAOQ,OAAOyzB,WACvCgC,IACLA,EAAWA,GAAyB,SAAb1D,EAAsB,WAAa,QAArC,qBAEnB0D,EAAS/zB,UAAUC,IAAK,GAAEgzB,KAAqB5C,MAC/C0D,EAAWA,GAAyB,SAAb1D,EAAsB,WAAa,QAArC,oBAEnB0D,EAAS/zB,UAAUC,IAAK,GAAEgzB,KAAqB5C,KAAYA,KAGhE,CAED,SAAS2D,EAAcryB,GACrB,MAAMoyB,EAAWpyB,EAAErL,OAAOqQ,QAAQghB,GAAkB7pB,EAAOQ,OAAOyzB,WAAWiB,cAC7E,IAAKe,EACH,OAEFpyB,EAAE2Y,iBACF,MAAMvU,EAAQ9E,EAAa8yB,GAAYj2B,EAAOQ,OAAOsN,eACrD,GAAI9N,EAAOQ,OAAO6N,KAAM,CACtB,GAAIrO,EAAO+J,YAAc9B,EAAO,OAChC,MAAMkuB,EAAgBn2B,EAAOqQ,oBAAoBpI,GAC3CmuB,EAAoBp2B,EAAOqQ,oBAAoBrQ,EAAO+J,WACxDosB,EAAgBn2B,EAAOoJ,OAAOvQ,OAASmH,EAAOuY,cAChDvY,EAAOuX,QAAQ,CACbvB,UAAWmgB,EAAgBC,EAAoB,OAAS,OACxDzd,iBAAkBwd,EAClBjgB,SAAS,IAIblW,EAAOgX,YAAY/O,EACpB,MACCjI,EAAOkW,QAAQjO,EAElB,CAED,SAAS+B,IAEP,MAAMkB,EAAMlL,EAAOkL,IACb1K,EAASR,EAAOQ,OAAOyzB,WAC7B,GAAI8B,IAAwB,OAE5B,IAGIh1B,EACA8S,EAJAlX,EAAKqD,EAAOi0B,WAAWt3B,GAC3BA,EAAK02B,EAAkB12B,GAIvB,MAAM6O,EACJxL,EAAOqL,SAAWrL,EAAOQ,OAAO6K,QAAQC,QACpCtL,EAAOqL,QAAQjC,OAAOvQ,OACtBmH,EAAOoJ,OAAOvQ,OACdw9B,EAAQr2B,EAAOQ,OAAO6N,KACxBlN,KAAKwI,KAAK6B,EAAexL,EAAOQ,OAAOsN,gBACvC9N,EAAOyL,SAAS5S,OAepB,GAdImH,EAAOQ,OAAO6N,MAChBwF,EAAgB7T,EAAO8T,mBAAqB,EAC5C/S,EACEf,EAAOQ,OAAOsN,eAAiB,EAC3B3M,KAAKwM,MAAM3N,EAAO+J,UAAY/J,EAAOQ,OAAOsN,gBAC5C9N,EAAO+J,gBACwB,IAArB/J,EAAOmP,WACvBpO,EAAUf,EAAOmP,UACjB0E,EAAgB7T,EAAO+T,oBAEvBF,EAAgB7T,EAAO6T,eAAiB,EACxC9S,EAAUf,EAAO4J,aAAe,GAIhB,YAAhBpJ,EAAOwf,MACPhgB,EAAOi0B,WAAW4B,SAClB71B,EAAOi0B,WAAW4B,QAAQh9B,OAAS,EACnC,CACA,MAAMg9B,EAAU71B,EAAOi0B,WAAW4B,QAClC,IAAIS,EACAte,EACAue,EA+BJ,GA9BI/1B,EAAOq0B,iBACTP,EAAaxwB,EAAiB+xB,EAAQ,GAAI71B,EAAOoK,eAAiB,QAAU,UAAU,GACtFzN,EAAGhE,SAAS+6B,IACVA,EAAM75B,MAAMmG,EAAOoK,eAAiB,QAAU,UAC5CkqB,GAAc9zB,EAAOs0B,mBAAqB,GADe,IAA3D,IAIEt0B,EAAOs0B,mBAAqB,QAAuBl2B,IAAlBiV,IACnCiiB,GAAsB/0B,GAAW8S,GAAiB,GAC9CiiB,EAAqBt1B,EAAOs0B,mBAAqB,EACnDgB,EAAqBt1B,EAAOs0B,mBAAqB,EACxCgB,EAAqB,IAC9BA,EAAqB,IAGzBQ,EAAan1B,KAAKC,IAAIL,EAAU+0B,EAAoB,GACpD9d,EAAYse,GAAcn1B,KAAKE,IAAIw0B,EAAQh9B,OAAQ2H,EAAOs0B,oBAAsB,GAChFyB,GAAYve,EAAYse,GAAc,GAExCT,EAAQl9B,SAASs9B,IACf,MAAMO,EAAkB,IACnB,CAAC,GAAI,QAAS,aAAc,QAAS,aAAc,SAASl5B,KAC5Dm5B,GAAY,GAAEj2B,EAAO20B,oBAAoBsB,OAG3Cn5B,KAAKo5B,GAAoB,iBAANA,GAAkBA,EAAEtwB,SAAS,KAAOswB,EAAEr5B,MAAM,KAAOq5B,IACtEC,OACHV,EAAS/zB,UAAUgH,UAAUstB,EAA7B,IAGE75B,EAAG9D,OAAS,EACdg9B,EAAQl9B,SAASi+B,IACf,MAAMC,EAAc1zB,EAAayzB,GAC7BC,IAAgB91B,GAClB61B,EAAO10B,UAAUC,OAAO3B,EAAO20B,kBAAkB93B,MAAM,MAErDmD,EAAOq0B,iBACLgC,GAAeP,GAAcO,GAAe7e,GAC9C4e,EAAO10B,UAAUC,OAAQ,GAAE3B,EAAO20B,yBAAyB93B,MAAM,MAE/Dw5B,IAAgBP,GAClBN,EAAeY,EAAQ,QAErBC,IAAgB7e,GAClBge,EAAeY,EAAQ,QAE1B,QAEE,CACL,MAAMA,EAASf,EAAQ90B,GAKvB,GAJI61B,GACFA,EAAO10B,UAAUC,OAAO3B,EAAO20B,kBAAkB93B,MAAM,MAGrDmD,EAAOq0B,eAAgB,CACzB,MAAMiC,EAAuBjB,EAAQS,GAC/BS,EAAsBlB,EAAQ7d,GACpC,IAAK,IAAIlZ,EAAIw3B,EAAYx3B,GAAKkZ,EAAWlZ,GAAK,EACxC+2B,EAAQ/2B,IACV+2B,EAAQ/2B,GAAGoD,UAAUC,OAAQ,GAAE3B,EAAO20B,yBAAyB93B,MAAM,MAIzE24B,EAAec,EAAsB,QACrCd,EAAee,EAAqB,OACrC,CACF,CACD,GAAIv2B,EAAOq0B,eAAgB,CACzB,MAAMmC,EAAuB71B,KAAKE,IAAIw0B,EAAQh9B,OAAQ2H,EAAOs0B,mBAAqB,GAC5EmC,GACH3C,EAAa0C,EAAuB1C,GAAc,EAAIiC,EAAWjC,EAC9D7G,EAAaviB,EAAM,QAAU,OACnC2qB,EAAQl9B,SAASi+B,IACfA,EAAO/8B,MAAMmG,EAAOoK,eAAiBqjB,EAAa,OAAU,GAAEwJ,KAA9D,GAEH,CACF,CACDt6B,EAAGhE,SAAQ,CAAC+6B,EAAOwD,KASjB,GARoB,aAAhB12B,EAAOwf,OACT0T,EAAMp6B,iBAAiBuwB,GAAkBrpB,EAAO60B,eAAe18B,SAASw+B,IACtEA,EAAWC,YAAc52B,EAAOu0B,sBAAsBh0B,EAAU,EAAhE,IAEF2yB,EAAMp6B,iBAAiBuwB,GAAkBrpB,EAAO80B,aAAa38B,SAAS0+B,IACpEA,EAAQD,YAAc52B,EAAOy0B,oBAAoBoB,EAAjD,KAGgB,gBAAhB71B,EAAOwf,KAAwB,CACjC,IAAIsX,EAEFA,EADE92B,EAAOo0B,oBACc50B,EAAOoK,eAAiB,WAAa,aAErCpK,EAAOoK,eAAiB,aAAe,WAEhE,MAAMmtB,GAASx2B,EAAU,GAAKs1B,EAC9B,IAAImB,EAAS,EACTC,EAAS,EACgB,eAAzBH,EACFE,EAASD,EAETE,EAASF,EAEX7D,EACGp6B,iBAAiBuwB,GAAkBrpB,EAAO+0B,uBAC1C58B,SAAS++B,IACRA,EAAW79B,MAAMsD,UAAa,6BAA4Bq6B,aAAkBC,KAC5EC,EAAW79B,MAAM2pB,mBAAsB,GAAExjB,EAAOQ,OAAOC,SAAvD,GAEL,CACmB,WAAhBD,EAAOwf,MAAqBxf,EAAOm0B,cACrCjB,EAAMzJ,UAAYzpB,EAAOm0B,aAAa30B,EAAQe,EAAU,EAAGs1B,GACxC,IAAfa,GAAkB9uB,EAAK,mBAAoBsrB,KAE5B,IAAfwD,GAAkB9uB,EAAK,mBAAoBsrB,GAC/CtrB,EAAK,mBAAoBsrB,IAEvB1zB,EAAOQ,OAAO+O,eAAiBvP,EAAOsL,SACxCooB,EAAMxxB,UAAUlC,EAAO0jB,SAAW,MAAQ,UAAUljB,EAAO2yB,UAC5D,GAEJ,CACD,SAASwE,IAEP,MAAMn3B,EAASR,EAAOQ,OAAOyzB,WAC7B,GAAI8B,IAAwB,OAC5B,MAAMvqB,EACJxL,EAAOqL,SAAWrL,EAAOQ,OAAO6K,QAAQC,QACpCtL,EAAOqL,QAAQjC,OAAOvQ,OACtBmH,EAAOoJ,OAAOvQ,OAEpB,IAAI8D,EAAKqD,EAAOi0B,WAAWt3B,GAC3BA,EAAK02B,EAAkB12B,GACvB,IAAIi7B,EAAiB,GACrB,GAAoB,YAAhBp3B,EAAOwf,KAAoB,CAC7B,IAAI6X,EAAkB73B,EAAOQ,OAAO6N,KAChClN,KAAKwI,KAAK6B,EAAexL,EAAOQ,OAAOsN,gBACvC9N,EAAOyL,SAAS5S,OAElBmH,EAAOQ,OAAO6c,UACdrd,EAAOQ,OAAO6c,SAAS/R,SACvBusB,EAAkBrsB,IAElBqsB,EAAkBrsB,GAEpB,IAAK,IAAI1M,EAAI,EAAGA,EAAI+4B,EAAiB/4B,GAAK,EACpC0B,EAAOg0B,aACToD,GAAkBp3B,EAAOg0B,aAAap2B,KAAK4B,EAAQlB,EAAG0B,EAAO00B,aAE7D0C,GAAmB,IAAGp3B,EAAO+zB,wBAAwB/zB,EAAO00B,kBAAkB10B,EAAO+zB,gBAG1F,CACmB,aAAhB/zB,EAAOwf,OAEP4X,EADEp3B,EAAOk0B,eACQl0B,EAAOk0B,eAAet2B,KAAK4B,EAAQQ,EAAO60B,aAAc70B,EAAO80B,YAG7E,gBAAe90B,EAAO60B,wCAEP70B,EAAO80B,uBAGT,gBAAhB90B,EAAOwf,OAEP4X,EADEp3B,EAAOi0B,kBACQj0B,EAAOi0B,kBAAkBr2B,KAAK4B,EAAQQ,EAAO+0B,sBAE5C,gBAAe/0B,EAAO+0B,iCAG5Cv1B,EAAOi0B,WAAW4B,QAAU,GAC5Bl5B,EAAGhE,SAAS+6B,IACU,WAAhBlzB,EAAOwf,OACT0T,EAAMzJ,UAAY2N,GAAkB,IAElB,YAAhBp3B,EAAOwf,MACThgB,EAAOi0B,WAAW4B,QAAQnyB,QACrBgwB,EAAMp6B,iBAAiBuwB,GAAkBrpB,EAAO00B,cAEtD,IAEiB,WAAhB10B,EAAOwf,MACT5X,EAAK,mBAAoBzL,EAAG,GAE/B,CACD,SAAS6lB,IACPxiB,EAAOQ,OAAOyzB,WAAatK,EACzB3pB,EACAA,EAAOqkB,eAAe4P,WACtBj0B,EAAOQ,OAAOyzB,WACd,CAAEt3B,GAAI,sBAER,MAAM6D,EAASR,EAAOQ,OAAOyzB,WAC7B,IAAKzzB,EAAO7D,GAAI,OAChB,IAAIA,EACqB,iBAAd6D,EAAO7D,IAAmBqD,EAAO8I,YAC1CnM,EAAKqD,EAAOrD,GAAGse,WAAW5hB,cAAcmH,EAAO7D,KAE5CA,GAA2B,iBAAd6D,EAAO7D,KACvBA,EAAK,IAAI9B,SAASvB,iBAAiBkH,EAAO7D,MAEvCA,IACHA,EAAK6D,EAAO7D,IAETA,GAAoB,IAAdA,EAAG9D,SAGZmH,EAAOQ,OAAOqiB,mBACO,iBAAdriB,EAAO7D,IACdyF,MAAMC,QAAQ1F,IACdA,EAAG9D,OAAS,IAEZ8D,EAAK,IAAIqD,EAAOrD,GAAGrD,iBAAiBkH,EAAO7D,KAEvCA,EAAG9D,OAAS,IACd8D,EAAKA,EAAGsC,QAAQy0B,GACVpwB,EAAeowB,EAAO,WAAW,KAAO1zB,EAAOrD,KAElD,KAGHyF,MAAMC,QAAQ1F,IAAqB,IAAdA,EAAG9D,SAAc8D,EAAKA,EAAG,IAElDrE,OAAOkS,OAAOxK,EAAOi0B,WAAY,CAC/Bt3B,OAGFA,EAAK02B,EAAkB12B,GACvBA,EAAGhE,SAAS+6B,IACU,YAAhBlzB,EAAOwf,MAAsBxf,EAAO0zB,WACtCR,EAAMxxB,UAAUC,IAAI3B,EAAOi1B,gBAG7B/B,EAAMxxB,UAAUC,IAAI3B,EAAO40B,cAAgB50B,EAAOwf,MAClD0T,EAAMxxB,UAAUC,IAAInC,EAAOoK,eAAiB5J,EAAOk1B,gBAAkBl1B,EAAOm1B,eAExD,YAAhBn1B,EAAOwf,MAAsBxf,EAAOq0B,iBACtCnB,EAAMxxB,UAAUC,IAAK,GAAE3B,EAAO40B,gBAAgB50B,EAAOwf,gBACrD8V,EAAqB,EACjBt1B,EAAOs0B,mBAAqB,IAC9Bt0B,EAAOs0B,mBAAqB,IAGZ,gBAAhBt0B,EAAOwf,MAA0Bxf,EAAOo0B,qBAC1ClB,EAAMxxB,UAAUC,IAAI3B,EAAOg1B,0BAGzBh1B,EAAO0zB,WACTR,EAAM16B,iBAAiB,QAASk9B,GAG7Bl2B,EAAOsL,SACVooB,EAAMxxB,UAAUC,IAAI3B,EAAO2yB,UAC5B,IAEJ,CAED,SAASlK,IACP,MAAMzoB,EAASR,EAAOQ,OAAOyzB,WAC7B,GAAI8B,IAAwB,OAC5B,IAAIp5B,EAAKqD,EAAOi0B,WAAWt3B,GACvBA,IACFA,EAAK02B,EAAkB12B,GACvBA,EAAGhE,SAAS+6B,IACVA,EAAMxxB,UAAUgH,OAAO1I,EAAO0yB,aAC9BQ,EAAMxxB,UAAUgH,OAAO1I,EAAO40B,cAAgB50B,EAAOwf,MACrD0T,EAAMxxB,UAAUgH,OACdlJ,EAAOoK,eAAiB5J,EAAOk1B,gBAAkBl1B,EAAOm1B,eAEtDn1B,EAAO0zB,WACTR,EAAMz6B,oBAAoB,QAASi9B,EACpC,KAIDl2B,EAAOi0B,WAAW4B,SACpB71B,EAAOi0B,WAAW4B,QAAQl9B,SAAS+6B,GACjCA,EAAMxxB,UAAUgH,UAAU1I,EAAO20B,kBAAkB93B,MAAM,OAE9D,CAEDwJ,EAAG,mBAAmB,KACpB,IAAK7G,EAAOi0B,aAAej0B,EAAOi0B,WAAWt3B,GAAI,OACjD,MAAM6D,EAASR,EAAOQ,OAAOyzB,WAC7B,IAAIt3B,GAAEA,GAAOqD,EAAOi0B,WACpBt3B,EAAK02B,EAAkB12B,GACvBA,EAAGhE,SAAS+6B,IACVA,EAAMxxB,UAAUgH,OAAO1I,EAAOk1B,gBAAiBl1B,EAAOm1B,eACtDjC,EAAMxxB,UAAUC,IAAInC,EAAOoK,eAAiB5J,EAAOk1B,gBAAkBl1B,EAAOm1B,cAA5E,GAFF,IAMF9uB,EAAG,QAAQ,MACgC,IAArC7G,EAAOQ,OAAOyzB,WAAW3oB,QAE3BuZ,KAEArC,IACAmV,IACA3tB,IACD,IAEHnD,EAAG,qBAAqB,UACU,IAArB7G,EAAOmP,WAChBnF,GACD,IAEHnD,EAAG,mBAAmB,KACpBmD,GAAM,IAERnD,EAAG,wBAAwB,KACzB8wB,IACA3tB,GAAM,IAERnD,EAAG,WAAW,KACZoiB,GAAO,IAETpiB,EAAG,kBAAkB,KACnB,IAAIlK,GAAEA,GAAOqD,EAAOi0B,WAChBt3B,IACFA,EAAK02B,EAAkB12B,GACvBA,EAAGhE,SAAS+6B,GACVA,EAAMxxB,UAAUlC,EAAOsL,QAAU,SAAW,OAAOtL,EAAOQ,OAAOyzB,WAAWd,aAE/E,IAEHtsB,EAAG,eAAe,KAChBmD,GAAM,IAERnD,EAAG,SAAS,CAACmtB,EAAInwB,KACf,MAAMyW,EAAWzW,EAAErL,OACnB,IAAImE,GAAEA,GAAOqD,EAAOi0B,WAEpB,GADK7xB,MAAMC,QAAQ1F,KAAKA,EAAK,CAACA,GAAIsC,QAAQ4C,KAAcA,KAEtD7B,EAAOQ,OAAOyzB,WAAWt3B,IACzBqD,EAAOQ,OAAOyzB,WAAWjB,aACzBr2B,GACAA,EAAG9D,OAAS,IACXyhB,EAASpY,UAAU4N,SAAS9P,EAAOQ,OAAOyzB,WAAWiB,aACtD,CACA,GACEl1B,EAAO8gB,aACL9gB,EAAO8gB,WAAWC,QAAUzG,IAAata,EAAO8gB,WAAWC,QAC1D/gB,EAAO8gB,WAAWE,QAAU1G,IAAata,EAAO8gB,WAAWE,QAE9D,OACF,MAAMmT,EAAWx3B,EAAG,GAAGuF,UAAU4N,SAAS9P,EAAOQ,OAAOyzB,WAAWf,aAEjE9qB,GADe,IAAb+rB,EACG,iBAEA,kBAEPx3B,EAAGhE,SAAS+6B,GAAUA,EAAMxxB,UAAUkyB,OAAOp0B,EAAOQ,OAAOyzB,WAAWf,cACvE,KAGH,MAcMrO,EAAU,KACd7kB,EAAOrD,GAAGuF,UAAUC,IAAInC,EAAOQ,OAAOyzB,WAAW2B,yBACjD,IAAIj5B,GAAEA,GAAOqD,EAAOi0B,WAChBt3B,IACFA,EAAK02B,EAAkB12B,GACvBA,EAAGhE,SAAS+6B,GAAUA,EAAMxxB,UAAUC,IAAInC,EAAOQ,OAAOyzB,WAAW2B,4BAErE3M,GAAO,EAGT3wB,OAAOkS,OAAOxK,EAAOi0B,WAAY,CAC/BnP,OAzBa,KACb9kB,EAAOrD,GAAGuF,UAAUgH,OAAOlJ,EAAOQ,OAAOyzB,WAAW2B,yBACpD,IAAIj5B,GAAEA,GAAOqD,EAAOi0B,WAChBt3B,IACFA,EAAK02B,EAAkB12B,GACvBA,EAAGhE,SAAS+6B,GACVA,EAAMxxB,UAAUgH,OAAOlJ,EAAOQ,OAAOyzB,WAAW2B,4BAGpDpT,IACAmV,IACA3tB,GAAM,EAeN6a,UACA8S,SACA3tB,SACAwY,OACAyG,WAEH,ECxgBc,SAAuDlpB,GAAA,IAApCC,OAAEA,EAAFinB,aAAUA,EAAVpgB,GAAwBA,EAAxBuB,KAA4BA,GAAQrI,EACpE,MAAMlF,EAAWF,IAEjB,IAGIm9B,EACAC,EACAC,EACAC,EANAvd,GAAY,EACZqV,EAAU,KACVmI,EAAc,KA0BlB,SAASnjB,IACP,IAAK/U,EAAOQ,OAAO23B,UAAUx7B,KAAOqD,EAAOm4B,UAAUx7B,GAAI,OACzD,MAAMw7B,UAAEA,EAAWltB,aAAcC,GAAQlL,GACnCo4B,OAAEA,EAAFz7B,GAAUA,GAAOw7B,EACjB33B,EAASR,EAAOQ,OAAO23B,UACvBj3B,EAAWlB,EAAOQ,OAAO6N,KAAOrO,EAAO8R,aAAe9R,EAAOkB,SAEnE,IAAIm3B,EAAUN,EACVO,GAAUN,EAAYD,GAAY72B,EAClCgK,GACFotB,GAAUA,EACNA,EAAS,GACXD,EAAUN,EAAWO,EACrBA,EAAS,IACCA,EAASP,EAAWC,IAC9BK,EAAUL,EAAYM,IAEfA,EAAS,GAClBD,EAAUN,EAAWO,EACrBA,EAAS,GACAA,EAASP,EAAWC,IAC7BK,EAAUL,EAAYM,GAEpBt4B,EAAOoK,gBACTguB,EAAOv+B,MAAMsD,UAAa,eAAcm7B,aACxCF,EAAOv+B,MAAMuL,MAAS,GAAEizB,QAExBD,EAAOv+B,MAAMsD,UAAa,oBAAmBm7B,UAC7CF,EAAOv+B,MAAMyL,OAAU,GAAE+yB,OAEvB73B,EAAO+3B,OACTz8B,aAAai0B,GACbpzB,EAAG9C,MAAM2+B,QAAU,EACnBzI,EAAUl0B,YAAW,KACnBc,EAAG9C,MAAM2+B,QAAU,EACnB77B,EAAG9C,MAAM2pB,mBAAqB,OAA9B,GACC,KAEN,CAKD,SAASvZ,IACP,IAAKjK,EAAOQ,OAAO23B,UAAUx7B,KAAOqD,EAAOm4B,UAAUx7B,GAAI,OAEzD,MAAMw7B,UAAEA,GAAcn4B,GAChBo4B,OAAEA,EAAFz7B,GAAUA,GAAOw7B,EAEvBC,EAAOv+B,MAAMuL,MAAQ,GACrBgzB,EAAOv+B,MAAMyL,OAAS,GACtB0yB,EAAYh4B,EAAOoK,eAAiBzN,EAAGsH,YAActH,EAAG4T,aAExD0nB,EACEj4B,EAAO+D,MACN/D,EAAOqM,YACNrM,EAAOQ,OAAOqL,oBACb7L,EAAOQ,OAAOiM,eAAiBzM,EAAOyL,SAAS,GAAK,IAEvDssB,EADuC,SAArC/3B,EAAOQ,OAAO23B,UAAUJ,SACfC,EAAYC,EAEZ3tB,SAAStK,EAAOQ,OAAO23B,UAAUJ,SAAU,IAGpD/3B,EAAOoK,eACTguB,EAAOv+B,MAAMuL,MAAS,GAAE2yB,MAExBK,EAAOv+B,MAAMyL,OAAU,GAAEyyB,MAIzBp7B,EAAG9C,MAAM4+B,QADPR,GAAW,EACM,OAEA,GAEjBj4B,EAAOQ,OAAO23B,UAAUI,OAC1B57B,EAAG9C,MAAM2+B,QAAU,GAGjBx4B,EAAOQ,OAAO+O,eAAiBvP,EAAOsL,SACxC6sB,EAAUx7B,GAAGuF,UAAUlC,EAAO0jB,SAAW,MAAQ,UAAU1jB,EAAOQ,OAAO23B,UAAUhF,UAEtF,CACD,SAASuF,EAAmB70B,GAC1B,OAAO7D,EAAOoK,eAAiBvG,EAAE80B,QAAU90B,EAAE+0B,OAC9C,CACD,SAASC,EAAgBh1B,GACvB,MAAMs0B,UAAEA,EAAWltB,aAAcC,GAAQlL,GACnCrD,GAAEA,GAAOw7B,EAEf,IAAIW,EACJA,GACGJ,EAAmB70B,GAClBvB,EAAc3F,GAAIqD,EAAOoK,eAAiB,OAAS,QACjC,OAAjB0tB,EAAwBA,EAAeC,EAAW,KACpDC,EAAYD,GACfe,EAAgB33B,KAAKC,IAAID,KAAKE,IAAIy3B,EAAe,GAAI,GACjD5tB,IACF4tB,EAAgB,EAAIA,GAGtB,MAAMvG,EACJvyB,EAAOmR,gBAAkBnR,EAAO2R,eAAiB3R,EAAOmR,gBAAkB2nB,EAE5E94B,EAAOwR,eAAe+gB,GACtBvyB,EAAO+U,aAAawd,GACpBvyB,EAAO2T,oBACP3T,EAAO0S,qBACR,CACD,SAASqmB,EAAYl1B,GACnB,MAAMrD,EAASR,EAAOQ,OAAO23B,WACvBA,UAAEA,EAAFz3B,UAAaA,GAAcV,GAC3BrD,GAAEA,EAAFy7B,OAAMA,GAAWD,EACvBzd,GAAY,EACZod,EACEj0B,EAAErL,SAAW4/B,EACTM,EAAmB70B,GACnBA,EAAErL,OAAOgK,wBAAwBxC,EAAOoK,eAAiB,OAAS,OAClE,KACNvG,EAAE2Y,iBACF3Y,EAAE6a,kBAEFhe,EAAU7G,MAAM2pB,mBAAqB,QACrC4U,EAAOv+B,MAAM2pB,mBAAqB,QAClCqV,EAAgBh1B,GAEhB/H,aAAao8B,GAEbv7B,EAAG9C,MAAM2pB,mBAAqB,MAC1BhjB,EAAO+3B,OACT57B,EAAG9C,MAAM2+B,QAAU,GAEjBx4B,EAAOQ,OAAOkM,UAChB1M,EAAOU,UAAU7G,MAAM,oBAAsB,QAE/CuO,EAAK,qBAAsBvE,EAC5B,CACD,SAASm1B,EAAWn1B,GAClB,MAAMs0B,UAAEA,EAAFz3B,UAAaA,GAAcV,GAC3BrD,GAAEA,EAAFy7B,OAAMA,GAAWD,EAElBzd,IACD7W,EAAE2Y,eAAgB3Y,EAAE2Y,iBACnB3Y,EAAEisB,aAAc,EACrB+I,EAAgBh1B,GAChBnD,EAAU7G,MAAM2pB,mBAAqB,MACrC7mB,EAAG9C,MAAM2pB,mBAAqB,MAC9B4U,EAAOv+B,MAAM2pB,mBAAqB,MAClCpb,EAAK,oBAAqBvE,GAC3B,CACD,SAASo1B,EAAUp1B,GACjB,MAAMrD,EAASR,EAAOQ,OAAO23B,WACvBA,UAAEA,EAAFz3B,UAAaA,GAAcV,GAC3BrD,GAAEA,GAAOw7B,EAEVzd,IACLA,GAAY,EACR1a,EAAOQ,OAAOkM,UAChB1M,EAAOU,UAAU7G,MAAM,oBAAsB,GAC7C6G,EAAU7G,MAAM2pB,mBAAqB,IAEnChjB,EAAO+3B,OACTz8B,aAAao8B,GACbA,EAAc37B,GAAS,KACrBI,EAAG9C,MAAM2+B,QAAU,EACnB77B,EAAG9C,MAAM2pB,mBAAqB,OAA9B,GACC,MAELpb,EAAK,mBAAoBvE,GACrBrD,EAAO04B,eACTl5B,EAAOkY,iBAEV,CAED,SAASpR,EAAOM,GACd,MAAM+wB,UAAEA,EAAF33B,OAAaA,GAAWR,EACxBrD,EAAKw7B,EAAUx7B,GACrB,IAAKA,EAAI,OACT,MAAMnE,EAASmE,EACTw8B,IAAiB34B,EAAOsiB,kBAAmB,CAAEV,SAAS,EAAOH,SAAS,GACtEmX,IAAkB54B,EAAOsiB,kBAAmB,CAAEV,SAAS,EAAMH,SAAS,GAC5E,IAAKzpB,EAAQ,OACb,MAAM6gC,EAAyB,OAAXjyB,EAAkB,mBAAqB,sBAC3D5O,EAAO6gC,GAAa,cAAeN,EAAaI,GAChDt+B,EAASw+B,GAAa,cAAeL,EAAYG,GACjDt+B,EAASw+B,GAAa,YAAaJ,EAAWG,EAC/C,CAUD,SAAS5W,IACP,MAAM2V,UAAEA,EAAWx7B,GAAI28B,GAAat5B,EACpCA,EAAOQ,OAAO23B,UAAYxO,EACxB3pB,EACAA,EAAOqkB,eAAe8T,UACtBn4B,EAAOQ,OAAO23B,UACd,CAAEx7B,GAAI,qBAER,MAAM6D,EAASR,EAAOQ,OAAO23B,UAC7B,IAAK33B,EAAO7D,GAAI,OAEhB,IAAIA,EAsBAy7B,EArBqB,iBAAd53B,EAAO7D,IAAmBqD,EAAO8I,YAC1CnM,EAAKqD,EAAOrD,GAAGse,WAAW5hB,cAAcmH,EAAO7D,KAE5CA,GAA2B,iBAAd6D,EAAO7D,GAEbA,IACVA,EAAK6D,EAAO7D,IAFZA,EAAK9B,EAASvB,iBAAiBkH,EAAO7D,IAMtCqD,EAAOQ,OAAOqiB,mBACO,iBAAdriB,EAAO7D,IACdA,EAAG9D,OAAS,GACoC,IAAhDygC,EAAShgC,iBAAiBkH,EAAO7D,IAAI9D,SAErC8D,EAAK28B,EAASjgC,cAAcmH,EAAO7D,KAEjCA,EAAG9D,OAAS,IAAG8D,EAAKA,EAAG,IAE3BA,EAAGuF,UAAUC,IAAInC,EAAOoK,eAAiB5J,EAAOk1B,gBAAkBl1B,EAAOm1B,eAGrEh5B,IACFy7B,EAASz7B,EAAGtD,cAAe,IAAG2G,EAAOQ,OAAO23B,UAAUoB,aACjDnB,IACHA,EAAS1+B,EAAc,MAAOsG,EAAOQ,OAAO23B,UAAUoB,WACtD58B,EAAGyc,OAAOgf,KAId9/B,OAAOkS,OAAO2tB,EAAW,CACvBx7B,KACAy7B,WAGE53B,EAAOg5B,WAtDNx5B,EAAOQ,OAAO23B,UAAUx7B,IAAOqD,EAAOm4B,UAAUx7B,IACrDmK,EAAO,MAyDHnK,GACFA,EAAGuF,UAAUlC,EAAOsL,QAAU,SAAW,OAAOtL,EAAOQ,OAAO23B,UAAUhF,UAE3E,CACD,SAASlK,IACP,MAAMzoB,EAASR,EAAOQ,OAAO23B,UACvBx7B,EAAKqD,EAAOm4B,UAAUx7B,GACxBA,GACFA,EAAGuF,UAAUgH,OAAOlJ,EAAOoK,eAAiB5J,EAAOk1B,gBAAkBl1B,EAAOm1B,eA9DzE31B,EAAOQ,OAAO23B,UAAUx7B,IAAOqD,EAAOm4B,UAAUx7B,IACrDmK,EAAO,MAiER,CAvRDmgB,EAAa,CACXkR,UAAW,CACTx7B,GAAI,KACJo7B,SAAU,OACVQ,MAAM,EACNiB,WAAW,EACXN,eAAe,EACf/F,UAAW,wBACXoG,UAAW,wBACXE,uBAAwB,4BACxB/D,gBAAkB,8BAClBC,cAAgB,+BAIpB31B,EAAOm4B,UAAY,CACjBx7B,GAAI,KACJy7B,OAAQ,MAwQVvxB,EAAG,QAAQ,MAC+B,IAApC7G,EAAOQ,OAAO23B,UAAU7sB,QAE1BuZ,KAEArC,IACAvY,IACA8K,IACD,IAEHlO,EAAG,4CAA4C,KAC7CoD,GAAU,IAEZpD,EAAG,gBAAgB,KACjBkO,GAAY,IAEdlO,EAAG,iBAAiB,CAACmtB,EAAIzzB,MA9OzB,SAAuBA,GAChBP,EAAOQ,OAAO23B,UAAUx7B,IAAOqD,EAAOm4B,UAAUx7B,KACrDqD,EAAOm4B,UAAUC,OAAOv+B,MAAM2pB,mBAAsB,GAAEjjB,MACvD,CA4OC4P,CAAc5P,EAAd,IAEFsG,EAAG,kBAAkB,KACnB,MAAMlK,GAAEA,GAAOqD,EAAOm4B,UAClBx7B,GACFA,EAAGuF,UAAUlC,EAAOsL,QAAU,SAAW,OAAOtL,EAAOQ,OAAO23B,UAAUhF,UACzE,IAEHtsB,EAAG,WAAW,KACZoiB,GAAO,IAGT,MAUMpE,EAAU,KACd7kB,EAAOrD,GAAGuF,UAAUC,IAAInC,EAAOQ,OAAO23B,UAAUsB,wBAC5Cz5B,EAAOm4B,UAAUx7B,IACnBqD,EAAOm4B,UAAUx7B,GAAGuF,UAAUC,IAAInC,EAAOQ,OAAO23B,UAAUsB,wBAE5DxQ,GAAO,EAGT3wB,OAAOkS,OAAOxK,EAAOm4B,UAAW,CAC9BrT,OAnBa,KACb9kB,EAAOrD,GAAGuF,UAAUgH,OAAOlJ,EAAOQ,OAAO23B,UAAUsB,wBAC/Cz5B,EAAOm4B,UAAUx7B,IACnBqD,EAAOm4B,UAAUx7B,GAAGuF,UAAUgH,OAAOlJ,EAAOQ,OAAO23B,UAAUsB,wBAE/DjX,IACAvY,IACA8K,GAAY,EAaZ8P,UACA5a,aACA8K,eACAyN,OACAyG,WAEH,EC3Vc,SAA8DlpB,GAAA,IAkBvEgwB,EACA2J,GAnB2B15B,OAAEA,EAAFinB,aAAUA,EAAVpgB,GAAwBA,EAAxBuB,KAA4BA,EAA5B5H,OAAkCA,GAAUT,EAC3EC,EAAOohB,SAAW,CAChBC,SAAS,EACTC,QAAQ,EACRqY,SAAU,GAGZ1S,EAAa,CACX7F,SAAU,CACR9V,SAAS,EACT9O,MAAO,IACPo9B,mBAAmB,EACnBC,sBAAsB,EACtBC,iBAAiB,EACjBC,kBAAkB,EAClBC,mBAAmB,KAKvB,IAEIC,EAEAC,EACAxf,EACAyf,EACAC,EACAC,EACAC,EATAC,EAAqB/5B,GAAUA,EAAO4gB,SAAW5gB,EAAO4gB,SAAS5kB,MAAQ,IACzEg+B,EAAuBh6B,GAAUA,EAAO4gB,SAAW5gB,EAAO4gB,SAAS5kB,MAAQ,IAE3Ei+B,GAAoB,IAAI9+B,MAAOsF,QAQnC,SAASy5B,EAAgB72B,GAClB7D,IAAUA,EAAOmH,WAAcnH,EAAOU,WACvCmD,EAAErL,SAAWwH,EAAOU,YACxBV,EAAOU,UAAUzH,oBAAoB,gBAAiByhC,GACtDlZ,IACD,CAED,MAAMmZ,EAAe,KACnB,GAAI36B,EAAOmH,YAAcnH,EAAOohB,SAASC,QAAS,OAC9CrhB,EAAOohB,SAASE,OAClB4Y,GAAY,EACHA,IACTM,EAAuBP,EACvBC,GAAY,GAEd,MAAMP,EAAW35B,EAAOohB,SAASE,OAC7B2Y,EACAQ,EAAoBD,GAAuB,IAAI7+B,MAAOsF,UAC1DjB,EAAOohB,SAASuY,SAAWA,EAC3BvxB,EAAK,mBAAoBuxB,EAAUA,EAAWY,GAC9Cb,EAAM19B,uBAAsB,KAC1B2+B,GAAY,GADd,EAmBIC,EAAOC,IACX,GAAI76B,EAAOmH,YAAcnH,EAAOohB,SAASC,QAAS,OAClDnlB,qBAAqBw9B,GACrBiB,IAEA,IAAIn+B,OAA8B,IAAfq+B,EAA6B76B,EAAOQ,OAAO4gB,SAAS5kB,MAAQq+B,EAC/EN,EAAqBv6B,EAAOQ,OAAO4gB,SAAS5kB,MAC5Cg+B,EAAuBx6B,EAAOQ,OAAO4gB,SAAS5kB,MAC9C,MAAMs+B,EAtBc,MACpB,IAAIC,EAQJ,GANEA,EADE/6B,EAAOqL,SAAWrL,EAAOQ,OAAO6K,QAAQC,QAC1BtL,EAAOoJ,OAAOnK,QAAQ2J,GACpCA,EAAQ1G,UAAU4N,SAAS,yBAC3B,GAEc9P,EAAOoJ,OAAOpJ,EAAO4J,cAElCmxB,EAAe,OAEpB,OAD0BzwB,SAASywB,EAAc1mB,aAAa,wBAAyB,GACvF,EAW0B2mB,IAEvBx0B,OAAO+D,MAAMuwB,IACdA,EAAoB,QACE,IAAfD,IAEPr+B,EAAQs+B,EACRP,EAAqBO,EACrBN,EAAuBM,GAEzBb,EAAmBz9B,EACnB,MAAMiE,EAAQT,EAAOQ,OAAOC,MACtBw6B,EAAU,KACTj7B,IAAUA,EAAOmH,YAClBnH,EAAOQ,OAAO4gB,SAAS2Y,kBACpB/5B,EAAO4R,aAAe5R,EAAOQ,OAAO6N,MAAQrO,EAAOQ,OAAOsJ,QAC7D9J,EAAOyX,UAAUhX,GAAO,GAAM,GAC9B2H,EAAK,aACKpI,EAAOQ,OAAO4gB,SAAS0Y,kBACjC95B,EAAOkW,QAAQlW,EAAOoJ,OAAOvQ,OAAS,EAAG4H,GAAO,GAAM,GACtD2H,EAAK,cAGFpI,EAAO6R,OAAS7R,EAAOQ,OAAO6N,MAAQrO,EAAOQ,OAAOsJ,QACvD9J,EAAOkX,UAAUzW,GAAO,GAAM,GAC9B2H,EAAK,aACKpI,EAAOQ,OAAO4gB,SAAS0Y,kBACjC95B,EAAOkW,QAAQ,EAAGzV,GAAO,GAAM,GAC/B2H,EAAK,aAGLpI,EAAOQ,OAAOkM,UAChB+tB,GAAoB,IAAI9+B,MAAOsF,UAC/BjF,uBAAsB,KACpB4+B,GAAG,KAEN,EAcH,OAZIp+B,EAAQ,GACVV,aAAai0B,GACbA,EAAUl0B,YAAW,KACnBo/B,GAAO,GACNz+B,IAEHR,uBAAsB,KACpBi/B,GAAO,IAKJz+B,CAAP,EAGI0+B,EAAQ,KACZl7B,EAAOohB,SAASC,SAAU,EAC1BuZ,IACAxyB,EAAK,gBAAL,EAGI0qB,EAAO,KACX9yB,EAAOohB,SAASC,SAAU,EAC1BvlB,aAAai0B,GACb7zB,qBAAqBw9B,GACrBtxB,EAAK,eAAL,EAEI+yB,EAAQ,CAAC3lB,EAAU4lB,KACvB,GAAIp7B,EAAOmH,YAAcnH,EAAOohB,SAASC,QAAS,OAClDvlB,aAAai0B,GACRva,IACH8kB,GAAsB,GAGxB,MAAMW,EAAU,KACd7yB,EAAK,iBACDpI,EAAOQ,OAAO4gB,SAASwY,kBACzB55B,EAAOU,UAAU1H,iBAAiB,gBAAiB0hC,GAEnDlZ,GACD,EAIH,GADAxhB,EAAOohB,SAASE,QAAS,EACrB8Z,EAMF,OALIf,IACFJ,EAAmBj6B,EAAOQ,OAAO4gB,SAAS5kB,OAE5C69B,GAAe,OACfY,IAGF,MAAMz+B,EAAQy9B,GAAoBj6B,EAAOQ,OAAO4gB,SAAS5kB,MACzDy9B,EAAmBz9B,IAAS,IAAIb,MAAOsF,UAAYw5B,GAC/Cz6B,EAAO6R,OAASooB,EAAmB,IAAMj6B,EAAOQ,OAAO6N,OACvD4rB,EAAmB,IAAGA,EAAmB,GAC7CgB,IAAO,EAGHzZ,EAAS,KAEVxhB,EAAO6R,OAASooB,EAAmB,IAAMj6B,EAAOQ,OAAO6N,MACxDrO,EAAOmH,YACNnH,EAAOohB,SAASC,UAGnBoZ,GAAoB,IAAI9+B,MAAOsF,UAC3Bq5B,GACFA,GAAsB,EACtBM,EAAIX,IAEJW,IAEF56B,EAAOohB,SAASE,QAAS,EACzBlZ,EAAK,kBAAL,EAGIizB,EAAqB,KACzB,GAAIr7B,EAAOmH,YAAcnH,EAAOohB,SAASC,QAAS,OAClD,MAAMxmB,EAAWF,IACgB,WAA7BE,EAASygC,kBACXhB,GAAsB,EACtBa,GAAM,IAEyB,YAA7BtgC,EAASygC,iBACX9Z,GACD,EAGG+Z,EAAkB13B,IACA,UAAlBA,EAAEuW,cACNkgB,GAAsB,EACtBa,GAAM,GAAN,EAGIK,EAAkB33B,IACA,UAAlBA,EAAEuW,aACFpa,EAAOohB,SAASE,QAClBE,GACD,EAyBH3a,EAAG,QAAQ,KACL7G,EAAOQ,OAAO4gB,SAAS9V,UAtBvBtL,EAAOQ,OAAO4gB,SAAS4Y,oBACzBh6B,EAAOrD,GAAG3D,iBAAiB,eAAgBuiC,GAC3Cv7B,EAAOrD,GAAG3D,iBAAiB,eAAgBwiC,IAU5B7gC,IACR3B,iBAAiB,mBAAoBqiC,GAY5CZ,GAAoB,IAAI9+B,MAAOsF,UAC/Bi6B,IACD,IAGHr0B,EAAG,WAAW,KAvBZ7G,EAAOrD,GAAG1D,oBAAoB,eAAgBsiC,GAC9Cv7B,EAAOrD,GAAG1D,oBAAoB,eAAgBuiC,GAS7B7gC,IACR1B,oBAAoB,mBAAoBoiC,GAe7Cr7B,EAAOohB,SAASC,SAClByR,GACD,IAGHjsB,EAAG,yBAAyB,CAACmtB,EAAIvzB,EAAO+U,MAClCxV,EAAOmH,WAAcnH,EAAOohB,SAASC,UACrC7L,IAAaxV,EAAOQ,OAAO4gB,SAASyY,qBACtCsB,GAAM,GAAM,GAEZrI,IACD,IAGHjsB,EAAG,mBAAmB,MAChB7G,EAAOmH,WAAcnH,EAAOohB,SAASC,UAErCrhB,EAAOQ,OAAO4gB,SAASyY,qBACzB/G,KAGFpY,GAAY,EACZyf,GAAgB,EAChBG,GAAsB,EACtBF,EAAoBv+B,YAAW,KAC7By+B,GAAsB,EACtBH,GAAgB,EAChBgB,GAAM,EAAN,GACC,MAJH,IAOFt0B,EAAG,YAAY,KACb,IAAI7G,EAAOmH,WAAcnH,EAAOohB,SAASC,SAAY3G,EAArD,CAIA,GAHA5e,aAAas+B,GACbt+B,aAAai0B,GAET/vB,EAAOQ,OAAO4gB,SAASyY,qBAGzB,OAFAM,GAAgB,OAChBzf,GAAY,GAIVyf,GAAiBn6B,EAAOQ,OAAOkM,SAAS8U,IAC5C2Y,GAAgB,EAChBzf,GAAY,CAZoD,CAYhE,IAGF7T,EAAG,eAAe,MACZ7G,EAAOmH,WAAcnH,EAAOohB,SAASC,UACzCgZ,GAAe,EAAf,IAGF/hC,OAAOkS,OAAOxK,EAAOohB,SAAU,CAC7B8Z,QACApI,OACAqI,QACA3Z,UAEH,ECvTc,SAA6CzhB,GAAA,IAA9BC,OAAEA,EAAFinB,aAAUA,EAAVpgB,GAAwBA,GAAM9G,EAC1DknB,EAAa,CACXwU,OAAQ,CACNz7B,OAAQ,KACR07B,sBAAsB,EACtBC,iBAAkB,EAClBC,sBAAuB,4BACvBC,qBAAsB,mBAI1B,IAAIvnB,GAAc,EACdwnB,GAAgB,EAMpB,SAASC,IACP,MAAMC,EAAeh8B,EAAOy7B,OAAOz7B,OACnC,IAAKg8B,GAAgBA,EAAa70B,UAAW,OAE7C,MAAMwN,EAAeqnB,EAAarnB,aAC5BD,EAAesnB,EAAatnB,aAClC,GAAIA,GAAgBA,EAAaxS,UAAU4N,SAAS9P,EAAOQ,OAAOi7B,OAAOG,uBACvE,OACF,GAAI,MAAOjnB,EAAuD,OAClE,IAAI0D,EAEFA,EADE2jB,EAAax7B,OAAO6N,KACP/D,SACb0xB,EAAatnB,aAAaL,aAAa,2BACvC,IAGaM,EAEb3U,EAAOQ,OAAO6N,KAChBrO,EAAOgX,YAAYqB,GAEnBrY,EAAOkW,QAAQmC,EAElB,CAED,SAASmK,IACP,MAAQiZ,OAAQQ,GAAiBj8B,EAAOQ,OACxC,GAAI8T,EAAa,OAAO,EACxBA,GAAc,EACd,MAAM4nB,EAAcl8B,EAAO3H,YAC3B,GAAI4jC,EAAaj8B,kBAAkBk8B,EACjCl8B,EAAOy7B,OAAOz7B,OAASi8B,EAAaj8B,OACpC1H,OAAOkS,OAAOxK,EAAOy7B,OAAOz7B,OAAOqkB,eAAgB,CACjD5U,qBAAqB,EACrBmF,qBAAqB,IAEvBtc,OAAOkS,OAAOxK,EAAOy7B,OAAOz7B,OAAOQ,OAAQ,CACzCiP,qBAAqB,EACrBmF,qBAAqB,IAEvB5U,EAAOy7B,OAAOz7B,OAAOgK,cAChB,GAAI7R,EAAS8jC,EAAaj8B,QAAS,CACxC,MAAMm8B,EAAqB7jC,OAAOkS,OAAO,GAAIyxB,EAAaj8B,QAC1D1H,OAAOkS,OAAO2xB,EAAoB,CAChC1sB,qBAAqB,EACrBmF,qBAAqB,IAEvB5U,EAAOy7B,OAAOz7B,OAAS,IAAIk8B,EAAYC,GACvCL,GAAgB,CACjB,CAGD,OAFA97B,EAAOy7B,OAAOz7B,OAAOrD,GAAGuF,UAAUC,IAAInC,EAAOQ,OAAOi7B,OAAOI,sBAC3D77B,EAAOy7B,OAAOz7B,OAAO6G,GAAG,MAAOk1B,IACxB,CACR,CAED,SAAS/xB,EAAOmM,GACd,MAAM6lB,EAAeh8B,EAAOy7B,OAAOz7B,OACnC,IAAKg8B,GAAgBA,EAAa70B,UAAW,OAE7C,MAAMsC,EACkC,SAAtCuyB,EAAax7B,OAAOiJ,cAChBuyB,EAAatyB,uBACbsyB,EAAax7B,OAAOiJ,cAG1B,IAAI2yB,EAAmB,EACvB,MAAMC,EAAmBr8B,EAAOQ,OAAOi7B,OAAOG,sBAa9C,GAXI57B,EAAOQ,OAAOiJ,cAAgB,IAAMzJ,EAAOQ,OAAOiM,iBACpD2vB,EAAmBp8B,EAAOQ,OAAOiJ,eAG9BzJ,EAAOQ,OAAOi7B,OAAOC,uBACxBU,EAAmB,GAGrBA,EAAmBj7B,KAAKwM,MAAMyuB,GAE9BJ,EAAa5yB,OAAOzQ,SAASiQ,GAAYA,EAAQ1G,UAAUgH,OAAOmzB,KAEhEL,EAAax7B,OAAO6N,MACnB2tB,EAAax7B,OAAO6K,SAAW2wB,EAAax7B,OAAO6K,QAAQC,QAE5D,IAAK,IAAIxM,EAAI,EAAGA,EAAIs9B,EAAkBt9B,GAAK,EACzC8C,EACEo6B,EAAajxB,SACZ,6BAA4B/K,EAAO+J,UAAYjL,OAChDnG,SAASiQ,IACTA,EAAQ1G,UAAUC,IAAIk6B,EAAtB,SAIJ,IAAK,IAAIv9B,EAAI,EAAGA,EAAIs9B,EAAkBt9B,GAAK,EACrCk9B,EAAa5yB,OAAOpJ,EAAO+J,UAAYjL,IACzCk9B,EAAa5yB,OAAOpJ,EAAO+J,UAAYjL,GAAGoD,UAAUC,IAAIk6B,GAK9D,MAAMV,EAAmB37B,EAAOQ,OAAOi7B,OAAOE,iBACxCW,EAAYX,IAAqBK,EAAax7B,OAAO6N,KAC3D,GAAIrO,EAAO+J,YAAciyB,EAAajyB,WAAauyB,EAAW,CAC5D,MAAMC,EAAqBP,EAAapyB,YACxC,IAAI4yB,EACAxmB,EACJ,GAAIgmB,EAAax7B,OAAO6N,KAAM,CAC5B,MAAMouB,EAAiBT,EAAa5yB,OAAOnK,QACxC2J,GAAYA,EAAQyL,aAAa,6BAAgC,GAAErU,EAAO+J,cAC3E,GACFyyB,EAAiBR,EAAa5yB,OAAOlK,QAAQu9B,GAE7CzmB,EAAYhW,EAAO4J,YAAc5J,EAAO6T,cAAgB,OAAS,MAClE,MACC2oB,EAAiBx8B,EAAO+J,UACxBiM,EAAYwmB,EAAiBx8B,EAAO6T,cAAgB,OAAS,OAE3DyoB,IACFE,GAAgC,SAAdxmB,EAAuB2lB,GAAoB,EAAIA,GAIjEK,EAAahrB,sBACbgrB,EAAahrB,qBAAqB9R,QAAQs9B,GAAkB,IAExDR,EAAax7B,OAAOiM,eAEpB+vB,EADEA,EAAiBD,EACFC,EAAiBr7B,KAAKwM,MAAMlE,EAAgB,GAAK,EAEjD+yB,EAAiBr7B,KAAKwM,MAAMlE,EAAgB,GAAK,EAGpE+yB,EAAiBD,GACjBP,EAAax7B,OAAOsN,eAItBkuB,EAAa9lB,QAAQsmB,EAAgBrmB,EAAU,OAAIvX,GAEtD,CACF,CA/IDoB,EAAOy7B,OAAS,CACdz7B,OAAQ,MAgJV6G,EAAG,cAAc,KACf,MAAM40B,OAAEA,GAAWz7B,EAAOQ,OAC1B,GAAKi7B,GAAWA,EAAOz7B,OACvB,GAA6B,iBAAlBy7B,EAAOz7B,QAAuBy7B,EAAOz7B,kBAAkBxB,YAAa,CAC7E,MAAM3D,EAAWF,IACX+hC,EAA0B,KAC9B,MAAMC,EACqB,iBAAlBlB,EAAOz7B,OAAsBnF,EAASxB,cAAcoiC,EAAOz7B,QAAUy7B,EAAOz7B,OACrF,GAAI28B,GAAiBA,EAAc38B,OACjCy7B,EAAOz7B,OAAS28B,EAAc38B,OAC9BwiB,IACAxY,GAAO,QACF,GAAI2yB,EAAe,CACxB,MAAMC,EAAkB/4B,IACtB43B,EAAOz7B,OAAS6D,EAAE2tB,OAAO,GACzBmL,EAAc1jC,oBAAoB,OAAQ2jC,GAC1Cpa,IACAxY,GAAO,GACPyxB,EAAOz7B,OAAOgK,SACdhK,EAAOgK,QAAP,EAEF2yB,EAAc3jC,iBAAiB,OAAQ4jC,EACxC,CACD,OAAOD,CAAP,EAGIE,EAAyB,KAC7B,GAAI78B,EAAOmH,UAAW,OACAu1B,KAEpB1gC,sBAAsB6gC,EACvB,EAEH7gC,sBAAsB6gC,EACvB,MACCra,IACAxY,GAAO,EACR,IAEHnD,EAAG,4CAA4C,KAC7CmD,GAAM,IAERnD,EAAG,iBAAiB,CAACmtB,EAAIzzB,KACvB,MAAMy7B,EAAeh8B,EAAOy7B,OAAOz7B,OAC9Bg8B,IAAgBA,EAAa70B,WAClC60B,EAAa7rB,cAAc5P,EAA3B,IAEFsG,EAAG,iBAAiB,KAClB,MAAMm1B,EAAeh8B,EAAOy7B,OAAOz7B,OAC9Bg8B,IAAgBA,EAAa70B,WAC9B20B,GACFE,EAAa/S,SACd,IAGH3wB,OAAOkS,OAAOxK,EAAOy7B,OAAQ,CAC3BjZ,OACAxY,UAEH,EC3Nc,SAAwDjK,GAAA,IAAtCC,OAAEA,EAAFinB,aAAUA,EAAV7e,KAAwBA,EAAxBd,KAA8BA,GAAQvH,EACrEknB,EAAa,CACX5J,SAAU,CACR/R,SAAS,EACTwxB,UAAU,EACVC,cAAe,EACfC,gBAAgB,EAChBC,oBAAqB,EACrBC,sBAAuB,EACvB1K,QAAQ,EACR2K,gBAAiB,OAiNrB7kC,OAAOkS,OAAOxK,EAAQ,CACpBqd,SAAU,CACRrD,aA/MJ,WACE,MAAM5Z,EAAYJ,EAAOtD,eACzBsD,EAAO+U,aAAa3U,GACpBJ,EAAOmQ,cAAc,GACrBnQ,EAAOia,gBAAgBuN,WAAW3uB,OAAS,EAC3CmH,EAAOqd,SAAS0C,WAAW,CAAEM,WAAYrgB,EAAOkL,IAAMlL,EAAOI,WAAaJ,EAAOI,WAClF,EA0MGkd,YAxMJ,WACE,MAAQrD,gBAAiB5R,EAAnBmR,QAAyBA,GAAYxZ,EAEZ,IAA3BqI,EAAKmf,WAAW3uB,QAClBwP,EAAKmf,WAAW9jB,KAAK,CACnB6uB,SAAU/Y,EAAQxZ,EAAOoK,eAAiB,SAAW,UACrD/J,KAAMgI,EAAKuU,iBAGfvU,EAAKmf,WAAW9jB,KAAK,CACnB6uB,SAAU/Y,EAAQxZ,EAAOoK,eAAiB,WAAa,YACvD/J,KAAM5D,KAET,EA4LGsjB,WA1LJ,SAAoCuL,GAAA,IAAhBjL,WAAEA,GAAciL,EAClC,MAAM9qB,OAAEA,EAAFE,UAAUA,EAAWuK,aAAcC,EAAnCO,SAAwCA,EAAUwO,gBAAiB5R,GAASrI,EAG5EkgB,EADezjB,IACW4L,EAAKuU,eAErC,GAAIyD,GAAcrgB,EAAOmR,eACvBnR,EAAOkW,QAAQlW,EAAO4J,kBAGxB,GAAIyW,GAAcrgB,EAAO2R,eACnB3R,EAAOoJ,OAAOvQ,OAAS4S,EAAS5S,OAClCmH,EAAOkW,QAAQzK,EAAS5S,OAAS,GAEjCmH,EAAOkW,QAAQlW,EAAOoJ,OAAOvQ,OAAS,OAJ1C,CASA,GAAI2H,EAAO6c,SAASyf,SAAU,CAC5B,GAAIz0B,EAAKmf,WAAW3uB,OAAS,EAAG,CAC9B,MAAMukC,EAAgB/0B,EAAKmf,WAAW6V,MAChCC,EAAgBj1B,EAAKmf,WAAW6V,MAEhCE,EAAWH,EAAc7K,SAAW+K,EAAc/K,SAClDlyB,EAAO+8B,EAAc/8B,KAAOi9B,EAAcj9B,KAChDL,EAAOqnB,SAAWkW,EAAWl9B,EAC7BL,EAAOqnB,UAAY,EACflmB,KAAK0M,IAAI7N,EAAOqnB,UAAY7mB,EAAO6c,SAAS8f,kBAC9Cn9B,EAAOqnB,SAAW,IAIhBhnB,EAAO,KAAO5D,IAAQ2gC,EAAc/8B,KAAO,OAC7CL,EAAOqnB,SAAW,EAErB,MACCrnB,EAAOqnB,SAAW,EAEpBrnB,EAAOqnB,UAAY7mB,EAAO6c,SAAS6f,sBAEnC70B,EAAKmf,WAAW3uB,OAAS,EACzB,IAAI2kC,EAAmB,IAAOh9B,EAAO6c,SAAS0f,cAC9C,MAAMU,EAAmBz9B,EAAOqnB,SAAWmW,EAE3C,IAAIE,EAAc19B,EAAOI,UAAYq9B,EACjCvyB,IAAKwyB,GAAeA,GAExB,IACIC,EADAC,GAAW,EAEf,MAAMC,EAA2C,GAA5B18B,KAAK0M,IAAI7N,EAAOqnB,UAAiB7mB,EAAO6c,SAAS4f,oBACtE,IAAIa,EACJ,GAAIJ,EAAc19B,EAAO2R,eACnBnR,EAAO6c,SAAS2f,gBACdU,EAAc19B,EAAO2R,gBAAkBksB,IACzCH,EAAc19B,EAAO2R,eAAiBksB,GAExCF,EAAsB39B,EAAO2R,eAC7BisB,GAAW,EACXv1B,EAAKkX,qBAAsB,GAE3Bme,EAAc19B,EAAO2R,eAEnBnR,EAAO6N,MAAQ7N,EAAOiM,iBAAgBqxB,GAAe,QACpD,GAAIJ,EAAc19B,EAAOmR,eAC1B3Q,EAAO6c,SAAS2f,gBACdU,EAAc19B,EAAOmR,eAAiB0sB,IACxCH,EAAc19B,EAAOmR,eAAiB0sB,GAExCF,EAAsB39B,EAAOmR,eAC7BysB,GAAW,EACXv1B,EAAKkX,qBAAsB,GAE3Bme,EAAc19B,EAAOmR,eAEnB3Q,EAAO6N,MAAQ7N,EAAOiM,iBAAgBqxB,GAAe,QACpD,GAAIt9B,EAAO6c,SAASmV,OAAQ,CACjC,IAAIxf,EACJ,IAAK,IAAI+qB,EAAI,EAAGA,EAAItyB,EAAS5S,OAAQklC,GAAK,EACxC,GAAItyB,EAASsyB,IAAML,EAAa,CAC9B1qB,EAAY+qB,EACZ,KACD,CAQDL,EAJAv8B,KAAK0M,IAAIpC,EAASuH,GAAa0qB,GAC7Bv8B,KAAK0M,IAAIpC,EAASuH,EAAY,GAAK0qB,IACX,SAA1B19B,EAAO6c,eAEOpR,EAASuH,GAETvH,EAASuH,EAAY,GAErC0qB,GAAeA,CAChB,CAOD,GANII,GACFx2B,EAAK,iBAAiB,KACpBtH,EAAOuX,SAAP,IAIoB,IAApBvX,EAAOqnB,UAMT,GAJEmW,EADEtyB,EACiB/J,KAAK0M,MAAM6vB,EAAc19B,EAAOI,WAAaJ,EAAOqnB,UAEpDlmB,KAAK0M,KAAK6vB,EAAc19B,EAAOI,WAAaJ,EAAOqnB,UAEpE7mB,EAAO6c,SAASmV,OAAQ,CAQ1B,MAAMwL,EAAe78B,KAAK0M,KAAK3C,GAAOwyB,EAAcA,GAAe19B,EAAOI,WACpE69B,EAAmBj+B,EAAO2L,gBAAgB3L,EAAO4J,aAErD4zB,EADEQ,EAAeC,EACEz9B,EAAOC,MACjBu9B,EAAe,EAAIC,EACM,IAAfz9B,EAAOC,MAEQ,IAAfD,EAAOC,KAE7B,OACI,GAAID,EAAO6c,SAASmV,OAEzB,YADAxyB,EAAOkY,iBAIL1X,EAAO6c,SAAS2f,gBAAkBY,GACpC59B,EAAOwR,eAAemsB,GACtB39B,EAAOmQ,cAAcqtB,GACrBx9B,EAAO+U,aAAa2oB,GACpB19B,EAAOyW,iBAAgB,EAAMzW,EAAO6c,gBACpC7c,EAAOyV,WAAY,EACnB9R,EAAqBjD,GAAW,KACzBV,IAAUA,EAAOmH,WAAckB,EAAKkX,sBACzCnX,EAAK,kBACLpI,EAAOmQ,cAAc3P,EAAOC,OAC5B5E,YAAW,KACTmE,EAAO+U,aAAa4oB,GACpBh6B,EAAqBjD,GAAW,KACzBV,IAAUA,EAAOmH,WACtBnH,EAAO0W,eAAP,GAFF,GAIC,GANH,KAQO1W,EAAOqnB,UAChBjf,EAAK,8BACLpI,EAAOwR,eAAeksB,GACtB19B,EAAOmQ,cAAcqtB,GACrBx9B,EAAO+U,aAAa2oB,GACpB19B,EAAOyW,iBAAgB,EAAMzW,EAAO6c,gBAC/B7c,EAAOyV,YACVzV,EAAOyV,WAAY,EACnB9R,EAAqBjD,GAAW,KACzBV,IAAUA,EAAOmH,WACtBnH,EAAO0W,eAAP,MAIJ1W,EAAOwR,eAAeksB,GAGxB19B,EAAO2T,oBACP3T,EAAO0S,qBACR,KAAM,IAAIlS,EAAO6c,SAASmV,OAEzB,YADAxyB,EAAOkY,iBAEE1X,EAAO6c,UAChBjV,EAAK,6BACN,GAEI5H,EAAO6c,SAASyf,UAAY5c,GAAY1f,EAAOkgB,gBAClD1gB,EAAOwR,iBACPxR,EAAO2T,oBACP3T,EAAO0S,sBAjKR,CAmKF,IASF,EC9Nc,SAAkC3S,GAAA,IAAZC,OAAEA,GAAUD,EAC/CzH,OAAOkS,OAAOxK,EAAQ,CACpB8pB,YAAaA,GAAY/F,KAAK/jB,GAC9BmqB,aAAcA,GAAapG,KAAK/jB,GAChCqqB,SAAUA,GAAStG,KAAK/jB,GACxB0qB,YAAaA,GAAY3G,KAAK/jB,GAC9B6qB,gBAAiBA,GAAgB9G,KAAK/jB,IAEzC,G,OVLD2mB,EAAOqE,IAAIlE,I"}