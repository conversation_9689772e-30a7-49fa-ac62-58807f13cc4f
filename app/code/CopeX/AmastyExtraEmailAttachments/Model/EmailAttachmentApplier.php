<?php

namespace CopeX\AmastyExtraEmailAttachments\Model;

use Amasty\ProductAttachment\Controller\Adminhtml\RegistryConstants;
use Amasty\ProductAttachment\Model\File\FileScope\DataProviders\Product;
use Amasty\ProductAttachment\Model\SourceOptions\AttachmentType;
use Amasty\ProductAttachment\Model\Filesystem\Directory;
use Amasty\ProductAttachment\Api\FileRepositoryInterface;
use Fooman\EmailAttachments\Model\ContentAttacher;
use Magento\Framework\Event\Observer;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Directory\ReadInterface;

class EmailAttachmentApplier
{

    private ContentAttacher $contentAttacher;
    private FileRepositoryInterface $fileRepository;
    private ReadInterface $mediaReader;
    private Product $productDataProvider;

    public function __construct(ContentAttacher         $contentAttacher,
                                FileRepositoryInterface $fileRepository,
                                Filesystem              $fileSystem,
                                Product                 $productDataProvider
    )
    {
        $this->contentAttacher = $contentAttacher;
        $this->fileRepository = $fileRepository;
        $this->mediaReader = $fileSystem->getDirectoryRead(DirectoryList::MEDIA);
        $this->productDataProvider = $productDataProvider;
    }

    /**
     * @param array $attachmentIds
     * @param Observer $observer
     * @return void
     */
    public function attachFiles($productIds, $attachmentIds, $customerGroupId, $storeId, Observer $observer)
    {
        $attachments = $this->getProductAttachments($productIds, $storeId, $attachmentIds);
        foreach ($attachments as $attachmentId => $fileData) {
            $file = $this->fileRepository->getById($attachmentId);
            $file->addData($fileData);
            if ($this->canProcessFile($file, $customerGroupId)) {
                $this->addAttachmentToEmail($file, $attachmentId, $observer);
            }
        }
    }

    public function canProcessFile($file, $customerGroupId)
    {
        if (!$file->isVisible() || $file->getAttachmentType() !== AttachmentType::FILE) {
            return false;
        }
        if ($customerGroups = $file->getCustomerGroups()) {
            if (!in_array($customerGroupId, $customerGroups)) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param $productIds
     * @param $storeId
     * @param array $attachmentIds
     * @return array
     */
    public function getProductAttachments($productIds, $storeId, array $attachmentIds): array
    {
        $productAttachments = [];
        foreach ($productIds as $productId) {
            $productAttachments = array_merge($productAttachments, $this->productDataProvider->execute(
                [RegistryConstants::PRODUCT => $productId,
                    RegistryConstants::STORE => $storeId]
            ));
        }
        $attachments = [];
        foreach ($productAttachments as $productAttachment) {
            if (in_array($productAttachment['file_id'], $attachmentIds)) {
                $attachments [$productAttachment['file_id']] = $productAttachment;
            }
        }
        return $attachments;
    }

    /**
     * @param \Amasty\ProductAttachment\Api\Data\FileInterface $file
     * @param $attachmentId
     * @param Observer $observer
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function addAttachmentToEmail(\Amasty\ProductAttachment\Api\Data\FileInterface $file, $attachmentId, Observer $observer): void
    {
        $filePath = $this->mediaReader->getAbsolutePath(
            Directory::DIRECTORY_CODES[Directory::ATTACHMENT] . DIRECTORY_SEPARATOR . $file->getFilePath()
        );

        if ($this->mediaReader->isExist($filePath)) {

            $fileContent = $this->mediaReader->readFile($filePath);
            $fileEnding = ".".pathinfo($filePath, PATHINFO_EXTENSION);
            $fileName = $file->getLabel() ?? $file->getFileName() ?? $attachmentId;
            $fileName .= $fileEnding;

            if ($fileContent) {
                $this->contentAttacher->addGeneric(
                    $fileContent,
                    $fileName,
                    ContentAttacher::TYPE_OCTETSTREAM,
                    $observer->getAttachmentContainer()
                );
            }
        }
    }
}
