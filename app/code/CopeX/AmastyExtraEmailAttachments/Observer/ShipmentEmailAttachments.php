<?php

namespace CopeX\AmastyExtraEmailAttachments\Observer;

use CopeX\AmastyExtraEmailAttachments\Model\EmailAttachmentApplier;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Model\Order;


class ShipmentEmailAttachments implements ObserverInterface
{

    /**
     * @var EmailAttachmentApplier
     */
    private $applier;

    public function __construct( EmailAttachmentApplier $applier)
    {
        $this->applier = $applier;
    }

    public function getIds(Order $order): array
    {
        $orderItems = $order->getAllItems();
        $attachmentIds = [];
        $productIds = [];
        foreach ($orderItems as $orderItem) {
            $product = $orderItem->getProduct();
            $productIds []= $product->getId();
            if($product->getAttachmentShipping()) {
                $attachmentIds = array_merge($attachmentIds, explode(",", $product->getAttachmentShipping()));
            }
        }
        return [$productIds, $attachmentIds];
    }

    public function getCustomerGroupId(Order $order){
        return $order->getCustomerGroupId();
    }

    public function execute(Observer $observer)
    {
        /**
         * @var $order Order
         */
        $shipment = $observer->getShipment();
        $order = $shipment->getOrder();
        [$productIds, $attachmentIds] = $this->getIds($order);
        $customerGroupId = $this->getCustomerGroupId($order);
        $this->applier->attachFiles($productIds, $attachmentIds, $customerGroupId, $order->getStoreId(), $observer);

    }
}
