<?php
declare(strict_types=1);

namespace CopeX\AllTopProductOptions\Plugin\Magento\Sales\Model\Order\Pdf\Items;

use CopeX\AllTopProductOptions\Helper\Options;

class AbstractItems
{
    /** @var Options */
    private $optionsHelper;

    /**
     * @param Options $optionsHelper
     */
    public function __construct(Options $optionsHelper)
    {
        $this->optionsHelper = $optionsHelper;
    }

    public function afterGetItemOptions(
        \Magento\Sales\Model\Order\Pdf\Items\AbstractItems $subject,
        $options
    ) {
        return $this->optionsHelper->updateOptionsText($options);
    }
}
