<?php
declare(strict_types=1);

namespace CopeX\AllTopProductOptions\Plugin\Magento\Sales\Block\Order\Email\Items;

use CopeX\AllTopProductOptions\Helper\Options;

class DefaultItems
{
    /** @var Options */
    private $optionsHelper;

    /**
     * @param Options $optionsHelper
     */
    public function __construct(Options $optionsHelper)
    {
        $this->optionsHelper = $optionsHelper;
    }

    public function afterGetItemOptions(
        \Magento\Sales\Block\Order\Email\Items\DefaultItems $subject,
                                                            $options
    ) {
        return $this->optionsHelper->updateOptionsText($options);
    }
}
