<?php
declare(strict_types=1);

namespace CopeX\AllTopProductOptions\Plugin\Magento\Sales\Block\Order\Email\Items\Order;

use CopeX\AllTopProductOptions\Helper\Options;

class DefaultOrder
{
    /** @var Options */
    private $optionsHelper;

    /**
     * @param Options $optionsHelper
     */
    public function __construct(Options $optionsHelper)
    {
        $this->optionsHelper = $optionsHelper;
    }

    public function afterGetItemOptions(
        \Magento\Sales\Block\Order\Email\Items\Order\DefaultOrder $subject,
                                                                  $options
    ) {
        return $this->optionsHelper->updateOptionsText($options);
    }
}
