<?php

namespace CopeX\AllTopProductOptions\ViewModel;

use Magento\Framework\View\Element\Block\ArgumentInterface;

class Alltop implements ArgumentInterface
{

    public function hasAlltop($product){
        return true;
    }

    public function isAlltopParent($option){
        foreach ($option->getValues() as $value) {
            if(strpos($value->getTitle(),"[parent-alltop]") !== false){
                return true;
            }
        }
        return false;
    }

    public function isAlltopChild($option){
        if(strpos($option->getTitle(),"[child-alltop]") !== false){
            return true;
        }
        return false;
    }

}