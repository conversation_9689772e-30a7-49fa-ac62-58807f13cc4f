<?php

namespace CopeX\AllTopProductOptions\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;

class Options extends AbstractHelper
{
    /** @var OptionTitle */
    private $optionTitleHelper;

    /**
     * @param Context $context
     * @param OptionTitle $optionTitleHelper
     */
    public function __construct(
        Context     $context,
        OptionTitle $optionTitleHelper
    )
    {
        parent::__construct($context);
        $this->optionTitleHelper = $optionTitleHelper;
    }

    public function updateOptionsText($options)
    {
        foreach ($options as $key => $option) {
            if (isset($option['label'])) {
                $options[$key]['label'] = $this->optionTitleHelper->getPlainTitleText($option['label']);
            }

            if (isset($option['value'])) {
                $options[$key]['value'] = $this->optionTitleHelper->getPlainTitleText($option['value']);
            }

            if (isset($option['print_value'])) {
                $options[$key]['print_value'] = $this->optionTitleHelper->getPlainTitleText($option['print_value']);
            }
        }

        return $options;
    }
}
