# Mage2 Module CopeX ProductDetailSeoLink

## Main Functionalities
This module allows to add additional category links to the product detail view
The configuration can be found under system->configuration->catalog->product_detail_seo_link.

## FEATURES
 - Choose which stores should be enabled / disabled
 - Select a "Brand"-Category
    - The defined category will be used to get some cross links and to realize the output
    - Permission is that the brands are built with regular categories

 - The output will then be shown as: "See more %s at %s."
 - German translation is: "Weitere Produkte der Kategorien %s der Marke %s."
