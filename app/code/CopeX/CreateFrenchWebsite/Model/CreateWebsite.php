<?php

declare(strict_types=1);

namespace CopeX\CreateFrenchWebsite\Model;

use Magento\Store\Model\WebsiteFactory;
use Magento\Store\Model\GroupFactory;
use Magento\Store\Model\StoreFactory;
use Magento\Store\Model\ResourceModel\Group;
use Magento\Store\Model\ResourceModel\Store;
use Magento\Store\Model\ResourceModel\Website;
use Magento\Framework\Event\ManagerInterface;
use Magento\Catalog\Model\Product\Action;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory as ProductCollectionFactory;
use Magento\Framework\App\State;
use Magento\Framework\App\Area;

/**
 * Class CreateWebsite
 * @package CopeX\CreateFrenchWebsite\Model
 */
class CreateWebsite
{
    /**
     * @var WebsiteFactory
     */
    protected WebsiteFactory $websiteFactory;

    /**
     * @var GroupFactory
     */
    protected GroupFactory $groupFactory;

    /**
     * @var StoreFactory
     */
    protected StoreFactory $storeFactory;

    /**
     * @var Website
     */
    protected Website $websiteResourceModel;

    /**
     * @var Store
     */
    protected Store $storeResourceModel;

    /**
     * @var Group
     */
    protected Group $groupResourceModel;

    /**
     * @var ManagerInterface
     */
    protected ManagerInterface $eventManager;

    /**
     * @var Action
     */
    protected Action $action;

    /**
     * @var ProductCollectionFactory
     */
    protected ProductCollectionFactory $productCollectionFactory;

    /**
     * @var State
     */
    protected State $state;

    /**
     * CreateWebsite constructor.
     * @param WebsiteFactory $websiteFactory
     * @param GroupFactory $groupFactory
     * @param StoreFactory $storeFactory
     * @param Website $websiteResourceModel
     * @param Store $storeResourceModel
     * @param Group $groupResourceModel
     * @param ManagerInterface $eventManager
     * @param Action $action
     * @param ProductCollectionFactory $productCollectionFactory
     * @param State $state
     */
    public function __construct(
        WebsiteFactory $websiteFactory,
        GroupFactory $groupFactory,
        StoreFactory $storeFactory,
        Website $websiteResourceModel,
        Store $storeResourceModel,
        Group $groupResourceModel,
        ManagerInterface $eventManager,
        Action $action,
        ProductCollectionFactory $productCollectionFactory,
        State $state
    ) {
        $this->websiteFactory = $websiteFactory;
        $this->groupFactory = $groupFactory;
        $this->storeFactory = $storeFactory;
        $this->websiteResourceModel = $websiteResourceModel;
        $this->storeResourceModel = $storeResourceModel;
        $this->groupResourceModel = $groupResourceModel;
        $this->eventManager = $eventManager;
        $this->action = $action;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->state = $state;
    }

    /**
     * @throws \Magento\Framework\Exception\AlreadyExistsException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function installRun()
    {
        //Set area code
        $this->state->setAreaCode(Area::AREA_FRONTEND);

        //Create website
        $website = $this->websiteFactory->create();
        $website->load('gfp_fr');
        if (!$website->getId()) {
            $website->setCode('gfp_fr');
            $website->setName('GFP FR');
            $website->setDefaultGroupId(1);
            $this->websiteResourceModel->save($website);
        }

        //Create group
        if ($website->getId()) {
            $group = $this->groupFactory->create();
            $groupExists = $this->existStoreGroup();
            if (!$groupExists) {
                $group->setWebsiteId($website->getWebsiteId());
                $group->setName('FR');
                $group->setRootCategoryId(2);
                $group->setDefaultStoreId(1);
                $group->setCode('fr');
                $this->groupResourceModel->save($group);
            }
        }

        //Create store
        $store = $this->storeFactory->create();
        $store->load('fr');
        if (!$store->getId()) {
            $group = $this->groupFactory->create();
            $group->load('FR', 'name');
            $store->setCode('fr');
            $store->setName('FR');
            $store->setWebsite($website);
            $store->setGroupId($group->getId());
            $store->setData('is_active','1');
            $this->storeResourceModel->save($store);
            $this->eventManager->dispatch('store_add', ['store' => $store]);
        }

        //Upload products to website
        $websiteId = $website->load('gfp_fr')->getId();
        $this->assignProductsToWebsite($websiteId);
    }

    protected function existStoreGroup()
    {
        return $this->groupResourceModel->getConnection()->fetchOne($this->groupResourceModel->getConnection()->select()
            ->from($this->groupResourceModel->getTable('store_group'), [new \Zend_Db_Expr(1)])
            ->where('name = ?', 'FR'));
    }

    /**
     * @param $websiteId
     */
    protected function assignProductsToWebsite($websiteId)
    {
        $collection = $this->productCollectionFactory->create();
        foreach ($collection as $product) {
            $this->action->updateWebsites(
                [$product->getId()],
                [$websiteId],
                'add'
            );
        }
    }
}
