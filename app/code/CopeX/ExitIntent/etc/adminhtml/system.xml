<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="exitintent" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label">
            <label>Exit Intent</label>
            <tab>catalog</tab>
            <resource>CopeX_ExitIntent::config_copex_exitintent</resource>
            <group id="settings" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label">
                <label>Settings</label>
                <field id="enabled_product" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label"
                       type="select">
                    <label>Show on product page</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="show_on_first_visit" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label"
                       type="select">
                    <label>Only show on firts visit</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="within_seconds" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label"
                       type="text">
                    <label>Only show within the first x seconds</label>
                </field>
                <field id="show_once" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label"
                       type="select">
                    <label>Show only once</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
