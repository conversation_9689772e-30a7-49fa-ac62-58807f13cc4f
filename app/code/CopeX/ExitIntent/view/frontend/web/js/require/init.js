define(['jquery','mage/cookies'], function ($,cookies) {
        return function (data) {
            var now = new Date();
            const exit = e => {
                const shouldExit =
                    [...e.target.classList].includes('exit-intent-popup') || // user clicks on mask
                    e.target.className === 'close' || // user clicks on the close icon
                    e.keyCode === 27; // user hits escape

                if (shouldExit) {
                    document.querySelector('.exit-intent-popup').classList.remove('visible');
                }
            };

            const mouseEvent = e => {
                if( !e.toElement &&
                    !e.relatedTarget &&
                    e.clientY < 10 ){
                    showExitIntentPopup()
                }
            };

            const mouseMove = e => {
                if (e.movementY < 0 && (e.y <= 0 || e.movementY < -e.y)) {
                    showExitIntentPopup()
                }
            };

            const showExitIntentPopup = () => {
                if( firstVisit === undefined || data.firstVisit === "0" || (firstVisit && data.firstVisit === "1" ) ){
                    let elapsed = new Date(), dif = ( elapsed.getTime() - now.getTime()) / 1000;
                    if( data.seconds === "0" || dif < data.seconds){
                        document.removeEventListener('mouseout', mouseEvent);
                        document.removeEventListener('mouseout', mouseMove);
                        document.querySelector('.exit-intent-popup').classList.add('visible');
                        if (data.showOnce) {
                            $.mage.cookies.set('exitIntentShown', true, {"lifetime": 30});
                        }
                    }
                }
            }

            if (!$.mage.cookies.get('exitIntentShown')) {
                setTimeout(() => {
                    document.addEventListener('mouseout', mouseEvent);
                    document.addEventListener('mousemove', mouseMove);
                    document.addEventListener('keydown', exit);
                    document.querySelector('.exit-intent-popup').addEventListener('click', exit);
                }, 0);
            }
        }
    }
);
