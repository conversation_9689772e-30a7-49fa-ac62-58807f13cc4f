<?php
namespace CopeX\ResourceHints\Observer\Framework\View\Layout;

use CopeX\ResourceHints\Helper\Config;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\View\Page\Config as PageConfig;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Store\Model\StoreManagerInterface;

class Builder implements ObserverInterface
{


    /** @var Config $config */
    private $config;

    /** @var PageConfig $pageConfig */
    private $pageConfig;

    /** @var Filesystem $filesystem */
    private $filesystem;

    /** @var StoreManagerInterface $storeManager */
    private $storeManager;


    /**
     * Builder constructor.
     *
     * @param Filesystem $filesystem
     * @param Config $config
     * @param PageConfig  $pageConfig
     * @param StoreManagerInterface  $storeManager
     */
    public function __construct(
        Filesystem $filesystem,
        Config $config,
        PageConfig $pageConfig,
        StoreManagerInterface $storeManager,
    ) {
        $this->filesystem = $filesystem;
        $this->config = $config;
        $this->pageConfig  = $pageConfig;
        $this->storeManager = $storeManager;
    }

    /**
     * @param Observer $observer
     *
     * @return $this
     */
    public function execute(Observer $observer)
    {
        $storeId = $this->storeManager->getStore()->getId();
        $config = $this->config->getResourceConfig($storeId);

        if (!$config) {
            return $this;
        }

        $themeCode = $this->config->getThemeCode($storeId);
        $resourceHints = $this->sort($config);
        $localeCode = $this->config->getLocaleCode($storeId);

        $staticVersion = $this->getStaticVersion();
        $layoutHandles = $observer->getLayout()->getUpdate()->getHandles();

        foreach ($resourceHints as $resource) {
            if( ($resource['handle'] ?? false) && count(array_intersect( explode(",", $resource['handle']) , $layoutHandles) ) === 0){
                continue;
            }
            $attributes = [];
            $attributes['rel'] = $resource['type'];
            $resource['resource'] = str_replace(
                ['VERSION','THEME','LANG'],
                [$staticVersion ? ('version' . $staticVersion) : "",$themeCode, $localeCode ],
                $resource['resource']
            );

            if ($resource['type'] == 'preload') {
                $attributes['as'] = $resource['preload_as'];
            }
            if ($resource['preload_as'] === "font" || $resource['preload_as'] === "style") {
                $attributes['crossorigin'] = 'crossorigin';
            }

            $this->pageConfig->addRemotePageAsset(
                $resource['resource'],
                'link_rel',
                [
                    'attributes' => $attributes
                ]
            );
        }

        return $this;
    }

    private function sort(array $resourceHints)
    {
        usort($resourceHints, function ($first, $second) {
            return $first['sort_order'] <=> $second['sort_order'];
        });

        return $resourceHints;
    }

    /**
     * @return string
     */
    public function getStaticVersion(): string
    {
        try {
            /** @var \Magento\Framework\Filesystem\Directory\ReadInterface $mediaRead */
            $mediaRead = $this->filesystem->getDirectoryRead(DirectoryList::PUB);
            if( $mediaRead->isExist("static/deployed_version.txt") ){
                return $mediaRead->readFile("static/deployed_version.txt");
            }
        }catch (\Exception $e){}
        return "";
    }
}