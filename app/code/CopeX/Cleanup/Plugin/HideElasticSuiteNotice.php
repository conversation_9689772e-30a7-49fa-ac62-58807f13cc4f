<?php

namespace CopeX\Cleanup\Plugin;

use Smile\ElasticsuiteCore\Model\System\Message\NotificationAboutVersions;
use Smile\ElasticsuiteTracker\Model\Condition\CanViewNotification;

class HideElasticSuiteNotice
{
    /**
     * @param NotificationAboutVersions $subject
     * @param callable                  $proceed
     * @return bool
     */
    public function aroundIsDisplayed(NotificationAboutVersions $subject, callable $proceed): bool
    {
        return false;
    }

    /**
     * @param CanViewNotification $subject
     * @param callable            $proceed
     * @param array               $arguments
     * @return bool
     */
    public function aroundIsVisible(CanViewNotification $subject, callable $proceed, array $arguments): bool
    {
        return false;
    }
}
