<?php

declare(strict_types=1);

namespace Cope<PERSON>\ImageTracker\Cron;

use Cope<PERSON>\ImageTracker\Model\ImageScanner;
use <PERSON>X\ImageTracker\Logger\Logger;

/**
 * Class ScanMissingImages
 * @package CopeX\ImageTracker\Cron
 */
class ScanMissingImages
{
    /**
     * @var ImageScanner
     */
    private $imageScanner;

    /**
     * @var Logger
     */
    private $logger;

    /**
     * ScanMissingImages constructor.
     *
     * @param ImageScanner $imageScanner
     * @param Logger $logger
     */
    public function __construct(
        ImageScanner $imageScanner,
        Logger $logger
    ) {
        $this->imageScanner = $imageScanner;
        $this->logger = $logger;
    }

    /**
     * Execute cron job
     *
     * @return void
     */
    public function execute(): void
    {
        try {
            $this->logger->info('Starting scheduled image scan via cron');
            
            $startTime = microtime(true);
            $results = $this->imageScanner->scanMissingImages();
            $endTime = microtime(true);
            
            $executionTime = round($endTime - $startTime, 2);
            
            $this->logger->info('Scheduled image scan completed', [
                'execution_time' => $executionTime,
                'results' => $results
            ]);
            
        } catch (\Exception $e) {
            $this->logger->error('Error during scheduled image scan: ' . $e->getMessage(), [
                'exception' => $e->getTraceAsString()
            ]);
        }
    }
}
