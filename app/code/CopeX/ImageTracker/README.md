# CopeX Image Tracker Module

## Overview

The CopeX Image Tracker module is designed to monitor and track missing product images in Magento 2. It helps identify when product images are deleted from the filesystem while their database entries remain, allowing you to track when and what causes image deletions.

## Features

- **Automated Scanning**: Runs every 30 minutes via cron job to scan for missing images
- **Database Tracking**: Stores missing image information in a dedicated database table
- **Detailed Logging**: Logs all activities to `/var/log/copex_image_tracker.log`
- **Console Commands**: Manual scanning and reporting capabilities
- **Status Tracking**: Tracks when images go missing and when they are resolved

## Installation

1. Copy the module files to `app/code/CopeX/ImageTracker/`
2. Run the following commands:
   ```bash
   php bin/magento module:enable CopeX_ImageTracker
   php bin/magento setup:upgrade
   php bin/magento setup:di:compile
   php bin/magento cache:flush
   ```

## Database Schema

The module creates a table `copex_image_tracker_missing_images` with the following structure:

- `entity_id`: Primary key
- `image_path`: Path to the missing image
- `product_id`: Associated product ID
- `product_sku`: Associated product SKU
- `image_type`: Type of image (base, small, thumbnail, etc.)
- `media_gallery_id`: Media gallery entry ID
- `first_detected`: When the missing image was first detected
- `last_checked`: When the image was last checked
- `status`: Current status (missing, resolved)
- `additional_info`: Additional information

## Console Commands

### Scan for Missing Images
```bash
php bin/magento copex:image-tracker:scan
```
Options:
- `--verbose` or `-v`: Enable verbose output

### Generate Missing Images Report
```bash
php bin/magento copex:image-tracker:report
```
Options:
- `--status` or `-s`: Filter by status (missing, resolved) - default: missing
- `--limit` or `-l`: Limit number of results - default: 50
- `--since`: Show images detected since date (Y-m-d H:i:s format)

Examples:
```bash
# Show all missing images
php bin/magento copex:image-tracker:report

# Show resolved images
php bin/magento copex:image-tracker:report --status=resolved

# Show missing images detected in the last 24 hours
php bin/magento copex:image-tracker:report --since="2024-01-01 00:00:00"

# Show only 10 results
php bin/magento copex:image-tracker:report --limit=10
```

## Cron Job

The module automatically runs a scan every 30 minutes via the cron job `copex_image_tracker_scan`. The cron expression is `*/30 * * * *`.

## Logging

All activities are logged to `/var/log/copex_image_tracker.log` including:
- New missing images detected
- Images that have been resolved
- Scan results and statistics
- Error messages

## How It Works

1. **Image Scanning**: The module scans all images in the `catalog_product_entity_media_gallery` table
2. **File Verification**: For each image, it checks if the physical file exists in the filesystem
3. **Missing Image Detection**: If a file doesn't exist, it's recorded as missing
4. **Status Tracking**: The module tracks when images go missing and when they're restored
5. **Logging**: All events are logged with timestamps for analysis

## Troubleshooting

### Check if the module is enabled
```bash
php bin/magento module:status CopeX_ImageTracker
```

### Check cron jobs
```bash
php bin/magento cron:run --group=default
```

### View logs
```bash
tail -f var/log/copex_image_tracker.log
```

### Manual database check
```sql
SELECT * FROM copex_image_tracker_missing_images WHERE status = 'missing' ORDER BY first_detected DESC;
```

## Use Cases

- **Debugging Image Deletions**: Track when and which images are being deleted
- **System Monitoring**: Monitor the health of your product image library
- **Maintenance**: Identify orphaned database entries for cleanup
- **Performance Analysis**: Understand patterns in image deletion

## Technical Details

- **Namespace**: CopeX\ImageTracker
- **Module Name**: CopeX_ImageTracker
- **Dependencies**: Magento_Catalog
- **Database Table**: copex_image_tracker_missing_images
- **Log File**: var/log/copex_image_tracker.log
- **Cron Group**: default
- **Cron Schedule**: Every 30 minutes (*/30 * * * *)

## Support

For issues or questions regarding this module, please check the log files first and ensure all dependencies are properly installed.
