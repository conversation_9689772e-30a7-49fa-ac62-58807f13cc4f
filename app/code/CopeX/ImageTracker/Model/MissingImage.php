<?php

declare(strict_types=1);

namespace CopeX\ImageTracker\Model;

use Magento\Framework\Model\AbstractModel;
use CopeX\ImageTracker\Api\Data\MissingImageInterface;

/**
 * Class MissingImage
 * @package CopeX\ImageTracker\Model
 */
class MissingImage extends AbstractModel implements MissingImageInterface
{
    const CACHE_TAG = 'copex_image_tracker_missing_image';

    /**
     * @var string
     */
    protected $_cacheTag = self::CACHE_TAG;

    /**
     * @var string
     */
    protected $_eventPrefix = 'copex_image_tracker_missing_image';

    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(\CopeX\ImageTracker\Model\ResourceModel\MissingImage::class);
    }

    /**
     * Get entity ID
     *
     * @return int|null
     */
    public function getEntityId(): ?int
    {
        return $this->getData(self::ENTITY_ID) ? (int)$this->getData(self::ENTITY_ID) : null;
    }

    /**
     * Set entity ID
     *
     * @param int $entityId
     * @return MissingImageInterface
     */
    public function setEntityId($entityId): MissingImageInterface
    {
        return $this->setData(self::ENTITY_ID, $entityId);
    }

    /**
     * Get image path
     *
     * @return string|null
     */
    public function getImagePath(): ?string
    {
        return $this->getData(self::IMAGE_PATH);
    }

    /**
     * Set image path
     *
     * @param string $imagePath
     * @return MissingImageInterface
     */
    public function setImagePath(string $imagePath): MissingImageInterface
    {
        return $this->setData(self::IMAGE_PATH, $imagePath);
    }

    /**
     * Get product ID
     *
     * @return int|null
     */
    public function getProductId(): ?int
    {
        return $this->getData(self::PRODUCT_ID) ? (int)$this->getData(self::PRODUCT_ID) : null;
    }

    /**
     * Set product ID
     *
     * @param int|null $productId
     * @return MissingImageInterface
     */
    public function setProductId(?int $productId): MissingImageInterface
    {
        return $this->setData(self::PRODUCT_ID, $productId);
    }

    /**
     * Get product SKU
     *
     * @return string|null
     */
    public function getProductSku(): ?string
    {
        return $this->getData(self::PRODUCT_SKU);
    }

    /**
     * Set product SKU
     *
     * @param string|null $productSku
     * @return MissingImageInterface
     */
    public function setProductSku(?string $productSku): MissingImageInterface
    {
        return $this->setData(self::PRODUCT_SKU, $productSku);
    }

    /**
     * Get image type
     *
     * @return string|null
     */
    public function getImageType(): ?string
    {
        return $this->getData(self::IMAGE_TYPE);
    }

    /**
     * Set image type
     *
     * @param string|null $imageType
     * @return MissingImageInterface
     */
    public function setImageType(?string $imageType): MissingImageInterface
    {
        return $this->setData(self::IMAGE_TYPE, $imageType);
    }

    /**
     * Get media gallery ID
     *
     * @return int|null
     */
    public function getMediaGalleryId(): ?int
    {
        return $this->getData(self::MEDIA_GALLERY_ID) ? (int)$this->getData(self::MEDIA_GALLERY_ID) : null;
    }

    /**
     * Set media gallery ID
     *
     * @param int|null $mediaGalleryId
     * @return MissingImageInterface
     */
    public function setMediaGalleryId(?int $mediaGalleryId): MissingImageInterface
    {
        return $this->setData(self::MEDIA_GALLERY_ID, $mediaGalleryId);
    }

    /**
     * Get first detected time
     *
     * @return string|null
     */
    public function getFirstDetected(): ?string
    {
        return $this->getData(self::FIRST_DETECTED);
    }

    /**
     * Set first detected time
     *
     * @param string $firstDetected
     * @return MissingImageInterface
     */
    public function setFirstDetected(string $firstDetected): MissingImageInterface
    {
        return $this->setData(self::FIRST_DETECTED, $firstDetected);
    }

    /**
     * Get last checked time
     *
     * @return string|null
     */
    public function getLastChecked(): ?string
    {
        return $this->getData(self::LAST_CHECKED);
    }

    /**
     * Set last checked time
     *
     * @param string $lastChecked
     * @return MissingImageInterface
     */
    public function setLastChecked(string $lastChecked): MissingImageInterface
    {
        return $this->setData(self::LAST_CHECKED, $lastChecked);
    }

    /**
     * Get status
     *
     * @return string|null
     */
    public function getStatus(): ?string
    {
        return $this->getData(self::STATUS);
    }

    /**
     * Set status
     *
     * @param string $status
     * @return MissingImageInterface
     */
    public function setStatus(string $status): MissingImageInterface
    {
        return $this->setData(self::STATUS, $status);
    }

    /**
     * Get additional info
     *
     * @return string|null
     */
    public function getAdditionalInfo(): ?string
    {
        return $this->getData(self::ADDITIONAL_INFO);
    }

    /**
     * Set additional info
     *
     * @param string|null $additionalInfo
     * @return MissingImageInterface
     */
    public function setAdditionalInfo(?string $additionalInfo): MissingImageInterface
    {
        return $this->setData(self::ADDITIONAL_INFO, $additionalInfo);
    }
}
