<?php

declare(strict_types=1);

namespace Cope<PERSON>\ImageTracker\Model;

use Cope<PERSON>\ImageTracker\Api\Data\MissingImageInterface;
use Cope<PERSON>\ImageTracker\Api\Data\MissingImageInterfaceFactory;
use Cope<PERSON>\ImageTracker\Api\MissingImageRepositoryInterface;
use Cope<PERSON>\ImageTracker\Logger\Logger;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory as ProductCollectionFactory;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Io\File;
use Magento\Framework\App\ResourceConnection;

/**
 * Class ImageScanner
 * @package CopeX\ImageTracker\Model
 */
class ImageScanner
{
    /**
     * @var ProductCollectionFactory
     */
    private $productCollectionFactory;

    /**
     * @var Filesystem
     */
    private $filesystem;

    /**
     * @var File
     */
    private $file;

    /**
     * @var MissingImageInterfaceFactory
     */
    private $missingImageFactory;

    /**
     * @var MissingImageRepositoryInterface
     */
    private $missingImageRepository;

    /**
     * @var Logger
     */
    private $logger;

    /**
     * @var ResourceConnection
     */
    private $resourceConnection;

    /**
     * ImageScanner constructor.
     *
     * @param ProductCollectionFactory $productCollectionFactory
     * @param Filesystem $filesystem
     * @param File $file
     * @param MissingImageInterfaceFactory $missingImageFactory
     * @param MissingImageRepositoryInterface $missingImageRepository
     * @param Logger $logger
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        ProductCollectionFactory $productCollectionFactory,
        Filesystem $filesystem,
        File $file,
        MissingImageInterfaceFactory $missingImageFactory,
        MissingImageRepositoryInterface $missingImageRepository,
        Logger $logger,
        ResourceConnection $resourceConnection
    ) {
        $this->productCollectionFactory = $productCollectionFactory;
        $this->filesystem = $filesystem;
        $this->file = $file;
        $this->missingImageFactory = $missingImageFactory;
        $this->missingImageRepository = $missingImageRepository;
        $this->logger = $logger;
        $this->resourceConnection = $resourceConnection;
    }

    /**
     * Scan for missing product images
     *
     * @return array
     */
    public function scanMissingImages(): array
    {
        $results = [
            'new_missing' => 0,
            'resolved' => 0,
            'still_missing' => 0,
            'errors' => []
        ];

        try {
            $mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);
            $mediaPath = $mediaDirectory->getAbsolutePath();
            $catalogProductPath = $mediaPath . 'catalog' . DIRECTORY_SEPARATOR . 'product';

            // Get all images from media gallery
            $connection = $this->resourceConnection->getConnection();
            $mediaGalleryTable = $this->resourceConnection->getTableName('catalog_product_entity_media_gallery');
            $mediaGalleryValueTable = $this->resourceConnection->getTableName('catalog_product_entity_media_gallery_value');
            $productEntityTable = $this->resourceConnection->getTableName('catalog_product_entity');

            $select = $connection->select()
                ->from(['mg' => $mediaGalleryTable], ['value_id', 'value'])
                ->joinLeft(
                    ['mgv' => $mediaGalleryValueTable],
                    'mg.value_id = mgv.value_id',
                    ['entity_id']
                )
                ->joinLeft(
                    ['pe' => $productEntityTable],
                    'mgv.entity_id = pe.entity_id',
                    ['sku']
                )
                ->where('mg.disabled = 0');

            $mediaGalleryImages = $connection->fetchAll($select);

            foreach ($mediaGalleryImages as $imageData) {
                $imagePath = $imageData['value'];
                $fullImagePath = $catalogProductPath . $imagePath;
                $productId = (int)$imageData['entity_id'];
                $productSku = $imageData['sku'];
                $mediaGalleryId = (int)$imageData['value_id'];

                // Check if file exists
                if (!$this->file->fileExists($fullImagePath)) {
                    $this->handleMissingImage($imagePath, $productId, $productSku, $mediaGalleryId, $results);
                } else {
                    $this->handleFoundImage($imagePath, $productId, $results);
                }
            }

            $this->logger->info('Image scan completed', $results);
        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
            $this->logger->error('Error during image scan: ' . $e->getMessage());
        }

        return $results;
    }

    /**
     * Handle missing image
     *
     * @param string $imagePath
     * @param int|null $productId
     * @param string|null $productSku
     * @param int $mediaGalleryId
     * @param array &$results
     */
    private function handleMissingImage(string $imagePath, ?int $productId, ?string $productSku, int $mediaGalleryId, array &$results): void
    {
        try {
            // Check if this missing image is already tracked
            $existingMissingImage = $this->missingImageRepository->getByImagePathAndProductId($imagePath, $productId);
            
            // Update last checked time
            $existingMissingImage->setLastChecked(date('Y-m-d H:i:s'));
            $this->missingImageRepository->save($existingMissingImage);
            $results['still_missing']++;
            
        } catch (NoSuchEntityException $e) {
            // New missing image - create record
            $missingImage = $this->missingImageFactory->create();
            $missingImage->setImagePath($imagePath);
            $missingImage->setProductId($productId);
            $missingImage->setProductSku($productSku);
            $missingImage->setMediaGalleryId($mediaGalleryId);
            $missingImage->setStatus(MissingImageInterface::STATUS_MISSING);
            $missingImage->setFirstDetected(date('Y-m-d H:i:s'));
            $missingImage->setLastChecked(date('Y-m-d H:i:s'));
            
            $this->missingImageRepository->save($missingImage);
            $results['new_missing']++;
            
            $this->logger->info('New missing image detected', [
                'image_path' => $imagePath,
                'product_id' => $productId,
                'product_sku' => $productSku,
                'media_gallery_id' => $mediaGalleryId
            ]);
        }
    }

    /**
     * Handle found image (resolve if it was previously missing)
     *
     * @param string $imagePath
     * @param int|null $productId
     * @param array &$results
     */
    private function handleFoundImage(string $imagePath, ?int $productId, array &$results): void
    {
        try {
            $existingMissingImage = $this->missingImageRepository->getByImagePathAndProductId($imagePath, $productId);
            
            // Image was found - mark as resolved
            $existingMissingImage->setStatus(MissingImageInterface::STATUS_RESOLVED);
            $existingMissingImage->setLastChecked(date('Y-m-d H:i:s'));
            $this->missingImageRepository->save($existingMissingImage);
            $results['resolved']++;
            
            $this->logger->info('Missing image resolved', [
                'image_path' => $imagePath,
                'product_id' => $productId
            ]);
            
        } catch (NoSuchEntityException $e) {
            // Image exists and was never missing - nothing to do
        }
    }
}
