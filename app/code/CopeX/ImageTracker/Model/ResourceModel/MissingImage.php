<?php

declare(strict_types=1);

namespace CopeX\ImageTracker\Model\ResourceModel;

use Magento\Framework\Model\ResourceModel\Db\AbstractDb;

/**
 * Class MissingImage
 * @package CopeX\ImageTracker\Model\ResourceModel
 */
class MissingImage extends AbstractDb
{
    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init('copex_image_tracker_missing_images', 'entity_id');
    }
}
