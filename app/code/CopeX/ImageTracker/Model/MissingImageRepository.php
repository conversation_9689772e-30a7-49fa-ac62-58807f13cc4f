<?php

declare(strict_types=1);

namespace Cope<PERSON>\ImageTracker\Model;

use Cope<PERSON>\ImageTracker\Api\Data\MissingImageInterface;
use CopeX\ImageTracker\Api\MissingImageRepositoryInterface;
use Cope<PERSON>\ImageTracker\Model\ResourceModel\MissingImage as MissingImageResource;
use CopeX\ImageTracker\Model\ResourceModel\MissingImage\CollectionFactory;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchResultsInterface;
use Magento\Framework\Api\SearchResultsInterfaceFactory;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Class MissingImageRepository
 * @package CopeX\ImageTracker\Model
 */
class MissingImageRepository implements MissingImageRepositoryInterface
{
    /**
     * @var MissingImageResource
     */
    private $resource;

    /**
     * @var MissingImageFactory
     */
    private $missingImageFactory;

    /**
     * @var CollectionFactory
     */
    private $collectionFactory;

    /**
     * @var SearchResultsInterfaceFactory
     */
    private $searchResultsFactory;

    /**
     * MissingImageRepository constructor.
     *
     * @param MissingImageResource $resource
     * @param MissingImageFactory $missingImageFactory
     * @param CollectionFactory $collectionFactory
     * @param SearchResultsInterfaceFactory $searchResultsFactory
     */
    public function __construct(
        MissingImageResource $resource,
        MissingImageFactory $missingImageFactory,
        CollectionFactory $collectionFactory,
        SearchResultsInterfaceFactory $searchResultsFactory
    ) {
        $this->resource = $resource;
        $this->missingImageFactory = $missingImageFactory;
        $this->collectionFactory = $collectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
    }

    /**
     * Save missing image
     *
     * @param MissingImageInterface $missingImage
     * @return MissingImageInterface
     * @throws CouldNotSaveException
     */
    public function save(MissingImageInterface $missingImage): MissingImageInterface
    {
        try {
            $this->resource->save($missingImage);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__('Could not save missing image: %1', $exception->getMessage()));
        }
        return $missingImage;
    }

    /**
     * Get missing image by ID
     *
     * @param int $entityId
     * @return MissingImageInterface
     * @throws NoSuchEntityException
     */
    public function getById(int $entityId): MissingImageInterface
    {
        $missingImage = $this->missingImageFactory->create();
        $this->resource->load($missingImage, $entityId);
        if (!$missingImage->getEntityId()) {
            throw new NoSuchEntityException(__('Missing image with ID "%1" does not exist.', $entityId));
        }
        return $missingImage;
    }

    /**
     * Get missing image by image path and product ID
     *
     * @param string $imagePath
     * @param int|null $productId
     * @return MissingImageInterface
     * @throws NoSuchEntityException
     */
    public function getByImagePathAndProductId(string $imagePath, ?int $productId = null): MissingImageInterface
    {
        $collection = $this->collectionFactory->create();
        $collection->addFieldToFilter('image_path', $imagePath);
        if ($productId !== null) {
            $collection->addFieldToFilter('product_id', $productId);
        } else {
            $collection->addFieldToFilter('product_id', ['null' => true]);
        }
        
        $missingImage = $collection->getFirstItem();
        if (!$missingImage->getEntityId()) {
            throw new NoSuchEntityException(__('Missing image with path "%1" and product ID "%2" does not exist.', $imagePath, $productId));
        }
        return $missingImage;
    }

    /**
     * Get list of missing images
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return SearchResultsInterface
     */
    public function getList(SearchCriteriaInterface $searchCriteria): SearchResultsInterface
    {
        $collection = $this->collectionFactory->create();
        
        foreach ($searchCriteria->getFilterGroups() as $filterGroup) {
            foreach ($filterGroup->getFilters() as $filter) {
                $collection->addFieldToFilter($filter->getField(), [$filter->getConditionType() => $filter->getValue()]);
            }
        }
        
        $sortOrders = $searchCriteria->getSortOrders();
        if ($sortOrders) {
            foreach ($sortOrders as $sortOrder) {
                $collection->addOrder($sortOrder->getField(), $sortOrder->getDirection());
            }
        }
        
        $collection->setCurPage($searchCriteria->getCurrentPage());
        $collection->setPageSize($searchCriteria->getPageSize());
        
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($searchCriteria);
        $searchResults->setItems($collection->getItems());
        $searchResults->setTotalCount($collection->getSize());
        
        return $searchResults;
    }

    /**
     * Delete missing image
     *
     * @param MissingImageInterface $missingImage
     * @return bool
     * @throws CouldNotDeleteException
     */
    public function delete(MissingImageInterface $missingImage): bool
    {
        try {
            $this->resource->delete($missingImage);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__('Could not delete missing image: %1', $exception->getMessage()));
        }
        return true;
    }

    /**
     * Delete missing image by ID
     *
     * @param int $entityId
     * @return bool
     * @throws NoSuchEntityException
     * @throws CouldNotDeleteException
     */
    public function deleteById(int $entityId): bool
    {
        return $this->delete($this->getById($entityId));
    }
}
