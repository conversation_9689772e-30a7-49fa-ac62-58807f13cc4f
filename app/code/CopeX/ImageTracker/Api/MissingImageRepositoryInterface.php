<?php

declare(strict_types=1);

namespace CopeX\ImageTracker\Api;

use Cope<PERSON>\ImageTracker\Api\Data\MissingImageInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchResultsInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Interface MissingImageRepositoryInterface
 * @package CopeX\ImageTracker\Api
 */
interface MissingImageRepositoryInterface
{
    /**
     * Save missing image
     *
     * @param MissingImageInterface $missingImage
     * @return MissingImageInterface
     * @throws LocalizedException
     */
    public function save(MissingImageInterface $missingImage): MissingImageInterface;

    /**
     * Get missing image by ID
     *
     * @param int $entityId
     * @return MissingImageInterface
     * @throws NoSuchEntityException
     */
    public function getById(int $entityId): MissingImageInterface;

    /**
     * Get missing image by image path and product ID
     *
     * @param string $imagePath
     * @param int|null $productId
     * @return MissingImageInterface
     * @throws NoSuchEntityException
     */
    public function getByImagePathAndProductId(string $imagePath, ?int $productId = null): MissingImageInterface;

    /**
     * Get list of missing images
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return SearchResultsInterface
     */
    public function getList(SearchCriteriaInterface $searchCriteria): SearchResultsInterface;

    /**
     * Delete missing image
     *
     * @param MissingImageInterface $missingImage
     * @return bool
     * @throws LocalizedException
     */
    public function delete(MissingImageInterface $missingImage): bool;

    /**
     * Delete missing image by ID
     *
     * @param int $entityId
     * @return bool
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function deleteById(int $entityId): bool;
}
