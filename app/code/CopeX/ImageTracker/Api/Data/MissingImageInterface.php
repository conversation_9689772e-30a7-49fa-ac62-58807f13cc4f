<?php

declare(strict_types=1);

namespace CopeX\ImageTracker\Api\Data;

/**
 * Interface MissingImageInterface
 * @package CopeX\ImageTracker\Api\Data
 */
interface MissingImageInterface
{
    const ENTITY_ID = 'entity_id';
    const IMAGE_PATH = 'image_path';
    const PRODUCT_ID = 'product_id';
    const PRODUCT_SKU = 'product_sku';
    const IMAGE_TYPE = 'image_type';
    const MEDIA_GALLERY_ID = 'media_gallery_id';
    const FIRST_DETECTED = 'first_detected';
    const LAST_CHECKED = 'last_checked';
    const STATUS = 'status';
    const ADDITIONAL_INFO = 'additional_info';

    const STATUS_MISSING = 'missing';
    const STATUS_RESOLVED = 'resolved';

    /**
     * Get entity ID
     *
     * @return int|null
     */
    public function getEntityId(): ?int;

    /**
     * Set entity ID
     *
     * @param int $entityId
     * @return MissingImageInterface
     */
    public function setEntityId($entityId): MissingImageInterface;

    /**
     * Get image path
     *
     * @return string|null
     */
    public function getImagePath(): ?string;

    /**
     * Set image path
     *
     * @param string $imagePath
     * @return MissingImageInterface
     */
    public function setImagePath(string $imagePath): MissingImageInterface;

    /**
     * Get product ID
     *
     * @return int|null
     */
    public function getProductId(): ?int;

    /**
     * Set product ID
     *
     * @param int|null $productId
     * @return MissingImageInterface
     */
    public function setProductId(?int $productId): MissingImageInterface;

    /**
     * Get product SKU
     *
     * @return string|null
     */
    public function getProductSku(): ?string;

    /**
     * Set product SKU
     *
     * @param string|null $productSku
     * @return MissingImageInterface
     */
    public function setProductSku(?string $productSku): MissingImageInterface;

    /**
     * Get image type
     *
     * @return string|null
     */
    public function getImageType(): ?string;

    /**
     * Set image type
     *
     * @param string|null $imageType
     * @return MissingImageInterface
     */
    public function setImageType(?string $imageType): MissingImageInterface;

    /**
     * Get media gallery ID
     *
     * @return int|null
     */
    public function getMediaGalleryId(): ?int;

    /**
     * Set media gallery ID
     *
     * @param int|null $mediaGalleryId
     * @return MissingImageInterface
     */
    public function setMediaGalleryId(?int $mediaGalleryId): MissingImageInterface;

    /**
     * Get first detected time
     *
     * @return string|null
     */
    public function getFirstDetected(): ?string;

    /**
     * Set first detected time
     *
     * @param string $firstDetected
     * @return MissingImageInterface
     */
    public function setFirstDetected(string $firstDetected): MissingImageInterface;

    /**
     * Get last checked time
     *
     * @return string|null
     */
    public function getLastChecked(): ?string;

    /**
     * Set last checked time
     *
     * @param string $lastChecked
     * @return MissingImageInterface
     */
    public function setLastChecked(string $lastChecked): MissingImageInterface;

    /**
     * Get status
     *
     * @return string|null
     */
    public function getStatus(): ?string;

    /**
     * Set status
     *
     * @param string $status
     * @return MissingImageInterface
     */
    public function setStatus(string $status): MissingImageInterface;

    /**
     * Get additional info
     *
     * @return string|null
     */
    public function getAdditionalInfo(): ?string;

    /**
     * Set additional info
     *
     * @param string|null $additionalInfo
     * @return MissingImageInterface
     */
    public function setAdditionalInfo(?string $additionalInfo): MissingImageInterface;
}
