<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="copex_image_tracker_missing_images" resource="default" engine="innodb" comment="CopeX Image Tracker Missing Images">
        <column xsi:type="int" name="entity_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Entity ID"/>
        <column xsi:type="varchar" name="image_path" nullable="false" length="500" comment="Image Path"/>
        <column xsi:type="int" name="product_id" padding="10" unsigned="true" nullable="true" comment="Product ID"/>
        <column xsi:type="varchar" name="product_sku" nullable="true" length="64" comment="Product SKU"/>
        <column xsi:type="varchar" name="image_type" nullable="true" length="50" comment="Image Type (base, small, thumbnail, etc.)"/>
        <column xsi:type="int" name="media_gallery_id" padding="10" unsigned="true" nullable="true" comment="Media Gallery ID"/>
        <column xsi:type="timestamp" name="first_detected" nullable="false" default="CURRENT_TIMESTAMP" comment="First Detection Time"/>
        <column xsi:type="timestamp" name="last_checked" nullable="false" default="CURRENT_TIMESTAMP" on_update="true" comment="Last Check Time"/>
        <column xsi:type="varchar" name="status" nullable="false" length="20" default="missing" comment="Status (missing, resolved)"/>
        <column xsi:type="text" name="additional_info" nullable="true" comment="Additional Information"/>
        
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
        
        <constraint xsi:type="unique" referenceId="COPEX_IMAGE_TRACKER_MISSING_IMAGES_IMAGE_PATH_PRODUCT_ID">
            <column name="image_path"/>
            <column name="product_id"/>
        </constraint>
        
        <index referenceId="COPEX_IMAGE_TRACKER_MISSING_IMAGES_PRODUCT_ID" indexType="btree">
            <column name="product_id"/>
        </index>
        
        <index referenceId="COPEX_IMAGE_TRACKER_MISSING_IMAGES_STATUS" indexType="btree">
            <column name="status"/>
        </index>
        
        <index referenceId="COPEX_IMAGE_TRACKER_MISSING_IMAGES_FIRST_DETECTED" indexType="btree">
            <column name="first_detected"/>
        </index>
    </table>
</schema>
