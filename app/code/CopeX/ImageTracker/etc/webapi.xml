<?xml version="1.0" ?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
	<route url="/V1/copex-imagetracker/misssingimages" method="POST">
		<service class="CopeX\ImageTracker\Api\MissingImageRepositoryInterface" method="save"/>
		<resources>
			<resource ref="CopeX_ImageTracker::MissingImages_save"/>
		</resources>
	</route>
	<route url="/V1/copex-imagetracker/misssingimages/search" method="GET">
		<service class="CopeX\ImageTracker\Api\MissingImageRepositoryInterface" method="getList"/>
		<resources>
			<resource ref="CopeX_ImageTracker::MissingImages_view"/>
		</resources>
	</route>
	<route url="/V1/copex-imagetracker/misssingimages/:emailblacklistId" method="GET">
		<service class="CopeX\ImageTracker\Api\MissingImageRepositoryInterface" method="getById"/>
		<resources>
			<resource ref="CopeX_ImageTracker::MissingImages_view"/>
		</resources>
	</route>
	<route url="/V1/copex-imagetracker/misssingimages/:emailblacklistId" method="PUT">
		<service class="CopeX\ImageTracker\Api\MissingImageRepositoryInterface" method="save"/>
		<resources>
			<resource ref="CopeX_ImageTracker::MissingImages_update"/>
		</resources>
	</route>
	<route url="/V1/copex-imagetracker/misssingimages/:emailblacklistId" method="DELETE">
		<service class="CopeX\ImageTracker\Api\MissingImageRepositoryInterface" method="deleteById"/>
		<resources>
			<resource ref="CopeX_ImageTracker::MissingImages_delete"/>
		</resources>
	</route>
</routes>
