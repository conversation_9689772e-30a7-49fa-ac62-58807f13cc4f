<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="copex:image-tracker:scan" xsi:type="object">CopeX\ImageTracker\Console\Command\ScanMissingImagesCommand</item>
                <item name="copex:image-tracker:report" xsi:type="object">CopeX\ImageTracker\Console\Command\ReportMissingImagesCommand</item>
            </argument>
        </arguments>
    </type>

    <!-- Logger Configuration -->
    <type name="CopeX\ImageTracker\Logger\Handler">
        <arguments>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem\Driver\File</argument>
        </arguments>
    </type>
    
    <type name="CopeX\ImageTracker\Logger\Logger">
        <arguments>
            <argument name="name" xsi:type="string">imageTrackerLogger</argument>
            <argument name="handlers" xsi:type="array">
                <item name="system" xsi:type="object">CopeX\ImageTracker\Logger\Handler</item>
            </argument>
        </arguments>
    </type>
    
    <!-- Repository Configuration -->
    <preference for="CopeX\ImageTracker\Api\Data\MissingImageInterface" 
                type="CopeX\ImageTracker\Model\MissingImage"/>
    <preference for="CopeX\ImageTracker\Api\MissingImageRepositoryInterface" 
                type="CopeX\ImageTracker\Model\MissingImageRepository"/>
</config>
