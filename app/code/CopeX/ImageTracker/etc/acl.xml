<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
	<acl>
		<resources>
			<resource id="Magento_Backend::admin">
				<resource id="CopeX_ImageTracker::MissingImages" title="MissingImages" sortOrder="10">
					<resource id="CopeX_ImageTracker::MissingImages_save" title="Save MissingImages" sortOrder="10"/>
					<resource id="CopeX_ImageTracker::MissingImages_delete" title="Delete MissingImages" sortOrder="20"/>
					<resource id="CopeX_ImageTracker::MissingImages_update" title="Update MissingImages" sortOrder="30"/>
					<resource id="CopeX_ImageTracker::MissingImages_view" title="View MissingImages" sortOrder="40"/>
				</resource>
			</resource>
		</resources>
	</acl>
</config>
