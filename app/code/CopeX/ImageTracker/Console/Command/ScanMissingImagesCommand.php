<?php

declare(strict_types=1);

namespace CopeX\ImageTracker\Console\Command;

use Cope<PERSON>\ImageTracker\Model\ImageScanner;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class ScanMissingImagesCommand
 * @package CopeX\ImageTracker\Console\Command
 */
class ScanMissingImagesCommand extends Command
{
    const COMMAND_NAME = 'copex:image-tracker:scan';

    /**
     * @var ImageScanner
     */
    private $imageScanner;

    /**
     * ScanMissingImagesCommand constructor.
     *
     * @param ImageScanner $imageScanner
     * @param string|null $name
     */
    public function __construct(
        ImageScanner $imageScanner,
        string $name = null
    ) {
        $this->imageScanner = $imageScanner;
        parent::__construct($name);
    }

    /**
     * Configure command
     */
    protected function configure(): void
    {
        $this->setName(self::COMMAND_NAME);
        $this->setDescription('Scan for missing product images and track them in database');
    }

    /**
     * Execute command
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Starting image scan...</info>');
        
        $startTime = microtime(true);
        $results = $this->imageScanner->scanMissingImages();
        $endTime = microtime(true);
        
        $executionTime = round($endTime - $startTime, 2);
        
        $output->writeln('<info>Image scan completed in ' . $executionTime . ' seconds</info>');
        $output->writeln('<comment>Results:</comment>');
        $output->writeln('  - New missing images: ' . $results['new_missing']);
        $output->writeln('  - Resolved images: ' . $results['resolved']);
        $output->writeln('  - Still missing images: ' . $results['still_missing']);
        
        if (!empty($results['errors'])) {
            $output->writeln('<error>Errors encountered:</error>');
            foreach ($results['errors'] as $error) {
                $output->writeln('  - ' . $error);
            }
            return Command::FAILURE;
        }
        
        if ($input->getOption('verbose')) {
            $output->writeln('<comment>Detailed results logged to var/log/copex_image_tracker.log</comment>');
        }
        
        return Command::SUCCESS;
    }
}
