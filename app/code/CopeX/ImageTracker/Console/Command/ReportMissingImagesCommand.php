<?php

declare(strict_types=1);

namespace CopeX\ImageTracker\Console\Command;

use Cope<PERSON>\ImageTracker\Api\MissingImageRepositoryInterface;
use CopeX\ImageTracker\Api\Data\MissingImageInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\SortOrderBuilder;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Helper\Table;

/**
 * Class ReportMissingImagesCommand
 * @package CopeX\ImageTracker\Console\Command
 */
class ReportMissingImagesCommand extends Command
{
    const COMMAND_NAME = 'copex:image-tracker:report';
    const OPTION_STATUS = 'status';
    const OPTION_LIMIT = 'limit';
    const OPTION_SINCE = 'since';

    /**
     * @var MissingImageRepositoryInterface
     */
    private $missingImageRepository;

    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;

    /**
     * @var SortOrderBuilder
     */
    private $sortOrderBuilder;

    /**
     * ReportMissingImagesCommand constructor.
     *
     * @param MissingImageRepositoryInterface $missingImageRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param SortOrderBuilder $sortOrderBuilder
     * @param string|null $name
     */
    public function __construct(
        MissingImageRepositoryInterface $missingImageRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        SortOrderBuilder $sortOrderBuilder,
        string $name = null
    ) {
        $this->missingImageRepository = $missingImageRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->sortOrderBuilder = $sortOrderBuilder;
        parent::__construct($name);
    }

    /**
     * Configure command
     */
    protected function configure(): void
    {
        $this->setName(self::COMMAND_NAME);
        $this->setDescription('Generate report of missing product images');
        $this->addOption(
            self::OPTION_STATUS,
            's',
            InputOption::VALUE_OPTIONAL,
            'Filter by status (missing, resolved)',
            'missing'
        );
        $this->addOption(
            self::OPTION_LIMIT,
            'l',
            InputOption::VALUE_OPTIONAL,
            'Limit number of results',
            50
        );
        $this->addOption(
            self::OPTION_SINCE,
            null,
            InputOption::VALUE_OPTIONAL,
            'Show images detected since date (Y-m-d H:i:s format)'
        );
    }

    /**
     * Execute command
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $status = $input->getOption(self::OPTION_STATUS);
        $limit = (int)$input->getOption(self::OPTION_LIMIT);
        $since = $input->getOption(self::OPTION_SINCE);

        $this->searchCriteriaBuilder->addFilter('status', $status);
        
        if ($since) {
            $this->searchCriteriaBuilder->addFilter('first_detected', $since, 'gteq');
        }

        $sortOrder = $this->sortOrderBuilder
            ->setField('first_detected')
            ->setDescendingDirection()
            ->create();

        $searchCriteria = $this->searchCriteriaBuilder
            ->addSortOrder($sortOrder)
            ->setPageSize($limit)
            ->create();

        $searchResults = $this->missingImageRepository->getList($searchCriteria);
        $missingImages = $searchResults->getItems();

        if (empty($missingImages)) {
            $output->writeln('<info>No missing images found with the specified criteria.</info>');
            return Command::SUCCESS;
        }

        $output->writeln('<info>Missing Images Report</info>');
        $output->writeln('<comment>Status: ' . $status . '</comment>');
        if ($since) {
            $output->writeln('<comment>Since: ' . $since . '</comment>');
        }
        $output->writeln('<comment>Total found: ' . $searchResults->getTotalCount() . ' (showing ' . count($missingImages) . ')</comment>');
        $output->writeln('');

        $table = new Table($output);
        $table->setHeaders([
            'ID',
            'Image Path',
            'Product ID',
            'Product SKU',
            'Media Gallery ID',
            'First Detected',
            'Last Checked',
            'Status'
        ]);

        /** @var MissingImageInterface $missingImage */
        foreach ($missingImages as $missingImage) {
            $table->addRow([
                $missingImage->getEntityId(),
                $missingImage->getImagePath(),
                $missingImage->getProductId() ?: 'N/A',
                $missingImage->getProductSku() ?: 'N/A',
                $missingImage->getMediaGalleryId() ?: 'N/A',
                $missingImage->getFirstDetected(),
                $missingImage->getLastChecked(),
                $missingImage->getStatus()
            ]);
        }

        $table->render();

        return Command::SUCCESS;
    }
}
