<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Cron:etc/crontab.xsd">
    <group id="default">
        <job name="copex_autorefreshcache_cron" instance="CopeX\AutoRefreshCache\Model\Cron" method="refreshInvalid">
            <config_path>system/cache/auto_flush_cron</config_path>
        </job>
        <job name="copex_autorefreshcache_cron2" instance="CopeX\AutoRefreshCache\Model\Cron2" method="refreshInvalid">
            <config_path>system/cache/auto_flush_cron2</config_path>
        </job>
    </group>
</config>
