<?php

namespace CopeX\AutoRefreshCache\Plugin;

use Cope<PERSON>\AutoRefreshCache\Model\Config;
use Magento\AdminNotification\Model\System\Message\CacheOutdated;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class HideNotification
{

    private ScopeConfigInterface $scopeConfig;

    public function __construct(ScopeConfigInterface $scopeConfig)
    {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * @param CacheOutdated $subject
     * @param bool          $result
     * @return bool
     */
    public function afterIsDisplayed(CacheOutdated $subject, bool $result): bool
    {
        if($this->shouldHide()){
            return false;
        }
        return $result;
    }

    private function shouldHide()
    {
        return $this->scopeConfig->getValue(Config::IS_ENABLED, ScopeInterface::SCOPE_STORE) && $this->scopeConfig->getValue(Config::HIDE_NOTIFICATION, ScopeInterface::SCOPE_STORE);
    }
}