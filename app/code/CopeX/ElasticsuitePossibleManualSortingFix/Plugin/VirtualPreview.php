<?php

namespace CopeX\ElasticsuitePossibleManualSortingFix\Plugin;

use Magento\Backend\Model\Session;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use \Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Psr\Log\LoggerInterface;
use Smile\ElasticsuiteVirtualCategory\Controller\Adminhtml\Category\Virtual\Preview;

class VirtualPreview
{

    private RequestInterface $request;
    private ResponseInterface $response;
    private SerializerInterface $serializer;
    private LoggerInterface $logger;
    private Session $session;
    private CollectionFactory $productCollectionFactory;

    public function __construct(
        RequestInterface $request,
        ResponseInterface $response,
        SerializerInterface $serializer,
        LoggerInterface $logger,
        Session $session,
        CollectionFactory $productCollectionFactory
    ) {
        $this->request = $request;
        $this->response = $response;
        $this->serializer = $serializer;
        $this->logger = $logger;
        $this->session = $session;
        $this->productCollectionFactory = $productCollectionFactory;
    }

    /**
     * @param Preview $subject
     * @param callable $proceed
     * @return void
     */
    public function afterExecute(Preview $subject)
    {
        $content = $this->response->getContent();
        $unserializedContent = $this->serializer->unserialize($content);
        $storeId    = $this->request->getParam('store');
        $productPositions = $this->request->getParam('product_position', []);
        $ids = array_unique(array_merge(array_keys($productPositions), array_map(fn($item) => $item['id'], $unserializedContent['products'])));
        $activeProductIds = $this->getActiveProducts($ids, $storeId);
        $activeUnserialized = array_filter($unserializedContent['products'], fn($item) => in_array($item['id'], $activeProductIds));
        $activeUnserializedCount = count($activeUnserialized);
        $this->saveDataInSession($activeUnserializedCount);
    }

    private function getActiveProducts($ids, $storeId)
    {
        $collection = $this->productCollectionFactory->create()
            ->setStoreId($storeId)
            ->addAttributeToFilter('entity_id', ['in' => $ids])
            ->addAttributeToFilter(
                'status',
                ['eq' => \Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_ENABLED]
            )
            ->addAttributeToFilter(
                'visibility',
                ['in' => [
                    \Magento\Catalog\Model\Product\Visibility::VISIBILITY_IN_CATALOG,
                    \Magento\Catalog\Model\Product\Visibility::VISIBILITY_BOTH
                ]]
            )
            ->addStoreFilter($storeId);
        return $collection->getAllIds();
    }

    private function saveDataInSession($size)
    {
        $categoryId = $this->request->getParam('entity_id');
        $storeId = $this->request->getParam('store_id');
        $this->session->setData('virtualcategory_productcount_' . $categoryId . "_store_id_" . $storeId, $size);

    }
}
