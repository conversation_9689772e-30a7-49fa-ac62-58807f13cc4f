<?php

namespace CopeX\ImageTagNextgen\Plugin;

use CopeX\HyvaTheme\ViewModel\ImageTag;
use CopeX\ImageTagNextgen\Helper\NextGenImages;

class AddNextGenImages
{

    private NextGenImages $nextGenImages;

    public function __construct(NextGenImages $nextGenImages)
    {
        $this->nextGenImages = $nextGenImages;
    }

    /**
     * @param ImageTag $subject
     * @param null     $result
     */
    public function afterBeforeSetData(ImageTag $subject, $result)
    {
        if (!($result['sources'] ?? "")) {
            $images = $result['responsive_images'] ?? [$result['src']];
            $result['sources'] = $this->nextGenImages->getNextGenSources($images);
        }
        return $result;
    }
}