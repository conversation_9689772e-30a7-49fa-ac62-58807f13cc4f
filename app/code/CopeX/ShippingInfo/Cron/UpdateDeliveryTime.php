<?php

namespace CopeX\ShippingInfo\Cron;

use CopeX\ShippingInfo\ViewModel\ShippingInfo;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class UpdateDeliveryTime
{
    public const DELIVERY_TIME_ATTR_CODE = 'idealo_lieferzeit';
    private LoggerInterface $logger;
    private ShippingInfo $shippingInfo;
    private CollectionFactory $productCollection;
    private StoreManagerInterface $storeManager;

    public function __construct(LoggerInterface $logger, ShippingInfo $shippingInfo, CollectionFactory $productCollection, StoreManagerInterface $storeManager)
    {
        $this->logger = $logger;
        $this->shippingInfo = $shippingInfo;
        $this->productCollection = $productCollection;
        $this->storeManager = $storeManager;
    }

    /**
     * Update deliver time info of products
     */
    public function execute(): void
    {
        $this->logger->info('Starting to update delivery time...');
        try {
            $productCollection = $this->productCollection->create();
            foreach($this->storeManager->getStores(true) as $store){
                $this->logger->info('Updating Delivery-Data for Store ' . $store->getCode());
                foreach ($productCollection as $product) {
                    $product->setStoreId($store->getId());
                    $this->updateDeliveryTimeInfo($product);
                }
                $this->logger->info('Updating Delivery-Data for Store ' . $store->getCode() . ' finished');
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
        $this->logger->info('Delivery-Data successfully updated.');
    }

    protected function updateDeliveryTimeInfo(Product $product): void
    {
        $deliveryTimeInfo = $this->shippingInfo->getDeliveryTimeInfoblock($product);
        $product->addAttributeUpdate(self::DELIVERY_TIME_ATTR_CODE, $deliveryTimeInfo, $product->getStoreId());
    }
}
