<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\KlarnaIncrementIdFix\Observer\Kp;

use CopeX\KlarnaIncrementIdFix\Plugin\GetOrder;

class MerchantReferenceUpdate implements \Magento\Framework\Event\ObserverInterface
{

    private GetOrder $getOrder;

    public function __construct(GetOrder $getOrder)
    {
        $this->getOrder = $getOrder;
    }

    /**
     * Execute observer
     *
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     */
    public function execute(
        \Magento\Framework\Event\Observer $observer
    ) {
        $object = $observer->getEvent()->getMerchantReferenceObject();
        $object->setData('merchant_reference_1', $this->getOrder->getIncrementId());
    }
}

