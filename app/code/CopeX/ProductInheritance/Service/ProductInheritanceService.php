<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Service;

use CopeX\ProductInheritance\Api\InheritanceProcessorInterface;
use CopeX\ProductInheritance\Model\Config;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;

class ProductInheritanceService
{
    public function __construct(
        private readonly ProductRepositoryInterface $productRepository,
        private readonly Config $config,
        private readonly array $processors = []
    ) {
    }

    /**
     * Internal method to process inheritance with optional saving
     */
    public function inheritFromProduct(ProductInterface $targetProduct, string $sourceProductSku): bool
    {
        $success = true;
        try {
            $sourceProduct = $this->productRepository->get($sourceProductSku);
            // Process all registered processors
            foreach ($this->getProcessors() as $processor) {
                try {
                    if (! $processor->process($targetProduct, $sourceProduct)) {
                        $success = false;
                        $this->log("Failed to process {$processor->getName()} for product ID {$targetProduct->getId()}");
                    }
                } catch (\Exception $e) {
                    $this->log("Error in {$processor->getName()} processor for product ID {$targetProduct->getId()}: " .
                               $e->getMessage());
                    $success = false;
                }
            }
        } catch (\Exception $e) {
            $this->log("Error in product inheritance for product ID {$targetProduct->getId()}: " . $e->getMessage());
            return false;
        }

        return $success;
    }

    /**
     * @return array<InheritanceProcessorInterface>
     */
    public function getProcessors(): array
    {
        $processors = [];
        foreach ($this->processors as $processor) {
            if (! $processor instanceof InheritanceProcessorInterface) {
                continue;
            }
            if ($processor->isEnabled()) {
                $processors[] = $processor;
            }
        }
        return $processors;
    }

    private function getSourceProduct($sourceSku)
    {
        try {
            return $this->productRepository->get($sourceSku);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Log message if logging is enabled
     */
    private function log(string $message): void
    {
        $this->config->log('[ProductInheritance] ' . $message);
    }
}
