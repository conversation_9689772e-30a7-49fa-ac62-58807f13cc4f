<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Observer;

use CopeX\ProductInheritance\Model\Config;
use CopeX\ProductInheritance\Service\ProductInheritanceService;
use Magento\Catalog\Model\Product;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class ProductSave implements ObserverInterface
{
    public function __construct(
        private readonly Config $config,
        private readonly ProductInheritanceService $inheritanceService
    ) {
    }

    /**
     * Execute observer
     */
    public function execute(Observer $observer): void
    {
        if ($this->config->shouldProcess()) {
            /** @var Product $product */
            $product = $observer->getEvent()->getProduct();

            // Check if inherit_from_sku has been set
            $inheritFromSku = $this->getInheritFromSku($product);
            // Only process if the product is not locked
            if ($inheritFromSku && $this->lockProduct($product)) {
                $this->inheritanceService->inheritFromProduct($product, $inheritFromSku);
                $this->unlockProduct($product);
            }
        }
    }

    private function getInheritFromSku($product)
    {
        if ($product->getId()) {
            return $product->getData('inherit_from_sku');
        }
        return null;
    }

    /**
     * Mark product as being processed to prevent loops
     *
     * @param $product
     */
    private function lockProduct($product): bool
    {
        if (! $product->getData('_inheritance_processing')) {
            // Mark product as being processed to prevent loops
            $product->setData('_inheritance_processing', true);
            return true;
        }
        return false;
    }

    private function unlockProduct($product): void
    {
        $product->unsetData('_inheritance_processing');
    }
}
