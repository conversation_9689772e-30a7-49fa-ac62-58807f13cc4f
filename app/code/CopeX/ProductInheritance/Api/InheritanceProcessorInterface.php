<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Api;

use Magento\Catalog\Api\Data\ProductInterface;

interface InheritanceProcessorInterface
{
    /**
     * Process inheritance for a specific type of data
     */
    public function process(ProductInterface $targetProduct, ProductInterface $sourceProduct): bool;

    /**
     * Get processor name for logging
     */
    public function getName(): string;

    /**
     * Check if processor is enabled
     */
    public function isEnabled(): bool;
}
