<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Model\Processor;

use CopeX\ProductInheritance\Api\InheritanceProcessorInterface;
use CopeX\ProductInheritance\Model\Config;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\Product\Gallery\Processor as GalleryProcessor;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Io\File;

class ImageProcessor implements InheritanceProcessorInterface
{
    public function __construct(
        private readonly Config $config,
        private readonly GalleryProcessor $galleryProcessor,
        private readonly File $file,
        private readonly Filesystem $filesystem
    ) {
    }

    /**
     * Process image inheritance - copy image references, not files
     */
    public function process(ProductInterface $targetProduct, ProductInterface $sourceProduct): bool
    {
        if (! $this->isEnabled()) {
            return false;
        }

        try {
            $sourceEntries = $this->getMediaGalleryEntries($sourceProduct);
            if (empty($sourceEntries)) {
                return false;
            }

            $overwrite = $this->config->shouldOverwriteExisting();
            $targetEntries = $this->getMediaGalleryEntries($targetProduct);

            if (! $overwrite && ! empty($targetEntries)) {
                return false;
            }

            $sourceFiles = array_map(
                static fn ($entry) => $entry->getFile(),
                $sourceEntries
            );

            // Remove child images
            // if ($overwrite) {
                // $this->removeObsoleteImages($targetProduct, $targetEntries, $sourceFiles);
            // }

            $this->addSourceImages($targetProduct, $sourceEntries);
            $this->copyImageRoles($sourceProduct, $targetProduct);

            $this->config->log(sprintf(
                'Inherited %d image references from "%s" to "%s".',
                count($sourceEntries),
                $sourceProduct->getSku(),
                $targetProduct->getSku()
            ));

            return true;
        } catch (\Exception $e) {
            $this->config->log(sprintf(
                'Error processing image inheritance from "%s" to "%s": %s.',
                $sourceProduct->getSku(),
                $targetProduct->getSku(),
                $e->getMessage()
            ));
            return false;
        }
    }

    public function getName(): string
    {
        return 'Image Processor';
    }

    public function isEnabled(): bool
    {
        return $this->config->isImageProcessorEnabled();
    }

    /**
     * Remove images from target that are not present in source
     */
    private function removeObsoleteImages(
        ProductInterface $target,
        array $targetEntries,
        array $sourceFiles
    ): void {
        foreach ($targetEntries as $entry) {
            if (! in_array($entry->getFile(), $sourceFiles, true)) {
                $this->galleryProcessor->removeImage($target, $entry->getFile());
            }
        }
    }

    /**
     * Add all source images to target if file exists
     */
    private function addSourceImages(ProductInterface $target, array $sourceEntries): void
    {
        $mediaDir = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);
        foreach ($sourceEntries as $entry) {
            $absolute = $mediaDir->getAbsolutePath('catalog/product' . $entry->getFile());
            if ($this->file->fileExists($absolute)) {
                $target->addImageToMediaGallery(
                    $absolute,
                    $entry->getData('types'),
                    true,
                    (bool) $entry->isDisabled()
                );
            }
        }
    }

    /**
     * Retrieve media gallery entries from product
     */
    private function getMediaGalleryEntries(ProductInterface $product): array
    {
        return $product->getMediaGalleryEntries() ?: [];
    }

    /**
     * Copy image role assignments from source to target product
     */
    private function copyImageRoles(ProductInterface $source, ProductInterface $target): void
    {
        $roles = [
            'Image' => 'getImage',
            'SmallImage' => 'getSmallImage',
            'Thumbnail' => 'getThumbnail',
            'SwatchImage' => 'getSwatchImage',
        ];

        foreach ($roles as $setterName => $getterMethod) {
            $value = $source->{$getterMethod}();
            if ($value && $value !== 'no_selection') {
                $target->{'set' . $setterName}($value);
            }
        }
    }
}
