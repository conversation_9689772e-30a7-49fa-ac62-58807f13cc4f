<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Psr\Log\LoggerInterface;

class Config
{
    private const XML_PATH_ENABLED = 'copex_product_inheritance/general/enabled';
    private const XML_PATH_AUTO_SYNC = 'copex_product_inheritance/general/auto_sync';
    private const XML_PATH_LOG_ENABLED = 'copex_product_inheritance/general/log_enabled';
    private const XML_PATH_CRON_ENABLED = 'copex_product_inheritance/general/cron_enabled';
    private const XML_PATH_INHERIT_ATTRIBUTES = 'copex_product_inheritance/attributes/inherit_attributes';
    private const XML_PATH_EXCLUDE_EMPTY = 'copex_product_inheritance/attributes/exclude_empty';
    private const XML_PATH_OVERWRITE_EXISTING = 'copex_product_inheritance/attributes/overwrite_existing';
    private const XML_PATH_ENABLE_IMAGE_PROCESSOR = 'copex_product_inheritance/processors/enable_image_processor';
    private const XML_PATH_ENABLE_ATTACHMENT_PROCESSOR = 'copex_product_inheritance/processors/enable_attachment_processor';
    private const XML_PATH_ENABLE_LINKED_PRODUCTS_PROCESSOR = 'copex_product_inheritance/processors/enable_linked_products_processor';
    private const XML_PATH_LINKED_PRODUCT_TYPES = 'copex_product_inheritance/processors/linked_product_types';
    private bool $isInCli = false;

    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly LoggerInterface $logger,
    ) {
    }

    public function getConfigValue($path, ?int $storeId = null)
    {
        return $this->scopeConfig->getValue(
            $path,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function isEnabled(?int $storeId = null): bool
    {
        return !! $this->getConfigValue(self::XML_PATH_ENABLED, $storeId);
    }

    public function isAutoSyncEnabled(?int $storeId = null): bool
    {
        return !! $this->getConfigValue(self::XML_PATH_AUTO_SYNC, $storeId);
    }

    public function isLoggingEnabled(?int $storeId = null): bool
    {
        return !! $this->getConfigValue(self::XML_PATH_LOG_ENABLED, $storeId);
    }

    public function getInheritAttributes(?int $storeId = null): array
    {
        $value = $this->getConfigValue(self::XML_PATH_INHERIT_ATTRIBUTES, $storeId);

        return $value ? explode(',', $value) : [];
    }

    public function shouldExcludeEmpty(?int $storeId = null): bool
    {
        return !! $this->getConfigValue(self::XML_PATH_EXCLUDE_EMPTY, $storeId);
    }

    public function shouldOverwriteExisting(?int $storeId = null): bool
    {
        return !! $this->getConfigValue(self::XML_PATH_OVERWRITE_EXISTING, $storeId);
    }

    public function isImageProcessorEnabled(?int $storeId = null): bool
    {
        return !! $this->getConfigValue(self::XML_PATH_ENABLE_IMAGE_PROCESSOR, $storeId);
    }

    public function isAttachmentProcessorEnabled(?int $storeId = null): bool
    {
        return !! $this->getConfigValue(self::XML_PATH_ENABLE_ATTACHMENT_PROCESSOR, $storeId);
    }

    public function isLinkedProductsProcessorEnabled(?int $storeId = null): bool
    {
        return !! $this->getConfigValue(self::XML_PATH_ENABLE_LINKED_PRODUCTS_PROCESSOR, $storeId);
    }

    public function isCronEnabled(?int $storeId = null): bool
    {
        return !! $this->getConfigValue(self::XML_PATH_CRON_ENABLED, $storeId);
    }

    public function getLinkedProductTypes(?int $storeId = null): array
    {
        $value = $this->getConfigValue(self::XML_PATH_LINKED_PRODUCT_TYPES, $storeId);
        return $value ? explode(',', $value) : [];
    }

    public function isInCli(): bool
    {
        return $this->isInCli;
    }

    public function setIsInCli(bool $isInCli): void
    {
        $this->isInCli = $isInCli;
    }

    public function log(string $message): void
    {
        if ($this->isLoggingEnabled()) {
            $this->logger->info('[ProductInheritance] ' . $message);
        }
    }

    public function shouldProcess(): bool
    {
        return $this->isEnabled() && ($this->isAutoSyncEnabled() || $this->isInCli());
    }
}
