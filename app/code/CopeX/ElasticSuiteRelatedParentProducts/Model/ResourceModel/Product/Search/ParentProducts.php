<?php

namespace CopeX\ElasticSuiteRelatedParentProducts\Model\ResourceModel\Product\Search;

use Magento\Search\Model\Query;

class ParentProducts extends \Magento\Framework\Model\ResourceModel\Db\AbstractDb
{
    /**
     * @var string
     */
    const LINK_TABLE_NAME = 'catalog_product_link';
    const EAV_TABLE_NAME = 'eav_attribute';
    const EAV_ATTRIBUTE_VALUE = 'catalog_product_entity_varchar';

    const LINK_TYPE = 1; //relation

    /**
     * @var \Magento\Framework\Indexer\IndexerRegistry
     */
    private $indexerRegistry;

    /**
     * Constructor.
     *
     * @param \Magento\Framework\Model\ResourceModel\Db\Context $context         Context.
     * @param \Magento\Framework\Indexer\IndexerRegistry        $indexerRegistry Indexer registry.
     * @param string                                            $connectionName  Connection name.
     */
    public function __construct(
        \Magento\Framework\Model\ResourceModel\Db\Context $context,
        \Magento\Framework\Indexer\IndexerRegistry $indexerRegistry,
        $connectionName = null
    ) {
        $this->indexerRegistry = $indexerRegistry;
        parent::__construct($context, $connectionName);
    }

    /**
     * Get query position for a product list.
     *
     * @param array $productIds Product ids.
     * @param int   $storeId    Store ids.
     *
     * @return array
     */
    public function getByProductIds(array $productIds, $storeId)
    {
        $eavTable = $this->getTable(self::EAV_TABLE_NAME);
        $nameAttributeId = $this->getConnection()->fetchOne($this->getConnection()->select()->from($eavTable,['attribute_id'])->where('attribute_code = ?','name')->where('entity_type_id = ?',4));
        $eavAttributeValue = $this->getTable(self::EAV_ATTRIBUTE_VALUE);
        $select = $this->getBaseSelect()
            ->joinInner($eavAttributeValue, "main_table.product_id = {$eavAttributeValue}.entity_id", ['value'])
            ->where('linked_product_id IN(?)', $productIds)
            ->where('store_id = ?', $storeId)
            ->where('link_type_id = ?', self::LINK_TYPE)
            ->where('attribute_id = ?', $nameAttributeId)
            ->columns(['product_id', 'linked_product_id']);

        return $this->getConnection()->fetchAll($select);
    }



    /**
     * @SuppressWarnings(PHPMD.CamelCaseMethodName)
     * {@inheritDoc}
     */
    protected function _construct()
    {
        $this->_setMainTable(self::LINK_TABLE_NAME);
    }

    /**
     * Reindex product on position change.
     *
     * @param array $productIds Product ids to be reindexed.
     *
     * @return void
     */
    private function reindex($productIds)
    {
        $this->indexerRegistry->get(\Magento\CatalogSearch\Model\Indexer\Fulltext::INDEXER_ID)->reindexList($productIds);
    }

    /**
     * Init a base select with the main table.
     *
     * @return \Zend_Db_Select
     */
    private function getBaseSelect()
    {
        $select = $this->getConnection()->select();
        $select->from(['main_table' => $this->getMainTable()], []);

        return $select;
    }
}
