<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\ProductDetailLazyLoad\Controller\Product;

use Hyva\Theme\ViewModel\CurrentProduct;
use Magento\Catalog\Helper\Product\View;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\Response\Http;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\View\Result\PageFactory;
use Magento\Review\Model\AppendSummaryDataFactory;
use Magento\Review\Model\Review;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class Get extends \Magento\Framework\App\Action\Action implements HttpGetActionInterface, \Magento\Catalog\Controller\Product\View\ViewInterface
{

    /**
     * @var PageFactory
     */
    protected $resultPageFactory;
    /**
     * @var Json
     */
    protected $serializer;
    /**
     * @var LoggerInterface
     */
    protected $logger;
    /**
     * @var Http
     */
    protected $http;
    private RequestInterface $request;
    private View $viewHelper;
    private CurrentProduct $currentProduct;
    private ScopeConfigInterface $scopeConfig;
    private AppendSummaryDataFactory $appendSummaryDataFactory;
    private StoreManagerInterface $storeManager;

    /**
     * Constructor
     *
     * @param PageFactory $resultPageFactory
     * @param Json $json
     * @param LoggerInterface $logger
     * @param Http $http
     */
    public function __construct(
        Context $context,
        PageFactory $resultPageFactory,
        Json $json,
        LoggerInterface $logger,
        Http $http,
        RequestInterface $request,
        View $viewHelper,
        CurrentProduct $currentProduct,
        ScopeConfigInterface $scopeConfig,
        AppendSummaryDataFactory $appendSummaryDataFactory,
        StoreManagerInterface $storeManager
    ) {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
        $this->serializer = $json;
        $this->logger = $logger;
        $this->http = $http;
        $this->request = $request;
        $this->viewHelper = $viewHelper;
        $this->currentProduct = $currentProduct;
        $this->scopeConfig = $scopeConfig;
        $this->appendSummaryDataFactory = $appendSummaryDataFactory;
        $this->storeManager = $storeManager;
    }

    /**
     * Execute view action
     *
     * @return ResultInterface
     */
    public function execute()
    {
        try {
            $productId = (int) $this->request->getParam('id');
            $page = $this->resultPageFactory->create();
            $page->getLayout()->getUpdate()
                ->addHandle('default')
                ->addHandle('catalog_product_view')
                ->addHandle('catalog_product_view_type_simple');
            $this->viewHelper->prepareAndRender($page, $productId, $this,  new \Magento\Framework\DataObject());
            $product = $this->currentProduct->get();
            if ($product->getRatingSummary() === null) {
                $this->appendSummaryDataFactory->create()
                    ->execute(
                        $product,
                        (int)$this->storeManager->getStore()->getId(),
                        Review::ENTITY_PRODUCT_CODE
                    );
            }
            $baseUrl = $this->scopeConfig->getValue("web/unsecure/base_url", ScopeInterface::SCOPE_STORE);
            $productUrl = "/" . str_replace($baseUrl,"",$product->getProductUrl());
            $this->request->setRequestUri($productUrl);
            $details['details'] = $this->getResponseContent($page->getLayout());
            return $this->jsonResponse($details);
        } catch (LocalizedException $e) {
            return $this->jsonResponse($e->getMessage());
        } catch (\Exception $e) {
            $this->logger->critical($e);
            return $this->jsonResponse($e->getMessage());
        }
    }

    public function getResponseContent($layout){
        return $layout->getBlock('product.info.details')->toHtml();
    }

    /**
     * Create json response
     *
     * @return ResultInterface
     */
    public function jsonResponse($response = '')
    {
        // $this->http->getHeaders()->clearHeaders(); clearheaders prevents varnish to cache request
        $this->http->setHeader('Content-Type', 'application/json');
        return $this->http->setBody(
            $this->serializer->serialize($response)
        );
    }

}

