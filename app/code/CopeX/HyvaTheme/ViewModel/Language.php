<?php

namespace CopeX\HyvaTheme\ViewModel;

use Jajuma\HyvaFlags\ViewModel\FlagiconsCircle;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class Language implements ArgumentInterface
{
    private FlagiconsCircle $flagiconsCircle;

    /**
     * Language constructor.
     */
    public function __construct(
        FlagiconsCircle $flagiconsCircle,
    ) {
        $this->flagiconsCircle = $flagiconsCircle;
    }

    /**
     * @param $code
     */
    public function getStoreFlag($code, $classes = 'w-6 h-6 sm:w-7 sm:h-7'): string
    {
        if($code == 'de') $code = 'at';
        return $this->flagiconsCircle->renderHtml($code, $classes);
    }
}
