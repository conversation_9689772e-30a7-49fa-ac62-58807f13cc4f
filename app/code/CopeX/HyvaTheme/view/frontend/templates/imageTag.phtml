<?php

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Framework\View\Element\Template $block */

$class = $block->getClass();
$sources = $block->getSources();
$style = $block->getStyle();
$src = $block->getSrc();
$srcset = $block->getSrcset();
$sizes = $block->getSizes();
$alt = $block->getAlt();
$width = $block->getWidth();
$height = $block->getHeight();
$title = $block->getTitle();
$lazyload = $block->getLazyload();
$fetchpriority = $block->getFetchpriority();
$additionalAttributes = $block->getAdditionalAttributes() ?? '';
?>
<?php if ($sources) : ?>
<picture class="<?= $class ?>"<?= $lazyload ? (' loading="lazy"') : ''; ?>>
    <?php foreach ($sources as $source) : ?>
        <source type="<?= $source['type'] ?>"
                <?php if( isset($source['srcset'])): ?>srcset="<?= $source['srcset'] ?>"<?php endif; ?>
                <?php if( isset($source['src'])): ?>src="<?= $source['src'] ?>"<?php endif; ?>
                <?php if( isset($source['sizes'])): ?>sizes="<?= $source['sizes'] ?>"<?php endif; ?>
                <?php if( $fetchpriority ): ?>fetchpriority="<?= $escaper->escapeHtmlAttr($fetchpriority) ?>"<?php endif; ?>
            >
    <?php endforeach; ?>
<?php endif; ?>
    <img
        <?= $style ? (' style="' . $escaper->escapeHtmlAttr($style) . '"') : ''; ?>
        <?= $class ? (' class="' . $escaper->escapeHtmlAttr($class) . '"') : ''; ?>
        <?= $src ? (' src="' . $escaper->escapeUrl($src) . '"') : ''; ?>
        <?= $srcset ? (' srcset="' . $escaper->escapeUrl($srcset) . '"') : ''; ?>
        <?= $sizes ? (' sizes="' . $escaper->escapeHtmlAttr($sizes) . '"') : ''; ?>
        <?= $alt ? (' alt="' . $escaper->escapeHtmlAttr($alt) . '"') : ''; ?>
        <?= $width ? (' width="' . $escaper->escapeHtmlAttr($width) . '"') : ''; ?>
        <?= $height ? (' height="' . $escaper->escapeHtmlAttr($height) . '"') : ''; ?>
        <?= $title ? (' title="' . $escaper->escapeHtmlAttr($title) . '"') : ''; ?>
        <?= ($fetchpriority && !$sources) ? (' fetchpriority="' . $escaper->escapeHtmlAttr($fetchpriority) . '"') : ''; ?>
        <?= $lazyload ? (' loading="lazy" decoding="async"') : ''; ?>
        <?= $additionalAttributes ?>
    >
<?php if($sources) : ?>
</picture>
<?php endif; ?>