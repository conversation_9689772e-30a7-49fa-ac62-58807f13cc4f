<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Checkout\Model\CompositeConfigProvider">
        <arguments>
            <argument name="configProviders" xsi:type="array">
                <item name="tax_info_config_provider" xsi:type="object">CopeX\TaxInfo\Model\Checkout\ConfigProvider</item>
            </argument>
        </arguments>
    </type>
</config>
