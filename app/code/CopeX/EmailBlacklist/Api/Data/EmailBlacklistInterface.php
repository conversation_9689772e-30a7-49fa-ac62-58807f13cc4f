<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\EmailBlacklist\Api\Data;

interface EmailBlacklistInterface
{

    const EMAIL = 'email';
    const EMAILBLACKLIST_ID = 'emailblacklist_id';
    const CREATED_AT = 'created_at';

    /**
     * Get emailblacklist_id
     * @return string|null
     */
    public function getEmailblacklistId();

    /**
     * Set emailblacklist_id
     * @param string $emailblacklistId
     * @return \CopeX\EmailBlacklist\EmailBlacklist\Api\Data\EmailBlacklistInterface
     */
    public function setEmailblacklistId($emailblacklistId);

    /**
     * Get email
     * @return string|null
     */
    public function getEmail();

    /**
     * Set email
     * @param string $email
     * @return \CopeX\EmailBlacklist\EmailBlacklist\Api\Data\EmailBlacklistInterface
     */
    public function setEmail($email);

    /**
     * Get created_at
     * @return string|null
     */
    public function getCreatedAt();

    /**
     * Set created_at
     * @param string $createdAt
     * @return \CopeX\EmailBlacklist\EmailBlacklist\Api\Data\EmailBlacklistInterface
     */
    public function setCreatedAt($createdAt);
}

