<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Cope<PERSON>\EmailBlacklist\Model;

use Cope<PERSON>\EmailBlacklist\Api\Data\EmailBlacklistInterface;
use Cope<PERSON>\EmailBlacklist\Api\Data\EmailBlacklistInterfaceFactory;
use Cope<PERSON>\EmailBlacklist\Api\Data\EmailBlacklistSearchResultsInterfaceFactory;
use Cope<PERSON>\EmailBlacklist\Api\EmailBlacklistRepositoryInterface;
use CopeX\EmailBlacklist\Model\ResourceModel\EmailBlacklist as ResourceEmailBlacklist;
use CopeX\EmailBlacklist\Model\ResourceModel\EmailBlacklist\CollectionFactory as EmailBlacklistCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class EmailBlacklistRepository implements EmailBlacklistRepositoryInterface
{

    /**
     * @var EmailBlacklist
     */
    protected $searchResultsFactory;

    /**
     * @var CollectionProcessorInterface
     */
    protected $collectionProcessor;

    /**
     * @var ResourceEmailBlacklist
     */
    protected $resource;

    /**
     * @var EmailBlacklistInterfaceFactory
     */
    protected $emailBlacklistFactory;

    /**
     * @var EmailBlacklistCollectionFactory
     */
    protected $emailBlacklistCollectionFactory;


    /**
     * @param ResourceEmailBlacklist $resource
     * @param EmailBlacklistInterfaceFactory $emailBlacklistFactory
     * @param EmailBlacklistCollectionFactory $emailBlacklistCollectionFactory
     * @param EmailBlacklistSearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        ResourceEmailBlacklist $resource,
        EmailBlacklistInterfaceFactory $emailBlacklistFactory,
        EmailBlacklistCollectionFactory $emailBlacklistCollectionFactory,
        EmailBlacklistSearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor
    ) {
        $this->resource = $resource;
        $this->emailBlacklistFactory = $emailBlacklistFactory;
        $this->emailBlacklistCollectionFactory = $emailBlacklistCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
    }

    /**
     * @inheritDoc
     */
    public function save(EmailBlacklistInterface $emailBlacklist)
    {
        try {
            $this->resource->save($emailBlacklist);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the emailBlacklist: %1',
                $exception->getMessage()
            ));
        }
        return $emailBlacklist;
    }

    /**
     * @inheritDoc
     */
    public function get($emailBlacklistId)
    {
        $emailBlacklist = $this->emailBlacklistFactory->create();
        $this->resource->load($emailBlacklist, $emailBlacklistId);
        if (!$emailBlacklist->getId()) {
            throw new NoSuchEntityException(__('EmailBlacklist with id "%1" does not exist.', $emailBlacklistId));
        }
        return $emailBlacklist;
    }

    /**
     * @inheritDoc
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->emailBlacklistCollectionFactory->create();
        
        $this->collectionProcessor->process($criteria, $collection);
        
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);
        
        $items = [];
        foreach ($collection as $model) {
            $items[] = $model;
        }
        
        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function delete(EmailBlacklistInterface $emailBlacklist)
    {
        try {
            $emailBlacklistModel = $this->emailBlacklistFactory->create();
            $this->resource->load($emailBlacklistModel, $emailBlacklist->getEmailblacklistId());
            $this->resource->delete($emailBlacklistModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the EmailBlacklist: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function deleteById($emailBlacklistId)
    {
        return $this->delete($this->get($emailBlacklistId));
    }
}

