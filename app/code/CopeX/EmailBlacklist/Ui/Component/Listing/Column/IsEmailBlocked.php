<?php

namespace CopeX\EmailBlacklist\Ui\Component\Listing\Column;

use Cope<PERSON>\EmailBlacklist\Model\ResourceModel\EmailBlacklist\Collection;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;

class IsEmailBlocked extends Column
{

    private Collection $blackListCollection;
    private UrlInterface $urlBuilder;

    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        Collection $blackListCollection,
        UrlInterface $urlBuilder,
        array $components = [],
        array $data = []
    )
    {

        parent::__construct($context, $uiComponentFactory, $components, $data);
        $this->blackListCollection = $blackListCollection;
        $this->urlBuilder = $urlBuilder;
    }

    /**
     * This method adds values to the column, specified in sales_order_grid.xml, to the sales order grid
     *
     * @param array $dataSource
     * @return array
     */
    public function prepareDataSource(array $dataSource)
    {
        if (isset($dataSource['data']['items'])) {
            $customerEmails = [];
            foreach ($dataSource['data']['items'] as $item) {
                $customerEmails [] = $item['customer_email'];
            }

            $blackListCollection = $this->blackListCollection->addFieldToFilter("email", ['in' => $customerEmails]);
            $fieldName = $this->getData('name');
            foreach ($dataSource['data']['items'] as & $item) {

                //get the value for this order
                $blackListItem = $blackListCollection->getItemByColumnValue("email", $item['customer_email']);

                $url = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGNsYXNzPSJoLTYgdy02IiBmaWxsPSJub25lIiB2aWV3Qm94PSIwIDAgMjQgMjQiIHN0cm9rZT0iIzAwREI0QSIgc3Ryb2tlLXdpZHRoPSIyIj4KICA8cGF0aCBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGQ9Ik01IDEzbDQgNEwxOSA3IiAvPgo8L3N2Zz4=";

                if($blackListItem){
                    $url = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGNsYXNzPSJoLTYgdy02IiBmaWxsPSJub25lIiB2aWV3Qm94PSIwIDAgMjQgMjQiIHN0cm9rZT0iI0RCMDYwMCIgc3Ryb2tlLXdpZHRoPSIyIj4KICA8cGF0aCBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGQ9Ik02IDE4TDE4IDZNNiA2bDEyIDEyIiAvPgo8L3N2Zz4=";
                    $item[$fieldName . '_link'] = $this->urlBuilder->getUrl(
                        'copex_emailblacklist/emailblacklist/edit',
                        ['emailblacklist_id' => $blackListItem->getId()]
                    );
                }

                $item[$fieldName . '_src'] = $url;
                $item[$fieldName . '_alt'] = '';
                $item[$fieldName . '_orig_src'] = $url;

            }
        }

        return $dataSource;
    }
}