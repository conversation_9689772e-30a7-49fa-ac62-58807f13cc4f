<?xml version="1.0" ?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
	<route url="/V1/copex-emailblacklist/emailblacklist" method="POST">
		<service class="CopeX\EmailBlacklist\Api\EmailBlacklistRepositoryInterface" method="save"/>
		<resources>
			<resource ref="CopeX_EmailBlacklist::EmailBlacklist_save"/>
		</resources>
	</route>
	<route url="/V1/copex-emailblacklist/emailblacklist/search" method="GET">
		<service class="CopeX\EmailBlacklist\Api\EmailBlacklistRepositoryInterface" method="getList"/>
		<resources>
			<resource ref="CopeX_EmailBlacklist::EmailBlacklist_view"/>
		</resources>
	</route>
	<route url="/V1/copex-emailblacklist/emailblacklist/:emailblacklistId" method="GET">
		<service class="CopeX\EmailBlacklist\Api\EmailBlacklistRepositoryInterface" method="get"/>
		<resources>
			<resource ref="CopeX_EmailBlacklist::EmailBlacklist_view"/>
		</resources>
	</route>
	<route url="/V1/copex-emailblacklist/emailblacklist/:emailblacklistId" method="PUT">
		<service class="CopeX\EmailBlacklist\Api\EmailBlacklistRepositoryInterface" method="save"/>
		<resources>
			<resource ref="CopeX_EmailBlacklist::EmailBlacklist_update"/>
		</resources>
	</route>
	<route url="/V1/copex-emailblacklist/emailblacklist/:emailblacklistId" method="DELETE">
		<service class="CopeX\EmailBlacklist\Api\EmailBlacklistRepositoryInterface" method="deleteById"/>
		<resources>
			<resource ref="CopeX_EmailBlacklist::EmailBlacklist_delete"/>
		</resources>
	</route>
</routes>
