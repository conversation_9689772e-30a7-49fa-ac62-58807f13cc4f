<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
	<acl>
		<resources>
			<resource id="Magento_Backend::admin">
				<resource id="CopeX_EmailBlacklist::EmailBlacklist" title="EmailBlacklist" sortOrder="10">
					<resource id="CopeX_EmailBlacklist::EmailBlacklist_save" title="Save EmailBlacklist" sortOrder="10"/>
					<resource id="CopeX_EmailBlacklist::EmailBlacklist_delete" title="Delete EmailBlacklist" sortOrder="20"/>
					<resource id="CopeX_EmailBlacklist::EmailBlacklist_update" title="Update EmailBlacklist" sortOrder="30"/>
					<resource id="CopeX_EmailBlacklist::EmailBlacklist_view" title="View EmailBlacklist" sortOrder="40"/>
				</resource>
			</resource>
		</resources>
	</acl>
</config>
