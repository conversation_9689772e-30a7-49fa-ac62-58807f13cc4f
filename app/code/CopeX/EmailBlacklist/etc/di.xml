<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<preference for="CopeX\EmailBlacklist\Api\EmailBlacklistRepositoryInterface" type="CopeX\EmailBlacklist\Model\EmailBlacklistRepository"/>
	<preference for="CopeX\EmailBlacklist\Api\Data\EmailBlacklistInterface" type="CopeX\EmailBlacklist\Model\EmailBlacklist"/>
	<preference for="CopeX\EmailBlacklist\Api\Data\EmailBlacklistSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
	<virtualType name="CopeX\EmailBlacklist\Model\ResourceModel\EmailBlacklist\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
		<arguments>
			<argument name="mainTable" xsi:type="string">copex_emailblacklist_emailblacklist</argument>
			<argument name="resourceModel" xsi:type="string">CopeX\EmailBlacklist\Model\ResourceModel\EmailBlacklist\Collection</argument>
		</arguments>
	</virtualType>
	<type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
		<arguments>
			<argument name="collections" xsi:type="array">
				<item name="copex_emailblacklist_emailblacklist_listing_data_source" xsi:type="string">CopeX\EmailBlacklist\Model\ResourceModel\EmailBlacklist\Grid\Collection</item>
			</argument>
		</arguments>
	</type>
</config>
