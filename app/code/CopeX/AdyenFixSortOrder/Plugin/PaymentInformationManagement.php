<?php
/**
 *
 * Adyen Payment module (https://www.adyen.com/)
 *
 * Copyright (c) 2023 Adyen N.V. (https://www.adyen.com/)
 * See LICENSE.txt for license details.
 *
 * Author: Adyen <<EMAIL>>
 */

namespace CopeX\AdyenFixSortOrder\Plugin;

use Magento\Payment\Model\MethodInterface;
use Magento\Checkout\Api\Data\PaymentDetailsInterface;
use Magento\Checkout\Model\PaymentInformationManagement as MagentoPaymentInformationManagement;
use Magento\Store\Model\StoreManagerInterface;

class PaymentInformationManagement
{

    private StoreManagerInterface $storeManager;

    public function __construct(
        StoreManagerInterface $storeManager
    ) {
        $this->storeManager = $storeManager;
    }

    public function afterGetPaymentInformation(
        MagentoPaymentInformationManagement $magentoPaymentInformationManagement,
        PaymentDetailsInterface $result,
        int $cartId
    ): PaymentDetailsInterface {

        $methodsInstances = $result->getPaymentMethods();
        $storeId = $this->storeManager->getStore()->getId();
        uasort(
            $methodsInstances,
            function (MethodInterface $a, MethodInterface $b) use ($storeId) {
                return (int)$a->getConfigData('sort_order', $storeId) - (int)$b->getConfigData('sort_order', $storeId);
            }
        );
        $result->setPaymentMethods($methodsInstances);
        return $result;
    }
}
