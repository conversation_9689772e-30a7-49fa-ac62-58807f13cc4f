<?php

namespace CopeX\GTMOrderAddressOnSuccess\DataLayer;

use MagePal\GoogleTagManager\DataLayer\OrderData\OrderAbstract;

class Address extends OrderAbstract
{
    /**
     * @return array
     */
    public function getData()
    {
        $order = $this->getOrder();
        $data =  [
            'email' => $order->getCustomerEmail(),
            'shipping_address' => $this->getAddressData($order->getShippingAddress()),
            'billing_address' => $this->getAddressData($order->getBillingAddress())
        ];

        return $data;
    }

    private function getAddressData(?\Magento\Sales\Api\Data\OrderAddressInterface $address)
    {
        if ($address && $address->getEntityId()) {
            return [
                "firstname" => $address->getFirstname(),
                "lastname" => $address->getLastname(),
                "street" => is_array($address->getStreet()) ? implode(" ",$address->getStreet()): $address->getStreet(),
                "postcode" => $address->getPostcode(),
                "city" => $address->getCity(),
                "country_id" => $address->getCountryId(),
                "company" => $address->getCompany(),
                "telephone" => $address->getTelephone(),
                "vatid" => $address->getVatId(),
                "prefix" => $address->getPrefix(),
                "suffix" => $address->getSuffix(),
            ];
        }
        return "";
    }
}