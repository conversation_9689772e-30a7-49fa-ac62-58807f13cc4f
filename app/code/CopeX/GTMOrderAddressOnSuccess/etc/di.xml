<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="MagePal\GoogleTagManager\DataLayer\OrderData\OrderProvider">
        <arguments>
            <argument name="orderProviders" xsi:type="array">
                <item name="order-provider-address" xsi:type="object">CopeX\GTMOrderAddressOnSuccess\DataLayer\Address</item>
            </argument>
        </arguments>
    </type>
</config>