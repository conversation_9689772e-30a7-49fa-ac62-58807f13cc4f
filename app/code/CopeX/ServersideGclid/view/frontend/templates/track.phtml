<?php
/**
* @var $block \Magento\Framework\View\Element\Template
*/
/** @var \CopeX\ServersideGclid\ViewModel\Config $viewModel */
$viewModel = $block->getViewModel();
?>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        const urlParams = new URLSearchParams(window.location.search);
        const paramValue = urlParams.get(atob('Z2NsaWQ='));
        if (paramValue) {
            document.cookie = "<?= $viewModel ? $viewModel->getCookieName() : base64_decode('ZGlsY2c=') ?>=" + btoa(paramValue) + "; expires=Fri, 31 Dec 9999 23:59:59 GMT; path=/";
        }
    });
</script>