<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SwatchRenderer;
use Magento\Framework\Escaper;
use Magento\Swatches\Block\Product\Renderer\Configurable;
use Magento\Swatches\ViewModel\Product\Renderer\Configurable as ConfigurableViewModel;

// phpcs:disable Generic.Files.LineLength.TooLong

/** @var Configurable $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ConfigurableViewModel $configurableViewModel */
$configurableViewModel = $viewModels->require(ConfigurableViewModel::class);

/** @var SwatchRenderer $swatchRendererViewModel */
$swatchRendererViewModel = $viewModels->require(SwatchRenderer::class);

$product = $block->getProduct();
$productId = $product->getId();

$attributes = $block->decorateArray($block->getAllowAttributes());

$layout = $block->getLayout();
$swatchItemBlock = $layout->getBlock('product.swatch.item');
$swatchItemBlock->setData('product_id', $productId);

$tooltipBlockHtml = $block->getBlockHtml('product.swatch.tooltip');
?>
<?php if ($product->isSaleable() && count($attributes)): ?>
    <script>
        function initConfigurableSwatchOptions_<?= (int) $productId ?>() {
            const configurableOptionsComponent = initConfigurableOptions(
                '<?= (int) $productId ?>',
                <?= /* @noEscape */ $block->getJsonConfig() ?>
            );
            const swatchOptionsComponent = initSwatchOptions(
                <?= /* @noEscape */ $block->getJsonSwatchConfig() ?>
            );

            return Object.assign(
                configurableOptionsComponent,
                swatchOptionsComponent
            );
        }
    </script>

    <div x-data="const configurableOptionsComponent = initConfigurableOptions(
                '<?= (int) $productId ?>',
                <?= /* @noEscape */ $block->getJsonConfig() ?>
            );
            const swatchOptionsComponent = initSwatchOptions(
                <?= /* @noEscape */ $block->getJsonSwatchConfig() ?>
            );

            return Object.assign(
                configurableOptionsComponent,
                swatchOptionsComponent
            );"
         x-init="init(); initShowSwatchesIntersect();"
         @private-content-loaded.window="onGetCartData($event.detail.data)"
         class="relative mb-6"
    >
        <h2 class="mb-4 text-xl font-medium text-gray-900 title-font">
            <?= $escaper->escapeHtml(__('Product Options:')) ?>
        </h2>
        <div>
            <?php foreach ($attributes as $attribute): ?>
                <?php $attributeId = $attribute->getAttributeId(); ?>
                <?php $productAttribute = $attribute->getProductAttribute();  ?>
                <?php if ($swatchRendererViewModel->isSwatchAttribute($productAttribute)): ?>
                    <div class="swatch-attribute border-t last:border-b border-container min-h-14
                            <?= $escaper->escapeHtmlAttr($productAttribute->getAttributeCode()) ?>">
                        <template x-if="showSwatches">
                            <div class="flex flex-col sm:flex-row items-center py-4 sm:py-1 w-full border-gray-300">

                                <label class="w-full sm:w-1/2 text-left text-gray-700 label product-option-label"
                                       for="attribute<?= $escaper->escapeHtmlAttr($productAttribute->getAttributeCode()) ?>"
                                >
                                    <span>
                                        <?= $escaper->escapeHtml($productAttribute->getStoreLabel()) ?>
                                    </span>
                                </label>
                                <div class="w-full sm:ml-2 sm:w-1/2 text-left text-gray-900 product-option-values">
                                    <div
                                         class="flex items-center -mx-4 space-x-4 swatch-attribute-options"
                                         role="radiogroup"
                                    >
                                        <template
                                            x-for="(item, index) in optionConfig.attributes[<?= (int) $attributeId ?>].options"
                                            :key="item.id"
                                        >
                                            <?= /* @noEscape */ $swatchItemBlock->setData('attribute_id', $attributeId)->toHtml(); ?>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                <?php else: ?>
                    <div class="flex items-center py-2 w-full border-t border-gray-300 last:border-b">
                        <label class="w-1/2 text-left text-gray-700 label"
                               for="attribute<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>"
                        >
                            <span>
                                <?= $escaper->escapeHtml($attribute->getProductAttribute()->getStoreLabel()) ?>
                            </span>
                        </label>
                        <div class="ml-2 w-1/2 text-left text-gray-900">
                            <select name="super_attribute[<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>]"
                                    id="attribute<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>"
                                    class="form-select super-attribute-select"
                                    x-on:change="changeOption(<?= (int) $attribute->getAttributeId() ?>, event.target.value)"
                                    required="required">
                                <option value="">
                                    <?= $escaper->escapeHtml(__('Choose an Option...')) ?>
                                </option>
                                <template
                                    x-for="(item, index) in getAllowedAttributeOptions(<?= (int) $attribute->getAttributeId() ?>)"
                                    :key="item.id"
                                >
                                    <option
                                        :value="item.id"
                                        x-html="item.label"
                                        :selected="selectedValues[<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>] ===
                                item.id">
                                    </option>
                                </template>
                            </select>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
        <?= /* @noEscape */ $tooltipBlockHtml ?>
    </div>

<?php endif;?>
