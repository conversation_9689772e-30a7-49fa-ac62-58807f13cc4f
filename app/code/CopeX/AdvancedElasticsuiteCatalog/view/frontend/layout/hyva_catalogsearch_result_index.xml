<?xml version="1.0"?>
<!--
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <attribute name="class" value="page-with-filter"/>
        <referenceContainer name="content">
<!--            <referenceBlock name="product_list_toolbar_pager" template="CopeX_AdvancedElasticsuiteCatalog::pager/pager.phtml" />-->
            <container name="category.list.view" htmlTag="div" htmlClass="category-list-view" />
            <block class="CopeX\AdvancedElasticsuiteCatalog\Block\AdvancedCatalog" name="CopeX.elastic.navigation"
                   template="CopeX_AdvancedElasticsuiteCatalog::configuration.phtml" ifconfig="smile_advanced_elasticsuite_catalog/general/active" />
            <block class="Magento\Framework\View\Element\Template"
                   name="attribute-filter-js"
                   template="CopeX_AdvancedElasticsuiteCatalog::catalog/layer/filter/js/attribute-filter-js.phtml" ifconfig="smile_advanced_elasticsuite_catalog/general/active"
            />
            <block class="Magento\Framework\View\Element\Template"
                   name="slider-filter-js"
                   template="CopeX_AdvancedElasticsuiteCatalog::catalog/layer/filter/js/slider-filter-js.phtml" ifconfig="smile_advanced_elasticsuite_catalog/general/active"
            />
        </referenceContainer>
        <referenceBlock name="catalogsearch.navigation.renderer">

            <referenceBlock name="catalogsearch.navigation.renderer.attribute"
                template="CopeX_AdvancedElasticsuiteCatalog::catalog/layer/filter/attribute.phtml">
            </referenceBlock>

            <referenceBlock class="Smile\ElasticsuiteCatalog\Block\Navigation\Renderer\Category"
                   name="catalogsearch.navigation.renderer.category"
                   template="CopeX_AdvancedElasticsuiteCatalog::catalog/layer/filter/default.phtml"/>

            <referenceBlock name="catalogsearch.navigation.renderer.slider"
                   template="CopeX_AdvancedElasticsuiteCatalog::catalog/layer/filter/slider.phtml">
            </referenceBlock>

            <referenceBlock name="catalogsearch.navigation.renderer.price.slider"
                   template="CopeX_AdvancedElasticsuiteCatalog::catalog/layer/filter/slider.phtml">
            </referenceBlock>
            <referenceBlock class="Hyva\SmileElasticsuite\Block\SmileElasticsuiteSwatches\Navigation\Renderer\Swatches"
                   name="catalog.navigation.renderer.swatches" />
        </referenceBlock>
        <referenceBlock name="renderer.slider" remove="true"/>
        <referenceBlock name="renderer.price.slider" remove="true"/>
    </body>
</page>
