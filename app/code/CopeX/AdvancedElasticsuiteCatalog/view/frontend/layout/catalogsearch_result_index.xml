<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <attribute name="class" value="page-with-filter"/>
        <attribute name="class" value="catalog-category-view"/>

        <move element="search.result" destination="category.list.view" />
        <referenceContainer name="columns">
            <block name="loading.script" template="CopeX_AdvancedElasticsuiteCatalog::catalog/layer/loading.phtml" before="-" />
        </referenceContainer>
        <referenceContainer name="content">
            <container name="category.list.view" htmlTag="div" htmlClass="category-list-view" />
            <block class="CopeX\AdvancedElasticsuiteCatalog\Block\AdvancedCatalog" name="CopeX.elastic.navigation"
                   template="CopeX_AdvancedElasticsuiteCatalog::configuration.phtml" ifconfig="smile_advanced_elasticsuite_catalog/general/active" />
        </referenceContainer>
        <referenceBlock name="product.price.render.default">
            <arguments>
                <argument name="is_product_list" xsi:type="boolean">true</argument>
            </arguments>
        </referenceBlock>
    </body>
</page>
