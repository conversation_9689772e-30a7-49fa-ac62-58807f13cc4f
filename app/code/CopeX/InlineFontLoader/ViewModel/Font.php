<?php

namespace CopeX\InlineFontLoader\ViewModel;

use Magento\Framework\App\CacheInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\View\Asset\File\NotFoundException;
use Magento\Framework\View\Asset\Repository;
use Magento\Store\Model\ScopeInterface;
use Magento\Framework\App\Config\ScopeConfigInterface as ScopeConfig;
use Magento\Framework\Filesystem;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\View\Design\Theme\ThemeProviderInterface;


class Font implements \Magento\Framework\View\Element\Block\ArgumentInterface
{

    const WEB_CONFIG_INLINE_FONTS_ENABLED = "web/inline_font_loader/enabled";
    const WEB_CONFIG_INLINE_FONTS = "web/inline_font_loader/config";
    const INLINE_FONT_LOADER_CONFIG = "inline_font_loader_config";

    protected $types = [
        'woff2' => 'woff2',
        'woff' => 'woff',
        'ttf' => 'truetype',
        'svg' => 'svg',
        'eot' => 'embedded-opentype'
        ];
    private $config = false;
    private Repository $assetRepository;
    private RequestInterface $request;
    private CacheInterface $cache;

    public function __construct(
        Filesystem $filesystem,
        ScopeConfig $scopeConfig,
        StoreManagerInterface $storeManager,
        ThemeProviderInterface $themeProvider,
        SerializerInterface $serializer,
        Repository $assetRepository,
        RequestInterface $request,
        CacheInterface $cache
    ) {
        $this->filesystem = $filesystem;
        $this->scopeConfig = $scopeConfig;

        $this->storeManager = $storeManager;
        $this->themeProvider = $themeProvider;
        $this->serializer = $serializer;
        $this->assetRepository = $assetRepository;
        $this->request = $request;
        $this->cache = $cache;
    }

    private function getConfig(){
        $this->config = $this->cache->load(self::INLINE_FONT_LOADER_CONFIG);
        if ($this->config) {
            $this->config = $this->serializer->unserialize($this->config);
        }
        else{
            $this->config = [];
            $config = $this->getFontConfig();
            foreach ($config as $configItem) {
                if(substr($configItem['definition'],-1) !== ";"){
                    $configItem['definition'] .= ";";
                }
                $files = $this->getFiles($configItem['path']);
                if($files){
                    $fileOutput = [];
                    preg_match("/font-family:'?([a-zA-Z ]*)'?/", $configItem['definition'], $fontName);
                    if (isset($fontName[1])) {
                        $fileOutput [] = "local('" . $fontName[1] . "')";
                    }
                    foreach($files as $type => $file){
                        $fileOutput []= "url('".$file . "') format(" . $type . ")";
                    }
                    $configItem['files'] = $files;
                    $configItem['output'] = $configItem['definition'] . "src:" .  implode(",",$fileOutput);
                    $this->config []= $configItem;
                }
            }
             $this->cache->save(
                $this->serializer->serialize($this->config),
                 self::INLINE_FONT_LOADER_CONFIG,
                ["config"]
            );
        }
        return $this->config;
    }

    public function getFonts(){
        if($this->isEnabled()){
            return $this->getConfig();
        }
        return [];
    }

    public function isEnabled(){
        return $this->getConfigValue(self::WEB_CONFIG_INLINE_FONTS_ENABLED);
    }

    public function getFontConfig(){
        return $this->serializer->unserialize($this->getConfigValue(self::WEB_CONFIG_INLINE_FONTS));
    }

    private function getConfigValue($path){
        $storeId = $this->storeManager->getStore()->getId();
        return $this->scopeConfig->getValue(
            $path,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    private function getStaticVersion(){
        $version = "";
        $mediaRead = $this->filesystem->getDirectoryRead(DirectoryList::PUB);
        if($mediaRead->isExist("static/deployed_version.txt")){
            $version = $mediaRead->readFile("static/deployed_version.txt");
        }
        return $version;
    }



    //[woff2 => url('/static/VERSION/frontend/THEME/LOCALE/fonts/roboto.woff2')]
    private function getFiles($path)
    {
        $files = [];
        $params = ['_secure' => $this->request->isSecure()];
        foreach ($this->types as $fileEnding => $type) {
            $asset = $this->assetRepository->createAsset(implode(".",[$path,$fileEnding]), $params);
            try{
                if($asset->getSourceFile()){
                    $files [$type] = $asset->getUrl();
                }
            }
            catch(NotFoundException $e){}
        }
        return $files;
    }

}