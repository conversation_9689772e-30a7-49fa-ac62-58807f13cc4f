<?php

namespace CopeX\DefaultShippingCountry\Plugin\Magento\Checkout\Block\Checkout;

class LayoutProcessor
{

    private \Magento\Checkout\Model\Session\Proxy $checkoutSession;

    public function __construct( \Magento\Checkout\Model\Session\Proxy $checkoutSession)
    {
        $this->checkoutSession = $checkoutSession;
    }

    /**
     * @param \Magento\Checkout\Block\Checkout\LayoutProcessor $subject
     * @param array $jsLayout
     * @return array
     */
    public function afterProcess(
        \Magento\Checkout\Block\Checkout\LayoutProcessor $subject,
        $jsLayoutResult,
        array $jsLayout
    )
    {
        if($this->checkoutSession->getQuote() && $this->checkoutSession->getQuote()->getShippingAddress() &&  $country = $this->checkoutSession->getQuote()->getShippingAddress()->getCountryId()){
            $jsLayoutResult['components']['checkout']['children']['steps']['children']['shipping-step']
            ['children']['shippingAddress']['children']['billingAddress']['children']['address-fieldset']['children']['country_id']['value'] = $country;
            $jsLayoutResult['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']['children']['shipping-address-fieldset']['children']['country_id']['value'] = $country;
        }
        return $jsLayoutResult;
    }
}