<?php

namespace CopeX\DefaultShippingCountry\Plugin\Magento\Checkout\CustomerData;

use Magento\Checkout\Model\Session;
use Magento\Quote\Model\Quote;

/**
 * Class Cart
 * @package CopeX\DefaultShippingCountry\Plugin\Magento\Checkout\CustomerData
 */
class Cart
{

    /** @var Session */
    protected $checkoutSession;

    /** @var Quote|null */
    protected $quote = null;



    /**
     * Cart constructor.
     * @param Session $checkoutSession
     */
    public function __construct(
        Session $checkoutSession
    )
    {
        $this->checkoutSession = $checkoutSession;
    }

    /**
     * @param \Magento\Checkout\CustomerData\Cart $subject
     * @param array $result
     * @return array $result
     */
    public function afterGetSectionData(
        \Magento\Checkout\CustomerData\Cart $subject,
        $result
    )
    {
        $quote = $this->getQuote();

        if($quote->getShippingAddress()){
            $address = $quote->getShippingAddress();
            $result['shipping_method'] = $address->getShippingMethod();
            $result['shipping_amount'] = $address->getShippingInclTax();
            if ($shippingRate = $address->getShippingRateByCode($address->getShippingMethod())) {
                $result['shipping_rate'] = $shippingRate->getData();
            }
         }
        return $result;
    }

    /**
     * @return Quote|null
     */
    protected function getQuote()
    {
        if (null === $this->quote) {
            try {
                $this->quote = $this->checkoutSession->getQuote();
            } catch (\Exception $e) {
                return $this->quote;
            }
        }

        return $this->quote;
    }
}
