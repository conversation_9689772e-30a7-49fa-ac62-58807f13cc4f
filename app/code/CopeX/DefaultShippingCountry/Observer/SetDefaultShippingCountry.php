<?php

namespace CopeX\DefaultShippingCountry\Observer;

use Magento\Directory\Helper\Data;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;
use Magento\Quote\Model\Quote;

class SetDefaultShippingCountry implements ObserverInterface
{

    private Data $data;

    public function __construct( Data $data)
    {
        $this->data = $data;
    }

    /**
     * Observer for sales_quote_collect_totals_before
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        $event = $observer->getEvent();
        /**
         * @var $quote Quote
         */
        $quote = $event->getQuote();
        $shippingAddress = $quote->getShippingAddress();
        if ($shippingAddress) {
            if (!$shippingAddress->getCountryId()) {
                $defaultCountryId = $this->getDefaultCountry($quote->getStoreId());
                $shippingAddress->setCountryId($defaultCountryId);
                $shippingAddress->setCollectShippingRates(true)->collectShippingRates();
                $shippingRatesCollection = $shippingAddress->getShippingRatesCollection();
                if ($shippingRatesCollection && $firstShippingRate = $shippingRatesCollection->getFirstItem()) {
                    $shippingAddress->setShippingMethod($firstShippingRate->getCode());
                }
            }
        }
    }

    public function getDefaultCountry($storeId){
        return $this->data->getDefaultCountry($storeId);
    }
}
