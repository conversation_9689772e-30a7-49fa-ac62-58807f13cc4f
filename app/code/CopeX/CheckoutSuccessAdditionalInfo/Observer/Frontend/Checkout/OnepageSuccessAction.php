<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\CheckoutSuccessAdditionalInfo\Observer\Frontend\Checkout;

use Magento\Framework\Event\ObserverInterface;
use Magento\Store\Model\ScopeInterface;

class OnepageSuccessAction implements ObserverInterface
{
    /**
     * @var \Magento\Framework\Registry
     */
    protected \Magento\Framework\Registry $registry;
    /**
     * @var \Magento\Sales\Model\OrderFactory
     */
    protected \Magento\Sales\Model\OrderFactory $orderFactory;
    /*
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig;

    /**
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Sales\Model\OrderFactory $orderFactory
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        \Magento\Framework\Registry                        $registry,
        \Magento\Sales\Model\OrderFactory                  $orderFactory,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
    )
    {
        $this->registry = $registry;
        $this->orderFactory = $orderFactory;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Add additional order info to success page
     *
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        $order = $observer->getOrder();
        $this->registry->register('current_order', $order);
    }
}


