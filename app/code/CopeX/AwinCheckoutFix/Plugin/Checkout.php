<?php

namespace CopeX\AwinCheckoutFix\Plugin;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Event\Observer;

class Checkout
{

    private $scopeConfig;

    public function __construct(ScopeConfigInterface $scopeConfig)
    {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * @param \Awin\AdvertiserTracking\Observer\Checkout $subject
     * @param callable $proceed
     * @param Observer $observer
     * @return void
     */
    public function aroundExecute(\Awin\AdvertiserTracking\Observer\Checkout $subject, callable $proceed, Observer $observer): void
    {
        try { //surround with try catch
            if ( intval($this->scopeConfig->getValue('awin_settings/general/awin_advertiser_id',
                    \Magento\Store\Model\ScopeInterface:: SCOPE_STORE)) > 0 ) { //only do something if it is configured
                $proceed($observer);
            }
        } catch (\Exception $e) {
        }
    }

    public function beforeGet_server_to_server_url(\Awin\AdvertiserTracking\Observer\Checkout $subject, $order, $awc, $channel){
        // if($awc == "" && $channel == "aw") throw new \Exception("no awin tracking please");
        return [$order, $awc, $channel];
    }
}