<?xml version="1.0"?>
<!--
/**
 * Copyright © 2016 FireGento e.V.
 * See LICENSE.md bundled with this module for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <block class="CopeX\CartDiscountPercentage\Block\Price\Details" name="copex.percent.product.price.details"
               as="price_percent" template="CopeX_CartDiscountPercentage::product/price/percentage.phtml"/>
        <referenceBlock name="minicart">
            <arguments>
                <argument name="jsLayout" xsi:type="array">
                    <item name="types" xsi:type="array"/>
                    <item name="components" xsi:type="array">
                        <item name="minicart_content" xsi:type="array">
                            <item name="children" xsi:type="array">
                                <item name="reduction.container" xsi:type="array">
                                    <item name="component" xsi:type="string">uiComponent</item>
                                    <item name="config" xsi:type="array">
                                        <item name="displayArea" xsi:type="string">reductionContainer</item>
                                    </item>
                                    <item name="children" xsi:type="array">
                                        <item name="reduction" xsi:type="array">
                                            <item name="component" xsi:type="string">CopeX_CartDiscountPercentage/js/view/checkout/minicart/reduction</item>
                                            <item name="config" xsi:type="array">
                                                <item name="template" xsi:type="string">CopeX_CartDiscountPercentage/minicart/reduction</item>
                                            </item>
                                            <item name="children" xsi:type="array">
                                                <item name="reduction.total.original_price" xsi:type="array">
                                                    <item name="component" xsi:type="string">uiComponent</item>
                                                    <item name="config" xsi:type="array">
                                                        <item name="displayArea" xsi:type="string">totalOriginalPrice</item>
                                                        <item name="template" xsi:type="string">CopeX_CartDiscountPercentage/minicart/reduction/total_original_price</item>
                                                    </item>
                                                </item>
                                                <item name="reduction.total.reduction" xsi:type="array">
                                                    <item name="component" xsi:type="string">uiComponent</item>
                                                    <item name="config" xsi:type="array">
                                                        <item name="displayArea" xsi:type="string">totalReduction</item>
                                                        <item name="template" xsi:type="string">CopeX_CartDiscountPercentage/minicart/reduction/total_reduction</item>
                                                    </item>
                                                </item>
                                            </item>
                                        </item>
                                    </item>
                                </item>
                            </item>
                        </item>
                    </item>
                </argument>
            </arguments>
        </referenceBlock>
    </body>
</page>
