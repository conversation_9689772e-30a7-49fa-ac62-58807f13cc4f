<tr class="total-old-price">
    <th class="old-price label" data-bind="html: $t('UVP')"></th>
    <td class="old-price price" data-bind="html:cart().total_original_price"></td>
</tr>
<tr class="total-percentage">
    <td colspan="2" class="reduction" data-bind="html:cart().total_reduction"></td>
</tr>

<!-- ko if: isBothPricesDisplayed() -->
<tr class="totals sub excl">
    <th class="mark" scope="row">
        <span data-bind="i18n: title"></span>
        <span data-bind="i18n: excludingTaxMessage"></span>
    </th>
    <td class="amount">
        <span class="price" data-bind="html: getValue(), attr: {'data-th': excludingTaxMessage}"></span>
    </td>
</tr>
<tr class="totals sub incl">
    <th class="mark" scope="row">
        <span data-bind="i18n: title"></span>
        <span data-bind="i18n: includingTaxMessage"></span>
    </th>
    <td class="amount">
        <span class="price" data-bind="html: getValueInclTax(), attr: {'data-th': includingTaxMessage}"></span>
    </td>
</tr>
<!-- /ko -->
<!-- ko if: !isBothPricesDisplayed() && isIncludingTaxDisplayed() -->
<tr class="totals sub">
    <th data-bind="i18n: title" class="mark" scope="row"></th>
    <td class="amount">
        <span class="price" data-bind="html: getValueInclTax(), attr: {'data-th': title}"></span>
    </td>
</tr>
<!-- /ko -->
<!-- ko if: !isBothPricesDisplayed() && !isIncludingTaxDisplayed() -->
<tr class="totals sub">
    <th data-bind="i18n: title" class="mark" scope="row"></th>
    <td class="amount">
        <span class="price" data-bind="html: getValue(), attr: {'data-th': title}"></span>
    </td>
</tr>
<!-- /ko -->
