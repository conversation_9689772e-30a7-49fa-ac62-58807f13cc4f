define(function () {
    'use strict';

    var mixin = {
        getValue: function () {
            var price;

            if (!this.isCalculated()) {
                return this.notCalculatedMessage;
            }
            price =  this.totals()['shipping_incl_tax'];

            return this.getFormattedPrice(price);
        }
    };

    return function (target) {
        return target.extend(mixin);
    };
});
