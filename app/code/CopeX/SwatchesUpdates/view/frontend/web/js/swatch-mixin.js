define([
    'jquery',
    'mage/translate',
    'priceUtils',
    'mage/url'
], function ($, $t, priceUtils, url) {
    'use strict';

    var swatchMixin = {

        calculatePriceDIff: function ($options, $widget) {
            $options.each(function (key, item) {
                let $item = $(item);
                let attributeId = $item.parents('.swatch-attribute').data('attribute-id');
                let id = $item.data('option-id');
                if ($widget.optionsMap.hasOwnProperty(attributeId)) {
                    let priceDiff = $widget.optionsMap[attributeId][id]['price'] - $widget.options.jsonConfig.prices.finalPrice['amount'];
                    $item.data('surcharge', priceDiff);
                    if (priceDiff > 0) {
                        $item.append('<div class="custom-swatch-item-price product-options-item-price"><span>' + $t('Surcharge') + '</span><span class="product-option-item-price">+ ' + priceUtils.formatPrice(priceDiff, $widget.options.jsonConfig.currencyFormat) + '</span></div>');
                    }
                }
            });
        },

        addBadgeAndComment: function ($options, $widget) {
            var badges = $widget.options.jsonConfig.option_badges;

            badges && $options.each(function (key, item) {
                let $item = $(item), $swatchAttribute = $item.parents('.swatch-attribute') ;
                let attributeCode = $swatchAttribute.data('attribute-code');
                let id = $item.data('option-id');
                if (badges.hasOwnProperty(attributeCode) && badges[attributeCode].hasOwnProperty('info')){
                    let $label = $swatchAttribute.find('label');
                    if($label.find('.item-info').length === 0){
                        $label.append('<div class="item-info"><i class="mgz-icon-box-element fas mgz-fa-info-circle"><span role="tooltip">' + badges[attributeCode]['info'] + '</span></i></div>');
                    }
                }
                if (badges.hasOwnProperty(attributeCode) && badges[attributeCode].hasOwnProperty(id)) {
                    let $customLabel = $item.find('.custom-swatch-item-label');
                    let config = badges[attributeCode][id];
                    $item.data('badge_config', config);
                    $customLabel.append('<div class="custom-swatch-item-badge"></div>');
                    let $badContainer = $item.find('.custom-swatch-item-badge');
                    if (config.hasOwnProperty('badge') && config.badge) {
                        $badContainer.append('<span>' + $t(config.badge) + '</span>');
                    }
                    if (config.hasOwnProperty('comment') && config.comment) {
                        $customLabel.append('<div class="custom-swatch-item-comment"><span>' + $t(config.comment) + '</span></div>');
                    }
                    if (config.hasOwnProperty('info') && config.info) {
                        $badContainer.append('<div class="item-info"><i class="mgz-icon-box-element fas mgz-fa-info-circle"><span role="tooltip">' + config.info + '</span></i></div>');
                    }
                }
            });
        },

        _RenderControls: function () {
            this._super();
            // get the cheapest options first
            this._sortOptions();
            let $widget = this;
            $('.' + this.options.classes.attributeClass).addClass("product-options-info-inner");
            let $options = $('.' + this.options.classes.optionClass);
            $('.' + this.options.classes.attributeLabelClass).addClass("product-options-special-headline").wrap('<label></label>');
            $options.addClass("custom-swatch-item product-options-info-item").wrapInner('<div class="custom-swatch-item-label"><div class="product-options-item-name"></div></div>');
            $('.' + this.options.classes.attributeClass + " input").attr('data-msg-required',$t('Please choose an option'));
            this.addBadgeAndComment($options, $widget);
            this.calculatePriceDIff($options, $widget);
            // this.selectFirstNoSurchargeOption($options);
        },

        selectFirstNoSurchargeOption: function($options){
            let attributeSet = [];
            $options.each(function (key, item) {
                    let $item = $(item);
                    let attributeId = $item.parents('.swatch-attribute').data('attribute-id');
                    if($item.data('surcharge') === 0 && attributeSet[attributeId] === undefined){
                        attributeSet[attributeId] = true;
                        $item.trigger('click');
                    }
            });
        },

        _sortOptions: function(){
            let $widget = this, jsonConfig = this.options.jsonConfig;
            $.each(jsonConfig.attributes, function () {
                var item = this;
                $.each(item.options, function () {
                    if (this.products.length > 0) {
                        this.products.sort(function(a, b) {
                            return jsonConfig.optionPrices[a].finalPrice.amount - jsonConfig.optionPrices[b].finalPrice.amount }
                        );
                        $widget.optionsMap[item.id][this.id] = {
                            price: parseInt(
                                jsonConfig.optionPrices[this.products[0]].finalPrice.amount,
                                10
                            ),
                            products: this.products
                        };
                    }
                });
            });
        },

        _OnClick: function ($this, $widget) {
            this._super($this, $widget);
            this._updateSetChooser($this, $widget);
            this._updateSaveInfo($this, $widget);
            this._updateDeliveryTimeInfo();
            this._updateProductDescription();
            this._updateRelatedItemsBasedOnSelectedOption();
        },

        loadImage: function (lazyImage) {
            lazyImage.src = lazyImage.dataset.src;
            lazyImage.classList.remove("lzy_img");
        },

        loadImages: function () {
            var images = document.querySelectorAll('.block.related img.lzy_img');

            for (var image of images) {
                this.loadImage(image);
            }
        },

        _updateSaveInfo: function($this, $widget){
            $(".product-options-bottom .old-price.special-price").removeClass("special-price");
            let prices = $widget._getNewPrices(),
                oldPrice = prices.oldPrice.amount,
                finalPrice = prices.finalPrice.amount,
                priceDiff = oldPrice - finalPrice;
            if (priceDiff > 0) {
                $('.save-info-block .save-info .price').html(priceUtils.formatPrice(priceDiff,$widget.options.jsonConfig.currencyFormat));
            }
        },

        _updateSetChooser: function($this, $widget) {
            let $parent = $this.parents('.' + $widget.options.classes.attributeClass),
                attributeId = $parent.data('attribute-id'), $setChooserItems = $('.product-setchooser-items'),
                attributeCode = this._getAttributeCodeById(attributeId);
            if (!$this.hasClass('selected')) {
                $setChooserItems.find('.product-setchooser-actions').removeClass(attributeCode + '-selected');
            } else {
                $setChooserItems.find('.product-setchooser-actions').removeClass(attributeCode + '-selected');
                $setChooserItems.find('#product-setchooser-actions-' + $this.data('option-id')).addClass(attributeCode + '-selected');
            }
        },

        /**
         * Sets mediaCache for cases when jsonConfig contains preSelectedGallery on layered navigation result pages
         *
         * @private
         */
        _updateRelatedItemsBasedOnSelectedOption: function () {
            var selected_options = {};
            var self = this;

            $('div.swatch-attribute').each(function (k, item) {
                var attribute_id = $(item).data('attribute-id'),
                    option_selected = $(item).attr('data-option-selected');

                if (!attribute_id || !option_selected) {
                    return;
                }
                selected_options[attribute_id] = option_selected.toString();
            });

            var product_id_index = $('[data-role=swatch-options]').data('mageSwatchRenderer').options.jsonConfig.index;
            var productData = this._determineProductData();
            var selectedProductId = productData.productId;

            $.each(product_id_index, function (product_id, attributes) {
                var productIsSelected = function (attributes, selected_options) {
                    return _.isEqual(attributes, selected_options);
                }

                if (productIsSelected(attributes, selected_options)) {
                    selectedProductId = product_id;
                }
            });

            var data = {productId: selectedProductId};

            if (data.productId === productData.productId) {
                return;
            }
            $('#product_addtocart_form').data('product-related-id', selectedProductId);

            var loader = $('.loader');

            $.ajax({
                method: 'GET',
                url: url.build('extendconfigurable/index/index'),
                data: data,
                cache: true,
                beforeSend: function (request) {
                    loader.show();
                },
                success: function (result) {
                    var relatedBlock = $('.block.related');
                    let formKey = $.cookie('form_key');
                    let $html = $(result.html);
                    $html.find('[name="form_key"]').val(formKey);
                    relatedBlock.parent().html($html);

                    showRelatedProducts($('.block.related .item.product'), relatedBlock.data('limit'));
                    self.loadImages();
                    loader.hide();
                },
                error: function (request, error) {
                    loader.hide();
                }
            });

            var showRelatedProducts = function (elements, limit) {
                var index;

                if (limit === 0) {
                    limit = elements.length;
                }

                for (index = 0; index < limit; index++) {
                    $(elements[index]).show();
                }
            };
        },

        _updateDeliveryTimeInfo: function () {
            let productId = this.getProductId(),
                optionDeliveryTime = this.options.jsonConfig.optionDeliveryTime,
                deliveryTimeInfo = productId ? optionDeliveryTime[productId] : optionDeliveryTime[Object.keys(optionDeliveryTime)[0]],
                productShippingInfo = $(this.options.selectorProduct + ' .product-info-price-shippinginfo');

            if (typeof productShippingInfo === 'undefined') {
                return;
            }

            if (deliveryTimeInfo.custom) {
                var shippingInfoContainer = productShippingInfo.find('#delivery-time-infoblock');
                shippingInfoContainer.html(deliveryTimeInfo.content);
            } else {
                productShippingInfo.html(deliveryTimeInfo.content);
            }
        },

        _updateProductDescription: function () {
            var self = this;
            var upsellOuterContainer = $('.product-upsell-outer');

            if (!upsellOuterContainer.length) {
                return;
            }
            var selected_options = {};

            $('div.swatch-attribute').each(function (k, item) {
                var attribute_id = $(item).data('attribute-id'),
                    option_selected = $(item).data('option-selected');

                if (!attribute_id || !option_selected) {
                    return;
                }
                selected_options[attribute_id] = option_selected;
            });

            var product_id_index = $('[data-role=swatch-options]').data('mageSwatchRenderer').options.jsonConfig.index;
            var productData = this._determineProductData();
            var selectedProductId = productData.productId;

            $.each(product_id_index, function (product_id, attributes) {
                var productIsSelected = function (attributes, selected_options) {
                    return _.isEqual(attributes, selected_options);
                }

                if (productIsSelected(attributes, selected_options)) {
                    selectedProductId = product_id;
                }
            });

            if (selectedProductId === productData.productId) {
                return;
            }

            var data = {
                productId: selectedProductId,
                parentProductId: productData.productId,
                redirectLocation: window.location.pathname
            };

            var loader = $('.product-upsell-outer .loader');

            $.ajax({
                method: 'POST',
                url: url.build('extendconfigurable/index/upsell'),
                data: data,
                beforeSend: function (request) {
                    loader.show();
                },
                success: function (result) {
                    if (result.html) {
                        upsellOuterContainer.replaceWith(result.html);
                        self.loadImages();
                        addFormSelectIcon();
                    }
                    loader.hide();
                },
                error: function (request, error) {
                    loader.hide();
                }
            });

            var addFormSelectIcon = function () {
                $('select').each(function () {
                    var $this = $(this);

                    $this.wrap('<div class="select-wrapper"></div>');
                    $this.after('<div class="select-icon"></div>');
                });
            }
        }

    };

    return function (targetWidget) {
        $.widget('mage.SwatchRenderer', targetWidget, swatchMixin);
        return $.mage.SwatchRenderer;
    };
});