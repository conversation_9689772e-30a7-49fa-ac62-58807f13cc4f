<?php

namespace CopeX\SwatchesUpdates\Plugin\Magento\ConfigurableProduct\Block\Product\View\Type;

use Magento\Cms\Block\BlockFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Serialize\SerializerInterface;

class Configurable
{
    protected $serializer;
    private BlockFactory $blockFactory;

    public function __construct(
        SerializerInterface $serializer,
        BlockFactory $blockFactory
    ) {
        $this->serializer = $serializer;
        $this->blockFactory = $blockFactory;
    }

    public function afterGetJsonConfig(
        \Magento\ConfigurableProduct\Block\Product\View\Type\Configurable $subject,
        $result
    ) {
        $result = $this->serializer->unserialize($result);
        try {
            if($subject->getProduct()->getOptionBadges()){
                $options = $this->serializer->unserialize($subject->getProduct()->getOptionBadges());
                foreach ($options as $attributeCode => $option) {
                    try{
                        $block = $this->blockFactory->create()->setBlockId(implode("_",
                            ["option", "tooltip", $attributeCode]));
                        if ($blockHtml = $block->toHtml()) {
                            $options[$attributeCode]['info'] = $blockHtml;
                        }
                    } catch (NoSuchEntityException $notFound) {}
                    foreach (array_keys($option) as $optionKey) {
                        try {
                            $block = $this->blockFactory->create()->setBlockId(implode("_",
                                ["option", "tooltip", $attributeCode, $optionKey]));
                            if ($blockHtml = $block->toHtml()) {
                                $options[$attributeCode][$optionKey]['info'] = $blockHtml;
                            }
                        } catch (NoSuchEntityException $notFound) {}
                    }
                }
                $result['option_badges'] = $options;
            }
        } catch (\InvalidArgumentException $e) {
        }
        return $this->serializer->serialize($result);
    }
}