# Hyvä Ajax AddToCart

## Summary
Adds Ajax Add to Cart functionality to Hyvä themes.

It is possible to use it as an AlpineJs component or with CSS-Selectors.


## Installation
```shell
composer require copex/module-hyva-ajax-add-to-cart
```

## Configuration
To configure the module go to Stores > Configuration > Sales > Checkout > Enable AJAX Add to Cart.

- Enable: Whether you want to use ajax cart or not (default: true)
- AJAX Add to Cart Delay: Delay how long the spinner should still spin after successful add-to-cart
- AJAX Add to Cart Selectors: CSS-Selector on which forms the Ajax-Cart should be applied
- Open Cart After Add To Cart: Open cart drawer after successful add to cart

## Code-Component Options
Each instance has some options how it should behave when adding to cart. These are the options:
- selectors: string of css-selectors. If it is null the current element will be used to bind to (default: null)
- dispatchSuccessEvent: dispatch success events (default: true)
  - product-addtocart-success
  - product-addtocart-success-<PRODUCT_ID>
- openCart: whether to open the cart drawer or not. (default: from the settings)
- delay: delay for how long the spinner should still be visible after success (default: from settings)
- showMessage: Show magento success message (default: false)
- onCompolete: Callback function to do anything after it is complete (if you don't want to use the events) (default: null)
- showError: Show error message (default: false)