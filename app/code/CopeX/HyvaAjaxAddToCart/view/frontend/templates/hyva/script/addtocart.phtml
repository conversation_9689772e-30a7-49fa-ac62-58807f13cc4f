<?php
/**
 * Copyright © CopeX. All rights reserved.

 */
declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\StoreConfig;
use \CopeX\HyvaAjaxAddToCart\Model\ConfigInterface;

/** @var Template  $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var StoreConfig $storeConfig */

$storeConfig = $viewModels->require(StoreConfig::class);
$enabled = $storeConfig->getStoreConfig(ConfigInterface::XML_PATH_ENABLE_AJAX_ADD_TO_CART) ?? false;
$delay = $storeConfig->getStoreConfig(ConfigInterface::XML_PATH_AJAX_ADD_TO_CART_DELAY)  ?? 0;
$openCart = $storeConfig->getStoreConfig(ConfigInterface::XML_PATH_AJAX_CART_OPEN_AFTER_ADD_TO_CART)  ? "true" : "false";

?>
<style>
    <?php /**
    .is-loading > :not(.loader) {
        visibility: hidden;
    } **/ ?>
    button.is-loading { opacity: 0.6};
</style>
<?php if($enabled) : ?>
<script>
    window.setAjaxCart = (customOptions = {}) => {
        return {
            options: Object.assign({selectors: null, dispatchSuccessEvent: true, openCart: <?= $openCart ?>, delay: "<?= $escaper->escapeJs($delay) ?>", showMessage: false, onComplete: null, showError: false}, customOptions),
            init() {
                const forms = this.options.selectors ? document.querySelectorAll(this.options.selectors) : [this.$el];
                forms.forEach(form => {
                    form.addEventListener('submit', async (e) => {
                        e.preventDefault();
                        const formData = new FormData(form);
                        const searchParams = new URLSearchParams(formData);
                        const button = this.getButton(form);
                        const loader = this.getLoader();

                        this.setButtonLoading(button, loader);

                        try {
                            const response = await fetch(e.currentTarget.action, {
                                method: 'POST',
                                body: searchParams,
                                headers: {
                                    'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
                                    'X-Requested-With': 'XMLHttpRequest'
                                }
                            });
                            if (!response.ok) {
                                return form.submit();
                            }
                            if (response.redirected) {
                                return window.location.href = response.url;
                            }
                            const data = await response.json();
                            const eventData = {added: Object.fromEntries(formData.entries()), response: data};
                            this.dispatchEvents(eventData);
                            this.openCart();
                            this.onComplete(eventData);
                            this.clearMessage();
                            window.dispatchEvent(new CustomEvent('reload-customer-section-data'));
                        } catch (err) {
                            window.dispatchEvent(new CustomEvent('product-addtocart-error'));
                            this.showErrorMessage(err);
                        } finally {
                            setTimeout(() => {
                                this.unsetButtonLoading(button, loader);
                            }, parseInt(this.options.delay, 10));
                        }
                    });
                });
            },
            getLoader() {
                const loader = document.createElement('div');

                loader.className = 'flex justify-center items-center loader';
                loader.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" xml:space="preserve" width="28" height="28">
                        <path fill="#fff"
                            d="M73 50c0-12.7-10.3-23-23-23S27 37.3 27 50m3.9 0c0-10.5 8.5-19.1 19.1-19.1S69.1 39.5 69.1 50">
                            <animateTransform attributeName="transform" attributeType="XML" type="rotate" dur="1s"
                                from="0 50 50" to="360 50 50"
                                repeatCount="indefinite"/>
                        </path>
                    </svg>
                `;
                return loader;
            },
            getButton(form) {
                return form.querySelector('button') ?
                    form.querySelector('button') :
                    document.getElementById('product-addtocart-button');
            },
            dispatchEvents(eventData) {
                if (this.options.dispatchSuccessEvent){
                    window.dispatchEvent(new CustomEvent('product-addtocart-success', {detail: eventData}));
                    window.dispatchEvent(new CustomEvent('product-addtocart-success-' + eventData.added.product, {detail: eventData}));
                }
            },
            openCart() {
                if (this.options.openCart) {
                    window.dispatchEvent(new CustomEvent('toggle-cart', {
                        detail: {
                            isOpen: true
                        }
                    }));
                }
            },
            onComplete(eventData) {
                if (typeof this.options.onComplete === "function") {
                    this.options.onComplete(eventData);
                }
            },
            clearMessage(){
                if(!this.options.showMessage){
                    const messageClearer = () => {
                        window.dispatchEvent(new CustomEvent('clear-messages'));
                        window.removeEventListener('private-content-loaded',messageClearer);
                    };
                    window.addEventListener('private-content-loaded', messageClearer)
                }
            },
            showErrorMessage(err){
                if (this.options.showError) {
                    typeof window.dispatchMessages !== "undefined" &&
                    window.dispatchMessages([{
                        text:'<?= $escaper->escapeJs(__('There was a problem adding your item to the cart.')) ?>',
                        type: 'error'
                    }], 5000);
                }
            },
            setButtonLoading(button, loader){
                button.prepend(loader);
                button.classList.add('flex', 'is-loading');
                button.disabled = true;
            },
            unsetButtonLoading(button, loader){
                loader.remove();
                button.classList.remove('is-loading');
                button.disabled = false;
            }
        }
    }
</script>
<?php else: ?>
<script>
    window.setAjaxCart = () => {return { init(){}}};
</script>
<?php endif; ?>