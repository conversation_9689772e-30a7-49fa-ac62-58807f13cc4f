<?php
/**
 * Copyright © CopeX. All rights reserved.

 */
declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\StoreConfig;
use \CopeX\HyvaAjaxAddToCart\Model\ConfigInterface;

/** @var Template  $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var StoreConfig $storeConfig */
$storeConfig = $viewModels->require(StoreConfig::class);
$selectors = $storeConfig->getStoreConfig(ConfigInterface::XML_PATH_AJAX_ADD_TO_CART_SELECTORS) ?? '.product_addtocart_form, #product_addtocart_form';

?>
<script>
    /** Init on pageload */
    window.setAjaxCart( {selectors: '<?= $selectors ?>', onComplete: function(data){window.dispatchEvent(new CustomEvent('product-added-to-cart',{detail: data}));}}).init();
</script>