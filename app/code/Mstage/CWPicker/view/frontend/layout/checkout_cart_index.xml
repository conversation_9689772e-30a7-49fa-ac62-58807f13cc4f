<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="page.top">
            <container name="service.wrapper.before.mobile" htmlTag="div" htmlClass="service-wrapper-before-mobile visible-xs">
                <block class="Magento\Cms\Block\Block" name="lageinfo-block-mobile">
                    <arguments>
                        <argument name="block_id" xsi:type="string">cartstaticcontent</argument>
                    </arguments>
                </block>
            </container>
        </referenceContainer>
    </body>
</page>
