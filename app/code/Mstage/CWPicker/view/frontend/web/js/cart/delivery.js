define(
    [
        'jquery',
        'mage/url',
        'loader'
    ],
    function ($, url) {
        'use strict';

        return function (config) {

            var selectField = $('#delivery-date-item-' + config.itemId);
            var cwpicker = '.cwpicker.item-' + config.itemId;
            var $loader = $(cwpicker).loader({
                icon: config.loaderUrl
            });

            function processChange() {
                $loader.loader("show");
                selectField.prop("disabled", true);
                var ajaxUrl = url.build('cwpicker/cart/change');
                var delivery_day = selectField.val();
                var min_delivery_day = selectField.prop('min');

                $.ajax({
                    url: ajaxUrl,
                    data: {
                        delivery_day: delivery_day,
                        min_delivery_day: min_delivery_day,
                        product_group: config.productGroup,
                        item_id: config.itemId
                    },
                    type: "POST",
                    success: function (result) {
                        selectField.prop("disabled", false);
                        $loader.loader("hide");
                        if (result.delivery_day && result.delivery_day !== false) {
                            var cwpicker_deliveryday = cwpicker + ' .cwpicker-deliveryday';
                            $(cwpicker_deliveryday).html(result.delivery_day);
                        }
                    },
                    error: function (result) {
                        selectField.prop("disabled", false);
                        $loader.loader("hide");
                    }
                });

                //make the value to the left of picker the same as the value in picker
                if (delivery_day && delivery_day !== false) {
                    var cwpicker_deliveryday = cwpicker + ' .cwpicker-deliveryday';
                    var d = new Date(delivery_day);
                    var dd = d.getDate() + '.' + (1 + d.getMonth()) + '.' + d.getUTCFullYear();
                    $(cwpicker_deliveryday).html(dd);
                }
            }

            // processChange();

            //input field change
            selectField.on("change", processChange);
        }
    }
);