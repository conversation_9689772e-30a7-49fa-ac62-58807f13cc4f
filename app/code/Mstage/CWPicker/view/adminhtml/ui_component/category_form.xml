<?xml version="1.0" encoding="UTF-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <fieldset name="general">
        <field name="delivery_time">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Mstage\CWPicker\Model\Config\Source\Options</item>
                <item name="config" xsi:type="array">
                    <item name="sortOrder" xsi:type="number">500</item>
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="formElement" xsi:type="string">select</item>
                    <item name="label" xsi:type="string" translate="true">Lieferzeit</item>
                </item>
            </argument>
        </field>
        <field name="delivery_time_infoblock">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="required" xsi:type="boolean">false</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                    <item name="sortOrder" xsi:type="number">501</item>
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="formElement" xsi:type="string">textarea</item>
                    <item name="label" translate="true" xsi:type="string">Kategorie - Lieferzeit Infotext</item>
                </item>
            </argument>
        </field>
    </fieldset>
</form>