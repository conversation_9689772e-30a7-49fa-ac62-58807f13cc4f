<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Mstage\CWPicker\Setup;

use Magento\Catalog\Model\Category;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Catalog\Setup\CategorySetupFactory;
use Magento\Framework\DB\Ddl\Table;
use Magento\Framework\Setup\InstallDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;


class InstallData implements InstallDataInterface
{
    /**
     * @var CategorySetupFactory
     */
    private $categorySetupFactory;

    /**
     * InstallData constructor.
     * @param CategorySetupFactory $categorySetupFactory
     */
    public function __construct(
        CategorySetupFactory $categorySetupFactory
    )
    {
        $this->categorySetupFactory = $categorySetupFactory;
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @param ModuleContextInterface $context
     */
    public function install(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();

        //Add category attribute for delivery-time
        $categorySetup = $this->categorySetupFactory->create(['setup' => $setup]);
        $entityTypeId = $categorySetup->getEntityTypeId(Category::ENTITY);
        $categorySetup->getDefaultAttributeSetId($entityTypeId);
        $categorySetup->addAttribute(
            Category::ENTITY, 'delivery_time', [
                'type' => 'int',
                'label' => 'Lieferzeit',
                'input' => 'select',
                'required' => false,
                'sort_order' => 100,
                'global' => ScopedAttributeInterface::SCOPE_STORE,
                'is_used_in_grid' => true,
                'is_visible_in_grid' => true,
                'is_filterable_in_grid' => true,
                'source' => 'Mstage\CWPicker\Model\Config\Source\Options',
                'visible' => true,
                'user_defined' => true,
                'default' => null,
                'group' => 'General Information',
                'backend' => ''
            ]
        );

        //Add delivery-day-attribute to Quote-Item
        $eavTable = $setup->getTable('quote_item');
        $columns = [
            'delivery_day' => [
                'type' => Table::TYPE_DATE,
                'nullable' => true,
                'comment' => 'Lieferdatum',
                'size' => null
            ],
        ];
        $connection = $setup->getConnection();
        foreach ($columns as $name => $definition) {
            $connection->addColumn($eavTable, $name, $definition);
        }

        //Add delivery-day-attribute to Sales-Order-Item
        $eavTable = $setup->getTable('sales_order_item');
        $columns = [
            'delivery_day' => [
                'type' => Table::TYPE_DATE,
                'nullable' => true,
                'comment' => 'Lieferdatum',
                'size' => null
            ],
        ];
        $connection = $setup->getConnection();
        foreach ($columns as $name => $definition) {
            $connection->addColumn($eavTable, $name, $definition);
        }

        $setup->endSetup();
    }
}