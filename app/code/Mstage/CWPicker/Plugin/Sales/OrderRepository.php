<?php

namespace Mstage\CWPicker\Plugin\Sales;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Api\Data\OrderExtensionFactory;


class OrderRepository
{
    protected OrderExtensionFactory $extensionFactory;
    private OrderItemRepositoryPlugin $orderItemRepositoryPlugin;

    public function __construct(OrderExtensionFactory $extensionFactory, OrderItemRepositoryPlugin $orderItemRepositoryPlugin)
    {
        $this->extensionFactory = $extensionFactory;
        $this->orderItemRepositoryPlugin = $orderItemRepositoryPlugin;
    }

    public function afterGet(OrderRepositoryInterface $subject, $order)
    {
        $extensionAttributes = $order->getExtensionAttributes();
        $extensionAttributes = $extensionAttributes ?: $this->extensionFactory->create();
        $extensionAttributes->setIsCustomDeliveryDay($order->getData('is_custom_delivery_day'));
        $extensionAttributes->setDeliveryComment($order->getData('delivery_comment'));
        $order->setExtensionAttributes($extensionAttributes);
        $this->getOrderItemInfo($order);
        return $order;
    }

    protected function getOrderItemInfo($order)
    {
        foreach ($order->getItems() as $orderItem) {
            $this->orderItemRepositoryPlugin->attachExtensionAttributes($orderItem);
        }
    }

    /**
     * @param \Magento\Sales\Api\OrderRepositoryInterface $subject
     * @param \Magento\Sales\Model\ResourceModel\Order\Collection $resultOrder
     * @return \Magento\Sales\Model\ResourceModel\Order\Collection
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterGetList(
        \Magento\Sales\Api\OrderRepositoryInterface $subject,
        \Magento\Sales\Model\ResourceModel\Order\Collection $resultOrder
    ) {
        /** @var  $order */
        foreach ($resultOrder->getItems() as $order) {
            $this->afterGet($subject, $order);
        }
        return $resultOrder;
    }
}