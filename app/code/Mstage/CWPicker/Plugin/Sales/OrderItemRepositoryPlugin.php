<?php

namespace Mstage\CWPicker\Plugin\Sales;

use Magento\Sales\Api\Data\OrderItemExtensionFactory;
use Magento\Sales\Api\Data\OrderItemExtensionInterface;
use Magento\Sales\Api\OrderItemRepositoryInterface;
use Magento\Sales\Api\Data\OrderItemInterface;
class OrderItemRepositoryPlugin
{

    /**
     * Order Extension Attributes Factory
     *
     * @var OrderItemExtensionFactory
     */
    protected $extensionFactory;

    /**
     * OrderItemRepositoryPlugin constructor
     *
     * @param OrderItemExtensionFactory $extensionFactory
     */
    public function __construct(OrderItemExtensionFactory $extensionFactory)
    {
        $this->extensionFactory = $extensionFactory;
    }

    /**
     *
     * @param OrderItemRepositoryInterface $subject
     * @param OrderItemInterface $orderItem
     *
     * @return OrderItemInterface
     */
    public function afterGet(OrderItemRepositoryInterface $subject, OrderItemInterface $orderItem)
    {
        return $this->attachExtensionAttributes($orderItem);
    }

    public function attachExtensionAttributes(OrderItemInterface $orderItem): OrderItemInterface
    {
        $extensionAttributes = $orderItem->getExtensionAttributes();
        $extensionAttributes = $extensionAttributes ?: $this->extensionFactory->create();
        $extensionAttributes->setIsCustomDeliveryDay($orderItem->getData('is_custom_delivery_day'));
        $extensionAttributes->setDeliveryDay($orderItem->getData('delivery_day'));
        $orderItem->setExtensionAttributes($extensionAttributes);
        return $orderItem;
    }

}