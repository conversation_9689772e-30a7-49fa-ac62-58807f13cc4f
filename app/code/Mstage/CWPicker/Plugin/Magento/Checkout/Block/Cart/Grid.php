<?php

namespace Mstage\CWPicker\Plugin\Magento\Checkout\Block\Cart;

use Exception;
use Mstage\CWPicker\Helper\Attribute as AttributeHelper;
use \Mstage\CWPicker\Logger\Logger;
use Magento\Checkout\Model\Session;

class Grid
{
    /**
     * @var AttributeHelper
     */
    protected $attributeHelper;

    /**
     * @var Logger
     */
    protected $logger;

    private $newResult = null;
    private Session $checkoutSession;

    /**
     * Grid constructor.
     * @param AttributeHelper $attributeHelper
     * @param Logger          $logger
     */
    public function __construct(
        AttributeHelper $attributeHelper,
        Session $checkoutSession,
        Logger $logger

    ) {
        $this->attributeHelper = $attributeHelper;
        $this->logger = $logger;
        $this->checkoutSession = $checkoutSession;
    }

    /**
     * @param \Magento\Checkout\Block\Cart\Grid $subject
     * @param                                   $result
     * @return array
     * @throws Exception
     */
    public function afterGetItems(\Magento\Checkout\Block\Cart\Grid $subject, $result)
    {
        if ($this->newResult) {
            return $this->newResult;
        }
        if ($result && !empty($result)) {
            //refresh delivery dates to make sure, that all dates are valid
            $quote = $this->checkoutSession->getQuote();
            $this->attributeHelper->refreshQuoteItemsDeliveryDates($quote);

            $productGroups = $this->getGroupedItems($result);
            $this->enhanceEarliestDeliveryDatePerGroup($productGroups);

            //go through the groups and assign the Zubehoer from the same group to the product with the earliest delivery date
            $newResult = [];
            foreach ($productGroups as $productGroupKey => $productGroupData) {

                $ignoreAccessories = false;
                $itemsForResult = $this->getPrimaryItems($productGroups,$productGroupKey);
                //check, if we have normal products in this group and if not, use Zubehoer items
                if (!isset($productGroups[$productGroupKey]['items']) && isset($productGroups[$productGroupKey]['accessories'])) {
                    $ignoreAccessories = true;
                }

                (current($itemsForResult))->setIsFirstItemOfGroup(true);

                //go through the products from this group
                foreach ($itemsForResult as $productItem) {
                    //if this is a product (not a lonely Zubehoer) item with the earliest delivery date, then put Zubehoer from this group under it
                    if (!$ignoreAccessories && $productItem->getId() === $productGroupData['earliestDeliverableItemId'] &&
                        isset($productGroups[$productGroupKey]['accessories'])) {
                        //add this item to the result
                        $newResult[] = $productItem;

                        //go through the Zubehoer items, add them to the result and mark them appropriately for output in default.phtml
                        $cnt = count($productGroups[$productGroupKey]['accessories']) -1;
                        foreach ($productGroups[$productGroupKey]['accessories'] as $key => $accessoryItem) {

                            //mark as first Zubehoer in the group
                            if ($key === 0) {
                                $accessoryItem->setIsFirstAttachmentItemOfGroup(true);
                            }

                            //mark as last item in the group
                            if ( $key === $cnt) {
                                //$accessoryItem->setIsLastItemOfGroup(true);
                                $accessoryItem->setMainItemId($productItem->getId());
                            }

                            //add to result
                            $newResult[] = $accessoryItem;
                        }
                    } else {
                        //add this item to the result
                        $newResult[] = $productItem;
                    }
                }
                (end($newResult))->setIsLastItemOfGroup(true);
            }
            $this->newResult = $newResult;
            return $newResult;
        }
        return $result;
    }

    /**
     * @param $result
     * @return array
     */
    private function getGroupedItems($result): array
    {
        $productGroups = [];
        usort($result, function ($itemA, $itemB) { return $itemA->getDeliveryDay() <=> $itemB->getDeliveryDay();});

        //go through the items and order them by their related parent id (previously: product group)
        foreach ($result as $quoteItem) {
            //use related parent id as group-key
            $relatedParentId = $quoteItem->getRelatedParentId();
            $isZubehoer = $this->attributeHelper->getIsZubehoer($quoteItem);
            $deliveryDay = $quoteItem->getDeliveryDay();

            $productGroups[$relatedParentId]['deliveryDay'] = $deliveryDay;
            $productGroups[$relatedParentId]['earliestDeliveryDay'] = strtotime(date('2100-01-01'));
            $productGroups[$relatedParentId]['earliestDeliverableItemId'] = 0;
            $productGroups[$relatedParentId]['productGroup'] = $this->attributeHelper->getProductGroup($quoteItem);

            //save the item to the corresponding array, based on if it is a Zubehoer
            if ($isZubehoer) {
                $productGroups[$relatedParentId]['accessories'][] = $quoteItem;
            } else {
                $productGroups[$relatedParentId]['items'][] = $quoteItem;
                $productGroups[$relatedParentId]['productId'] = $quoteItem->getProductId();
                $productGroups[$relatedParentId]['relatedParentId'] = $relatedParentId; //redundant but helpful for array_search
            }
        }

        foreach ($productGroups as $key => $group) {
            if (!isset($group['items'])) {
                $mainItems = array_reverse(array_column($productGroups, 'productId', 'relatedParentId'), true);
                //array reverse haengts an das zuletzt hinzugefuegte Hauptprodukt an... und wenn wieder eins neu dazu kommt, wirds dazu gehaengt - suboptimal...
                // currently this takes just the first fitting main product and adds the related product
                if (($retrievedKey = array_search($key, $mainItems)) !== false) { // check if productId exists and if it exists if
                    $accessoriesPart1 = $productGroups[$retrievedKey]['accessories'] ?? [];
                    $newDeliveryDate = $productGroups[$retrievedKey]['deliveryDay'];

                    foreach ($group['accessories'] as $accessoryItem) {
                        $accessoryItem->setDeliveryDay($newDeliveryDate);
                    }
                    $productGroups[$retrievedKey]['accessories'] = array_merge($accessoriesPart1, $group['accessories']);
                }
                else {
                    // rename key of group
                    $newGroupKey = "pg-" . $group['productGroup'];
                    // if group already exists, just merge accessories
                    if (isset($productGroups[$newGroupKey])) {
                        //add accessories of group to that
                        $productGroups[$newGroupKey]['accessories'] = array_merge($productGroups[$newGroupKey]['accessories'], $group['accessories']);
                    }
                    else { // move the entire group
                        $productGroups[$newGroupKey] = $group;
                    }
                }
                // unset the key for this group as we have renamed it
                unset($productGroups[$key]);
            }
        }
        return $productGroups;
    }

    /**
     * go through the groups and find a product with the earliest delivery date
     * (if no product is present in a group, then get the date from Zubehoer)
     * @param array $productGroups
     */
    private function enhanceEarliestDeliveryDatePerGroup(
        array &$productGroups
    ) {
        //go through the groups and find a product with the earliest delivery date (if no product is present in a group, then get the date from Zubehoer)
        foreach ($productGroups as $productGroupKey => $productGroupData) {
            $itemsForComparison = $this->getPrimaryItems($productGroups, $productGroupKey);

            //compare delivery date and set it as earliest for the whole group, if that is so
            foreach ($itemsForComparison as $item) {
                $itemDeliveryDayTimestamp = strtotime((string)$item->getDeliveryDay());
                if ($itemDeliveryDayTimestamp < $productGroups[$productGroupKey]['earliestDeliveryDay']) {
                    $productGroups[$productGroupKey]['earliestDeliveryDay'] = $itemDeliveryDayTimestamp;
                    $productGroups[$productGroupKey]['earliestDeliverableItemId'] = $item->getId();
                }
            }
        }
    }

    /**
     * check, if we have normal products in this group and if not, use Zubehoer items
     * @param array $productGroups
     * @param       $productGroupKey
     * @return array
     */
    private function getPrimaryItems(array $productGroups, $productGroupKey): array
    {
        if (isset($productGroups[$productGroupKey]['items'])) {
            $itemsForComparison = $productGroups[$productGroupKey]['items'];
        } elseif (isset($productGroups[$productGroupKey]['accessories'])) {
            $itemsForComparison = $productGroups[$productGroupKey]['accessories'];
        } else {
            $itemsForComparison = [];
            $this->logger->info(__CLASS__ .
                                ' - something is very wrong here: we have a product group, but no items for it. This should never happen. Ever! ');
        }
        return  $itemsForComparison;
    }
}
