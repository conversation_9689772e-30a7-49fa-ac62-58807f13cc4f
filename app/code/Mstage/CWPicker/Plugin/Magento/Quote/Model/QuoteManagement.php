<?php

namespace Mstage\CWPicker\Plugin\Magento\Quote\Model;

use Magento\Quote\Model\Quote;
use Mstage\CWPicker\Helper\Attribute as AttributeHelper;
use Mstage\CWPicker\Logger\Logger;

class QuoteManagement
{
    protected $attributeHelper;
    protected $logger;

    /**
     * Quote constructor.
     * @param AttributeHelper $attributeHelper
     * @param Logger $logger
     */
    public function __construct(
        AttributeHelper $attributeHelper,
        Logger $logger
    ) {
        $this->attributeHelper = $attributeHelper;
        $this->logger = $logger;
    }

    public function beforeSubmit(
        \Magento\Quote\Model\QuoteManagement $subject,
        Quote $quote,
        array $orderData = [])
    {
        //refresh delivery dates before submitting the quote (mostly because of the cases, when customers have a cart for a long time)
        $this->attributeHelper->refreshQuoteItemsDeliveryDates($quote);
    }

}
