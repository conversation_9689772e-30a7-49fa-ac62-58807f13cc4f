<?php

namespace Mstage\CWPicker\Block\Adminhtml\Order\View\Items\Renderer;

use Magento\Backend\Block\Template\Context;
use Magento\CatalogInventory\Api\StockConfigurationInterface;
use Magento\CatalogInventory\Api\StockRegistryInterface;
use Magento\Checkout\Helper\Data;
use Magento\Framework\DataObject;
use Magento\Framework\Registry;
use Magento\GiftMessage\Helper\Message;
use Magento\Sales\Block\Adminhtml\Order\View\Items\Renderer\DefaultRenderer as SalesDefaultRenderer;
use Magento\Sales\Model\Order\Item;
use Mstage\CWPicker\Helper\Attribute;

class DefaultRenderer extends SalesDefaultRenderer
{
    /**
     * @var Attribute
     */
    protected $attributeHelper;

    /**
     * DefaultRenderer constructor.
     * @param Context $context
     * @param StockRegistryInterface $stockRegistry
     * @param StockConfigurationInterface $stockConfiguration
     * @param Registry $registry
     * @param Message $messageHelper
     * @param Data $checkoutHelper
     * @param Attribute $attributeHelper
     * @param array $data
     */
    public function __construct(
        Context $context,
        StockRegistryInterface $stockRegistry,
        StockConfigurationInterface $stockConfiguration,
        Registry $registry,
        Message $messageHelper,
        Data $checkoutHelper,
        Attribute $attributeHelper,
        array $data = []
    ) {
        parent::__construct($context, $stockRegistry, $stockConfiguration, $registry, $messageHelper, $checkoutHelper, $data);
        $this->attributeHelper = $attributeHelper;
    }

    /**
     * @param DataObject|Item $item
     * @param string $column
     * @param null $field
     * @return string
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @since 100.1.0
     */
    public function getColumnHtml(DataObject $item, $column, $field = null)
    {
        $html = '';
        switch ($column) {
            case 'product':
                if ($this->canDisplayContainer()) {
                    $html .= '<div id="' . $this->getHtmlId() . '">';
                }
                $html .= $this->getColumnHtml($item, 'name');
                if ($this->canDisplayContainer()) {
                    $html .= '</div>';
                }
                break;
            case 'status':
                $html = $item->getStatus();
                break;
            case 'price-original':
                $html = $this->displayPriceAttribute('original_price');
                break;
            case 'tax-amount':
                $html = $this->displayPriceAttribute('tax_amount');
                break;
            case 'tax-percent':
                $html = $this->displayTaxPercent($item);
                break;
            case 'discont':
                $html = $this->displayPriceAttribute('discount_amount');
                break;
            case 'delivery-week':
                $html = '';
                if ($item->getDeliveryDay()) {
                    $html = $this->attributeHelper->getDeliveryDate($item->getDeliveryDay(),'W');
                }
                break;
            case 'is-custom-delivery-day':
                $attrValue = $item->getData("is_custom_delivery_day");
                switch ($attrValue) {
                    case "1";
                        $html = __("Yes");
                        break;
                    default:
                        $html = __("No");
                        break;
                }
                break;
            default:
                $html = parent::getColumnHtml($item, $column, $field);
        }
        return $html;
    }
}