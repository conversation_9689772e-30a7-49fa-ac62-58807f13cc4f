<?php

namespace Mstage\CWPicker\Block\Cart\Item;

    use DateTime;
    use Magento\Catalog\Block\Product\ImageBuilder;
    use Magento\Catalog\Helper\Product\Configuration;
    use Magento\Checkout\Model\Session;
    use Magento\Framework\Message\ManagerInterface;
    use Magento\Framework\Module\Manager;
    use Magento\Framework\Pricing\PriceCurrencyInterface;
    use Magento\Framework\Url\Helper\Data;
    use Magento\Framework\View\Element\Message\InterpretationStrategyInterface;
    use Magento\Framework\View\Element\Template\Context;
    use Mstage\CWPicker\Helper\Attribute as AttributeHelper;
    use Mstage\CWPicker\Logger\Logger;


class Renderer extends \Magento\Checkout\Block\Cart\Item\Renderer
{
    /**
     * @var AttributeHelper
     */
    protected $attributeHelper;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * Renderer constructor.
     * @param Context $context
     * @param Configuration $productConfig
     * @param Session $checkoutSession
     * @param ImageBuilder $imageBuilder
     * @param Data $urlHelper
     * @param ManagerInterface $messageManager
     * @param PriceCurrencyInterface $priceCurrency
     * @param Manager $moduleManager
     * @param InterpretationStrategyInterface $messageInterpretationStrategy
     * @param AttributeHelper $attributeHelper
     * @param Logger $logger
     * @param array $data
     */
    public function __construct(
        Context $context,
        Configuration $productConfig,
        Session $checkoutSession,
        ImageBuilder $imageBuilder,
        Data $urlHelper,
        ManagerInterface $messageManager,
        PriceCurrencyInterface $priceCurrency,
        Manager $moduleManager,
        InterpretationStrategyInterface $messageInterpretationStrategy,
        AttributeHelper $attributeHelper,
        Logger $logger,
        array $data = []
    ) {
        $this->attributeHelper = $attributeHelper;
        $this->logger = $logger;
        parent::__construct(
            $context,
            $productConfig,
            $checkoutSession,
            $imageBuilder,
            $urlHelper, $messageManager,
            $priceCurrency,
            $moduleManager,
            $messageInterpretationStrategy,
            $data
        );
    }

    /**
     * Get last week of current year (28.12. is always in last week)
     *
     * @return int
     */
    public function getLastWeekOfYear()
    {
        return idate('W', mktime(0, 0, 0, 12, 28));
    }

    /**
     * @param string $currentDeliveryDay
     * @return string
     */
    public function formatDeliveryDate($currentDeliveryDay)
    {
        $currentDeliveryDayArray = explode('-', (string)$currentDeliveryDay);
        $currentDeliveryDayArray = array_reverse($currentDeliveryDayArray);
        return implode('.', $currentDeliveryDayArray);
    }

    /**
     * @param $item
     * @return bool
     */
    public function isZubehoer($item)
    {
        return $this->attributeHelper->getIsZubehoer($item);
    }

    /**
     * @param $item
     * @return bool
     */
    public function isLastItemOfGroup($item)
    {
        return $this->attributeHelper->isLastItemOfGroup($item);
    }

    /**
     * @param $item
     * @return bool
     */
    public function isFirstAttachmentItemOfGroup($item)
    {
        if ($item->getIsFirstAttachmentItemOfGroup() && $item->getIsFirstAttachmentItemOfGroup() === true) {
            return true;
        }
        return false;
    }

    /**
     * @param $item
     * @return string
     */
    public function getProductGroup($item)
    {
        return $this->attributeHelper->getProductGroup($item);
    }

    /**
     * @param $deliveryDay
     * @return bool|DateTime|string
     */
    public function getDeliveryWeek($deliveryDay)
    {
        return $this->attributeHelper->getDeliveryDate($deliveryDay, 'W');
    }

    /**
     * Method, that returns:
     * - the ID of a product item, that has the earliest delivery day from this Zubehoer's product group
     * - the ID of this item in all other cases
     *
     * @param $item
     * @return int
     */
    public function getMainItemId ($item)
    {
        return $this->attributeHelper->getMainItemId($item);
    }

    /**
     * Get a quote item by its ID
     *
     * @param $id
     * @return false|\Magento\Quote\Model\Quote\Item
     */
    public function getItemById ($id)
    {
        return $this->_checkoutSession->getQuote()->getItemById($id);
    }

    /**
     * Method, that calls and passes the results of a method, that returns the first possible delivery date for a quote item
     *
     * @param \Magento\Quote\Model\Quote\Item $item
     * @return DateTime|string
     * @throws \Exception
     */
    public function getFirstPossibleDeliveryDay ($item)
    {
        return $this->attributeHelper->getFirstPossibleDeliveryDate($item, 'Y-m-d');
    }

    public function getLastPossibleDeliveryDate($firstPossibleDeliveryDate)
    {
        return $this->attributeHelper->getLastPossibleDeliveryDate($firstPossibleDeliveryDate, 'Y-m-d');
    }

    public function getMainItem($item)
    {
        return $this->attributeHelper->getMainItem($item);
    }

}