<?php


namespace Mstage\CWPicker\Block\Cart\Item\Renderer;

use DateTime;
use Magento\Catalog\Block\Product\ImageBuilder;
use Magento\Catalog\Helper\Product\Configuration;
use Magento\Catalog\Model\Product\Configuration\Item\ItemResolverInterface;
use Magento\Checkout\Model\Session;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\Module\Manager;
use Magento\Framework\Pricing\PriceCurrencyInterface;
use Magento\Framework\Url\Helper\Data;
use Magento\Framework\View\Element\Message\InterpretationStrategyInterface;
use Magento\Framework\View\Element\Template\Context;
use Mstage\CWPicker\Helper\Attribute as AttributeHelper;
use Mstage\CWPicker\Logger\Logger;


class Configurable extends \Magento\ConfigurableProduct\Block\Cart\Item\Renderer\Configurable
{
    /**
     * @var AttributeHelper
     */
    protected $attributeHelper;

    /**
     * @var Logger
     */
    protected $logger;

    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Catalog\Helper\Product\Configuration $productConfig,
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Catalog\Block\Product\ImageBuilder $imageBuilder,
        \Magento\Framework\Url\Helper\Data $urlHelper,
        \Magento\Framework\Message\ManagerInterface $messageManager,
        PriceCurrencyInterface $priceCurrency,
        \Magento\Framework\Module\Manager $moduleManager,
        InterpretationStrategyInterface $messageInterpretationStrategy,
        AttributeHelper $attributeHelper,
        Logger $logger,
        array $data = [],
        ItemResolverInterface $itemResolver = null
    ) {
        parent::__construct($context, $productConfig, $checkoutSession, $imageBuilder, $urlHelper, $messageManager,
            $priceCurrency, $moduleManager, $messageInterpretationStrategy, $data, $itemResolver);
        $this->attributeHelper = $attributeHelper;
        $this->logger = $logger;
    }

    /**
     * Get last week of current year (28.12. is always in last week)
     *
     * @return int
     */
    public function getLastWeekOfYear()
    {
        return idate('W', mktime(0, 0, 0, 12, 28));
    }

    /**
     * @param string $currentDeliveryDay
     * @return string
     */
    public function formatDeliveryDate($currentDeliveryDay)
    {
        $currentDeliveryDayArray = explode('-', $currentDeliveryDay);
        $currentDeliveryDayArray = array_reverse($currentDeliveryDayArray);
        return implode('.', $currentDeliveryDayArray);
    }

    /**
     * @param $item
     * @return bool
     */
    public function isZubehoer($item)
    {
        return $this->attributeHelper->getIsZubehoer($item);
    }

    /**
     * @param $item
     * @return bool
     */
    public function isLastItemOfGroup($item)
    {
        if ($item->getIsLastItemOfGroup() && $item->getIsLastItemOfGroup() === true) {
            return true;
        }
        return false;
    }

    /**
     * @param $item
     * @return bool
     */
    public function isFirstAttachmentItemOfGroup($item)
    {
        if ($item->getIsFirstAttachmentItemOfGroup() && $item->getIsFirstAttachmentItemOfGroup() === true) {
            return true;
        }
        return false;
    }

    /**
     * @param $item
     * @return string
     */
    public function getProductGroup($item)
    {
        return $this->attributeHelper->getProductGroup($item);
    }

    /**
     * @param $deliveryDay
     * @return bool|DateTime|string
     */
    public function getDeliveryWeek($deliveryDay)
    {
        return $this->attributeHelper->getDeliveryDate($deliveryDay, 'W');
    }

    /**
     * Method, that returns:
     * - the ID of a product item, that has the earliest delivery day from this Zubehoer's product group
     * - the ID of this item in all other cases
     *
     * @param $item
     * @return int
     */
    public function getMainItemId ($item)
    {
        if ($item->getMainItemId()) {
            return $item->getMainItemId();
        }
        else {
            return $item->getId();
        }
    }

    /**
     * Get a quote item by its ID
     *
     * @param $id
     * @return false|\Magento\Quote\Model\Quote\Item
     */
    public function getItemById ($id)
    {
        return $this->_checkoutSession->getQuote()->getItemById($id);
    }

    /**
     * Method, that calls and passes the results of a method, that returns the first possible delivery date for a quote item
     *
     * @param \Magento\Quote\Model\Quote\Item $item
     * @return DateTime|string
     * @throws \Exception
     */
    public function getFirstPossibleDeliveryDay ($item)
    {
        return $this->attributeHelper->getFirstPossibleDeliveryDate($item, 'Y-m-d');
    }

}