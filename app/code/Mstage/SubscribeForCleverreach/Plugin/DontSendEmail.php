<?php

namespace Mstage\SubscribeForCleverreach\Plugin;

use Magento\Newsletter\Model\Subscriber;

class DontSendEmail
{

    /**
     * @param \Magento\Newsletter\Model\Subscriber $subject
     * @param callable                             $proceed
     */
    public function aroundSendConfirmationSuccessEmail(\Magento\Newsletter\Model\Subscriber $subject, callable $proceed)
    {
        // return $proceed();
    }
}