<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Checkout\Model\CompositeConfigProvider">
        <arguments>
            <argument name="configProviders" xsi:type="array">
                <item name="cms_block_config_provider" xsi:type="object">Mstage\CheckoutBlockProvider\Model\ConfigProvider</item>
            </argument>
        </arguments>
    </type>
    <type name="Mstage\CheckoutBlockProvider\Model\ConfigProvider">
        <arguments>
            <argument name="blockIds" xsi:type="array">
                <item name="checkout_block" xsi:type="string">opc_bottom</item>
                <!--item name="another_block" xsi:type="string">another_block</item-->
            </argument>
        </arguments>
    </type>
</config>
