<?php

namespace Mstage\ExtendConfigurable\Controller\Index;

use Magento\Framework\App\Action\Context;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Registry;
use Magento\Catalog\Model\ProductRepository;

class Upsell extends \Magento\Framework\App\Action\Action
{
    /**
     * @var PageFactory
     */
    public $resultPageFactory;

    /**
     * @var JsonFactory
     */
    protected $resultJsonFactory;

    /**
     * @var Registry
     */
    private $coreRegistry;

    /**
     * @var ProductRepository
     */
    private $productRepository;

    /**
     * @param Context $context
     * @param PageFactory $resultPageFactory
     * @param JsonFactory $resultJsonFactory
     * @param Registry $registry
     * @param ProductRepository $productRepository
     */
    public function __construct(
        Context $context,
        PageFactory $resultPageFactory,
        JsonFactory $resultJsonFactory,
        Registry $registry,
        ProductRepository $productRepository
    )
    {
        $this->resultPageFactory = $resultPageFactory;
        $this->resultJsonFactory = $resultJsonFactory;
        $this->coreRegistry = $registry;
        $this->productRepository = $productRepository;

        parent::__construct($context);
    }

    /**
     * Index action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute()
    {
        if (!$this->getRequest()->getParam('productId') ||
            !$this->getRequest()->getParam('parentProductId') ||
            !$this->getRequest()->getParam('redirectLocation')) {
            return;
        }

        $this->getRequest()->setRequestUri($this->getRequest()->getParam('redirectLocation'));

        $resultJson = $this->resultJsonFactory->create();
        $resultPage = $this->resultPageFactory->create();

        $product = $this->productRepository->getById($this->getRequest()->getParam('productId'));

        if (count($product->getUpSellProductCollection()->setPositionOrder()->addStoreFilter()) === 0) {
            $product = $this->productRepository->getById($this->getRequest()->getParam('parentProductId'));
        }

        $resultPage->getLayout()
            ->createBlock('Magento\Catalog\Block\Product\ProductList\Upsell',
                'product.info.details.upsell')
            ->setData('type', 'upsell')
            ->setTemplate('Magento_Catalog::product/list/upselling.phtml');

        $block = $resultPage->getLayout()->getBlock('product.info.details.upsell');

        $this->coreRegistry->unregister('product');
        $this->coreRegistry->register('product', $product);

        $resultJson->setData(['html' => $block->toHtml()]);
        return $resultJson;
    }

}