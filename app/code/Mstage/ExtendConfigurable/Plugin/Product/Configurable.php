<?php

namespace Mstage\ExtendConfigurable\Plugin\Product;

use Magento\Framework\Serialize\SerializerInterface;
use CopeX\ShippingInfo\ViewModel\ShippingInfo;

class Configurable
{

    private $serializer;

    protected $shippingInfo;

    public function __construct(
        SerializerInterface $serializer,
        ShippingInfo $shippingInfo
    )
    {
        $this->serializer = $serializer;
        $this->shippingInfo = $shippingInfo;
    }

    public function afterGetJsonConfig(
        \Magento\ConfigurableProduct\Block\Product\View\Type\Configurable $subject,
        $jsonResult
    )
    {
        $result = $this->serializer->unserialize($jsonResult);

        //get the delivery time infoblocks for the variants of this configurable product
        $deliveryTimeInfos = [];
        foreach ($subject->getAllowProducts() as $product) {
            $deliveryTimeInfos[$product->getId()] = [ 'custom' => true, 'content' => $this->shippingInfo->getDeliveryTimeInfoblock($product) ];
        }

        $result['optionDeliveryTime'] = $deliveryTimeInfos;

        return $this->serializer->serialize($result);
    }
}