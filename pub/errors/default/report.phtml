<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
// @codingStandardsIgnoreFile
?>

<h1>There has been an error processing your request</h1>
<?php if ($this->showSentMsg): ?>
    <div class="message success message-success">
        <div>Your message was submitted and will be responded as soon as possible. Thank you for reporting.</div>
    </div>
<?php endif; ?>
<?php if ($this->showSendForm): ?>
    <div class="message info message-info">
        <div>
            We are currently experiencing some technical issues. We apologize for the inconvenience and will contact you shortly to resolve the issue. To help us serve you please fill in the form below.
        </div>
    </div>
    <?php if ($this->showErrorMsg): ?>
        <div class="message error message-error">
            <div>
                Please fill all required fields with valid information
            </div>
        </div>
    <?php endif; ?>
    <form action="<?= "{$this->getBaseUrl(true)}errors/report.php?id={$this->reportId}" ?>" method="post" id="form-validate" class="form">
        <fieldset class="fieldset" data-hasrequired="* Required Fields">
            <legend class="legend"><span>Personal Information</span></legend><br />
            <div class="field firstname required">
                <label for="firstname" class="label">First Name</label>
                <div class="control">
                    <input type="text" name="firstname" id="firstname" value="<?= $this->postData['firstName'] ?>" title="First Name" class="required-entry input-text" />
                </div>
            </div>
            <div class="field lastname required">
                <label for="lastname" class="label">Last Name</label>
                <div class="control">
                    <input type="text" name="lastname" id="lastname" value="<?= $this->postData['lastName'] ?>" title="Last Name" class="required-entry input-text" />
                </div>
            </div>
            <div class="field email required">
                <label for="email_address" class="label">Email Address</label>
                <div class="control">
                    <input type="text" name="email" id="email_address" value="<?= $this->postData['email'] ?>" title="Email Address" class="validate-email required-entry input-text" />
                </div>
            </div>
            <div class="field telephone">
                <label for="telephone" class="label">Telephone</label>
                <div class="control">
                    <input type="text" name="telephone" id="telephone" value="<?= $this->postData['telephone'] ?>" title="Telephone" class="input-text" />
                </div>
            </div>
            <div class="field comment">
                <label for="comment" class="label">Comment</label>
                <div class="control">
                    <textarea name="comment" cols="5" rows="3" class="textarea"><?= $this->postData['comment'] ?></textarea>
                </div>
            </div>
        </fieldset>
        <div class="actions-toolbar">
            <button name="submit" class="action primary" type="submit"><span>Submit</span></button>
        </div>
    </form>
<?php elseif ('' == $this->reportAction): ?>
    <p><em>Exception printing is disabled by default for security reasons.</em></p>
<?php endif; ?>

<?php if ($this->reportAction == 'print'): ?>
    <div class="trace-container">
        <div class="trace">
            <pre><?= htmlspecialchars($this->reportData[0]) ?></pre>
        </div>
    </div>
<?php endif; ?>
<?php if ($this->reportId): ?>
    <p>Error log record number: <?= $this->reportId ?></p>
<?php endif; ?>
